import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'terms'
    },
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full'
      },
      {
        path: 'overview',
        loadComponent: () => import('./overview/overview.component').then(m => m.OverviewComponent),
        data: {
          title: 'Discover'
        }
      },
      {
        path: 'application-info/:appId',
        loadComponent: () => import('./application-info/application-info.component').then(m => m.ApplicationInfoComponent),
        data: {
          title: 'application-info'
        }
      }
    ]
  }
];

