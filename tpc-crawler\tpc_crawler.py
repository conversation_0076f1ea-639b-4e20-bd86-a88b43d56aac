from selenium import webdriver
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import undetected_chromedriver as uc
import time, os, random, datetime, logging, requests, re,sys,json
from datetime import datetime,date
from redis import Redis 
from PIL import Image
from io import BytesIO
import pytesseract
import re, json,socket
import RPA

def tpcPicToVCode(file):
    captcha = Image.open(BytesIO(file))
    captcha1 = grayScaler(captcha, 80)
    captcha2 = dePoint(captcha1)
    result1 = pytesseract.image_to_string(captcha1)
    result2 = pytesseract.image_to_string(captcha2)
    result1 = re.sub('[^0-9]', '', result1)
    result2 = re.sub('[^0-9]', '', result2)

    vCode = None
    try:
        if len(result1) == 5:
            vCode = result1
            return vCode
        elif len(result2) == 5:
            vCode = result2
            return vCode
    except:
        return vCode

def crop(file, box, save_as):
    im = Image.open(file)
    cropped_im = im.crop(box) 
    cropped_im.save(save_as)

def grayScaler(Image, threshold=80):
    Image = Image.convert("L")
    pixels = Image.load()
    for x in range(Image.width):
        for y in range(Image.height):
            if pixels[x, y] > threshold:
                pixels[x, y] = 255
            else:
                pixels[x, y] = 0
    return Image

def dePoint(Image):
    pixdata = Image.load()
    w,h = Image.size
    for x in range(1, w-1):
        for y in range(1, h-1):
            white_point = 0
            if pixdata[x,y-1] > 245:
                white_point += 1
            if pixdata[x,y+1] > 245:
                white_point += 1
            if pixdata[x-1,y] > 245:
                white_point += 1
            if pixdata[x+1,y] > 245:
                white_point += 1
            if pixdata[x-1,y-1] > 245:
                white_point += 1
            if pixdata[x-1,y+1] > 245:
                white_point += 1
            if pixdata[x+1,y-1] > 245:
                white_point += 1
            if pixdata[x+1,y+1] > 245:
                white_point += 1
            if white_point > 5:
                pixdata[x,y] = 255
    return Image

def loopWaitAndClick(driver, buttonElementXpath, looptime=1000):
    loopCount = 1
    while True:
        time.sleep(random.uniform(3,4))
        if loopCount > looptime:
            raise
        else:
            loopCount += 1
        try:
            if driver.find_elements_by_xpath(buttonElementXpath):
                driver.find_element_by_xpath(buttonElementXpath).click()
                break
        except Exception as ButtonError:
            pass

def rawTextConversion(rawText, containerDict, lineBreaker='\n', delimiterList=[':']):
    rawText = rawText.replace('：', ':')
    str_infoList = rawText.split(lineBreaker)
    for i in range(len(str_infoList)):
        for delimiter in delimiterList:
            if delimiter in str_infoList[i]:
                title, content = str_infoList[i].split(delimiter)
                if content:
                    containerDict[title] = content
                    break
            else:
                for delimiter in delimiterList:
                    if delimiter in str_infoList[i-1]:
                        title, content = str_infoList[i-1].split(delimiter)
                        containerDict[title] = str_infoList[i]

## 沒有用到class特性，改成根據redis key來執行function

def driver_start (conf):
    service = Service(ChromeDriverManager().install()) ##確認是否共用？如果共用，則選項固定且寫在worker開始爬蟲之前，就不用在每個function內重複這段
    options = uc.ChromeOptions()
    time.sleep(3)
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-application-cache")
    options.add_argument("--disk-cache-size=1")
    options.add_argument("--disable-cache")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--lang=zh-TW")
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36")
    options.add_experimental_option("prefs", {
        "intl.accept_languages": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"
    })
    driver = webdriver.Chrome(service=service, options=options)
    driver.get(conf.TPC_crawler_URL)
    html = driver.page_source[:1000]
    return driver 
    
def log_in(conf,driver,account :str  = None , password : str = None) :
    try :
        loopCount = 1
        screenshot_filename =''
        crop_filename=''
        link = WebDriverWait(driver, 3).until(
        EC.element_to_be_clickable((By.XPATH, '//a[@href="/ebpps2/login"]'))
        )
        link.click()
        time.sleep(random.uniform(2,5))
        element = driver.find_element(By.XPATH, '//*[@id="captcha_id"]')
        location = element.location
        size = element.size
        screenshot_filename = '{}/screenshot{}.png'.format(conf.pic_path,random.randint(1000000, 9999999))
        crop_filename = screenshot_filename
        driver.save_screenshot(screenshot_filename)
        png = driver.get_screenshot_as_png()
        left = location['x']
        top = location['y']
        right = location['x'] + size['width']
        bottom = location['y'] + size['height']
        box = (left, top, right, bottom)
        crop(file=screenshot_filename,
            box=box,
            save_as=crop_filename)
        vCode = tpcPicToVCode(file=png)
        loopCount = 1
        while True:
            if vCode:
                break
            elif loopCount >= 31:
                raise Exception('vCode is None')            
            else:
                loopCount += 1
                driver.find_element(By.XPATH,'//*[@id="center"]/div/section/div/div/section/div[2]/div[1]/form/div[3]/div/a[2]').click()
                time.sleep(random.uniform(2, 5))
                driver.save_screenshot(screenshot_filename)
                png = driver.get_screenshot_as_png()
                crop(file=screenshot_filename,
                    box=box,
                    save_as=crop_filename)
                vCode = tpcPicToVCode(file=png)
        time.sleep(random.uniform(2,5))
        inputL = driver.find_element(By.XPATH,'//*[@id="username"]')
        inputL.clear()
        inputL.send_keys(account)
        time.sleep(random.uniform(2,5))
        inputP = driver.find_element(By.XPATH,'//*[@id="password"]')
        inputP.clear()
        inputP.send_keys(password)
        time.sleep(random.uniform(2,5))
        inputV = driver.find_element(By.XPATH,'//*[@id="code"]')
        inputV.send_keys(vCode)
        inputC = driver.find_element(By.XPATH,'//*[@id="center"]/div/section/div/div/section/div[2]/div[1]/form/div[4]/button')
        inputC.send_keys(Keys.ENTER)
        os.remove(screenshot_filename)
        time.sleep(random.uniform(2, 5))
        print('log_in ok')
    except Exception as LoginError:
        logging.error(f'LoginError: {LoginError}')
        raise LoginError
    
def history_page(driver,place_id :str = None):
    try:
        search_button =  driver.find_element(By.XPATH,'//*[@id="center"]/div/section/div[1]/a')
        driver.execute_script("arguments[0].click();", search_button)
        time.sleep(random.uniform(2,5))
        while True :
            filter_option = driver.find_element(By.CLASS_NAME,'filter-option')
            filter_option.click()
            time.sleep(random.uniform(2,5))
            inputP  =  driver.find_element(By.XPATH, '//input[@aria-label="Search"]')
            if inputP :
                break
        inputP.send_keys(place_id)
        inputP.send_keys(Keys.ENTER)
        time.sleep(random.uniform(2, 5))

        search_button = driver.find_element(By.NAME, "Search")
        search_button.click()
        time.sleep(random.uniform(2, 5))
        text = '歷史資訊'
        driver.find_element(By.XPATH,'//a[contains(text(), "' + text + '")]').click()
        time.sleep(random.uniform(2, 5))
        print('history_page ok')
    except Exception as HistoryPageError:
        logging.error(f'HistoryPageError: {HistoryPageError}')
        raise HistoryPageError

def get_year_month(driver):
    try :
        first_tbody = driver.find_element(By.XPATH, '(//tbody)[1]')
        yearmonth_list = []
        bill_months = first_tbody.find_elements(By.XPATH, './/td[@data-title="帳單月份"]')
        for td in bill_months:
            yearmonth_list.append(td.text)
        print('get_year_month ok')
        return yearmonth_list
    except Exception as YearMonthError:
        logging.error(f'YearMonthError: {YearMonthError}')
        raise YearMonthError
    
def get_information(conf,driver,place_id :str =None,target_month : str = None) :
    try:
        driver = driver
        tbody = driver.find_element(By.XPATH, "//table[@class='table_list search_list']/tbody")
        row = tbody.find_element(By.XPATH, ".//tr[td[contains(text(), '{}')]]".format(target_month))
        button = row.find_element(By.XPATH, ".//button[contains(text(), '明細')]")
        new_Bill = {}
        ebpps_url = button.get_attribute('onclick')
        ebpps_url_text = ebpps_url[24:-2]
        new_Bill.update({'ebpps_url' : ebpps_url_text })
        new_Bill.update({'status': row.find_element(By.XPATH,'./td[4]').text})
        bill_year_month = target_month
        bill_year_month = bill_year_month.replace("年", "").replace("月", "")
        year = str(int(bill_year_month[:3]) + 1911)
        month = bill_year_month[3:]
        new_Bill.update({'bill_year_month' : year + month})
        time.sleep(random.uniform(2, 5))
        driver.execute_script("arguments[0].click();", button)        
        custInfo_dict = dict()
        time.sleep(random.uniform(2, 5))
        if driver.find_elements(By.XPATH,'//th[contains(text(), "用戶名稱")]'):
            new_Bill['ebpps_name'] = driver.find_element(By.XPATH,'//th[contains(text(), "用戶名稱")]/following-sibling::td').text
        if driver.find_elements(By.XPATH,'//th[contains(text(), "用電地址")]'):
            new_Bill['ebpps_customer_address'] = driver.find_element(By.XPATH,'//th[contains(text(), "用電地址")]/following-sibling::td').text
        if driver.find_elements(By.XPATH,'//th[contains(text(), "通訊地址")]'):
            new_Bill['ebpps_bill_address'] = driver.find_element(By.XPATH,'//th[contains(text(), "通訊地址")]/following-sibling::td').text
        basic_caption = driver.find_element(By.XPATH, '//caption[contains(text(), "基本資料")]')
        basic_table = basic_caption.find_element(By.XPATH, './ancestor::table')
        basic_info = basic_table.find_element(By.TAG_NAME, 'tbody').text
        rawTextConversion(basic_info, custInfo_dict)
        basic_informaion_dict = basic_infromation(basic_info)
        electricity_caption = driver.find_element(By.XPATH, '//caption[contains(text(), "用電資料")]')
        driver.execute_script("arguments[0].scrollIntoView();", electricity_caption)
        electricity_table = electricity_caption.find_element(By.XPATH, './ancestor::table')
        electricity_info = electricity_table.find_element(By.TAG_NAME, 'tbody').text
        rawTextConversion(electricity_info, custInfo_dict)
        electricity_information_dict = electricity_information(conf,electricity_info)
        bill_caption = driver.find_element(By.XPATH, '//caption[contains(text(), "計費內容")]')
        driver.execute_script("arguments[0].scrollIntoView();", bill_caption)
        bill_table = bill_caption.find_element(By.XPATH, './ancestor::table')
        bill_info = bill_table.find_element(By.TAG_NAME, 'tbody').text           
        rawTextConversion(bill_info, custInfo_dict)
        bill_information_dict = bill_information(bill_info)
        saving_caption = driver.find_element(By.XPATH, '//caption[contains(text(), "節電資訊")]')
        driver.execute_script("arguments[0].scrollIntoView();", saving_caption)
        saving_table = saving_caption.find_element(By.XPATH, './ancestor::table')
        saving_info = saving_table.find_element(By.TAG_NAME, 'tbody')
        target_row = saving_info.find_element(By.XPATH, ".//tr[th[text()='本期']]")
        cells = target_row.find_elements(By.TAG_NAME, "td")
        data=[cell.text for cell in cells]
        saving_information_dict = saving_information(saving_info.text)
        meter_caption = driver.find_element(By.XPATH, '//caption[contains(text(), "電表資料")]')
        driver.execute_script("arguments[0].scrollIntoView();", meter_caption)
        meter_table = meter_caption.find_element(By.XPATH, './ancestor::table')
        meter_info = meter_table.find_element(By.TAG_NAME, 'tbody').text
        meter_infromation_dict = meter_information(meter_info)                 
        new_Bill.update(basic_informaion_dict)
        new_Bill.update(electricity_information_dict)
        new_Bill.update(bill_information_dict)
        new_Bill.update({'用電日數': data[0]})
        new_Bill.update({'度數': data[1]})
        new_Bill.update({'日平均度數':data[2]})
        new_Bill.update(saving_information_dict)
        new_Bill.update(meter_infromation_dict)
        new_Bill = update_dict_keys(conf,new_Bill)
        final_dict_modify(new_Bill)
        if new_Bill.get('kwh_peak') != 0 and new_Bill.get('kwh_semipeak') != 0 and new_Bill.get('kwh_offpeak') != 0 and new_Bill.get('kwh_saturday') :
            kwh_total = int(new_Bill.get('kwh_peak', 0) or 0) +int(new_Bill.get('kwh_semipeak', 0) or 0)+ int(new_Bill.get('kwh_offpeak', 0) or 0)+ int(new_Bill.get('kwh_saturday', 0) or 0)
            if kwh_total == new_Bill['kwh_total'] :
                pass
            else :
                logging.error('place_id:{}/month:{} kwh_total is not same from calculate and TPC bills'.format(place_id,month))
        url=f'{conf.api_base}/crawler/save_tpc_bill_information'
        a = requests.post(url= url,data = new_Bill)
        time.sleep(random.uniform(2, 5))
        print('get_information ok')
        print (f'{place_id}/{bill_year_month}/{a}')
    except Exception as GetInformationError:
        logging.error(f'GetInformationError: {GetInformationError}')
        raise GetInformationError
    
def log_out(driver):
    try:        
        time.sleep(random.uniform(2, 5))       
        button = driver.find_element(By.XPATH, "//input[@value='登出']")
        driver.execute_script("window.scrollTo(0, 0)") 
        time.sleep(random.uniform(2, 5))       
        driver.execute_script("arguments[0].click();", button)        
        print('h')
        time.sleep(random.uniform(2, 5))
        print('log_out ok')
    except Exception as LogoutError:
        logging.error(f'LogoutError: {LogoutError}')
        raise LogoutError
    
def place_id_setting_page(driver):
    try:
        setting = driver.find_element(By.XPATH, '//a[@title="基本資料設定"]')
        driver.execute_script("arguments[0].scrollIntoView();", setting)
        driver.execute_script("arguments[0].click();", setting)
        time.sleep(random.uniform(2, 5))
        place_id_setting = driver.find_element(By.XPATH,"//a[@title='電號管理']")
        place_id_setting.click()
        time.sleep(random.uniform(2, 5))
        print('place_id_setting_page ok')
    except Exception as PlaceIdSettingError:
        logging.error(f'PlaceIdSettingError: {PlaceIdSettingError}')
        raise PlaceIdSettingError
    
def bills_search_page(driver):
    try:
        overview =driver.find_element(By.XPATH,"//*[contains(@href, '/ebpps2/bill/myebill-overview')]")
        driver.execute_script("arguments[0].scrollIntoView();", overview)
        driver.execute_script("arguments[0].click();", overview)
        time.sleep(random.uniform(2, 5))
        print('bill_search_page ok')
    except Exception as BillsSearchPageError:
        logging.error(f'BillsSearchPageError: {BillsSearchPageError}')
        raise BillsSearchPageError

def add_place_id(driver,place_id:str = None,month : str =None,authentication : str =None,authentication_method : str = None):
    try:
        add_place_id = driver.find_element(By.ID,"openAddCucstAdminModal")
        driver.execute_script("arguments[0].scrollIntoView();", add_place_id)
        time.sleep(random.uniform(2, 5))
        driver.execute_script("arguments[0].click();", add_place_id)  
        time.sleep(random.uniform(2, 5))
        enter_place_id = driver.find_element(By.ID,"custNo")
        driver.execute_script("arguments[0].scrollIntoView();", enter_place_id)
        time.sleep(random.uniform(2, 5))
        enter_place_id.send_keys(place_id)
        time.sleep(random.uniform(2, 5))
        month_select = driver.find_element(By.ID,"yymm")
        driver.execute_script("arguments[0].scrollIntoView();", month_select)
        time.sleep(random.uniform(2, 5))
        select = Select(month_select)
        select.select_by_value("{}".format(month))
        time.sleep(random.uniform(2, 5))
        if authentication_method == 'fee_kwh':
            flowchrg_btn = driver.find_element(By.ID,"flowChrgBtn")
            driver.execute_script("arguments[0].scrollIntoView();", flowchrg_btn)
            time.sleep(random.uniform(2, 5))
            driver.execute_script("arguments[0].click();", flowchrg_btn) 
            time.sleep(random.uniform(2, 5)) 
            flowchrg = driver.find_element(By.ID,"flowChrg")
            driver.execute_script("arguments[0].scrollIntoView();", flowchrg)
            flowchrg.send_keys(authentication)
        elif authentication_method == 'last_five_numbers_of_document':
            billno_btn = driver.find_element(By.ID,"billNoBtn")
            driver.execute_script("arguments[0].scrollIntoView();", billno_btn)
            time.sleep(random.uniform(2, 5))
            driver.execute_script("arguments[0].click();", billno_btn)  
            billno = driver.find_element(By.ID,"billNo")
            driver.execute_script("arguments[0].scrollIntoView();", billno)
            billno.send_keys(authentication)
        elif authentication_method == 'last_five_numbers_of_payment_account':
            bankno_Btn = driver.find_element(By.ID,"bankNoBtn")
            driver.execute_script("arguments[0].scrollIntoView();", bankno_Btn)
            driver.execute_script("arguments[0].click();", bankno_Btn)  
            bankno = driver.find_element(By.ID,"bankNo")
            driver.execute_script("arguments[0].scrollIntoView();", bankno)
            bankno.send_keys(authentication)
        submit = driver.find_element(By.XPATH,"//input[@value='新增']")
        submit.click()
        time.sleep(random.uniform(2, 5))
        print('add_place_id ok')
    except Exception as AddPlaceIdError:
        logging.error(f'AddPlaceIdError: {AddPlaceIdError}')
        raise AddPlaceIdError
    
def delete_place_id(driver,place_id : str = None) :
    try:
        driver = driver
        place_id_page = driver.find_element(By.XPATH,"//*[contains(@href, '/ebpps2/bill/electricity-power-no-manage/n/all/1')]")
        place_id_page.click()
        time.sleep(random.uniform(2, 5))
        search = driver.find_element(By.NAME,"searchCustNo")
        search.send_keys(place_id)
        time.sleep(random.uniform(2, 5))
        enter = driver.find_element(By.XPATH,"//input[@value='查詢']")
        enter.click()
        time.sleep(random.uniform(2, 5))
        delete = driver.find_element(By.LINK_TEXT, "刪除")
        delete.click()
        time.sleep(random.uniform(2, 5))
        check = driver.find_element(By.XPATH, "//button[text()='是']")
        check.click()
        time.sleep(random.uniform(2, 5))
        ok = driver.find_element(By.XPATH, "//button[text()='OK']")
        time.sleep(random.uniform(2, 5))
        ok.click()
        print('delete_place_id ok')
    except Exception as DeletePlaceIdError:
        logging.error(f'DeletePlaceIdError: {DeletePlaceIdError}')
        raise DeletePlaceIdError

    
def yearmonthday_change(Date :str = None,type :int = None) :
    year = int(Date.split('年')[0]) + 1911
    month = int(Date.split('年')[1].split('月')[0])
    Day = int(Date.split('月')[1].split('日')[0])
    if type == 0 :
        Date = date(year, month, Day)
    elif type == 1 :
        Date = f'{year}{month}'
    elif type == 2 :
        Date = year
    return Date
    
def meter_information(data :str = None) :
    pattern = r"組別:(\d+)\((.*?)\)\s+表號:(\d+)\s+電表倍數:\s*(\d+)|" \
            r"(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)"

    matches = list(re.finditer(pattern, data))
    final_dict = {}

    for match in matches:
        groups = match.groups()
        groups = list(filter(None, groups))

        if len(groups) == 4:  
            final_dict['group'] = groups[0]
            final_dict['group_name'] = groups[1]
            final_dict['meter_number'] = groups[2]
            final_dict['multiplier'] = groups[3]
    return final_dict
    
def basic_infromation(data : str = None) :
    pattern = r"(\S+[\u4e00-\u9fff]+[\S]*)\s*[:：]\s*(.*)"
    matches = re.findall(pattern, data)
    final_data = {match[0]: match[1] for match in matches}
    return final_data

def electricity_information(conf,data : str = None) :

    final_output = {}
    current_section = None

    for line in (data or '').splitlines():
        line = line.strip()
        if not line:
            continue
        if ':' not in line:
            current_section = line
            continue
        match = re.match(r"(.+?):\s*([\d.]+)", line)
        if match:
            zh_key, raw_value = match.groups()
            value = float(raw_value) if '.' in raw_value else int(raw_value)
            for eng_key, zh_list in conf.tpc_bill_conversion_dict.items():
                if zh_key in zh_list :
                    final_output[eng_key] = value
                    break
    return final_output

    
def saving_information(data :str = None) :
    pattern = r"比較項目\s+用電日數\s+(\d+)\s+度數\s+(\d+)\s+日平均度數\s+([\d.]+)|" \
        r"減少用電量:(\d+)|" \
        r"每度燃料成本\s+([\d.,]+)元|" \
        r"本期碳排量\s+(\d+)公斤|" \
        r"每度繳交再生基金\s+([\d.,]+)元|" \
        r"當期每度平均電價\s+([\d.,]+)元|" \
        r"本期\s+(\d+)\s+(\d+)\s+([\d.]+)"
    matches = re.findall(pattern, data)
    final_data = {}
    if matches:
        for match in matches:
            if match[0]:
                final_data['用電日數'] = match[0]
                final_data['度數'] = match[1]
                final_data['日平均度數'] = match[2]
            elif match[3]:
                final_data['減少用電量'] = match[3]
            elif match[4]:
                final_data['每度燃料成本'] = match[4]
            elif match[5]:
                final_data['本期碳排量'] = match[5]
            elif match[6]:
                final_data['每度繳交再生基金'] = match[6]
            elif match[7]: 
                final_data['當期每度平均電價'] = match[7]
            elif match[8]:
                final_data['用電日數'] = match[8]
                final_data['度數'] = match[9]
                final_data['日平均度數'] = match[10]

    return final_data

def bill_information(data : str = None):
    pattern = r"([^\d：\n]+):\s*([\d,.-]+)元"
    matches = re.findall(pattern, data)
    final_data = {match[0].strip(): match[1].replace(',', '') for match in matches}
    return final_data

def update_dict_keys(conf,original_dict :dict = None):
    updated_dict = {}
    for key, value in original_dict.items():
        for en_key in conf.tpc_bill_conversion_dict.keys():
            ch_key_list = conf.tpc_bill_conversion_dict.get(en_key)
            if key in ch_key_list:
                new_key = en_key
                break
        else:
            new_key = key 
        updated_dict[new_key] = value
    
    return updated_dict

def final_dict_modify( data :dict = None) :
    if data.get('計費期間') != None :
        start_str, end_str = data['計費期間'].split(' 至 ')
        for label, s in zip(['date_bill_start', 'date_bill_end'], [start_str, end_str]):
            s = s.replace("年", "-").replace("月", "-").replace("日", "")
            y, m, d = s.split("-")
            data[label] = date(int(y) + 1911, int(m), int(d))
        del data['計費期間']
    if data.get('date_readmeter') != None :
        data['date_readmeter'] = yearmonthday_change(Date=data.get('date_readmeter'),type=0)
    if data.get('date_next_readmeter') != None :
        data['date_next_readmeter'] = yearmonthday_change(Date=data.get('date_next_readmeter'),type=0)
    if data.get('day_deducted') != None :
        data['day_deducted'] = yearmonthday_change(Date=data.get('day_deducted'),type=0)
    if data.get('day_next_deducted') != None :
        data['day_next_deducted'] = yearmonthday_change(Date=data.get('day_next_deducted'),type=0)
    if data.get('payment_deadline') != None :
        data['payment_deadline'] = yearmonthday_change(Date=data.get('payment_deadline'),type=0)
    if data.get('bill_date') != None :
        data['bill_date'] = yearmonthday_change(Date=data.get('bill_date'),type=0)
    if data.get('bill_date_next') != None :
        data['bill_date_next'] = yearmonthday_change(Date=data.get('bill_date_next'),type=0)
    for k, v in list(data.items()):
        if k != 'place_id' and k != 'company_id' and k != 'tpc_company_id':
            if isinstance(v, (datetime, date)):
                continue
            if isinstance(v, str):
                v_strip = v.replace(",", "").strip()
                try:
                    data[k] = float(v_strip) if '.' in v_strip else int(v_strip)
                    continue
                except:
                    pass
    return data
    
def send_request(conf,redis_client,place_id : str =None,month :str =None,account : str = None,password : str = None,email :str = None,failure :str=None):
    list_data = redis_client.lrange(conf.bill_crawler_key, 0, -1)
    redis_client.delete(conf.bill_crawler_key)
    append_new_request = True
    if list_data != []:
        for idx,data in enumerate(list_data):
            parsed_data = json.loads(data)  
            data_place_id = parsed_data.get('place_id')
            data_month = parsed_data.get('month')
            
            data_failure = parsed_data.get('failure')
            if place_id == data_place_id and month == data_month :
                data_failure.append(failure)
                append_new_request = False
                update_data = json.dumps(parsed_data)
                redis_client.rpush(conf.bill_crawler_key,update_data)
            else:
                update_data = json.dumps(parsed_data)
                redis_client.rpush(conf.bill_crawler_key,update_data)
        if append_new_request == True :
            update_data =json.dumps({"place_id": place_id,"month": month,"account": account,"password": password,"retry_count": "1","rest_time": "1","email":email,"failure": [failure]})
            redis_client.rpush(conf.bill_crawler_key,update_data)
    else: 
        redis_client.rpush(conf.bill_crawler_key, json.dumps({"place_id": place_id,"month": month,"account": account,"password": password,"retry_count": "1","rest_time": "1","email": email,"failure": [failure]}))

def driver_operation(selector,target_route,to_do,keys) :
    driver = driver
    locator_map = {
        "XPATH": By.XPATH,
        "CSS": By.CSS_SELECTOR,
        "ID": By.ID,
        "CLASS": By.CLASS_NAME
    }
    driver.execute_script("window.scrollTo(0, 0)") 
    target_element = driver.find_element(locator_map.get(selector),target_route)
    driver.execute_script("arguments[0].scrollIntoView();", target_element)
    if to_do == 'click' :
        driver.execute_script("arguments[0].click();", target_element)        
    if to_do == 'select' :
        pass
    if to_do == 'send_keys' :
        pass

def delete_tpc_crawler_request(conf,place_id):
    redis_client = redis_client
    redis_data = redis_client.lrange(conf.bill_crawler_key, 0, -1)
    delete_tpc_crawler_request = True
    for idx, request_str in enumerate(redis_data):
        data = json.loads(request_str)
        if data.get('place_id') == place_id :
            delete_tpc_crawler_request = False
    if delete_tpc_crawler_request :
        redis_data = redis_client.lrange(conf.yearmonth_crawler_key, 0, -1)
        for idx, request_str in enumerate(redis_data):
            data = json.loads(request_str)
            if data.get('place_id') == place_id:
                redis_client.lrem(conf.yearmonth_crawler_key, 1, request_str)  

def delete_place_id_request(place_id : str = None,place_id_request_list : dict = None):
    return {k: v for k,v in place_id_request_list.items() if k != place_id}
    
def call_tpc_yearmonth_crawler(tpc_yearmonth_crawler_list,conf,redis_client):
    driver = driver_start(conf=conf)
    while True:
        html = driver.page_source
        if "台灣電力公司電子帳單服務系統" in html:
            print("TPC driver start email")
            time.sleep(random.uniform(2, 5))
            break
        driver.quit()
        driver = driver_start(conf=conf)
    for account in list(tpc_yearmonth_crawler_list.keys()):
        value = tpc_yearmonth_crawler_list[account]        
        password = value.get("password")
        try :
            log_in(conf=conf,driver=driver,account=account,password=password)
            time.sleep(random.uniform(5,10))
        except Exception as e:
            print(e)
            del tpc_yearmonth_crawler_list[account]
            driver.quit()
            driver = driver_start(conf=conf)
            continue
        for place_id,place_id_request in value.get("place_id_request_dict").items():
            month = place_id_request.get("month")
            authentication_method = place_id_request.get('authentication_method')
            authentication = place_id_request.get("authentication")
            email = place_id_request.get('email')
            failure = place_id_request.get('failure')
            TPC_online_bills_status = place_id_request.get('TPC_online_bills_status')
            try :
                if TPC_online_bills_status == 'not_has_TPC_online_bills':
                    place_id_setting_page(driver=driver)
                    time.sleep(random.uniform(2, 5))
                    add_place_id(driver = driver,place_id=place_id, month=month, authentication=authentication, authentication_method=authentication_method)
                    time.sleep(random.uniform(2, 5))
                bills_search_page(driver=driver)
                time.sleep(random.uniform(2, 5))
                history_page(driver=driver, place_id=place_id)
                time.sleep(random.uniform(2, 5))
                year_month_list = get_year_month(driver=driver)
                time.sleep(random.uniform(2, 5))
            except Exception as e:
                value["place_id_request_dict"] = delete_place_id_request(place_id=place_id,place_id_request_list=value.get("place_id_request_dict"))
                print(e)
                driver.quit()
                driver = driver_start(conf=conf)
                log_in(conf=conf,driver=driver,account=account,password=password)
                continue
            try :
                for target_month in year_month_list:
                    target_month = target_month.replace("年", "").replace("月", "")
                    send_request(conf=conf,redis_client=redis_client ,account=account, password=password, place_id=place_id, month=target_month,email= email , failure= failure)
                    print(f"用電戶號: {place_id}，{target_month}月電費需求傳送完成")
                time.sleep(random.uniform(2, 5))
                if TPC_online_bills_status == "not_has_TPC_online_bills" :
                    items = redis_client.lrange(conf.delete_place_id_crawler_key, 0, -1)
                    if str(place_id) not in items:
                        redis_client.rpush(conf.delete_place_id_crawler_key, place_id)
            except Exception as e:
                print(e)
                continue
        log_out(driver=driver)
        time.sleep(random.uniform(2, 5))
        driver.quit()
def call_tpc_bill_crawler(tpc_bills_crawler_list,conf):
    driver = driver_start(conf=conf)
    while True:
        html = driver.page_source
        if "台灣電力公司電子帳單服務系統" in html:
            print("TPC driver start email")
            time.sleep(random.uniform(2, 5))
            break
        driver.quit()
        driver = driver_start(conf=conf)
    time.sleep(random.uniform(2, 5))
    for account, value in tpc_bills_crawler_list.items():   
        try:     
            password = value.get("password")
            log_in(conf=conf,driver=driver,account=account,password=password)
            time.sleep(random.uniform(2, 5))
        except Exception as e:
            print(e)
            while True:
                driver.quit()
                driver = driver_start(conf=conf)
                html = driver.page_source
                if "台灣電力公司電子帳單服務系統" in html:
                    print("TPC driver start email")
                    time.sleep(random.uniform(2, 5))
                    break
            continue
        for place_id,place_id_value in value.get('place_id_request_dict').items() :
            for yearmonth,month_value in place_id_value.items():
                try:
                    bills_search_page(driver=driver)
                    time.sleep(random.uniform(2, 5))   
                    history_page(driver=driver,place_id=place_id)
                    time.sleep(random.uniform(2, 5))
                    yearmonth = str(yearmonth).strip(' ')
                    year = str(yearmonth)[:-2]
                    month = str(yearmonth)[-2:]
                    target_month =   f"{year}年{month}月"
                    email = month_value.get('email')
                    failure = month_value.get('failure')
                    get_information(conf=conf,driver=driver,place_id  = place_id,target_month  = target_month)
                    time.sleep(random.uniform(2, 5))
                except Exception as e:
                    print(e)
                    value["place_id_request_dict"] = delete_place_id_request(place_id=place_id, place_id_request_list=value.get("place_id_request_dict"))
                    while True:
                        driver.quit()
                        driver = driver_start(conf=conf)
                        html = driver.page_source
                        if "台灣電力公司電子帳單服務系統" in html:
                            print("TPC driver start email")
                            time.sleep(random.uniform(2, 5))
                            break
                    log_in(conf=conf,driver=driver,account=account,password=password)
                    continue
                send_mail(url_list= email,place_id=place_id,target_month=yearmonth)
                print(f"用電戶號: {place_id}，{yearmonth}月電費爬蟲完成")
            delete_tpc_crawler_request(place_id=place_id)
            time.sleep(random.uniform(2, 5))
        log_out()
        time.sleep(random.uniform(2, 5))
        driver.quit()
def call_tpc_delete_place_id_crawler(tpc_delete_place_id_list,conf,redis_client):
    driver = driver_start(conf=conf)
    while True:
        html = driver.page_source
        if "台灣電力公司電子帳單服務系統" in html:
            print("TPC driver start email")
            time.sleep(random.uniform(2, 5))
            break
        driver.quit()
        driver = driver_start(conf=conf)
    try:
            log_in(conf=conf,driver=driver,account=conf.account,password=conf.password)
            time.sleep(random.uniform(2, 5))
    except:
        while True:
            driver.quit()
            driver = driver_start(conf=conf)
            html = driver.page_source
            if "台灣電力公司電子帳單服務系統" in html:
                print("TPC driver start email")
                time.sleep(random.uniform(2, 5))
                break
    for place_id in tpc_delete_place_id_list:
            try:
                time.sleep(random.uniform(2, 5))
                place_id_setting_page()
                time.sleep(random.uniform(2, 5))
                delete_place_id(driver=driver,place_id= place_id)
                time.sleep(random.uniform(2, 5))
                redis_client.lrem(conf.delete_place_id_crawler_key, 1, place_id)  
            except:
                del tpc_delete_place_id_list[place_id]
                while True:
                    driver.quit()
                    driver = driver_start(conf=conf)
                    html = driver.page_source
                    if "台灣電力公司電子帳單服務系統" in html:
                        print("TPC driver start email")
                        time.sleep(random.uniform(2, 5))
                        break
                log_in(conf=conf,driver=driver,account=conf.account,password=conf.password)
    time.sleep(random.uniform(2, 5))
    driver.quit()
def call_tpc_crawler(tpc_yearmonth_crawler_list,tpc_bills_crawler_list,tpc_delete_place_id_list,conf,redis_client,lock):
    if lock.acquire(blocking=False):
        #if tpc_yearmonth_crawler_list:
            #call_tpc_yearmonth_crawler(tpc_yearmonth_crawler_list,conf,redis_client)
        if tpc_bills_crawler_list:
            call_tpc_bill_crawler(tpc_bills_crawler_list,conf)
        if tpc_delete_place_id_list:
            call_tpc_delete_place_id_crawler(tpc_delete_place_id_list,conf,redis_client)
        lock.release()

def get_tpc_yearmonth_crawler_request(redis_client,conf):
        result_dict = {}
        redis_data = redis_client.lrange(conf.yearmonth_crawler_key, 0, -1)
        redis_client.delete(conf.yearmonth_crawler_key)
        for idx, request_str in enumerate(redis_data):
            update_result_dict = False
            data = json.loads(request_str)
            if conf.enable_exponential_backoff :
                if int(data.get('rest_time')) == int(data.get('retry_count')):
                    data['retry_count'] = int(data['retry_count']) * 2
                    data['rest_time'] = 0
                    update_result_dict = True
                else:
                    data['rest_time'] = int(data.get('rest_time')) + 1
                if int(data.get('retry_count')) > 512:
                    send_mail(url_list= data.get('failure'),place_id= data.get('place_id'),month= data.get("month"))
                redis_client.rpush(conf.yearmonth_crawler_key, json.dumps(data))
            else :
                update_result_dict = True
                redis_client.rpush(conf.yearmonth_crawler_key, json.dumps(data))
            if update_result_dict and data.get('account') in result_dict.keys():
                    result_dict[data.get('account')]["place_id_request_dict"][data.get('place_id')] = ({k: data[k] for k in ['TPC_online_bills_status' ,'month', 'authentication_method', 'authentication','email','failure']})
            if update_result_dict and data.get('account') not in result_dict.keys() :
                result_dict[data.get('account')] = {
                    "password" : data.get('password'),
                    "place_id_request_dict" :{data.get('place_id'):{k: data[k] for k in ['TPC_online_bills_status' ,'month', 'authentication_method', 'authentication','email','failure']}}
                }
        return result_dict
def get_tpc_bills_crawler_request(redis_client, conf):
    redis_client = redis_client
    result_dict = {}
    redis_data = redis_client.lrange(conf.bill_crawler_key, 0, -1)
    redis_client.delete(conf.bill_crawler_key)
    for idx, request_str in enumerate(redis_data):
        data = json.loads(request_str)
        update_result_dict = False
        if conf.enable_exponential_backoff:
            data['rest_time'] = int(data.get('rest_time')) + 1
            if int(data.get('rest_time')) > int(data.get('retry_count')):
                data['retry_count'] = int(data.get('retry_count')) * 2
                data['rest_time'] = 0
                update_result_dict = True
            elif int(data.get('retry_count')) > 512:
                send_mail(url_list= data.get('failure'),place_id= data.get('place_id'),month= data.get("month"))
            redis_client.rpush(conf.bill_crawler_key, json.dumps(data))
        else:
            update_result_dict = True
            redis_client.rpush(conf.bill_crawler_key, json.dumps(data))

        if update_result_dict and data.get('account') not in result_dict.keys():
            result_dict.update({
                data.get('account'):{'password':data.get('password'),'place_id_request_dict':{data.get('place_id'):{data.get('month'):{k:data.get(k)for k in ['email','failure','retry_count','rest_time']}}}}
            })
        if update_result_dict and data.get('place_id') not in result_dict.get(data.get('account')).get('place_id_request_dict').keys():
            result_dict.get(data.get('account')).get('place_id_request_dict').update({
                data.get('place_id'):{data.get('month'):{k:data.get(k) for k in ['email','failure','retry_count','rest_time']}}}
            )
        if update_result_dict and data.get('month') not in result_dict.get(data.get('account')).get('place_id_request_dict').get(data.get('place_id')).keys():
            result_dict.get(data.get('account')).get('place_id_request_dict').get(data.get('place_id')).update({
                data.get('month') : {k:data.get(k)for k in ['email','failure','retry_count','rest_time']}
            })
    return result_dict    
def get_tpc_delete_place_id_request(redis_client, conf): 
        result_list = []
        bills_crawler_request_list = []
        delete_place_id_request = redis_client.lrange(conf.delete_place_id_crawler_key, 0, -1)
        redis_data = redis_client.lrange(conf.bill_crawler_key, 0, -1)
        if redis_data != []:
            for idx, request_str in enumerate(redis_data):
                data = json.loads(request_str)
                data_place_id = data.get('place_id')
                bills_crawler_request_list.append(data_place_id)
            for place_id in delete_place_id_request:
                if place_id not in bills_crawler_request_list:
                    result_list.append(place_id)
        else:
            result_list = delete_place_id_request
        return result_list
def send_mail(url_list,place_id,target_month):
        error_shown = False
        for url_str in url_list:
            url=f'{url_str}/{place_id}/{target_month}'
            try:
                requests.get(url=url,timeout=3)
            except Exception as e :
                if not error_shown:
                    error_shown = True
                continue

    