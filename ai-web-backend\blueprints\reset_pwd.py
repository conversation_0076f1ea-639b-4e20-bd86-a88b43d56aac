from flask import request, Blueprint, jsonify
from werkzeug.security import generate_password_hash
import json
from accessories import token_store, sqldb, get_serializer, mongo, update_json_in_mongo

resetpwd_page = Blueprint('reset-pwd', __name__)

@resetpwd_page.route('/reset-password', methods=['POST'])
def reset_password():
    data = request.json
    token = data.get('token')
    new_password = data.get('password')
    try:
        serializer = get_serializer()
        email = serializer.loads(token, salt='forget-password-salt', max_age=300)
    except Exception as e:
        return jsonify({'error': 'Invalid or expired token'}), 400

    user = mongo.db.user.find_one({"email": email})
    if user:
        if not json.loads(token_store[token])['used']:
            hashed_password = generate_password_hash(new_password)
            update_json_in_mongo({
                            "password": hashed_password
                            }, 'user', user['_id'])

            json.loads(token_store[token])['used'] = True

            return jsonify({'message': 'Password reset successfully'}), 200
        else:
            return jsonify({'error': 'Token has already been used.'}), 400

    return jsonify({'error': 'User not found'}), 404
