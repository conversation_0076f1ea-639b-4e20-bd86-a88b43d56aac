<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div>
      <h2 class="mb-0" style="cursor: pointer">
        <strong>企業法人設定</strong>
      </h2>
    </div>
  </c-col>
</c-row>

<!-- 分頁標籤 -->
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'info'"
            (click)="setShowCard('info')"
            class="custom-link"
            >資訊</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'info'"
            class="custom-link active"
            >資訊</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'member'"
            (click)="setShowCard('member')"
            class="custom-link"
            >成員</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'member'"
            class="custom-link active"
            >成員</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'Payment_method'"
            (click)="setShowCard('Payment_method')"
            class="custom-link"
            >付款方式</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'Payment_method'"
            class="custom-link active"
            >付款方式</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'Payout_method'"
            (click)="setShowCard('Payout_method')"
            class="custom-link"
            >收款帳戶</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'Payout_method'"
            class="custom-link active"
            >收款帳戶</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'Receipts'"
            (click)="setShowCard('Receipts')"
            class="custom-link"
            >憑證</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'Receipts'"
            class="custom-link active"
            >憑證</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>

<!-- 法人資訊 -->
<c-card *ngIf="showCard === 'info'" class="mb-4">
  <c-card-header>
    <strong>資訊</strong>
  </c-card-header>
  <c-card-body>
    <div class="mb-3">
      <label class="form-label">統一編號</label>
      <input cInput class="form-control" [(ngModel)]="enterprise.taxId" disabled />
    </div>
    <div class="mb-3">
      <label class="form-label">名稱</label>
      <input cInput class="form-control" [(ngModel)]="enterprise.name"/>
    </div>
    <div class="mb-3">
      <label class="form-label">地址</label>
      <input cInput class="form-control" [(ngModel)]="enterprise.address" />
    </div>
    <div class="mb-3">
      <label class="form-label">電話</label>
      <input cInput class="form-control" [(ngModel)]="enterprise.telephone" />
    </div>
    <c-alert color="success" *ngIf="save_info_done">完成!</c-alert>
    <button cButton class="btn btn-primary" variant="outline" (click)="saveEnterprise()">儲存</button>
  </c-card-body>
</c-card>

<div class="d-flex flex-wrap gap-3">
  <!-- 成員管理 -->
  <c-card *ngIf="showCard === 'member'" class="flex-grow-1 flex-basis-0 mb-4" style="min-width: 300px;">
    <c-card-header class="d-flex justify-content-between align-items-center">
      <strong>成員管理</strong>
      <button
        class="btn btn-outline-danger btn-sm"
        (click)="toggleEditMode()"
      >
        <svg cIcon name="cilUserUnfollow" class="me-1"></svg>移除
      </button>
    </c-card-header>

    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex align-items-center justify-content-between"
        *ngFor="let member of enterprise.accounts"
      >
        <div class="d-flex align-items-center gap-2">
          <!-- 刪除按鈕 -->
          <button
            *ngIf="editMode"
            class="btn p-0 d-flex align-items-center justify-content-center"
            style="width: 24px; height: 24px; background-color: transparent; border: none;"
            (click)="confirmRemove(member)"
          >
            <svg
              cIcon
              name="cilMinus"
              style="color: red; width: 20px; height: 20px;"
            ></svg>
          </button>

          <!-- Email -->
          <div class="fw-bold">{{ member.email }}</div>
        </div>

        <!-- 角色勾選 -->
        <div class="d-flex gap-2">
          <div class="form-check" *ngFor="let role of availableRoles">
            <input
              class="form-check-input"
              type="checkbox"
              [id]="'role-' + member.email + '-' + role.value"
              [value]="role.value"
              [checked]="member.roles.includes(role.value)"
              (change)="onRoleChange(member, $event)"
            />
            <label class="form-check-label" [for]="'role-' + member.email + '-' + role.value">
              {{ role.label }}
            </label>
          </div>
        </div>
      </div>
    </div>
  </c-card>


  <!-- 申請列表 -->
  <c-card *ngIf="showCard === 'member'" class="flex-grow-1 flex-basis-0 mb-4" style="min-width: 300px;">
    <c-card-header>
      <strong>申請加入列表</strong>
    </c-card-header>

    <div *ngIf="enterprise.applications ? enterprise.applications.length > 0 : false; else noApplication">
      <div
        class="list-group list-group-flush"
        *ngFor="let applicant of enterprise.applications"
      >
        <div class="list-group-item d-flex align-items-center justify-content-between">
          <!-- Email -->
          <div class="flex-grow-1">
            <div class="fw-bold">{{ applicant.email }}</div>
          </div>

          <!-- 按鈕 -->
          <div class="d-flex gap-2">
            <!-- 接受 -->
            <button
              class="btn p-0 d-flex align-items-center justify-content-center"
              style="width: 36px; height: 36px; background-color: transparent; border: none;"
              (click)="reply_application(applicant, true)"
            >
              <svg cIcon name="cilCheckCircle" style="color: green; width: 32px; height: 32px;"></svg>
            </button>

            <!-- 拒絕 -->
            <button
              class="btn p-0 d-flex align-items-center justify-content-center"
              style="width: 36px; height: 36px; background-color: transparent; border: none;"
              (click)="reply_application(applicant, false)"
            >
              <svg cIcon name="cilXCircle" style="color: red; width: 32px; height: 32px;"></svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <ng-template #noApplication>
      <div class="list-group-item text-center text-muted">
        目前沒有申請
      </div>
    </ng-template>
  </c-card>
</div>
<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="!enterprise.payment_method  && showCard == 'Payment_method'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未設定企業付款方式</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="togglePaymentModal()"
            >此</span
          >設定付款方式
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>
<div class="border rounded shadow-sm p-3 bg-white" *ngIf="enterprise.payment_credit_card && enterprise.payment_method == 'credit'  && showCard == 'Payment_method'">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h6 class="mb-0 fw-bold text-primary">付款方式：信用卡</h6>
    <button
      cButton
      class="btn btn-sm btn-primary text-white"
      (click)="togglePaymentModal()"
    >
      變更付款方式
    </button>
  </div>

  <div class="row g-3">
    <div class="col-12 col-md-6">
      <div class="text-muted small">持卡人姓名</div>
      <div class="fw-semibold">{{ enterprise.payment_credit_card.name }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">卡號</div>
      <div class="fw-semibold">
        **** **** **** {{ enterprise.payment_credit_card.number.slice(-4) }}
      </div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">有效期限</div>
      <div class="fw-semibold">
        {{ enterprise.payment_credit_card.expMonth }}/{{ enterprise.payment_credit_card.expYear }}
      </div>
    </div>
  </div>
</div>

<div
  class="border rounded shadow-sm p-3 bg-white"
  *ngIf="enterprise.payment_bank && enterprise.payment_method == 'bank' && showCard == 'Payment_method'"
>
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h6 class="mb-0 fw-bold text-primary">付款方式：銀行帳戶</h6>
    <button
      cButton
      class="btn btn-sm btn-primary text-white"
      (click)="togglePaymentModal()"
    >
      變更付款方式
    </button>
  </div>

  <div class="row g-3">
    <div class="col-12 col-md-6">
      <div class="text-muted small">戶名</div>
      <div class="fw-semibold">{{ enterprise.payment_bank.name }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">總行代碼</div>
      <div class="fw-semibold">{{ enterprise.payment_bank.bankCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">分行代碼</div>
      <div class="fw-semibold">{{ enterprise.payment_bank.branchCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">帳號</div>
      <div class="fw-semibold">{{ enterprise.payment_bank.accountNumber }}</div>
    </div>
  </div>
</div>



<!-- 收款方式 -->
<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="!enterprise.payout_bank  && showCard == 'Payout_method'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未設定企業收款帳戶</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="togglePayoutModal()"
            >此</span
          >設定收款帳戶
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>

<div
  class="border rounded shadow-sm p-3 bg-white"
  *ngIf="enterprise.payout_bank && showCard == 'Payout_method'"
>
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h6 class="mb-0 fw-bold text-primary">收款帳戶資料</h6>
    <button
      cButton
      class="btn btn-sm btn-primary text-white"
      (click)="togglePayoutModal()"
    >
      變更收款帳戶
    </button>
  </div>

  <div class="row g-3">
    <div class="col-12 col-md-6">
      <div class="text-muted small">戶名</div>
      <div class="fw-semibold">{{ enterprise.payout_bank.name }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">總行代碼</div>
      <div class="fw-semibold">{{ enterprise.payout_bank.bankCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">分行代碼</div>
      <div class="fw-semibold">{{ enterprise.payout_bank.branchCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">帳號</div>
      <div class="fw-semibold">{{ enterprise.payout_bank.accountNumber }}</div>
    </div>
  </div>
</div>

<c-modal [visible]="paymentModalVisible" size="lg">
  <c-modal-header>
    <h5 cModalTitle>設定付款方式</h5>
    <button cButtonClose (click)="togglePaymentModal()"></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mb-3">
      <label class="form-label fw-bold d-block">選擇付款方式</label>
      <div class="d-flex gap-4">
        <div class="form-check">
          <input
            class="form-check-input"
            type="radio"
            name="paymentMethod"
            id="credit"
            value="credit"
            [(ngModel)]="paymentMethod"
          />
          <label class="form-check-label fw-semibold" for="credit">
            信用卡付款
          </label>
        </div>
        <div class="form-check">
          <input
            class="form-check-input"
            type="radio"
            name="paymentMethod"
            id="bank"
            value="bank"
            [(ngModel)]="paymentMethod"
          />
          <label class="form-check-label fw-semibold" for="bank">
            銀行帳戶匯款
          </label>
        </div>
      </div>
    </div>

    <!-- 信用卡 -->
    <div *ngIf="paymentMethod === 'credit'" class="mt-4">
      <h6 class="fw-bold mb-3">信用卡資料</h6>
      <div class="mb-3">
        <label class="form-label">卡號 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入 16 位數卡號"
          [(ngModel)]="creditCard.number"
        />
      </div>
      <div class="mb-3">
        <label class="form-label">持卡人姓名 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="如信用卡上所示"
          [(ngModel)]="creditCard.name"
        />
      </div>
      <div class="row g-3">
        <div class="col">
          <label class="form-label">有效月 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="MM"
            [(ngModel)]="creditCard.expMonth"
          />
        </div>
        <div class="col">
          <label class="form-label">有效年 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="YYYY"
            [(ngModel)]="creditCard.expYear"
          />
        </div>
        <div class="col">
          <label class="form-label">安全碼 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="CVV"
            [(ngModel)]="creditCard.cvv"
          />
        </div>
      </div>
    </div>

    <!-- 銀行帳戶 -->
    <div *ngIf="paymentMethod === 'bank'" class="mt-4">
      <h6 class="fw-bold mb-3">銀行帳戶資料</h6>
      <div class="mb-3">
        <label class="form-label">戶名 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入帳戶戶名"
          [(ngModel)]="bankAccount.name"
        />
      </div>
      <div class="row g-3">
        <div class="col">
          <label class="form-label">總行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 004"
            [(ngModel)]="bankAccount.bankCode"
          />
        </div>
        <div class="col">
          <label class="form-label">分行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 1234"
            [(ngModel)]="bankAccount.branchCode"
          />
        </div>
      </div>
      <div class="mt-3">
        <label class="form-label">帳號 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入完整7-14碼帳號"
          [(ngModel)]="bankAccount.accountNumber"
        />
      </div>
    </div>
  </c-modal-body>

  <c-modal-footer class="flex-column align-items-start gap-2">

    <div class="form-check ms-2">
      <input
        class="form-check-input"
        type="checkbox"
        id="confirmDataCorrect"
        [(ngModel)]="confirmDataCorrect"
      />
      <label class="form-check-label" for="confirmDataCorrect">
        我已確認付款資料正確無誤並同意<a
        href="/terms/create_enterprise"
        target="_blank"
        rel="noopener noreferrer"
      >
      平台使用條款
      </a>
      </label>
    </div>

    <div class="w-100 d-flex justify-content-end gap-2">
      <button
        cButton
        variant="ghost"
        (click)="togglePaymentModal()"
      >
        取消
      </button>
      <button
        cButton
        color="primary"
        class="text-white"
        (click)="savePayment()"
        [disabled]="isFormInvalid() || !confirmDataCorrect"
      >
        確認儲存
      </button>
    </div>

  </c-modal-footer>

</c-modal>



<c-modal [visible]="payoutModalVisible" size="lg">
  <c-modal-header>
    <h5 cModalTitle>設定收款帳戶</h5>
    <button cButtonClose (click)="togglePayoutModal()"></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mt-2">
      <h6 class="fw-bold mb-3">銀行帳戶資料</h6>
      <div class="mb-3">
        <label class="form-label">戶名 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入帳戶戶名"
          [(ngModel)]="bankAccount.name"
        />
      </div>
      <div class="row g-3">
        <div class="col">
          <label class="form-label">總行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 004"
            [(ngModel)]="bankAccount.bankCode"
          />
        </div>
        <div class="col">
          <label class="form-label">分行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 1234"
            [(ngModel)]="bankAccount.branchCode"
          />
        </div>
      </div>
      <div class="mt-3">
        <label class="form-label">帳號 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入完整7-14碼帳號"
          [(ngModel)]="bankAccount.accountNumber"
        />
      </div>
    </div>
  </c-modal-body>

  <c-modal-footer class="flex-column align-items-start gap-2">

    <div class="form-check ms-2">
      <input
        class="form-check-input"
        type="checkbox"
        id="confirmDataCorrect"
        [(ngModel)]="confirmDataCorrect"
      />
      <label class="form-check-label" for="confirmDataCorrect">
        我已確認收款資料正確無誤並同意<a
        href="/terms/create_enterprise"
        target="_blank"
        rel="noopener noreferrer"
      >
      平台使用條款
      </a>
      </label>
    </div>

    <div class="w-100 d-flex justify-content-end gap-2">
      <button
        cButton
        variant="ghost"
        (click)="togglePayoutModal()"
      >
        取消
      </button>
      <button
        cButton
        color="primary"
        class="text-white"
        (click)="savePayout()"
        [disabled]="isFormInvalid('payout') || !confirmDataCorrect"
      >
        確認儲存
      </button>
    </div>

  </c-modal-footer>

</c-modal>
