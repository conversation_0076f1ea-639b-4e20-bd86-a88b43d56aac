import { Component } from '@angular/core';
import { BillAnalysisService } from '../../../service/bill-analysis.service';
import { UserService } from '../../../service/user.service';
import { Router, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { cilSearch } from '@coreui/icons';
import { IconDirective } from '@coreui/icons-angular';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
} from '@coreui/angular';
import {
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
} from '@coreui/angular';

interface project {
  place_id: string;
  earliest_month: string;
  latest_month: string;
}



@Component({
  selector: 'app-bill-analysis-view',
  standalone: true,
  imports: [ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    InputGroupComponent,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    RouterModule,
    IconDirective,],
  templateUrl: './bill-analysis-overview.component.html',
  styleUrl: './bill-analysis-overview.component.scss'
})
export class BillAnalysisViewComponent {
  icons = { cilSearch };
  errmsg: string;
  place_id: string;
  earliest_month: string;
  latest_month: string;
  Place_Id_List: project[] = [];
  searchText: string = '';
  leave_visible: boolean;
  leave_index: number;
  hovering: boolean[];

  constructor( private BillAnalysisService : BillAnalysisService,
     ){
      this.errmsg = '';
      this.place_id = '';
      this.earliest_month = '';
      this.latest_month = '';
      this.errmsg = '';
      this.leave_visible = false;
      this.leave_index = 0;
      this.get_user_bill_analysis_place_id_list();
      this.hovering = this.filteredPlaceId().map(() => false);

     }
     get_user_bill_analysis_place_id_list() {
      this.BillAnalysisService.get_user_bill_analysis_place_id_list().subscribe(
        (data) => {
          console.log('Received data:', data);
          for (let i = 0; i < data.place_id_list.length; i++) {
            this.Place_Id_List[i] = {
              place_id: data.place_id_list[i].place_id,
              earliest_month: data.place_id_list[i].earliest_month,
              latest_month: data.place_id_list[i].latest_month            };
          }
          console.log('Place_Id_List:', this.Place_Id_List);
          localStorage.setItem('token', data.token);
        },
        (error) => {
          this.errmsg = 'Project find error!';

        }
      );
    }
  filteredPlaceId() {
    return this.Place_Id_List.filter((p) =>
      p.place_id.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }
  remove_place_id_analysis(place_id: string) {
    this.BillAnalysisService.remove_place_id_analysis(place_id).subscribe({
      next: (res) => {
        console.log('刪除成功:', res);
        // ✅ 方法 A：直接重新載入當前頁面
        window.location.reload();

      },
      error: (err) => {
        console.error('刪除失敗:', err);
      }
    });
  }
}
