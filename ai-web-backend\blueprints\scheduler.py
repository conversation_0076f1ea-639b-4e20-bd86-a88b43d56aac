from flask import request, Blueprint, jsonify
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required, role_required
from bson.objectid import ObjectId
from flask import current_app
import os
import datetime


scheduler_page = Blueprint('scheduler', __name__)


@scheduler_page.route('/get_scheduler', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_scheduler(token):
    user_email = get_user_info(token, 'email')
    user_roles = get_user_info(token, 'role')
    filtered_event = []
    all_event = list(mongo.db.events.find())
    for event in all_event:
        is_authorized = False
        project_code = event.get("project_code")
        event_roles = event.get("role", {})
        if 'project leader' in user_roles:
            user_projects = mongo.db.user.find_one({"email": user_email}).get('project', [])
            if project_code in user_projects:
                is_authorized = True
        if 'TPC_Officer' in user_roles:
            TPC_Officer_email_in_event = event_roles.get("TPC_Officer")
            if TPC_Officer_email_in_event == user_email:
                is_authorized = True
        if is_authorized:
            filtered_event.append(event)
    tasks_data = []
    event_types_set = set()
    event_names_set = set()
    statuses_set = set()
    project_names_set = set()
    for event in filtered_event:
        event_id = event.get("event_id")
        project_name = event.get("project_name")
        event_name = event.get("event_name")
        event_type = event.get("event_type")
        last_update = event.get("last_update")
        event_status = event.get("status")
        event_stage = event.get("stage")    
        task = {
            "project_name": project_name,
            "event_name": event_name,
            "event_type": event_type,
            "status": event_status,
            "stage": event_stage,
            "last_update": last_update,
            "event_id": event_id,
            "project_code": event.get("project_code"),
        }
        tasks_data.append(task)
        if event_type:
            event_types_set.add(event_type)
        if event_name:
            event_names_set.add(event_name)
        if project_name:
            project_names_set.add(project_name)
        if event_status:
            statuses_set.add(event_status)
    event_types = list(event_types_set)
    event_names = list(event_names_set)
    project_names = list(project_names_set)
    statuses = list(statuses_set)

    return jsonify({
        "tasks": tasks_data,
        "projectNames": project_names,
        "eventNames": event_names,
        "eventTypes": event_types,
        "statuses": statuses,
        "token": get_refresh_token(token)
    })
    
@scheduler_page.route('/get_gantt_data', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_gantt_data(token):
    
    events = mongo.db.events.find()
    gantt_data = []

    for event in events:
        event_id = event.get("event_id")
        event_name = event.get("event_name")

     
        histories = mongo.db.events_history.find({"event_id": event_id})

        steps = []
        for i in histories:
            steps.append({"name": i.get("name"), "start": i.get("start_time"), "end": i.get("end_time"), "status": i.get("status")})

        gantt_data.append({
            "name": event_name,
            "event_id": event_id,
            "steps": steps
        })

        
    return jsonify({
        "token": get_refresh_token(token),
        "gantt_data": gantt_data
    })

