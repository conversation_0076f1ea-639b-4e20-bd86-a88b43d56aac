<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>掃描設備位置</strong>
      </c-card-header>
      <c-card-body>
        <table [striped]="true" cTable class="mb-3" *ngIf="deviceIPList.length>0">
          <thead>
            <tr>
              <th>IP</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let ip of deviceIPList; let i = index">
              <th>
                {{ip}}
              </th>
            </tr>
          </tbody>
        </table>
        <c-col>
          <div *ngIf="scanerrMsg" class="alert alert-danger">
            {{scanerrMsg }}
          </div>
          <div *ngIf="scanpendingMsg" class="alert alert-info">
            {{scanpendingMsg }}
          </div>
          <div *ngIf="scansuccessMsg" class="alert alert-success">
            {{scansuccessMsg }}
          </div>
        </c-col>
        <c-col>
          <button cButton color="primary" class="btn btn-primary" (click)="scan()"  style="float: right;" variant="outline">掃描</button>
        </c-col>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>區網安裝器</strong>
      </c-card-header>
      <c-card-body>
        <c-input-group class="mb-4">
          <label [sm]="2" cCol cLabel="col" for="projectcode" class="mx-4">
            主機ID
          </label>
          <input aria-label="new_projectname" cFormControl [(ngModel)]="gateway_id" name="gateway_ID" placeholder = "99999999999_0" required/>
          <label [sm]="2" cCol cLabel="col" for="new_gateway_placeID" class="mx-4">
            閘道主機區網IP
          </label>
          <input aria-label="new_projectname" cFormControl [(ngModel)]="lan_IP" name="lan_IP" placeholder = "************" required/>
        </c-input-group>
        <div *ngIf="errMsg" class="alert alert-danger">
          {{errMsg }}
        </div>
        <div *ngIf="pendingMsg" class="alert alert-info">
          {{pendingMsg }}
        </div>
        <div *ngIf="successMsg" class="alert alert-success">
          {{successMsg }}
        </div>
        <button cButton color="primary" class="btn btn-primary" (click)="install()"  style="float: right;" variant="outline">安裝</button>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>區網解安裝器</strong>
      </c-card-header>
      <c-card-body>
        <c-input-group class="mb-4">
          <label [sm]="2" cCol cLabel="col" for="new_gateway_placeID" class="mx-4">
            閘道主機區網IP
          </label>
          <input aria-label="new_projectname" cFormControl [(ngModel)]="lan_IP_uninstall" name="lan_IP" placeholder = "************" required/>
        </c-input-group>
        <div *ngIf="errMsg_uninstall" class="alert alert-danger">
          {{errMsg_uninstall }}
        </div>
        <div *ngIf="pendingMsg_uninstall" class="alert alert-info">
          {{pendingMsg_uninstall }}
        </div>
        <div *ngIf="successMsg_uninstall" class="alert alert-success">
          {{successMsg_uninstall }}
        </div>
        <button cButton color="danger" (click)="uninstall()"  style="float: right;" variant="outline">解除安裝</button>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

