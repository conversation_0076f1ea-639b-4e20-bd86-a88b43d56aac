// 甘特圖相關接口
interface GanttEvent {
  id: string;
  name: string;
  steps: GanttStep[];
}

interface GanttStep {
  id: string;
  name: string;
  start_time: Date;
  end_time: Date;
  status: string;
}

// Event 相關接口
interface EventData {
  event_id: string;
  event_type: string;
  event_name: string;
  project_code: string;
  project_name: string;
  stage: string;
  status: string;
  last_update: string;
  role?: { [key: string]: string };
  [key: string]: any;
}

interface EventHistory {
  event_id: string;
  name: string;
  stage: string;
  status: string;
  start_time: string;
  end_time: string;
  owner: string;
  description?: string;
}

// 加進shared Module

import { Component, OnInit } from '@angular/core';
import { SchedulerService, SchedulerTask } from './../../service/scheduler.service';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../shared/shared.module';
import { event_config_list, event_type_trans } from './event_config';

import {


  AvatarModule,
  BadgeComponent,
  PaginationComponent,
  PageItemDirective,
  PageLinkDirective,

  SpinnerComponent,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import {
  cilSearch,
  cilPlus,
  cilTrash,
  cilPencil,
  cilSettings,
  cilOptions,
  cilCloudDownload,
  cilReload,
  cilMediaPlay,
  cilCalendar,
  cilClock,
  cilFilter,
  cilChevronLeft,
  cilChevronRight,
  cilChevronBottom,
  cilArrowTop,
  cilArrowBottom,
  cilSwapVertical
} from '@coreui/icons';

// 改成shared Module，缺的補

import { FullCalendarModule } from '@fullcalendar/angular';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import zhTwLocale from '@fullcalendar/core/locales/zh-tw';

@Component({
  selector: 'app-scheduler',
  standalone: true,
  imports: [
    SharedModule,
    AvatarModule,
    BadgeComponent,
    PaginationComponent,
    PageItemDirective,
    PageLinkDirective,
    SpinnerComponent,
    FullCalendarModule,
  ],
  templateUrl: './scheduler.component.html',
  styleUrl: './scheduler.component.scss'
})
export class SchedulerComponent implements OnInit {
  // 圖示
  icons = {
    cilSearch,
    cilPlus,
    cilTrash,
    cilPencil,
    cilSettings,
    cilOptions,
    cilCloudDownload,
    cilReload,
    cilMediaPlay,
    cilCalendar,
    cilClock,
    cilFilter,
    cilChevronLeft,
    cilChevronRight,
    cilChevronBottom,
    cilArrowTop,
    cilArrowBottom,
    cilSwapVertical
  };

  // 暴露 Math 對象給模板使用
  Math = Math;

  // 基礎資料
  tasks: SchedulerTask[] = [];
  filteredTasks: SchedulerTask[] = [];
  eventNameList: string[] = [];
  statusList: string[] = [];
  projectNameList: string[] = [];
  eventTypeList: string[] = [];

  // 新增：Event 資料
  events: EventData[] = [];
  eventHistories: EventHistory[] = [];

  // 輔助函數：獲取事件配置
  private getEventConfig(eventType: string): any {
    return event_config_list.find(config => 
      config.title === eventType
    ) || event_config_list[0]; // 預設使用第一個配置
  }

  // 輔助函數：替換路由連結變數
  private replaceRouterLinkVariables(routerLink: string, event: any): string {
    if (!routerLink) return '';
    
    return routerLink
      .replace(/%KEY%/g, event.event_id || '')
      .replace(/%PROJECT_CODE%/g, event.project_code || '')
      .replace(/%EVENT_ID%/g, event.event_id || '');
  }

  // 輔助函數：替換通知訊息變數
  private replaceNotificationVariables(message: string, event: any): string {
    if (!message) return '';
    
    return message
      .replace(/%TITLE%/g, event.event_name || event.title || '')
      .replace(/%DESCRIPTION%/g, event.project_name || event.description || '')
      .replace(/%PROJECT_NAME%/g, event.project_name || '');
  }

  // 專案代碼
  project_code: string = '';

  // 顯示卡片類型
  showCard: string = 'overview';

  // 搜尋和篩選
  searchText: string = '';
  statusFilter: string = 'all';
  eventNameFilter: string = 'all';
  projectNameFilter: string = 'all';

  // 分頁
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalItems: number = 0;

  // 載入狀態
  loading: boolean = true;

  // 排序
  sortField: string = 'last_update';
  sortDirection: 'asc' | 'desc' = 'desc';

  // 甘特圖相關變數
  ganttEvents: GanttEvent[] = [];
  timelineWidth: number = 2000;
  rowHeight: number = 40;
  periodWidth: number = 100;
  viewMode: 'day' | 'week' | 'month' | 'year' = 'day';
  zoomLevel: number = 1;
  ganttStartDate: Date = new Date();
  ganttEndDate: Date = new Date();

  // 時間滑桿相關變數
  // 固定天數選項索引 (負數表示過去，正數表示未來)
  dayOptions: number[] = [-30, 5, 10, 15, 30, 60, 90, 120, 180, 365];
  selectedDayIndex: number = 4; // 預設選擇索引4，即30天
  
  get selectedDays(): number {
    return this.dayOptions[this.selectedDayIndex];
  }

  get dayOptionLabel(): string {
    const days = this.selectedDays;
    if (days < 0) {
      return `過去${Math.abs(days)}天`;
    } else {
      return `未來${days}天`;
    }
  }

  // FullCalendar 行事曆配置
  calendarOptions = {
    plugins: [dayGridPlugin, interactionPlugin],
    initialView: 'dayGridMonth',
    locale: zhTwLocale,
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: '' // 移除周/年視圖，只保留月份
    },
    buttonText: {
      today: '今天',
      month: '月'
    },
    titleFormat: { 
      year: 'numeric' as const, 
      month: 'long' as const
    },
    dayHeaderFormat: { 
      weekday: 'short' as const
    },
    events: [], // 初始為空，將透過 loadCalendarEvents 載入
    editable: false,
    selectable: false,
    aspectRatio: 1.2,
    contentHeight: 500,
    dayMaxEventRows: 2, // 減少每天顯示的事件行數，讓每個事件更大
    dayMaxEvents: true,
    fixedWeekCount: false,
    expandRows: true,
    displayEventTime: false,
    eventDisplay: 'block',
    slotMinWidth: 80,
    height: 'auto',
    eventClassNames: 'fc-event-business',
    eventMinHeight: 70, // 增加最小事件高度
    eventClick: (info: any) => {
      // 點擊事件時跳轉到詳細頁面
      const eventData = info.event.extendedProps;
      this.openEventDetails(eventData, null);
    },
    eventDidMount: (info: any) => {
      // 自定義事件顯示樣式 - 更商務化
      const eventData = info.event.extendedProps;
      if (eventData) {
        const statusInfo = this.getStatusDisplay(eventData);
        const element = info.el;
        
        // 設定顏色 - 確保與總攬完全一致
        const statusColor = this.getEventColor(statusInfo.color);
        element.style.backgroundColor = statusColor;
        element.style.borderColor = statusColor;
        element.style.borderWidth = '2px';
        element.style.borderRadius = '8px';
        element.style.fontWeight = '600';
        element.style.fontSize = '14px'; // 增加字體大小
        element.style.padding = '6px 10px'; // 增加內邊距
        element.style.cursor = 'pointer';
        element.style.minHeight = '70px'; // 增加最小高度
        element.style.lineHeight = '1.4';
        
        // 設定 tooltip
        const eventTypeChinese = eventData.eventTypeChinese || this.getEventTypeDisplay(eventData);
        element.title = `專案：${eventData.project_name}\n事件：${eventTypeChinese}\n狀態：${statusInfo.text}\n更新：${eventData.last_update}`;
        
        // 設定內容 - 調整字體大小和間距
        const titleElement = element.querySelector('.fc-event-title');
        if (titleElement) {
          titleElement.innerHTML = `<div style="line-height: 1.4; padding: 3px;">
            <div style="font-weight: 700; font-size: 15px; margin-bottom: 3px; color: white;">${eventData.project_name}</div>
            <div style="font-size: 14px; margin-bottom: 3px; color: white; opacity: 0.95;">${eventTypeChinese}</div>
            <div style="font-size: 13px; color: white; font-weight: 600; opacity: 0.9;">${statusInfo.text}</div>
          </div>`;
        }
      }
    }
  };

  constructor(
    private schedulerService: SchedulerService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.initializeGanttDates();
  }

  ngOnInit(): void {
    // 獲取專案代碼
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';

    // 添加調試資訊
    console.log('Scheduler component initialized');
    console.log('Project code:', this.project_code);

    this.loadAllSchedulerData();
    this.initializeGanttData();
    this.updateGanttDateRange();
  }

  // 載入排程資料 - 統一使用 event collection
  loadAllSchedulerData(): void {
    this.loading = true;
    this.schedulerService.getScheduler().subscribe(
      (data) => {
        console.log('從後端收到的完整資料:', data);
        
        // 處理 events 資料
        this.events = data.tasks || [];
        console.log('處理後的 events:', this.events);
        
        // 轉換為 tasks 格式以相容現有邏輯
        this.tasks = this.events.map((event: any) => {
          const eventConfig = this.getEventConfig(event.event_type);
          
          // 確保資料欄位正確處理，避免 null 值
          const stage = event.stage || event.step_name || '申請'; // 預設為申請階段
          // 事件名稱應該使用 event_type，而不是 project_name
          const eventName = event.event_type || event.event_name || `事件${event.event_id}`;
          const lastUpdate = event.last_update || event.create_time || new Date().toISOString().split('T')[0];
          const status = event.status || 'pending'; // 預設為等待中
          
          console.log(`處理事件 ${event.event_id}:`, {
            原始event_name: event.event_name,
            原始stage: event.stage,
            原始status: event.status,
            原始last_update: event.last_update,
            處理後event_name: eventName,
            處理後stage: stage,
            處理後status: status,
            處理後last_update: lastUpdate
          });
          
          return {
            event_id: event.event_id,
            event_name: eventName,
            event_type: event.event_type,
            project_name: event.project_name,
            project_code: event.project_code,
            status: status,
            stage: stage,
            last_update: lastUpdate,
            // 直接在前端計算優先級，不依賴後端
            priority: this.calculateEventPriority({...event, status: status}),
            // 根據 event_config 產生的欄位
            routerLink: this.generateRouterLink({...event, stage: stage}, eventConfig),
            canPin: this.canUserPinEvent({...event, stage: stage}, eventConfig),
            notification: this.getEventNotification({...event, stage: stage}, eventConfig),
            // 受理號碼
            Acceptance_Number: event.Acceptance_Number
          } as SchedulerTask;
        });

        console.log('轉換後的 tasks:', this.tasks);
        console.log('範例 task 資料:', this.tasks.length > 0 ? this.tasks[0] : '無資料');

        // 測試狀態顯示功能
        if (this.tasks.length > 0) {
          const testTask = this.tasks[0];
          console.log('測試狀態顯示:', {
            task: testTask,
            statusDisplay: this.getStatusDisplay(testTask),
            coreStatusDisplay: this.getCoreStatusDisplay(testTask.status)
          });
          
          // 測試翻譯功能
          console.log('測試翻譯功能:', {
            'pending -> 中文': event_type_trans('pending', true),
            '等待中 -> 英文': event_type_trans('等待中', false),
            '申請 -> 英文': event_type_trans('申請', false),
            'application -> 中文': event_type_trans('application', true)
          });
        }

        // 載入下拉選單選項
        this.loadFilterOptions(data, this.events);

        this.applyFilters();
        
        // 載入行事曆事件
        this.loadCalendarEvents();
        
        this.loading = false;
      },
      (error) => {
        console.error('Error loading scheduler data:', error);
        this.loading = false;
      }
    );
  }

  // 計算事件優先權
  private calculateEventPriority(event: any): number {
    // 錯誤狀態始終具有最高優先級
    if (event.status === 'error') {
      return 0;  // 最高優先級
    }
    
    // 獲取當前用戶的角色
    const userEmail = localStorage.getItem('user_email') || '';
    const userRoles = (localStorage.getItem('user_roles') || '').split(',').map(r => r.trim());
    
    // 獲取事件配置
    const eventConfig = this.getEventConfig(event.event_type);
    if (!eventConfig || !eventConfig.stages) return 5; // 預設最低優先級
    
    // 獲取當前事件階段
    const stage = event.stage || '';
    const stageKey = event_type_trans(stage) || stage;
    
    // 如果找不到階段或階段在配置中不存在，使用預設優先權
    if (!stageKey || !eventConfig.stages[stageKey]) {
      return 5; // 預設最低優先級
    }
    
    // 獲取該階段對當前角色的優先順序
    if (userRoles.includes('project leader')) {
      // 1. 錯誤狀態最高 (已在前面處理)
      // 2. 有用戶動作的步驟 (如匯款)
      if (stageKey === 'payment' || stageKey === '匯款') {
        return 1; // 高優先級，需要用戶動作
      }
      return eventConfig.stages[stageKey].priority?.['project leader'] || 5;
    } else if (userRoles.includes('TPC_Officer')) {
      // 1. 錯誤狀態最高 (已在前面處理)
      // 2. 有用戶動作的步驟 (如電力公司受理)
      if (stageKey === 'power_acceptance' || stageKey === 'TPC_acceptance' || stageKey === '台電公司受理') {
        return 1; // 高優先級，需要用戶動作
      }
      return eventConfig.stages[stageKey].priority?.['TPC_Officer'] || 5;
    }
    
    return 5; // 預設最低優先級
  }

  // 根據 event_config 產生路由連結
  private generateRouterLink(event: EventData, config?: any): string {
    if (!config || !config.stages || !config.stages[event.stage]) return '';
    
    const stageConfig = config.stages[event.stage];
    if (!stageConfig.participants) return '';
    
    // 取得第一個參與者的路由連結
    const firstParticipant = Object.keys(stageConfig.participants)[0];
    const participantConfig = stageConfig.participants[firstParticipant];
    
    return this.replaceRouterLinkVariables(participantConfig.routerLink || '', event);
  }

  // 檢查使用者是否可以 pin 此事件
  private canUserPinEvent(event: EventData, config?: any): boolean {
    if (!config || !config.stages || !config.stages[event.stage]) return false;
    
    const stageConfig = config.stages[event.stage];
    if (!stageConfig.participants) return false;
    
    // 檢查是否有任何參與者設定了 pin
    return Object.values(stageConfig.participants).some((p: any) => p.pin === true);
  }

  // 獲取事件通知
  private getEventNotification(event: EventData, config?: any): string {
    if (!config || !config.stages || !config.stages[event.stage]) return '';
    
    const stageConfig = config.stages[event.stage];
    if (!stageConfig.notification) return '';
    
    // 取得第一個通知訊息
    const firstNotification = Object.values(stageConfig.notification)[0] as any;
    return this.replaceNotificationVariables(firstNotification.msg || '', event);
  }

  // 載入篩選選項
  private loadFilterOptions(data: any, events: any[]): void {
    // 強制從事件資料中提取事件類型，不使用後端提供的 eventNames
    // 因為後端的 eventNames 可能包含專案名稱而不是事件類型
    const uniqueEventTypes = [...new Set(events.map(e => e.event_type).filter(Boolean))];
    console.log('提取到的唯一事件類型:', uniqueEventTypes);
    
    // 確保事件名稱都翻譯為中文顯示
    this.eventNameList = uniqueEventTypes.map(eventType => {
      const translated = event_type_trans(eventType, true);
      console.log(`事件類型翻譯: ${eventType} -> ${translated}`);
      return translated || eventType; // 如果沒有翻譯，使用原始名稱
    });
    
    console.log('翻譯後的事件名稱列表:', this.eventNameList);
    
    // 從 API 回應或事件資料載入專案名稱和事件類型
    this.projectNameList = data.projectNames || [];
    this.eventTypeList = data.eventTypes || [];
    
    if (this.projectNameList.length === 0) {
      this.projectNameList = [...new Set(events.map(e => e.project_name).filter(Boolean))];
    }
    
    if (this.eventTypeList.length === 0) {
      this.eventTypeList = [...new Set(events.map(e => e.event_type).filter(Boolean))];
    }

    // 根據當前選擇的事件名稱來決定狀態列表
    this.updateStatusList(events);

    console.log('載入的事件資料數量:', events.length);
    console.log('範例事件資料:', events.length > 0 ? events[0] : '無資料');
    console.log('原始事件類型列表:', events.map(e => e.event_type));
    console.log('最終事件名稱列表 (eventNameList):', this.eventNameList);
    console.log('事件類型列表 (eventTypeList):', this.eventTypeList);
    console.log('狀態列表:', this.statusList);
    console.log('專案名稱列表:', this.projectNameList);
  }

  // 從 event_config 動態生成階段+狀態組合
  private generateStageStatusCombinations(): string[] {
    const combinations: string[] = [];
    
    // 遍歷 event_config_list 中的所有階段
    event_config_list.forEach(config => {
      if (config.stages) {
        Object.keys(config.stages).forEach(stage => {
          const translatedStage = event_type_trans(stage, true) || stage;
          
          // 為每個階段添加所有可能的狀態組合
          const statusValues = ['pending', 'in_process', 'done', 'error', 'canceled'];
          statusValues.forEach(status => {
            const translatedStatus = event_type_trans(status, true) || status;
            const combination = `${translatedStage}${translatedStatus}`;
            if (!combinations.includes(combination)) {
              combinations.push(combination);
            }
          });
        });
      }
    });
    
    // 從 event_config 動態獲取階段順序，而不是硬編碼
    const stageOrder: string[] = [];
    const statusOrder = ['等待中', '進行中', '完成', '錯誤', '取消'];
    
    // 遍歷 event_config_list 獲取實際的階段順序
    event_config_list.forEach(config => {
      if (config.stages) {
        Object.keys(config.stages).forEach(stage => {
          const translatedStage = event_type_trans(stage, true) || stage;
          if (!stageOrder.includes(translatedStage)) {
            stageOrder.push(translatedStage);
          }
        });
      }
    });
    
    const orderedCombinations: string[] = [];
    stageOrder.forEach(stage => {
      statusOrder.forEach(status => {
        const combination = `${stage}${status}`;
        if (combinations.includes(combination)) {
          orderedCombinations.push(combination);
        }
      });
    });
    
    // 添加任何未在預定義順序中的組合
    const remainingCombinations = combinations.filter(c => !orderedCombinations.includes(c));
    
    return [...orderedCombinations, ...remainingCombinations.sort()];
  }

  // 更新狀態列表 - 根據當前篩選條件顯示實際存在的狀態
  private updateStatusList(events: any[]): void {
    console.log('更新狀態列表，事件數量:', events.length);
    
    // 檢查是否有篩選事件名稱
    const hasEventNameFilter = this.eventNameFilter !== 'all';
    
    // 從實際篩選後的資料中提取狀態顯示文字
    const actualStatusTexts = new Set<string>();
    const actualCoreStatuses = new Set<string>(); // 實際存在的核心狀態
    
    events.forEach((event, index) => {
      try {
        // 事件列表永遠顯示「階段+狀態」格式
        const statusObj = this.getStatusDisplayFromConfig(
          event.event_type || '', 
          event.stage || '申請', 
          event.status || 'pending'
        );
        actualStatusTexts.add(statusObj.text);
        
        // 同時記錄核心狀態
        const coreStatus = this.getCoreStatusDisplay(event.status || 'pending');
        actualCoreStatuses.add(coreStatus);
        
        // 詳細調試前幾個事件
        if (index < 3) {
          console.log(`事件 ${index + 1} 狀態分析:`, {
            event_id: event.event_id,
            event_type: event.event_type,
            stage: event.stage,
            status: event.status,
            statusDisplayText: statusObj.text,
            coreStatus: coreStatus
          });
        }
      } catch (error) {
        console.warn('處理狀態時發生錯誤:', error);
        // 使用基本狀態作為備用
        const basicStatus = this.getCoreStatusDisplay(event.status || 'pending');
        actualCoreStatuses.add(basicStatus);
      }
    });
    
    // 下拉選單顯示邏輯
    if (hasEventNameFilter) {
      // 有篩選事件名稱時，下拉選單顯示「階段+狀態」組合
      // 從 event_config 動態生成階段+狀態組合
      const statusPriorityOrder = this.generateStageStatusCombinations();
      
      // 按優先順序排列實際存在的狀態
      this.statusList = statusPriorityOrder.filter(status => actualStatusTexts.has(status));
      
      // 添加任何未在預定義順序中的狀態
      const remainingStatuses = Array.from(actualStatusTexts)
        .filter(status => !statusPriorityOrder.includes(status))
        .sort();
      
      this.statusList = [...this.statusList, ...remainingStatuses];
    } else {
      // 沒有篩選事件名稱時，下拉選單只顯示實際存在的核心狀態
      const coreStatusOrder = [
        '等待中', '進行中', '完成', '錯誤', '取消'
      ];
      
      // 只顯示實際存在的核心狀態
      this.statusList = coreStatusOrder.filter(status => actualCoreStatuses.has(status));
      
      // 添加任何未在預定義順序中的狀態
      const remainingStatuses = Array.from(actualCoreStatuses)
        .filter(status => !coreStatusOrder.includes(status))
        .sort();
      
      this.statusList = [...this.statusList, ...remainingStatuses];
    }
    
    console.log('實際存在的狀態列表:', this.statusList);
    console.log('是否有事件名稱篩選:', hasEventNameFilter);
    console.log('實際存在的核心狀態:', Array.from(actualCoreStatuses));
  }

  // 切換排序方向
  toggleSort(field: string): void {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.applyFilters();
  }

  // 清除所有篩選條件
  clearAllFilters(): void {
    this.searchText = '';
    this.statusFilter = 'all';
    this.eventNameFilter = 'all';
    this.projectNameFilter = 'all';
    this.applyFilters();
  }

  // 設定事件名稱篩選並重置狀態篩選
  setEventNameFilter(eventName: string): void {
    console.log('=== 事件名稱篩選調試 ===');
    console.log('設定事件名稱篩選:', eventName);
    console.log('當前 eventNameList:', this.eventNameList);
    console.log('當前 projectNameList:', this.projectNameList);
    console.log('比較: eventNameList vs projectNameList');
    console.log('- eventNameList 是否包含專案名稱:', this.eventNameList.some(name => this.projectNameList.includes(name)));
    console.log('- projectNameList 是否包含事件名稱:', this.projectNameList.some(name => this.eventNameList.includes(name)));
    console.log('當前 tasks 中的事件類型 vs 專案名稱:', this.tasks.slice(0, 3).map(t => ({
      event_id: t.event_id,
      event_type: t.event_type, 
      project_name: t.project_name,
      translated_event_type: this.getEventTypeDisplay(t)
    })));
    console.log('=========================');
    
    this.eventNameFilter = eventName;
    // 當事件名稱篩選改變時，重置狀態篩選
    this.statusFilter = 'all';
    this.applyFilters();
  }

  // 設定狀態篩選
  setStatusFilter(status: string): void {
    this.statusFilter = status;
    this.applyFilters();
  }

  // 在數組中切換項目
  private toggleArrayItem<T>(array: T[], item: T): void {
    const index = array.indexOf(item);
    if (index !== -1) {
      array.splice(index, 1);
    } else {
      array.push(item);
    }
  }

  // 獲取已選擇的篩選項顯示文字
  getSelectedFiltersText(): string {
    const parts: string[] = [];

    if (this.statusFilter !== 'all') {
      parts.push(`狀態: ${this.statusFilter}`);
    }

    if (this.eventNameFilter !== 'all') {
      parts.push(`事件名稱: ${this.eventNameFilter}`);
    }

    if (this.projectNameFilter !== 'all') {
      parts.push(`專案名稱: ${this.projectNameFilter}`);
    }

    if (this.searchText) {
      parts.push(`搜尋: ${this.searchText}`);
    }

    return parts.length > 0 ? parts.join(' | ') : '';
  }

  // 變更頁碼
  pageChanged(event: any): void {
    this.currentPage = typeof event === 'number' ? event : this.currentPage;
  }

  // 取得當前頁面的任務
  getCurrentPageTasks(): SchedulerTask[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const pageTasks = this.filteredTasks.slice(startIndex, startIndex + this.itemsPerPage);
    return pageTasks;
  }

  // 獲取分頁頁碼陣列
  getPaginationPages(): number[] {
    const totalPages = this.getTotalPages();

    if (totalPages <= 7) {
      // 如果總頁數少於等於7，顯示所有頁碼
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    } else {
      // 如果總頁數大於7，顯示部分頁碼
      const pages: number[] = [];

      // 總是顯示當前頁
      pages.push(this.currentPage);

      // 顯示當前頁前後各最多2頁
      for (let i = 1; i <= 2; i++) {
        if (this.currentPage - i > 1) {
          pages.push(this.currentPage - i);
        }
        if (this.currentPage + i < totalPages) {
          pages.push(this.currentPage + i);
        }
      }

      // 排序並去重
      return [...new Set(pages)].sort((a, b) => a - b);
    }
  }

  // 獲取總頁數
  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage) || 1;
  }

  // 取得狀態顏色
  // 根據階段名稱取得基本顏色 (備用方法，優先使用 getStatusDisplay)
  getStatusColor(stage: string | undefined): string {
    // 優先從 event_config_list 獲取階段對應的顏色設定
    if (stage) {
      // 查找是否有英文對應的中文階段名稱
      const stageChinese = event_type_trans(stage, true) || stage;
      
      // 階段顏色對照表 (這應該與通用的色彩邏輯相符)
      switch (stageChinese) {
        case '申請': return 'info';
        case '電力公司受理': return 'primary';
        case '匯款': return 'warning';
        case '生效': return 'success';
        case '發票開立': return 'danger';
      }
    }
    
    // 預設顏色
    return 'secondary';
  }

  // 根據事件類型和階段動態產生狀態顯示資訊
  private getStatusDisplayFromConfig(eventType: string, stage: string, status: string): { text: string, color: string } {
    // 使用 event_config.ts 的翻譯函數
    const translatedStage = event_type_trans(stage, true) || stage || '';
    const translatedStatus = event_type_trans(status, true) || status;

    // 狀態顏色對照表
    const statusColors: Record<string, string> = {
      'pending': 'warning',
      'in_process': 'info', 
      'done': 'success',
      'canceled': 'secondary',
      'error': 'danger'
    };

    const color = statusColors[status] || 'primary';

    // 組合「階段+狀態」格式
    let displayText = '';
    if (translatedStage && translatedStatus) {
      displayText = `${translatedStage}${translatedStatus}`;
    } else if (translatedStatus) {
      displayText = translatedStatus;
    } else if (translatedStage) {
      displayText = translatedStage;
    } else {
      displayText = '未知狀態';
    }

    return { text: displayText, color: color };
  }

  // 獲取狀態顯示文字和顏色
  getStatusDisplay(task: any): { text: string, color: string } {
    // 事件列表永遠顯示「階段+狀態」格式
    try {
      const result = this.getStatusDisplayFromConfig(task.event_type, task.stage, task.status);
      return result;
    } catch (error) {
      console.error('Error in getStatusDisplay:', error);
      // 預設顯示
      return { text: task.status, color: 'primary' };
    }
  }

  // 獲取翻譯後的事件名稱顯示
  getEventNameDisplay(eventNameOrType: string | null | undefined): string {
    if (!eventNameOrType) return '';
    const translated = event_type_trans(eventNameOrType, true);
    return translated || eventNameOrType;
  }

  // 獲取事件類型的中文顯示 (專門用於 event_type)
  getEventTypeDisplay(task: any): string {
    const eventType = task.event_type || '';
    const translated = event_type_trans(eventType, true);
    return translated || eventType;
  }

  // 單純獲取狀態文字 (用於舊有模板兼容)
  getStatusText(status: string): string {
    // 簡化的中英文狀態對照表
    const statusMap: Record<string, string> = {
      'pending': '待處理',
      'in_process': '進行中',
      'done': '已完成',
      'canceled': '已取消',
      'error': '錯誤'
    };
    
    return statusMap[status] || status;
  }

  getStatusClass(task: any): string {
    if (!task) return 'bg-secondary';
    
    try {
      // 使用 getStatusDisplay 獲取狀態顏色
      const displayInfo = this.getStatusDisplay(task);
      const colorMap: Record<string, string> = {
        'success': 'bg-success',
        'info': 'bg-info',
        'warning': 'bg-warning',
        'danger': 'bg-danger',
        'secondary': 'bg-secondary',
        'primary': 'bg-primary'
      };
      
      return colorMap[displayInfo.color] || 'bg-secondary';
    } catch (error) {
      console.error('Error in getStatusClass:', error);
      // 預設返回藍色
      return 'bg-info';
    }
  }

  // 日期格式化，用於顯示
  formatDate(date: string): string {
    if (!date) return '';
    return date; // 日期已經是 YYYY-MM-DD 格式，不需要額外處理
  }


  // 設置顯示卡片
  setShowCard(card: string): void {
    this.showCard = card;
  }

  // 初始化甘特圖日期
  initializeGanttDates(): void {
    const today = new Date();
    this.ganttStartDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    this.ganttEndDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
  }

  // 初始化甘特圖數據
  // 日期輔助工具 - 支援多種日期格式
  private dateHelper(dateStr: string): Date {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 如果是相對日期格式 (如: "+5", "-10", "+15天", "-30天")
    if (dateStr.match(/^[+-]\d+天?$/)) {
      const days = parseInt(dateStr.replace('天', ''));
      return new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    }

    // 如果是絕對日期格式 (如: "2025-06-01", "2025/06/15")
    if (dateStr.match(/^\d{4}[-/]\d{1,2}[-/]\d{1,2}$/)) {
      return new Date(dateStr);
    }

    // 預設回傳今天
    return today;
  }

  initializeGanttData(): void {
    this.schedulerService.get_gantt_data().subscribe(
      (response) => {
        console.log('甘特圖數據:', response);
        if (response && response.gantt_data) {
          this.ganttEvents = response.gantt_data.map((event: any) => {
            // 根據 event_config 處理事件資料
            const eventConfig = this.getEventConfig(event.event_type || 'electricity_plan_change');
            
            return {
              id: event.event_id,
              name: event.name || event.event_name || '未命名事件',
              event_type: event.event_type,
              project_code: event.project_code,
              project_name: event.project_name,
              steps: event.steps.map((step: any, index: number) => ({
                id: `${event.event_id}-${index + 1}`,
                name: step.name,
                start_time: new Date(step.start || step.start_time),
                end_time: new Date(step.end || step.end_time),
                status: step.status.toLowerCase(),
                description: step.description || `${step.name}步驟`,
                owner: step.owner || '',
                // 根據 event_config 產生路由連結
                routerLink: eventConfig ? this.generateStepRouterLink(event, step, eventConfig) : ''
              }))
            };
          });

          console.log('轉換後的甘特圖事件:', this.ganttEvents);
          this.updateGanttDateRange();
        }
      },
      (error) => {
        console.error('載入甘特圖數據錯誤:', error);
      }
    );
  }

  // 為甘特圖步驟產生路由連結
  private generateStepRouterLink(event: any, step: any, config: any): string {
    const stageConfig = config.stages && config.stages[step.name || step.stage];
    if (!stageConfig || !stageConfig.participants) return '';
    
    const firstParticipant = Object.keys(stageConfig.participants)[0];
    const participantConfig = stageConfig.participants[firstParticipant];
    
    return this.replaceRouterLinkVariables(participantConfig.routerLink || '', {
      ...event,
      event_id: event.event_id || event.id,
      event_name: event.name || event.event_name
    });
  }



  // 甘特圖核心計算 - 合併所有計算邏輯
  ganttCalc = {
    msPerDay: 24 * 60 * 60 * 1000,
    timePos: (date: Date) => Math.floor((date.getTime() - this.ganttStartDate.getTime()) / this.ganttCalc.msPerDay) * this.periodWidth,
    timeWidth: (start: Date, end: Date) => Math.max(Math.ceil((end.getTime() - start.getTime()) / this.ganttCalc.msPerDay) * this.periodWidth, 50),
    stepTop: (eventIdx: number, stepIdx: number) => {
      let rows = 0;
      for (let i = 0; i < eventIdx; i++) rows += 1 + this.ganttEvents[i].steps.length;
      return (rows + 1 + stepIdx) * this.rowHeight + 4;
    },
    dateLabel: (date: Date) => `${date.getMonth() + 1}/${date.getDate()}`,
    dateCss: (date: Date) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const checkDate = new Date(date);
      checkDate.setHours(0, 0, 0, 0);
      
      if (checkDate.getTime() === today.getTime()) {
        return 'today';
      } else if (date.getDay() === 0 || date.getDay() === 6) {
        return 'weekend';
      } else {
        return '';
      }
    }
  };

  // 甘特圖資料 - 合併所有 getters
  get ganttData() {
    const totalRows = this.ganttEvents.reduce((total, event) => total + 1 + event.steps.length, 0);
    const periods: {date: Date, label: string, cssClass: string}[] = [];
    const current = new Date(this.ganttStartDate);
    while (current <= this.ganttEndDate) {
      const date = new Date(current);
      periods.push({ date, label: this.ganttCalc.dateLabel(date), cssClass: this.ganttCalc.dateCss(date) });
      current.setDate(current.getDate() + 1);
    }

    return {
      timePeriods: periods,
      totalRowsHeight: totalRows * this.rowHeight,
      totalStepsCount: this.ganttEvents.reduce((total, event) => total + event.steps.length, 0),
      ganttHeight: 80 + Math.max(200, totalRows * this.rowHeight)
    };
  }

  // 為模板提供便利訪問
  get timePeriods() { return this.ganttData.timePeriods; }
  get totalRowsHeight() { return this.ganttData.totalRowsHeight; }
  get totalStepsCount() { return this.ganttData.totalStepsCount; }
  get ganttHeight() { return this.ganttData.ganttHeight; }

  // 甘特圖滾動處理 - 合併所有滾动邏輯
  onTimelineScroll(scrollEvent: any): void {
    const headerElement = document.querySelector('.gantt-timeline-header') as HTMLElement;
    if (headerElement) headerElement.scrollLeft = scrollEvent.target.scrollLeft;
  }

  onHeaderScroll(scrollEvent: any): void {
    const containerElement = document.querySelector('.gantt-timeline-container') as HTMLElement;
    if (containerElement) containerElement.scrollLeft = scrollEvent.target.scrollLeft;
  }

  // 甘特圖控制 - 合併滑桿和重置功能
  resetView(): void {
    this.zoomLevel = 1;
    this.periodWidth = 100;
    this.selectedDayIndex = 4;
    this.updateGanttDateRange();
  }

  onSliderChange(event: any): void {
    this.selectedDayIndex = parseInt(event.target.value);
    this.updateGanttDateRange();
  }

  get sliderCalc() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const msPerDay = 24 * 60 * 60 * 1000;
    
    let rangeStartDate: Date;
    let rangeEndDate: Date;
    
    if (this.selectedDays < 0) {
      // 顯示過去的天數
      rangeStartDate = new Date(today.getTime() + this.selectedDays * msPerDay);
      rangeEndDate = new Date(today.getTime());
    } else {
      // 顯示未來的天數
      rangeStartDate = new Date(today.getTime());
      rangeEndDate = new Date(today.getTime() + this.selectedDays * msPerDay);
    }
    
    return {
      startDate: this.ganttStartDate,
      endDate: this.ganttEndDate,
      formatDate: this.ganttCalc.dateLabel,
      selectedText: `${this.dayOptionLabel} (${this.ganttCalc.dateLabel(rangeStartDate)} - ${this.ganttCalc.dateLabel(rangeEndDate)}) | 今天: ${this.ganttCalc.dateLabel(today)}`
    };
  }

  // 甘特圖日期範圍更新
  updateGanttDateRange(): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const msPerDay = 24 * 60 * 60 * 1000;
    
    // 計算選定的日期範圍（支持過去和未來）
    let rangeStartDate: Date;
    let rangeEndDate: Date;
    
    if (this.selectedDays < 0) {
      // 顯示過去的天數
      rangeStartDate = new Date(today.getTime() + this.selectedDays * msPerDay);
      rangeEndDate = new Date(today.getTime());
    } else {
      // 顯示未來的天數
      rangeStartDate = new Date(today.getTime());
      rangeEndDate = new Date(today.getTime() + this.selectedDays * msPerDay);
    }

    const allDates: Date[] = [];
    this.ganttEvents.forEach(event => event.steps.forEach(step => {
      allDates.push(step.start_time, step.end_time);
    }));

    if (allDates.length > 0) {
      const times = allDates.map(d => d.getTime());
      const dataMinTime = Math.min(...times);
      const dataMaxTime = Math.max(...times);
      
      // 計算顯示範圍：包含選定範圍以及所有事件的日期
      const displayStartTime = Math.min(rangeStartDate.getTime(), dataMinTime);
      const displayEndTime = Math.max(rangeEndDate.getTime(), dataMaxTime);
      
      // 在前後各加7天的緩衝
      this.ganttStartDate = new Date(displayStartTime - 7 * msPerDay);
      this.ganttEndDate = new Date(displayEndTime + 7 * msPerDay);
    } else {
      // 如果沒有事件數據，就顯示選定的日期範圍
      this.ganttStartDate = new Date(rangeStartDate.getTime() - 7 * msPerDay);
      this.ganttEndDate = new Date(rangeEndDate.getTime() + 7 * msPerDay);
    }

    this.timelineWidth = Math.max(this.timePeriods.length * this.periodWidth, 1200);
  }

  // 甘特圖點擊事件處理 - 統一處理
  onGanttClick(event: any, step: any | null, clickType: string): void {
    console.log(`點擊${clickType}:`, event.name || event.project_name, '事件ID:', event.id || event.event_id);

    // 在新分頁中開啟事件詳情頁面
    this.openEventDetails(event, step);
  }

  // 在新分頁中開啟事件詳情頁面
  openEventDetails(event: any, step: any | null): void {
    console.log('在新分頁開啟事件詳情:', event);

    // 獲取事件ID (適應不同來源的資料結構)
    const eventId = event.id || event.event_id || '';

    // 這裡要確保正確獲取事件類型，而不是專案名稱
    const eventType = event.event_type || '';
    const projectName = event.project_name || '';
    const status = event.status || (step ? step.status : '');
    const stage = event.stage || (step ? step.stage : '') || '';
    const lastUpdate = event.last_update || '';

    // 構建完整 URL - 使用新的路由格式
    const baseUrl = window.location.origin;
    
    // 構建完整 URL - 只使用 event_id 作為路徑參數
    let targetUrl = `${baseUrl}/project/event/${eventId}/info`;

    // 在新分頁中開啟
    window.open(targetUrl, '_blank');
  }

  // 獲取事件顏色 (用於行事曆) - 確保與總攬完全一致
  private getEventColor(colorType: string): string {
    const colorMap: Record<string, string> = {
      'success': '#198754',    // Bootstrap 5 success 顏色
      'info': '#0dcaf0',       // Bootstrap 5 info 顏色  
      'warning': '#ffc107',    // Bootstrap 5 warning 顏色
      'danger': '#dc3545',     // Bootstrap 5 danger 顏色
      'secondary': '#6c757d',  // Bootstrap 5 secondary 顏色
      'primary': '#0d6efd'     // Bootstrap 5 primary 顏色
    };
    return colorMap[colorType] || colorMap['primary'];
  }

  // 載入行事曆事件資料
  private loadCalendarEvents(): void {
    // 將 tasks 轉換為 FullCalendar 事件格式
    const calendarEvents = this.tasks
      .map(task => {
        const statusInfo = this.getStatusDisplay(task);
        // 翻譯事件類型名稱
        const eventTypeChinese = event_type_trans(task.event_type || '', true) || task.event_type || '';
        return {
          title: `${task.project_name} - ${eventTypeChinese}`,
          date: task.last_update,
          backgroundColor: this.getEventColor(statusInfo.color),
          borderColor: this.getEventColor(statusInfo.color),
          textColor: '#fff',
          extendedProps: {
            ...task,
            statusText: statusInfo.text,
            eventTypeChinese: eventTypeChinese
          }
        };
      });

    // 更新行事曆事件
    this.calendarOptions = {
      ...this.calendarOptions,
      events: calendarEvents as any
    };
  }

  // 應用篩選條件
  applyFilters(): void {
    let filtered = [...this.tasks];

    // 搜尋文字
    if (this.searchText) {
      const searchLower = this.searchText.toLowerCase();
      filtered = filtered.filter(task => {
        // 獲取狀態顯示文字
        let statusText = '';
        try {
          statusText = this.getStatusDisplay(task).text;
        } catch (error) {
          statusText = task.status || '';
        }
        
        return (task.project_name || '').toLowerCase().includes(searchLower) ||
               (this.getEventTypeDisplay(task) || '').toLowerCase().includes(searchLower) ||
               statusText.toLowerCase().includes(searchLower);
      });
    }

    // 狀態篩選 - 考慮中文/英文對照
    if (this.statusFilter !== 'all') {
      const hasEventNameFilter = this.eventNameFilter !== 'all';
      
      filtered = filtered.filter(task => {
        if (hasEventNameFilter) {
          // 有篩選事件名稱時，比較完整的「階段+狀態」格式
          try {
            const statusDisplay = this.getStatusDisplay(task).text;
            if (statusDisplay === this.statusFilter) {
              return true;
            }
          } catch (error) {
            // 忽略錯誤
          }
        } else {
          // 沒有篩選事件名稱時，只比較核心狀態
          const coreStatus = this.getCoreStatusDisplay(task.status);
          if (coreStatus === this.statusFilter) {
            return true;
          }
          
          // 也檢查英文狀態值的翻譯是否匹配
          const translatedStatus = event_type_trans(task.status, true);
          if (translatedStatus === this.statusFilter) {
            return true;
          }
          
          // 檢查原始英文狀態值
          if (task.status === this.statusFilter) {
            return true;
          }
        }
        
        // 檢查階段是否匹配 (使用 any 類型避免類型檢查錯誤)
        const taskAny = task as any;
        if (taskAny.stage && (taskAny.stage === this.statusFilter || 
                          event_type_trans(taskAny.stage, true) === this.statusFilter ||
                          event_type_trans(this.statusFilter) === taskAny.stage)) {
          return true;
        }
        
        return false;
      });
    }

    // 事件名稱篩選
    if (this.eventNameFilter !== 'all') {
      filtered = filtered.filter(task => {
        // 獲取翻譯後的事件類型名稱 (使用 event_type)
        const translatedEventType = event_type_trans(task.event_type || '', true) || task.event_type;
        return translatedEventType === this.eventNameFilter;
      });
    }

    // 專案名稱篩選
    if (this.projectNameFilter !== 'all') {
      filtered = filtered.filter(task => task.project_name === this.projectNameFilter);
    }

    // 根據篩選後的結果更新狀態列表
    this.updateStatusList(filtered);

    // 先按優先級排序
    filtered.sort((a, b) => {
      // 先比較優先級
      const priorityA = a.priority || 5;
      const priorityB = b.priority || 5;
      
      if (priorityA !== priorityB) {
        return priorityA - priorityB; // 優先級數字越小越優先
      }
      
      // 如果優先級相同，再按選定的排序字段排序
      let fieldA: any = a[this.sortField as keyof SchedulerTask];
      let fieldB: any = b[this.sortField as keyof SchedulerTask];

      // 處理日期字串的比較
      if (typeof fieldA === 'string' && (this.sortField === 'create_time' || this.sortField === 'last_update')) {
        fieldA = fieldA ? new Date(fieldA).getTime() : 0;
        fieldB = fieldB ? new Date(fieldB).getTime() : 0;
      }

      if (fieldA < fieldB) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (fieldA > fieldB) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    this.filteredTasks = filtered;
    this.totalItems = filtered.length;
  }

  // 獲取核心狀態顯示（不包含階段）
  private getCoreStatusDisplay(status: string): string {
    // 使用 event_config.ts 的核心狀態翻譯
    const translatedStatus = event_type_trans(status, true);
    return translatedStatus || status;
  }
}
