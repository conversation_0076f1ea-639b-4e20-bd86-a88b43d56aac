import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class InventoryService {

  constructor(private http: HttpClient) { }

  lan_install(gateway_id: string, lan_IP: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/inventory/lan_install`, {gateway_id: gateway_id, lan_IP: lan_IP}, { headers });
  }

  lan_uninstall(lan_IP: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/inventory/lan_uninstall`, {lan_IP: lan_IP}, { headers });
  }

  scan(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/inventory/scan_lan`, {}, { headers });
  }
}
