import { SharedService } from './../../../service/shared.service';
import { Component } from '@angular/core';
import { SharedModule, enterprise, appItem, team } from '../../../shared/shared.module';
import { allApps } from '../applications';
import { DiscoverService } from '../../../service/discover.service';
import { EnterpriseService } from '../../../service/enterprise.service';
import { TeamService } from '../../../service/team.service';
import { OnInit } from '@angular/core';
import { Router } from '@angular/router'

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss'
})
export class OverviewComponent implements OnInit {
  constructor(
    private sharedModule: SharedModule,
    private discoverService: DiscoverService,
    private enterpriseService: EnterpriseService,
    private teamService: TeamService,
    private SharedService: SharedService,
    private router: Router
  ){

  }
  menuItems = [
    { label: '最新', value: 'latest' },
    { label: '免費應用', value: 'free' },
    { label: '付費應用', value: 'paid' },
    { label: '授權應用', value: 'licensed' },
    { label: '能源使用戶', value: 'user' },
    { label: '能源服務商', value: 'service' },
    { label: '硬體服務應用', value: 'hardware' },
    { label: '軟體服務應用', value: 'software' }
  ];
  hasBoundPaymentAccount: boolean = false;
  hasBoundPayoutAccount: boolean = false;
  team: team;


  selectedMenu = 'latest';
  searchKeyword = '';
  filteredApps = [...allApps];

  ngOnInit() {
    this.teamService.get_team_info(this.SharedService.getRouteId('team_code')).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.team = data.data;

      },
      (error) => {

      }
    )
    this.teamService.check_enterprise_payment(this.SharedService.getRouteId('team_code')).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.hasBoundPaymentAccount = data.data;
      },
      (error) => {

      }
    )
    this.teamService.check_enterprise_payout(this.SharedService.getRouteId('team_code')).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.hasBoundPayoutAccount = data.data;
      },
      (error) => {

      }
    )
  }

  openApp(id: number){
    this.router.navigate([`/team/${this.team.team_code}/discover/application-info/${id}`])
  }

  onMenuChange(menu: string) {
    this.selectedMenu = menu;
  }

  get filtered_apps() {
    const keyword = this.searchKeyword.toLowerCase();

    return allApps.filter((app) => {
      const matchesKeyword =
        app.title.toLowerCase().includes(keyword) ||
        app.subtitle.toLowerCase().includes(keyword);

      // 判斷分類
      let matchesCategory = false;

      if (['popular_user', 'popular_service', 'latest'].includes(this.selectedMenu)) {
        matchesCategory = true; // 這三種不過濾分類
      } else if (['free', 'paid', 'licensed'].includes(this.selectedMenu)) {
        matchesCategory = app.type === this.selectedMenu;
      } else {
        // selectedMenu 是 category key，判斷 app.category 陣列是否包含該分類名稱
        const categoryName = this.getCategoryName(this.selectedMenu);
        matchesCategory = app.category.includes(categoryName);
      }

      return matchesKeyword && matchesCategory;
    });
  }



  getCategoryName(menu: string): string {
    const map = {
      user: '能源使用戶',
      service: '能源服務商',
      hardware: '硬體服務應用',
      software: '軟體服務應用'
    } as const;

    return map[menu as keyof typeof map] || '';
  }



}
