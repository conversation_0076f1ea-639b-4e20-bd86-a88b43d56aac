import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class BillAnalysisService {

  constructor(private http: HttpClient) { }
  submit_request(formData: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/crawler/submit_tpc_crawler_request`, formData, { headers });
  }
  get_user_bill_analysis_place_id_list(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/bill_analysis/get_user_bill_analysis_place_id_list`, {}, { headers });
  }
  get_bill_analysis_info(place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/bill_analysis/get_bill_analysis_info`, {place_id: place_id}, { headers });
  }
  get_place_id_bill_information(place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/bill_analysis/get_place_id_bill_information`, {place_id: place_id}, { headers });
  }
  remove_place_id_analysis(place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/bill_analysis/remove_place_id_analysis`, {place_id: place_id}, { headers });
  }
}


