from flask import request, Blueprint, jsonify,redirect, current_app
from accessories import mongo, sqldb,redis_client, update_json_in_mongo, send_mail
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required, token_required
from sqlalchemy import text
import time
from sqlalchemy.exc import OperationalError

def save_mail_event_to_SQL(mail_id, status): 
    data = request.args.to_dict()
    database = data.get('database')
    query_log_event = text("""
        INSERT INTO mail_event (mail_id, event, event_time)
        VALUES (:mail_id, :event, NOW())
    """)
    delay = 1  
    max_retries = 5 
    for attempt in range(max_retries):
        try:
            with sqldb.get_engine(current_app,bind = database).connect() as connection:
                connection.execute(query_log_event, {"mail_id": mail_id, "event": status})
                connection.commit()
            return  
        except OperationalError:
            if attempt < max_retries - 1:
                time.sleep(delay)
                delay *= 2 
            else:
                raise Exception(f"Database operation failed after {max_retries} attempts")
            
role_interaction_page = Blueprint('role_interaction', __name__)


@role_interaction_page.route('/send_project_collaboration_mail', methods=['OPTIONS','POST'])
@role_required(['project leader'])
def send_project_collaboration_mail(token):
    data = request.get_json()
    receiver_email = data.get("receiver")
    sender_email = get_user_info(token, 'email')
    sender_name = get_user_info(token, 'firstname') + ', ' + get_user_info(token, 'lastname')
    receiver = mongo.db.user.find_one({'email': receiver_email})
    project_code = data.get("project_code")
    project_name = mongo.db.project.find_one({'project_code': project_code}).get('project_name')
    routes = {'accept': 'role_interaction.accept_project_collaboration', 'reject': 'role_interaction.reject_project_collaboration'}
    if not receiver:
        return {"error": f"Receiver {receiver_email} not found"}, 404
    if project_code not in get_user_info(token, 'project'):
        return {"error": f"{sender_email} don't have this project"}, 403
    if project_code in receiver.get('project'):
        return {"error": f"Receiver {receiver_email} already has project {project_code}"}, 403
    if "project leader" not in receiver.get('role'):
        return {"error": f"Receiver {receiver_email} is not a project leader"}, 403
    send_mail(sender_email,receiver_email,"專案協作邀請", f"{sender_name}邀請您一同協作專案:{project_name}", project_code, 1, routes)
    return jsonify({"token": get_refresh_token(token)}), 200

@role_interaction_page.route('/accept_project_collaboration/<mail_token>',  methods=['OPTIONS','POST','GET'])
@token_required
def accept_project_collaboration(token, mail_token):
    stored_data = redis_client.hgetall(mail_token)
    if not stored_data:
        return jsonify({"error": "Invitation expired"}), 400
    mail_id = stored_data.get(b'mail_id').decode('utf-8')
    receiver_email = stored_data.get(b'receiver').decode('utf-8')
    project_code = stored_data.get(b'argument').decode('utf-8')
    project_name = mongo.db.project.find_one({'project_code': project_code}).get('project_name')
    sender = stored_data.get(b'sender').decode('utf-8')
    receiver_user = mongo.db.user.find_one({'email': receiver_email})
    if not receiver_user:
        return jsonify({"error": "Receiver not found"}), 404
    new_data = {}
    new_data['project'] = list(set(receiver_user.get("project", []) + [project_code.strip('"')]))
    update_json_in_mongo(
        data=new_data,
        collection_name="user",
        doc_name=receiver_user["_id"]
    )
    save_mail_event_to_SQL( mail_id,"accepted")
    redis_client.delete(mail_token)
    if request.method == 'POST':
        send_mail(receiver_email,sender,"系統訊息通知",f"{receiver_user.get('firstname') + ', ' + receiver_user.get('lastname')}接受{project_name}的專案協作",project_code,due_time=0)
        return jsonify({"token": get_refresh_token(token)}), 200
    else:
        return redirect(current_app.config['DOMAIN_NAME']+'/dashboard')

@role_interaction_page.route('/reject_project_collaboration/<mail_token>', methods=['OPTIONS','POST','GET'])
@token_required
def reject_project_collaboration(token, mail_token):
    stored_data = redis_client.hgetall(mail_token)
    if not stored_data:
        return jsonify({"error": "Invitation expired"}), 400
    mail_id = stored_data.get(b'mail_id').decode('utf-8')
    receiver_email = stored_data.get(b'receiver').decode('utf-8')
    sender = stored_data.get(b'sender').decode('utf-8')
    project_code = stored_data.get(b'argument').decode('utf-8')
    project_name = mongo.db.project.find_one({'project_code': project_code}).get('project_name')
    receiver_user = mongo.db.user.find_one({'email': receiver_email})
    save_mail_event_to_SQL(mail_id,"rejected")
    redis_client.delete(mail_token)
    if request.method == 'POST':
        send_mail(receiver_email,sender,"系統訊息通知",f"{receiver_user.get('firstname') + ', ' + receiver_user.get('lastname')}拒絕{project_name}的專案協作",project_code,due_time=0)
        return jsonify({"token": get_refresh_token(token)}), 200
    else:
       return redirect(current_app.config['DOMAIN_NAME']+'/dashboard')
