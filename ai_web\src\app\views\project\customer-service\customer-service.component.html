<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div *ngIf="project">
      <h2
        class="mb-0"
        [routerLink]="['/project', project_code]"
        style="cursor: pointer"
      >
        <strong>{{ project.project_name }} - 客戶服務</strong>
      </h2>
    </div>
  </c-col>
</c-row>
<div>
  <c-card class="mb-4" *ngIf="filteredCustomers().length > 0">
    <c-card-header class="d-flex justify-content-between align-items-center">
      <strong>客戶列表</strong>
      <div class="d-flex align-items-center gap-2" style="max-width: 300px">
        <button
          class="btn shadow-sm bg-white"
          style="
            border: none;
            width: 2.25rem;
            height: 2.25rem;
            padding: 0;
            font-size: 1.25rem;
            line-height: 1;
          "
          (click)="toggle_invite_customer_modal()"
        >
          +
        </button>
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg [cIcon]="icons.cilSearch" size="xl" title="搜尋電號"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋客戶名稱..."
            [(ngModel)]="searchCustomerText"
          />
        </div>
      </div>
    </c-card-header>
    <c-card-body>
      <c-row *ngFor="let customer of filteredCustomers(); let i = index" class="mb-3">
        <c-col>
          <c-card>
            <c-card-body
              class="d-flex justify-content-between align-items-center"
            >
              <div>
                <strong>{{ customer.name }}</strong> <br />
                <small class="text-muted">{{ customer.email }}</small>
              </div>
              <div>
                <c-dropdown>
                  <button
                    cDropdownToggle
                    [caret]="false"
                    class="btn bg-white d-flex justify-content-center align-items-center"
                    style="
                      border: none;
                      width: 2.25rem;
                      height: 2.25rem;
                      padding: 0;
                    "
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="black"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="12" cy="5" r="2" />
                      <circle cx="12" cy="12" r="2" />
                      <circle cx="12" cy="19" r="2" />
                    </svg>
                  </button>
                  <ul cDropdownMenu>
                    <a
                      cDropdownItem
                      style="cursor: pointer"
                      (click)="toggle_edit_customer_view_auth_modal(i)"
                      >編輯檢視權限</a
                    >
                    <a
                      cDropdownItem
                      style="cursor: pointer"
                      class="text-danger"
                      (click)="toggle_remove_customer_modal(i)"
                      >移除成員</a
                    >
                  </ul>
                </c-dropdown>
              </div>
            </c-card-body>
          </c-card>
        </c-col>
      </c-row>
      <c-row class="mb-3">
        <c-col>
          <c-card
            class="d-flex justify-content-center align-items-center"
            style="height: 100px; cursor: pointer;"
            (click)="toggle_invite_customer_modal()"
            (mouseenter)="hovering = true"
            (mouseleave)="hovering = false"
            [ngStyle]="hovering ? {
              transform: 'scale(1.03)',
              boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
            } : {}"
          >
            <c-card-body class="text-center">
              <span style="font-size: 2rem; color: #4CAF50;">＋</span>
              <div style="font-size: 0.9rem; color: #4CAF50;">邀請客戶</div>
            </c-card-body>
          </c-card>
        </c-col>
      </c-row>
    </c-card-body>
  </c-card>

  <div
    class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
    *ngIf="customer_list.length == 0"
  >
    <c-container>
      <c-row class="justify-content-center">
        <c-col md="8" class="text-center">
          <svg
            [cIcon]="icons.cilSmilePlus"
            style="width: 3rem; height: 3rem"
            class="mb-3 text-primary"
          ></svg>
          <h4 class="mb-2">授權客戶加入專案吧！</h4>
          <p class="text-body-secondary">
            按<span
              class="fw-bold text-primary"
              (click)="toggle_invite_customer_modal()"
              style="cursor: pointer"
              >此</span
            >開始
          </p>
        </c-col>
      </c-row>
    </c-container>
  </div>
</div>

<c-modal
  #verticallyCenteredModal
  alignment="top"
  #scrollableLongContentModal
  id="liveDemoModal"
  [visible]="invite_customer_visible"
  backdrop="static"
  size="lg"
>
  <c-modal-header>
    <h5 cModalTitle>加入客戶</h5>
    <button (click)="toggle_invite_customer_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <c-row class="mb-4">
      <label [sm]="2" cCol cLabel="col" class="mx-4"> 客戶信箱 </label>
      <c-col [sm]="9">
        <input
          aria-label="gmail"
          cFormControl
          [(ngModel)]="create_customer_email"
          placeholder="<EMAIL>"
          required
        />
      </c-col>
    </c-row>
    <c-row class="mb-4">
      <label [sm]="2" cCol cLabel="col" class="mx-4"> 角色權限 </label>
      <c-col [sm]="3" class="d-flex align-items-center">
        <input
          cFormCheckInput
          type="checkbox"
          [(ngModel)]="create_customer_view"
        />
        <label cFormCheckLabel class="mx-2">設備檢視權限</label>
      </c-col>
      <c-col [sm]="3" class="d-flex align-items-center">
        <input
          cFormCheckInput
          disabled
          type="checkbox"
          [(ngModel)]="create_customer_controller"
        />
        <label cFormCheckLabel class="mx-2">設備控制權限</label>
      </c-col>
      <c-col [sm]="3" class="d-flex align-items-center">
        <input
          cFormCheckInput
          disabled
          type="checkbox"
          [(ngModel)]="create_customer_accounting"
        />
        <label cFormCheckLabel class="mx-2">績效檢視權限</label>
      </c-col>
    </c-row>
    <c-row class="mb-4">
      <label cCol cLabel="col" class="mx-4">
        *請確認該信箱已註冊AI能管系統再送出
      </label>
    </c-row>
    <button
      cButton
      color="primary"
      class="btn btn-primary"
      (click)="add_customer()"
      style="float: right"
      variant="outline"
    >
      加入
    </button>
  </c-modal-body>
</c-modal>

<c-modal
  #verticallyCenteredModal
  alignment="top"
  #scrollableLongContentModal
  id="liveDemoModal"
  [visible]="edit_customer_view_auth_visible"
  backdrop="static"
>
  <c-modal-header>
    <h5 cModalTitle>編輯檢視權限</h5>
    <button
      (click)="toggle_edit_customer_view_auth_modal(0)"
      cButtonClose
    ></button>
  </c-modal-header>
  <c-modal-body>
    <c-row class="mb-4">
      <label cCol cLabel="col" class="mx-4" *ngIf="customer_list.length > 0">
        客戶名稱：{{ customer_list[editing_customer].name }}
      </label>
    </c-row>
    <c-row
      class="mb-4"
      *ngFor="let projectplot of projectplot_list; let i = index"
    >
      <c-col class="mx-4">
        <input
          cFormCheckInput
          id="Sunday"
          type="checkbox"
          [ngModel]="isSelected(editing_customer, projectplot)"
          (ngModelChange)="onDayChange($event, editing_customer, projectplot)"
        />
        <label cFormCheckLabel>{{ projectplot }}</label>
      </c-col>
    </c-row>
    <button
      cButton
      color="primary"
      class="btn btn-primary"
      (click)="edit_customer_view_auth()"
      style="float: right"
      variant="outline"
    >
      送出
    </button>
  </c-modal-body>
</c-modal>

<c-modal
  #verticallyCenteredModal
  alignment="center"
  #scrollableLongContentModal
  id="liveDemoModal"
  [visible]="remove_customer_visible"
  backdrop="static"
>
  <c-modal-header>
    <h5 cModalTitle>移除客戶訪問權限</h5>
    <button (click)="toggle_remove_customer_modal(0)" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <c-row class="mb-4">
      <label cCol cLabel="col" class="mx-4"> 確認移除？ </label>
    </c-row>
    <button
      cButton
      color="danger"
      class="btn btn-danger"
      (click)="remove_customer_auth()"
      style="float: right"
      variant="outline"
    >
      移除
    </button>
  </c-modal-body>
</c-modal>
