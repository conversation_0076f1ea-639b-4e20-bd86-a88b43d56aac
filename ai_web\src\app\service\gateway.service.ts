import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class GatewayService {

  constructor(private http: HttpClient) { }
  get_gateway_list(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_gateway_list`, {project_code: project_code}, { headers });
  }
  get_all_gateway_list(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_all_gateway_list`, {}, { headers });
  }
  get_project_gateway_list(project_code: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_project_gateway_list`, {project_code: project_code}, { headers });
  }
  create_gateway_and_config(project_code: string, place_id: string, object_serialNo: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/create_gateway`, {project_code: project_code, place_id: place_id, object_serialNo: object_serialNo}, { headers });
  }
  remove_gateway(project_code: any, gateway_id: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/remove_gateway`, {gateway_id: gateway_id, project_code: project_code}, { headers });
  }
  load_config(gateway_id: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/load_config`, {gateway_id: gateway_id}, { headers });
  }
  submit_config(config: any, set_remote: boolean): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/submit_config`, {config: config, set_remote: set_remote}, { headers });
  }
  create_SSH(gateway_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/create_SSH`, {gateway_id: gateway_id}, { headers });
  }
  check_connection(gateway_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/check_connection`, {gateway_id: gateway_id}, { headers });
  }
  get_pin_data(gateway_id: string, portName: string, slaveAddr: number, pinAddr: number, pinName: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_pin_data`, {gateway_id: gateway_id, portName: portName, slaveAddr: slaveAddr, pinAddr: pinAddr, pinName: pinName}, { headers });
  }
  get_MACAddress(gateway_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_MACAddress`, {gateway_id: gateway_id}, { headers });
  }
  get_invalid_serial_No(place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_invalid_serial_No`, {place_id: place_id}, { headers });
  }
  get_gateway_config_by_place_id(project_code: string, place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_gateway_config_by_place_id`, {project_code: project_code, place_id: place_id}, { headers });
  }
  get_remote_config(gateway_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_remote_config`, {gateway_id: gateway_id}, { headers });
  }
  get_log_list(gateway_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/get_log_list`, {gateway_id: gateway_id}, { headers });
  }
  update_available(version: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/update_available`, {version: version}, { headers });
  }
  update_gateway(gateway_id: string, version: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/gateway/update_gateway`, {gateway_id: gateway_id, version: version}, { headers });
  }
}
