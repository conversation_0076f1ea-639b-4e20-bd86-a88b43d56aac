import { ProjectService } from './../../../service/project.service';
import { Component } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilSearch } from '@coreui/icons';


interface project {
  project_code: string;
  project_name: string;
  place_id: string[];
}

interface member {
  name: string;
  email: string;
  role: string;
}

@Component({
  selector: 'app-project-edit',
  standalone: true,
  imports: [
    ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,
    IconDirective
  ],
  templateUrl: './project-edit.component.html',
  styleUrl: './project-edit.component.scss',
})
export class ProjectEditComponent {
  icons = { cilSearch };
  project: project;
  project_code: string = '';
  searchMemberText: string = '';
  placeId: string = '';
  errmsg: string = '';
  successMsg: string = '';
  pendingMsg: string = '';
  collab_email: string = '';
  hovering: boolean = false;
  member_list: member[] = [];

  invite_collab_visible: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService
  ) {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }

  load_project_info() {
    this.projectService.get_project_info(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = {
          project_code: data.data.project_code || '',
          project_name: data.data.project_name || '',
          place_id: data.data.place_id || [],
        };
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.project_code =
        this.route.snapshot.paramMap.get('project_code') || '';

      this.get_project_member();
    });
  }

  send_project_collaboration(collabReceiverEmail: string): void {
    this.projectService
      .send_project_collaboration(this.project_code, collabReceiverEmail)
      .subscribe(
        (data) => {
          console.log('發送成功:', data);
          localStorage.setItem('token', data.token);
          this.toggle_invite_collab_modal();
          alert('發送成功');
        },
        (error) => {
          alert('發送失敗');
        }
      );
  }

  get_project_member() {
    this.projectService.get_project_member(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        for (let i = 0; i < data.member_list.length; i++) {
          this.member_list[i] = {
            name: data.member_list[i].name,
            email: data.member_list[i].email,
            role: data.member_list[i].role,
          };
        }
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  toggle_invite_collab_modal() {
    this.invite_collab_visible = !this.invite_collab_visible;
  }
}
