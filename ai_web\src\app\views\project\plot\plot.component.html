<!-- 專案標題與建立按鈕 -->
<c-row class="align-items-center mb-4 px-3">
  <div *ngIf="project">
    <h2
      class="mb-0"
      [routerLink]="['/project', project_code]"
      style="cursor: pointer"
    >
      <strong>{{ project.project_name }} - 專案視圖</strong>
    </h2>
  </div>
</c-row>

<!-- 專案卡片列表 -->
<c-row class="g-4 px-3" *ngIf="project.place_id.length > 0">
  <c-col xs="12" md="6" lg="4" *ngFor="let projectplot of projectplot_list">
    <c-card class="project-card shadow-sm border-0">
      <c-card-body>
        <div>
          <h5 class="fw-bold mb-1">{{ projectplot }}</h5>
          <p class="text-muted mb-2">名稱：{{ projectplot }}</p>
        </div>
        <div class="action-buttons">
          <button
            cButton
            color="primary"
            class="text-white"
            size="sm"
            (click)="edit_projectplot(projectplot)"
          >
            編輯
          </button>
          <button
            cButton
            color="secondary"
            class="text-white"
            size="sm"
            (click)="view_projectplot(projectplot)"
          >
            預覽
          </button>
        </div>
      </c-card-body>
    </c-card>
  </c-col>

  <!-- 新增卡片 -->
  <c-col xs="12" md="6" lg="4" *ngIf="project.place_id.length > 0">
    <c-card
      class="h-100 border border-primary border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center"
      style="cursor: pointer; min-height: 200px"
      (click)="toggle_create_plot_view()"
    >
      <div>
        <p
          class="mt-2 mb-0 text-primary fw-bold"
          *ngIf="projectplot_list.length > 0"
        >
          新增專案視圖
        </p>
        <svg
          *ngIf="projectplot_list.length == 0"
          [cIcon]="icons.cilBaby"
          style="width: 2.5rem; height: 2.5rem; color: var(--cui-primary)"
        ></svg>
        <p
          class="mt-2 mb-0 text-primary fw-bold"
          *ngIf="projectplot_list.length == 0"
        >
          開始規劃吧！
        </p>
      </div>
    </c-card>
  </c-col>
</c-row>

<c-modal id="liveDemoModal" [visible]="visible">
  <c-modal-header>
    <h5 cModalTitle>新增專案視圖</h5>
    <button (click)="toggle_create_plot_view()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <c-row class="mb-3">
      <c-col>
        <label cCol cLabel="col" for="project" class="mx-2"> 名稱 </label>
      </c-col>
      <c-col>
        <input
          cCol
          cLabel="col"
          cFormControl
          id="name"
          type="text"
          value="{{ input_name }}"
          [(ngModel)]="input_name"
        />
      </c-col>
    </c-row>
    <c-row>
      <c-col>
        <label cCol cLabel="col" for="project" class="mx-2">
          *視圖新增後不可改變名稱
        </label>
      </c-col>
    </c-row>
  </c-modal-body>
  <c-modal-footer>
    <button
      cButton
      variant="outline"
      color="primary"
      (click)="add_projectplot()"
    >
      新增
    </button>
  </c-modal-footer>
</c-modal>
<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="project.place_id.length == 0"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未創建電號</h4>
        <p class="text-body-secondary">
          請至<span
          class="fw-bold text-primary"
          style="cursor: pointer"
          [routerLink]="['/project', project.project_code, 'list']"
          >資訊清單</span
        >頁面新增電號開始規劃！

        </p>
      </c-col>
    </c-row>
  </c-container>
</div>
