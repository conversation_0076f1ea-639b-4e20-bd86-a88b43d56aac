import { Injectable } from '@angular/core';
import { HttpClient,HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../environments/environment'

export interface DocumentDemandsForm {
  place_id: string;
  appNumber:  string;
}

@Injectable({
  providedIn: 'root'
})

export class DocumentDemandsService {

  constructor(private http: HttpClient) {}
  sent_bizform(data: DocumentDemandsForm): Observable<any> {
    const token = localStorage.getItem('token');
    return this.http.post<any>(`${environment.apiBaseUrl}/crawler/update_bizform/${token}`, data);
  }
  get_not_acceptance_number_event_info_list(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get(`${environment.apiBaseUrl}/service/get_not_acceptance_number_event_info_list`,{ headers });
  }
  get_requester_info(email:string):Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get(`${environment.apiBaseUrl}/service/get_requester_info/${email}`,{ headers });
  }
  take_case(event_id:string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get(`${environment.apiBaseUrl}/service/take_case/${event_id}`,{ headers });
  }
}
