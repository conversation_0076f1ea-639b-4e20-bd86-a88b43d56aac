import { Routes } from '@angular/router';
import { DefaultLayoutComponent } from './layout';
import { EnterpriseLayoutComponent } from './enterprise/layout';
import { TeamLayoutComponent } from './team/layout';
import { AuthGuard, EnterpriseAuthGuard, TeamAuthGuard } from './views/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full'
  },
  {
    path: '',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard],
    data: {
      title: 'Home'
    },
    children: [
      {
        path: 'admin',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/admin/routes').then((m) => m.routes)
      },
      {
        path: 'home',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/dashboard/routes').then((m) => m.routes)
      },
      {
        path: 'project',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/project/routes').then((m) => m.routes)
      },
      {
        path: 'device',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/device/routes').then((m) => m.routes)
      },
      {
        path: 'inventory',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/inventory/routes').then((m) => m.routes)
      },
      {
        path: 'bill-analysis',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/bill-analysis/routes').then((m) => m.routes)
      },
      {
        path: 'customer_service',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/customer_service/routes').then((m) => m.routes)
      },
      {
        path: 'discover',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/discover/routes').then((m) => m.routes)
      },
      {
        path: 'places',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/places/routes').then((m) => m.routes)
      },
      {
        path: 'groups',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/groups/routes').then((m) => m.routes)
      },
      {
        path: 'terms',
        canActivate: [AuthGuard],
        loadChildren: () => import('./views/terms/routes').then((m) => m.routes)
      },
      {
        path: 'mailbox',
        canActivate: [AuthGuard],
        loadComponent: () => import('./views/mailbox/mailbox.component').then(m => m.MailboxComponent),
        data: {
          title: 'RoleAccess Page'
        }
      },
      {
        path: 'scheduler',
        canActivate: [AuthGuard],
        loadComponent: () => import('./views/scheduler/scheduler.component').then(m => m.SchedulerComponent),
        data: {
          title: 'RoleAccess Page'
        }
      },
      {
        path: 'contact-us',
        canActivate: [AuthGuard],
        loadComponent: () => import('./views/contact-us/contact-us.component').then(m => m.ContactUsComponent),
        data: {
          title: 'RoleAccess Page'
        }
      },
      {
        path: 'documentDemands',
        canActivate: [AuthGuard],
        loadComponent: () => import('./views/documentDemands/documentDemands.component').then((m) => m.DocumentDemandsComponent)
      },
      {
        path: 'settings',
        canActivate: [AuthGuard],
        loadComponent: () => import('./views/settings/settings.component').then((m) => m.SettingsComponent)
      },
    ]
  },
  {
    path: 'enterprise/:taxId',
    component: EnterpriseLayoutComponent,
    canActivate: [EnterpriseAuthGuard],
    data: {
      title: 'Home'
    },
    children: [
      {
        path: 'dashboard',
        canActivate: [EnterpriseAuthGuard],
        loadChildren: () => import('./enterprise/dashboard/routes').then((m) => m.routes)
      },
      {
        path: 'team',
        canActivate: [EnterpriseAuthGuard],
        loadChildren: () => import('./enterprise/team/routes').then((m) => m.routes)
      },
      {
        path: 'settings',
        canActivate: [EnterpriseAuthGuard],
        loadComponent: () => import('./enterprise/settings/settings.component').then((m) => m.SettingsComponent)
      },
    ]
  },
  {
    path: 'team/:team_code',
    component: TeamLayoutComponent,
    canActivate: [TeamAuthGuard],
    data: {
      title: 'Home'
    },
    children: [
      {
        path: 'dashboard',
        canActivate: [TeamAuthGuard],
        loadComponent: () => import('./team/dashboard/dashboard.component').then((m) => m.DashboardComponent)
      },
      {
        path: 'discover',
        canActivate: [TeamAuthGuard],
        loadChildren: () => import('./team/discover/routes').then((m) => m.routes)
      },
      {
        path: 'management',
        canActivate: [TeamAuthGuard],
        loadChildren: () => import('./team/management/routes').then((m) => m.routes)
      },
      {
        path: 'places',
        canActivate: [TeamAuthGuard],
        loadChildren: () => import('./team/places/routes').then((m) => m.routes)
      },
      {
        path: 'project',
        canActivate: [TeamAuthGuard],
        loadChildren: () => import('./team/project/routes').then((m) => m.routes)
      },
      {
        path: 'settings',
        canActivate: [TeamAuthGuard],
        loadComponent: () => import('./team/settings/settings.component').then((m) => m.SettingsComponent)
      },
    ]
  },
  {
    path: 'project/device-dashboard-edit/:project_code/:projectplot_name',
    canActivate: [AuthGuard],
    loadComponent: () => import('./views/project/device-dashboard-edit/device-dashboard-edit.component').then(m => m.DeviceDashboardEditComponent),
    data: {
      title: 'Device-Dashboard-Edit'
    }
  },
  {
    path: 'project/device-dashboard-view/:project_code/:projectplot_name',
    canActivate: [AuthGuard],
    loadComponent: () => import('./views/project/device-dashboard-view/device-dashboard-view.component').then(m => m.DeviceDashboardViewComponent),
    data: {
      title: 'Device-Dashboard-View'
    }
  },
  {
    path: 'register-success',
    loadComponent: () => import('./views/pages/register-success/register-success.component').then(m => m.RegisterSuccessComponent),
    data: {
      title: '註冊成功！'
    }
  },
  {
    path: 'register-failed',
    loadComponent: () => import('./views/pages/register-failed/register-failed.component').then(m => m.RegisterFailedComponent),
    data: {
      title: '註冊失敗！'
    }
  },
  {
    path: 'login',
    loadComponent: () => import('./views/pages/login/login.component').then(m => m.LoginComponent),
    data: {
      title: 'Login Page'
    }
  },
  {
    path: 'register',
    loadComponent: () => import('./views/pages/register/register.component').then(m => m.RegisterComponent),
    data: {
      title: 'Register Page'
    }
  },
  {
    path: 'forget-pwd',
    loadComponent: () => import('./views/pages/forget-pwd/forget-pwd.component').then(m => m.ForgetPwdComponent),
    data: {
      title: 'Forget-password Page'
    }
  },
  {
    path: 'reset-pwd/:token',
    loadComponent: () => import('./views/pages/reset-pwd/reset-pwd.component').then(m => m.ResetPwdComponent),
    data: {
      title: 'Reset-password Page'
    }
  },
  {
    path: '404',
    loadComponent: () => import('./views/pages/page404/page404.component').then(m => m.Page404Component),
    data: {
      title: 'Reset-password Page'
    }
  },
  { path: '**', redirectTo: '404' }
];
