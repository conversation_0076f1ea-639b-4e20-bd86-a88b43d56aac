import { Component } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilHttps } from '@coreui/icons';

@Component({
  selector: 'app-contact-us',
  standalone: true,
  imports: [ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    IconDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,],
  templateUrl: './contact-us.component.html',
  styleUrl: './contact-us.component.scss'
})
export class ContactUsComponent {
  icons = { cilHttps };
}
