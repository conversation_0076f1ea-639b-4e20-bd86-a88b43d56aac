import { UserService } from './../../service/user.service';
import { SharedModule, bank, credit_card, user  } from '../../shared/shared.module';
import { Component } from '@angular/core';
import { RouterLink, Router } from '@angular/router';


@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [RouterLink,  SharedModule],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent  {
    public profile_picture: string = '';
    showCard: string = 'info';
    previewImage: string | null = null;
    selectedFile: File | null = null;
    filetooLarge = false;
    filenotJPG = false;
    visible = false;
    tabs = [
      { key: 'info', label: '個人資訊' },
      { key: 'payment', label: '付款方式' },
      { key: 'payout', label: '收款帳戶' }
    ];

    payoutAccount: bank = {
      name: '',
      bankCode: '',
      branchCode: '',
      accountNumber: ''
    };
    payment_credit_card: credit_card = {
      number: '',
      name: '',
      expMonth: '',
      expYear: '',
      cvv: ''
    };

    user: user = {
      firstname: '',
      lastname: '',
      phone: '',
      email: '',
      profile_picture: undefined,
      role: [],
      notification_method: {
        mail: true,
        web: true
      }
    };
    paymentModalVisible = false;
    payoutModalVisible = false;

    paymentMethod: 'credit' | 'bank' = 'credit';

    creditCard: credit_card = {
      number: '',
      name: '',
      expMonth: '',
      expYear: '',
      cvv: ''
    };

    bankAccount: bank = {
      name: '',
      bankCode: '',
      branchCode: '',
      accountNumber: ''
    };
    confirmDataCorrect: boolean = false;

    // 成員相關屬性
    loadingMembers = false;

    // 模態框相關屬性
    editMode = false;
    save_info_done = false;

    availableRoles = [
      { value: 'admin', label: '管理員' },
    ];
    constructor(private userService: UserService,
      private router: Router
    ) {
      this.get_self_info();
      this.getUserAvatar();

    }

    get_self_info(){
      this.userService.get_self_info().subscribe(
        (data) => {
          this.user = data.user;
          localStorage.setItem('token', data.token);
        },
        (error) => {

        }
      )
    };

    getUserAvatar(){
      this.userService.getUserAvatar().subscribe(
        (blob: Blob) => {
          if (blob) {
            const objectUrl = URL.createObjectURL(blob);
            this.profile_picture = objectUrl;
          }
        },
        (error) => {
          console.error('載入頭像失敗:', error);
        }
      );
    }

    toggleLiveDemo() {
      this.visible = !this.visible;
    }
    onFileSelected(event: any): void {
      const fileInput = event.target as HTMLInputElement;
      if (fileInput.files && fileInput.files[0]) {
        const file = fileInput.files[0];
        const validTypes = ['image/jpg', 'image/jpeg', 'image/png'];

        if (!validTypes.includes(file.type)) {
          this.filenotJPG = true;
          this.filetooLarge = false;
          this.selectedFile = null;
          this.toggleLiveDemo();
        } else if (file.size > 2 * 1024 * 1024) {
          this.filetooLarge = true;
          this.filenotJPG = false;
          this.selectedFile = null;
          this.toggleLiveDemo();
        } else {
          this.filenotJPG = false;
          this.filetooLarge = false;
          this.selectedFile = file;
          const reader = new FileReader();
          reader.onload = () => {
            this.previewImage = reader.result as string;
          };
          reader.readAsDataURL(file);
          const formData = new FormData();
          formData.append('profile_picture', file);
          this.userService.update_user_avatar(formData).subscribe(
            (data) => {
              localStorage.setItem('token', data.token);
              window.location.reload();
            },
            (error) => {

            }
          )
        }
      }

    }


    update_self_settings(): void {
      this.userService.update_self_settings(this.user).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          window.location.reload();
        },
        (error) => {

        }
      )
    }


      setShowCard(card: string) {
        this.showCard = card;
      }


      isFormInvalid(form: 'payment' | 'payout' = 'payment'): boolean {
        if (this.paymentMethod === 'credit' && form !== 'payout') {
          // 簡單信用卡驗證
          const { number, name, expMonth, expYear, cvv } = this.creditCard;
          const ccNumberValid = /^\d{16}$/.test(number.trim());
          const ccNameValid = name.trim().length > 0;
          const expMonthValid = /^\d{1,2}$/.test(expMonth.trim()) && +expMonth >= 1 && +expMonth <= 12;
          const expYearValid = /^\d{4}$/.test(expYear.trim());
          const cvvValid = /^\d{3,4}$/.test(cvv.trim());
          return !(ccNumberValid && ccNameValid && expMonthValid && expYearValid && cvvValid);
        }

        if (this.paymentMethod === 'bank' || form === 'payout') {
          const { name, bankCode, branchCode, accountNumber } = this.bankAccount;
          const accNameValid = name.trim().length > 0;
          const bankCodeValid = /^\d{3}$/.test(bankCode.trim());
          const branchCodeValid = /^\d{1,4}$/.test(branchCode.trim());
          const accountValid = /^\d{5,20}$/.test(accountNumber.trim());
          return !(accNameValid && bankCodeValid && branchCodeValid && accountValid);
        }

        return true; // fallback: invalid
      }


      toggleEditMode() {
        this.editMode = !this.editMode;
      }

      togglePaymentModal() {
        this.creditCard = {
          number: '',
          name: '',
          expMonth: '',
          expYear: '',
          cvv: ''
        };

        this.bankAccount = {
          name: '',
          bankCode: '',
          branchCode: '',
          accountNumber: ''
        };
        this.paymentModalVisible = !this.paymentModalVisible;
        this.confirmDataCorrect = false;
      }

      togglePayoutModal(){
        this.bankAccount = {
          name: '',
          bankCode: '',
          branchCode: '',
          accountNumber: ''
        };
        this.payoutModalVisible = !this.payoutModalVisible;
        this.confirmDataCorrect = false;
      }
      savePayment() {
        this.userService.update_payment(this.paymentMethod, this.creditCard, this.bankAccount).subscribe(
          (data) => {
            localStorage.setItem('token', data.token);
            this.togglePaymentModal();
            window.location.reload();
          },
          (error) => {

          }
        )
      }

      savePayout() {
        this.userService.update_payout(this.bankAccount).subscribe(
          (data) => {
            localStorage.setItem('token', data.token);
            this.togglePayoutModal();
            window.location.reload();
          },
          (error) => {

          }
        )

      }

}
