// Variable overrides
//
// If you want to customize your project please add your variables below.
.joint-selection .handle.rotate {
  display: none;  /* disables the rotate tool */
}
// 必须在导入 CoreUI 的 SCSS 文件之前重写
$primary: #4CAF50;

$enable-deprecation-messages: false !default;
$sidebar-bg: #1E3F20;
$sidebar-nav-link-active-bg: #345830;
$sidebar-nav-link-hover-bg: #4A7856;  /* 修改懸停時的背景色 */
.custom-1 {
  background-color: #1A1F16 !important; /* 設置背景色為綠色 */
  border: 5px solid #1A1F16;
  outline: none !important;
  color: white;                        /* 確保文字顏色為白色 */
}
.custom-2 {
  background-color: #1E3F20 !important; /* 設置背景色為綠色 */
  border: 5px solid #1E3F20;
  outline: none !important;
  color: white;                        /* 確保文字顏色為白色 */
}
.custom-3 {
  background-color: #345830 !important; /* 設置背景色為綠色 */
  border: 5px solid #345830;
  outline: none !important;
  color: white;                        /* 確保文字顏色為白色 */
}
.custom-4 {
  background-color: #4A7856 !important; /* 設置背景色為綠色 */
  border: 5px solid #4A7856;
  outline: none !important;
  color: white;                        /* 確保文字顏色為白色 */
}
.custom-5 {
  background-color: #94ECBE !important; /* 設置背景色為綠色 */
  border: 5px solid #94ECBE;
  outline: none !important;
  color: black;                        /* 確保文字顏色為白色 */
}
:root {
  --custom-1: #1A1F16;
  --custom-2: #1E3F20;
  --custom-3: #345830;
  --custom-4: #4A7856;
  --custom-5: #94ECBE;
}

