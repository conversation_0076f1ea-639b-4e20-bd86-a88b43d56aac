{"version": 3, "sources": ["../../../../../../node_modules/@fullcalendar/interaction/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { config, Emitter, elementClosest, applyStyle, whenTransitionDone, removeElement, ScrollController, ElementScrollController, computeInnerRect, WindowScrollController, ElementDragging, preventSelection, preventContextMenu, allowSelection, allowContextMenu, computeRect, getClippingParents, pointInsideRect, constrainPoint, intersectRects, getRectCenter, diffPoints, mapHash, rangeContainsRange, isDateSpansEqual, Interaction, interactionSettingsToStore, isDateSelectionValid, enableCursor, disableCursor, triggerDateSelect, compareNumbers, getElSeg, getRelevantEvents, EventImpl, createEmptyEventStore, applyMutationToEventStore, isInteractionValid, buildEventApis, interactionSettingsStore, startOfDay, diffDates, createDuration, getEventTargetViaRoot, identity, eventTupleToStore, parseDragMeta, elementMatches, refineEventDef, parseEventDef, getDefaultEventEnd, createEventInstance, BASE_OPTION_DEFAULTS } from '@fullcalendar/core/internal.js';\nconfig.touchMouseIgnoreWait = 500;\nlet ignoreMouseDepth = 0;\nlet listenerCnt = 0;\nlet isWindowTouchMoveCancelled = false;\n/*\nUses a \"pointer\" abstraction, which monitors UI events for both mouse and touch.\nTracks when the pointer \"drags\" on a certain element, meaning down+move+up.\n\nAlso, tracks if there was touch-scrolling.\nAlso, can prevent touch-scrolling from happening.\nAlso, can fire pointermove events when scrolling happens underneath, even when no real pointer movement.\n\nemits:\n- pointerdown\n- pointermove\n- pointerup\n*/\nclass PointerDragging {\n  constructor(containerEl) {\n    this.subjectEl = null;\n    // options that can be directly assigned by caller\n    this.selector = ''; // will cause subjectEl in all emitted events to be this element\n    this.handleSelector = '';\n    this.shouldIgnoreMove = false;\n    this.shouldWatchScroll = true; // for simulating pointermove on scroll\n    // internal states\n    this.isDragging = false;\n    this.isTouchDragging = false;\n    this.wasTouchScroll = false;\n    // Mouse\n    // ----------------------------------------------------------------------------------------------------\n    this.handleMouseDown = ev => {\n      if (!this.shouldIgnoreMouse() && isPrimaryMouseButton(ev) && this.tryStart(ev)) {\n        let pev = this.createEventFromMouse(ev, true);\n        this.emitter.trigger('pointerdown', pev);\n        this.initScrollWatch(pev);\n        if (!this.shouldIgnoreMove) {\n          document.addEventListener('mousemove', this.handleMouseMove);\n        }\n        document.addEventListener('mouseup', this.handleMouseUp);\n      }\n    };\n    this.handleMouseMove = ev => {\n      let pev = this.createEventFromMouse(ev);\n      this.recordCoords(pev);\n      this.emitter.trigger('pointermove', pev);\n    };\n    this.handleMouseUp = ev => {\n      document.removeEventListener('mousemove', this.handleMouseMove);\n      document.removeEventListener('mouseup', this.handleMouseUp);\n      this.emitter.trigger('pointerup', this.createEventFromMouse(ev));\n      this.cleanup(); // call last so that pointerup has access to props\n    };\n    // Touch\n    // ----------------------------------------------------------------------------------------------------\n    this.handleTouchStart = ev => {\n      if (this.tryStart(ev)) {\n        this.isTouchDragging = true;\n        let pev = this.createEventFromTouch(ev, true);\n        this.emitter.trigger('pointerdown', pev);\n        this.initScrollWatch(pev);\n        // unlike mouse, need to attach to target, not document\n        // https://stackoverflow.com/a/45760014\n        let targetEl = ev.target;\n        if (!this.shouldIgnoreMove) {\n          targetEl.addEventListener('touchmove', this.handleTouchMove);\n        }\n        targetEl.addEventListener('touchend', this.handleTouchEnd);\n        targetEl.addEventListener('touchcancel', this.handleTouchEnd); // treat it as a touch end\n        // attach a handler to get called when ANY scroll action happens on the page.\n        // this was impossible to do with normal on/off because 'scroll' doesn't bubble.\n        // http://stackoverflow.com/a/32954565/96342\n        window.addEventListener('scroll', this.handleTouchScroll, true);\n      }\n    };\n    this.handleTouchMove = ev => {\n      let pev = this.createEventFromTouch(ev);\n      this.recordCoords(pev);\n      this.emitter.trigger('pointermove', pev);\n    };\n    this.handleTouchEnd = ev => {\n      if (this.isDragging) {\n        // done to guard against touchend followed by touchcancel\n        let targetEl = ev.target;\n        targetEl.removeEventListener('touchmove', this.handleTouchMove);\n        targetEl.removeEventListener('touchend', this.handleTouchEnd);\n        targetEl.removeEventListener('touchcancel', this.handleTouchEnd);\n        window.removeEventListener('scroll', this.handleTouchScroll, true); // useCaptured=true\n        this.emitter.trigger('pointerup', this.createEventFromTouch(ev));\n        this.cleanup(); // call last so that pointerup has access to props\n        this.isTouchDragging = false;\n        startIgnoringMouse();\n      }\n    };\n    this.handleTouchScroll = () => {\n      this.wasTouchScroll = true;\n    };\n    this.handleScroll = ev => {\n      if (!this.shouldIgnoreMove) {\n        let pageX = window.scrollX - this.prevScrollX + this.prevPageX;\n        let pageY = window.scrollY - this.prevScrollY + this.prevPageY;\n        this.emitter.trigger('pointermove', {\n          origEvent: ev,\n          isTouch: this.isTouchDragging,\n          subjectEl: this.subjectEl,\n          pageX,\n          pageY,\n          deltaX: pageX - this.origPageX,\n          deltaY: pageY - this.origPageY\n        });\n      }\n    };\n    this.containerEl = containerEl;\n    this.emitter = new Emitter();\n    containerEl.addEventListener('mousedown', this.handleMouseDown);\n    containerEl.addEventListener('touchstart', this.handleTouchStart, {\n      passive: true\n    });\n    listenerCreated();\n  }\n  destroy() {\n    this.containerEl.removeEventListener('mousedown', this.handleMouseDown);\n    this.containerEl.removeEventListener('touchstart', this.handleTouchStart, {\n      passive: true\n    });\n    listenerDestroyed();\n  }\n  tryStart(ev) {\n    let subjectEl = this.querySubjectEl(ev);\n    let downEl = ev.target;\n    if (subjectEl && (!this.handleSelector || elementClosest(downEl, this.handleSelector))) {\n      this.subjectEl = subjectEl;\n      this.isDragging = true; // do this first so cancelTouchScroll will work\n      this.wasTouchScroll = false;\n      return true;\n    }\n    return false;\n  }\n  cleanup() {\n    isWindowTouchMoveCancelled = false;\n    this.isDragging = false;\n    this.subjectEl = null;\n    // keep wasTouchScroll around for later access\n    this.destroyScrollWatch();\n  }\n  querySubjectEl(ev) {\n    if (this.selector) {\n      return elementClosest(ev.target, this.selector);\n    }\n    return this.containerEl;\n  }\n  shouldIgnoreMouse() {\n    return ignoreMouseDepth || this.isTouchDragging;\n  }\n  // can be called by user of this class, to cancel touch-based scrolling for the current drag\n  cancelTouchScroll() {\n    if (this.isDragging) {\n      isWindowTouchMoveCancelled = true;\n    }\n  }\n  // Scrolling that simulates pointermoves\n  // ----------------------------------------------------------------------------------------------------\n  initScrollWatch(ev) {\n    if (this.shouldWatchScroll) {\n      this.recordCoords(ev);\n      window.addEventListener('scroll', this.handleScroll, true); // useCapture=true\n    }\n  }\n  recordCoords(ev) {\n    if (this.shouldWatchScroll) {\n      this.prevPageX = ev.pageX;\n      this.prevPageY = ev.pageY;\n      this.prevScrollX = window.scrollX;\n      this.prevScrollY = window.scrollY;\n    }\n  }\n  destroyScrollWatch() {\n    if (this.shouldWatchScroll) {\n      window.removeEventListener('scroll', this.handleScroll, true); // useCaptured=true\n    }\n  }\n  // Event Normalization\n  // ----------------------------------------------------------------------------------------------------\n  createEventFromMouse(ev, isFirst) {\n    let deltaX = 0;\n    let deltaY = 0;\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = ev.pageX;\n      this.origPageY = ev.pageY;\n    } else {\n      deltaX = ev.pageX - this.origPageX;\n      deltaY = ev.pageY - this.origPageY;\n    }\n    return {\n      origEvent: ev,\n      isTouch: false,\n      subjectEl: this.subjectEl,\n      pageX: ev.pageX,\n      pageY: ev.pageY,\n      deltaX,\n      deltaY\n    };\n  }\n  createEventFromTouch(ev, isFirst) {\n    let touches = ev.touches;\n    let pageX;\n    let pageY;\n    let deltaX = 0;\n    let deltaY = 0;\n    // if touch coords available, prefer,\n    // because FF would give bad ev.pageX ev.pageY\n    if (touches && touches.length) {\n      pageX = touches[0].pageX;\n      pageY = touches[0].pageY;\n    } else {\n      pageX = ev.pageX;\n      pageY = ev.pageY;\n    }\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = pageX;\n      this.origPageY = pageY;\n    } else {\n      deltaX = pageX - this.origPageX;\n      deltaY = pageY - this.origPageY;\n    }\n    return {\n      origEvent: ev,\n      isTouch: true,\n      subjectEl: this.subjectEl,\n      pageX,\n      pageY,\n      deltaX,\n      deltaY\n    };\n  }\n}\n// Returns a boolean whether this was a left mouse click and no ctrl key (which means right click on Mac)\nfunction isPrimaryMouseButton(ev) {\n  return ev.button === 0 && !ev.ctrlKey;\n}\n// Ignoring fake mouse events generated by touch\n// ----------------------------------------------------------------------------------------------------\nfunction startIgnoringMouse() {\n  ignoreMouseDepth += 1;\n  setTimeout(() => {\n    ignoreMouseDepth -= 1;\n  }, config.touchMouseIgnoreWait);\n}\n// We want to attach touchmove as early as possible for Safari\n// ----------------------------------------------------------------------------------------------------\nfunction listenerCreated() {\n  listenerCnt += 1;\n  if (listenerCnt === 1) {\n    window.addEventListener('touchmove', onWindowTouchMove, {\n      passive: false\n    });\n  }\n}\nfunction listenerDestroyed() {\n  listenerCnt -= 1;\n  if (!listenerCnt) {\n    window.removeEventListener('touchmove', onWindowTouchMove, {\n      passive: false\n    });\n  }\n}\nfunction onWindowTouchMove(ev) {\n  if (isWindowTouchMoveCancelled) {\n    ev.preventDefault();\n  }\n}\n\n/*\nAn effect in which an element follows the movement of a pointer across the screen.\nThe moving element is a clone of some other element.\nMust call start + handleMove + stop.\n*/\nclass ElementMirror {\n  constructor() {\n    this.isVisible = false; // must be explicitly enabled\n    this.sourceEl = null;\n    this.mirrorEl = null;\n    this.sourceElRect = null; // screen coords relative to viewport\n    // options that can be set directly by caller\n    this.parentNode = document.body; // HIGHLY SUGGESTED to set this to sidestep ShadowDOM issues\n    this.zIndex = 9999;\n    this.revertDuration = 0;\n  }\n  start(sourceEl, pageX, pageY) {\n    this.sourceEl = sourceEl;\n    this.sourceElRect = this.sourceEl.getBoundingClientRect();\n    this.origScreenX = pageX - window.scrollX;\n    this.origScreenY = pageY - window.scrollY;\n    this.deltaX = 0;\n    this.deltaY = 0;\n    this.updateElPosition();\n  }\n  handleMove(pageX, pageY) {\n    this.deltaX = pageX - window.scrollX - this.origScreenX;\n    this.deltaY = pageY - window.scrollY - this.origScreenY;\n    this.updateElPosition();\n  }\n  // can be called before start\n  setIsVisible(bool) {\n    if (bool) {\n      if (!this.isVisible) {\n        if (this.mirrorEl) {\n          this.mirrorEl.style.display = '';\n        }\n        this.isVisible = bool; // needs to happen before updateElPosition\n        this.updateElPosition(); // because was not updating the position while invisible\n      }\n    } else if (this.isVisible) {\n      if (this.mirrorEl) {\n        this.mirrorEl.style.display = 'none';\n      }\n      this.isVisible = bool;\n    }\n  }\n  // always async\n  stop(needsRevertAnimation, callback) {\n    let done = () => {\n      this.cleanup();\n      callback();\n    };\n    if (needsRevertAnimation && this.mirrorEl && this.isVisible && this.revertDuration && (\n    // if 0, transition won't work\n    this.deltaX || this.deltaY) // if same coords, transition won't work\n    ) {\n      this.doRevertAnimation(done, this.revertDuration);\n    } else {\n      setTimeout(done, 0);\n    }\n  }\n  doRevertAnimation(callback, revertDuration) {\n    let mirrorEl = this.mirrorEl;\n    let finalSourceElRect = this.sourceEl.getBoundingClientRect(); // because autoscrolling might have happened\n    mirrorEl.style.transition = 'top ' + revertDuration + 'ms,' + 'left ' + revertDuration + 'ms';\n    applyStyle(mirrorEl, {\n      left: finalSourceElRect.left,\n      top: finalSourceElRect.top\n    });\n    whenTransitionDone(mirrorEl, () => {\n      mirrorEl.style.transition = '';\n      callback();\n    });\n  }\n  cleanup() {\n    if (this.mirrorEl) {\n      removeElement(this.mirrorEl);\n      this.mirrorEl = null;\n    }\n    this.sourceEl = null;\n  }\n  updateElPosition() {\n    if (this.sourceEl && this.isVisible) {\n      applyStyle(this.getMirrorEl(), {\n        left: this.sourceElRect.left + this.deltaX,\n        top: this.sourceElRect.top + this.deltaY\n      });\n    }\n  }\n  getMirrorEl() {\n    let sourceElRect = this.sourceElRect;\n    let mirrorEl = this.mirrorEl;\n    if (!mirrorEl) {\n      mirrorEl = this.mirrorEl = this.sourceEl.cloneNode(true); // cloneChildren=true\n      // we don't want long taps or any mouse interaction causing selection/menus.\n      // would use preventSelection(), but that prevents selectstart, causing problems.\n      mirrorEl.style.userSelect = 'none';\n      mirrorEl.style.webkitUserSelect = 'none';\n      mirrorEl.style.pointerEvents = 'none';\n      mirrorEl.classList.add('fc-event-dragging');\n      applyStyle(mirrorEl, {\n        position: 'fixed',\n        zIndex: this.zIndex,\n        visibility: '',\n        boxSizing: 'border-box',\n        width: sourceElRect.right - sourceElRect.left,\n        height: sourceElRect.bottom - sourceElRect.top,\n        right: 'auto',\n        bottom: 'auto',\n        margin: 0\n      });\n      this.parentNode.appendChild(mirrorEl);\n    }\n    return mirrorEl;\n  }\n}\n\n/*\nIs a cache for a given element's scroll information (all the info that ScrollController stores)\nin addition the \"client rectangle\" of the element.. the area within the scrollbars.\n\nThe cache can be in one of two modes:\n- doesListening:false - ignores when the container is scrolled by someone else\n- doesListening:true - watch for scrolling and update the cache\n*/\nclass ScrollGeomCache extends ScrollController {\n  constructor(scrollController, doesListening) {\n    super();\n    this.handleScroll = () => {\n      this.scrollTop = this.scrollController.getScrollTop();\n      this.scrollLeft = this.scrollController.getScrollLeft();\n      this.handleScrollChange();\n    };\n    this.scrollController = scrollController;\n    this.doesListening = doesListening;\n    this.scrollTop = this.origScrollTop = scrollController.getScrollTop();\n    this.scrollLeft = this.origScrollLeft = scrollController.getScrollLeft();\n    this.scrollWidth = scrollController.getScrollWidth();\n    this.scrollHeight = scrollController.getScrollHeight();\n    this.clientWidth = scrollController.getClientWidth();\n    this.clientHeight = scrollController.getClientHeight();\n    this.clientRect = this.computeClientRect(); // do last in case it needs cached values\n    if (this.doesListening) {\n      this.getEventTarget().addEventListener('scroll', this.handleScroll);\n    }\n  }\n  destroy() {\n    if (this.doesListening) {\n      this.getEventTarget().removeEventListener('scroll', this.handleScroll);\n    }\n  }\n  getScrollTop() {\n    return this.scrollTop;\n  }\n  getScrollLeft() {\n    return this.scrollLeft;\n  }\n  setScrollTop(top) {\n    this.scrollController.setScrollTop(top);\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollTop = Math.max(Math.min(top, this.getMaxScrollTop()), 0);\n      this.handleScrollChange();\n    }\n  }\n  setScrollLeft(top) {\n    this.scrollController.setScrollLeft(top);\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollLeft = Math.max(Math.min(top, this.getMaxScrollLeft()), 0);\n      this.handleScrollChange();\n    }\n  }\n  getClientWidth() {\n    return this.clientWidth;\n  }\n  getClientHeight() {\n    return this.clientHeight;\n  }\n  getScrollWidth() {\n    return this.scrollWidth;\n  }\n  getScrollHeight() {\n    return this.scrollHeight;\n  }\n  handleScrollChange() {}\n}\nclass ElementScrollGeomCache extends ScrollGeomCache {\n  constructor(el, doesListening) {\n    super(new ElementScrollController(el), doesListening);\n  }\n  getEventTarget() {\n    return this.scrollController.el;\n  }\n  computeClientRect() {\n    return computeInnerRect(this.scrollController.el);\n  }\n}\nclass WindowScrollGeomCache extends ScrollGeomCache {\n  constructor(doesListening) {\n    super(new WindowScrollController(), doesListening);\n  }\n  getEventTarget() {\n    return window;\n  }\n  computeClientRect() {\n    return {\n      left: this.scrollLeft,\n      right: this.scrollLeft + this.clientWidth,\n      top: this.scrollTop,\n      bottom: this.scrollTop + this.clientHeight\n    };\n  }\n  // the window is the only scroll object that changes it's rectangle relative\n  // to the document's topleft as it scrolls\n  handleScrollChange() {\n    this.clientRect = this.computeClientRect();\n  }\n}\n\n// If available we are using native \"performance\" API instead of \"Date\"\n// Read more about it on MDN:\n// https://developer.mozilla.org/en-US/docs/Web/API/Performance\nconst getTime = typeof performance === 'function' ? performance.now : Date.now;\n/*\nFor a pointer interaction, automatically scrolls certain scroll containers when the pointer\napproaches the edge.\n\nThe caller must call start + handleMove + stop.\n*/\nclass AutoScroller {\n  constructor() {\n    // options that can be set by caller\n    this.isEnabled = true;\n    this.scrollQuery = [window, '.fc-scroller'];\n    this.edgeThreshold = 50; // pixels\n    this.maxVelocity = 300; // pixels per second\n    // internal state\n    this.pointerScreenX = null;\n    this.pointerScreenY = null;\n    this.isAnimating = false;\n    this.scrollCaches = null;\n    // protect against the initial pointerdown being too close to an edge and starting the scroll\n    this.everMovedUp = false;\n    this.everMovedDown = false;\n    this.everMovedLeft = false;\n    this.everMovedRight = false;\n    this.animate = () => {\n      if (this.isAnimating) {\n        // wasn't cancelled between animation calls\n        let edge = this.computeBestEdge(this.pointerScreenX + window.scrollX, this.pointerScreenY + window.scrollY);\n        if (edge) {\n          let now = getTime();\n          this.handleSide(edge, (now - this.msSinceRequest) / 1000);\n          this.requestAnimation(now);\n        } else {\n          this.isAnimating = false; // will stop animation\n        }\n      }\n    };\n  }\n  start(pageX, pageY, scrollStartEl) {\n    if (this.isEnabled) {\n      this.scrollCaches = this.buildCaches(scrollStartEl);\n      this.pointerScreenX = null;\n      this.pointerScreenY = null;\n      this.everMovedUp = false;\n      this.everMovedDown = false;\n      this.everMovedLeft = false;\n      this.everMovedRight = false;\n      this.handleMove(pageX, pageY);\n    }\n  }\n  handleMove(pageX, pageY) {\n    if (this.isEnabled) {\n      let pointerScreenX = pageX - window.scrollX;\n      let pointerScreenY = pageY - window.scrollY;\n      let yDelta = this.pointerScreenY === null ? 0 : pointerScreenY - this.pointerScreenY;\n      let xDelta = this.pointerScreenX === null ? 0 : pointerScreenX - this.pointerScreenX;\n      if (yDelta < 0) {\n        this.everMovedUp = true;\n      } else if (yDelta > 0) {\n        this.everMovedDown = true;\n      }\n      if (xDelta < 0) {\n        this.everMovedLeft = true;\n      } else if (xDelta > 0) {\n        this.everMovedRight = true;\n      }\n      this.pointerScreenX = pointerScreenX;\n      this.pointerScreenY = pointerScreenY;\n      if (!this.isAnimating) {\n        this.isAnimating = true;\n        this.requestAnimation(getTime());\n      }\n    }\n  }\n  stop() {\n    if (this.isEnabled) {\n      this.isAnimating = false; // will stop animation\n      for (let scrollCache of this.scrollCaches) {\n        scrollCache.destroy();\n      }\n      this.scrollCaches = null;\n    }\n  }\n  requestAnimation(now) {\n    this.msSinceRequest = now;\n    requestAnimationFrame(this.animate);\n  }\n  handleSide(edge, seconds) {\n    let {\n      scrollCache\n    } = edge;\n    let {\n      edgeThreshold\n    } = this;\n    let invDistance = edgeThreshold - edge.distance;\n    let velocity =\n    // the closer to the edge, the faster we scroll\n    invDistance * invDistance / (edgeThreshold * edgeThreshold) *\n    // quadratic\n    this.maxVelocity * seconds;\n    let sign = 1;\n    switch (edge.name) {\n      case 'left':\n        sign = -1;\n      // falls through\n      case 'right':\n        scrollCache.setScrollLeft(scrollCache.getScrollLeft() + velocity * sign);\n        break;\n      case 'top':\n        sign = -1;\n      // falls through\n      case 'bottom':\n        scrollCache.setScrollTop(scrollCache.getScrollTop() + velocity * sign);\n        break;\n    }\n  }\n  // left/top are relative to document topleft\n  computeBestEdge(left, top) {\n    let {\n      edgeThreshold\n    } = this;\n    let bestSide = null;\n    let scrollCaches = this.scrollCaches || [];\n    for (let scrollCache of scrollCaches) {\n      let rect = scrollCache.clientRect;\n      let leftDist = left - rect.left;\n      let rightDist = rect.right - left;\n      let topDist = top - rect.top;\n      let bottomDist = rect.bottom - top;\n      // completely within the rect?\n      if (leftDist >= 0 && rightDist >= 0 && topDist >= 0 && bottomDist >= 0) {\n        if (topDist <= edgeThreshold && this.everMovedUp && scrollCache.canScrollUp() && (!bestSide || bestSide.distance > topDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'top',\n            distance: topDist\n          };\n        }\n        if (bottomDist <= edgeThreshold && this.everMovedDown && scrollCache.canScrollDown() && (!bestSide || bestSide.distance > bottomDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'bottom',\n            distance: bottomDist\n          };\n        }\n        /*\n        TODO: fix broken RTL scrolling. canScrollLeft always returning false\n        https://github.com/fullcalendar/fullcalendar/issues/4837\n        */\n        if (leftDist <= edgeThreshold && this.everMovedLeft && scrollCache.canScrollLeft() && (!bestSide || bestSide.distance > leftDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'left',\n            distance: leftDist\n          };\n        }\n        if (rightDist <= edgeThreshold && this.everMovedRight && scrollCache.canScrollRight() && (!bestSide || bestSide.distance > rightDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'right',\n            distance: rightDist\n          };\n        }\n      }\n    }\n    return bestSide;\n  }\n  buildCaches(scrollStartEl) {\n    return this.queryScrollEls(scrollStartEl).map(el => {\n      if (el === window) {\n        return new WindowScrollGeomCache(false); // false = don't listen to user-generated scrolls\n      }\n      return new ElementScrollGeomCache(el, false); // false = don't listen to user-generated scrolls\n    });\n  }\n  queryScrollEls(scrollStartEl) {\n    let els = [];\n    for (let query of this.scrollQuery) {\n      if (typeof query === 'object') {\n        els.push(query);\n      } else {\n        /*\n        TODO: in the future, always have auto-scroll happen on element where current Hit came from\n        Ticket: https://github.com/fullcalendar/fullcalendar/issues/4593\n        */\n        els.push(...Array.prototype.slice.call(scrollStartEl.getRootNode().querySelectorAll(query)));\n      }\n    }\n    return els;\n  }\n}\n\n/*\nMonitors dragging on an element. Has a number of high-level features:\n- minimum distance required before dragging\n- minimum wait time (\"delay\") before dragging\n- a mirror element that follows the pointer\n*/\nclass FeaturefulElementDragging extends ElementDragging {\n  constructor(containerEl, selector) {\n    super(containerEl);\n    this.containerEl = containerEl;\n    // options that can be directly set by caller\n    // the caller can also set the PointerDragging's options as well\n    this.delay = null;\n    this.minDistance = 0;\n    this.touchScrollAllowed = true; // prevents drag from starting and blocks scrolling during drag\n    this.mirrorNeedsRevert = false;\n    this.isInteracting = false; // is the user validly moving the pointer? lasts until pointerup\n    this.isDragging = false; // is it INTENTFULLY dragging? lasts until after revert animation\n    this.isDelayEnded = false;\n    this.isDistanceSurpassed = false;\n    this.delayTimeoutId = null;\n    this.onPointerDown = ev => {\n      if (!this.isDragging) {\n        // so new drag doesn't happen while revert animation is going\n        this.isInteracting = true;\n        this.isDelayEnded = false;\n        this.isDistanceSurpassed = false;\n        preventSelection(document.body);\n        preventContextMenu(document.body);\n        // prevent links from being visited if there's an eventual drag.\n        // also prevents selection in older browsers (maybe?).\n        // not necessary for touch, besides, browser would complain about passiveness.\n        if (!ev.isTouch) {\n          ev.origEvent.preventDefault();\n        }\n        this.emitter.trigger('pointerdown', ev);\n        if (this.isInteracting &&\n        // not destroyed via pointerdown handler\n        !this.pointer.shouldIgnoreMove) {\n          // actions related to initiating dragstart+dragmove+dragend...\n          this.mirror.setIsVisible(false); // reset. caller must set-visible\n          this.mirror.start(ev.subjectEl, ev.pageX, ev.pageY); // must happen on first pointer down\n          this.startDelay(ev);\n          if (!this.minDistance) {\n            this.handleDistanceSurpassed(ev);\n          }\n        }\n      }\n    };\n    this.onPointerMove = ev => {\n      if (this.isInteracting) {\n        this.emitter.trigger('pointermove', ev);\n        if (!this.isDistanceSurpassed) {\n          let minDistance = this.minDistance;\n          let distanceSq; // current distance from the origin, squared\n          let {\n            deltaX,\n            deltaY\n          } = ev;\n          distanceSq = deltaX * deltaX + deltaY * deltaY;\n          if (distanceSq >= minDistance * minDistance) {\n            // use pythagorean theorem\n            this.handleDistanceSurpassed(ev);\n          }\n        }\n        if (this.isDragging) {\n          // a real pointer move? (not one simulated by scrolling)\n          if (ev.origEvent.type !== 'scroll') {\n            this.mirror.handleMove(ev.pageX, ev.pageY);\n            this.autoScroller.handleMove(ev.pageX, ev.pageY);\n          }\n          this.emitter.trigger('dragmove', ev);\n        }\n      }\n    };\n    this.onPointerUp = ev => {\n      if (this.isInteracting) {\n        this.isInteracting = false;\n        allowSelection(document.body);\n        allowContextMenu(document.body);\n        this.emitter.trigger('pointerup', ev); // can potentially set mirrorNeedsRevert\n        if (this.isDragging) {\n          this.autoScroller.stop();\n          this.tryStopDrag(ev); // which will stop the mirror\n        }\n        if (this.delayTimeoutId) {\n          clearTimeout(this.delayTimeoutId);\n          this.delayTimeoutId = null;\n        }\n      }\n    };\n    let pointer = this.pointer = new PointerDragging(containerEl);\n    pointer.emitter.on('pointerdown', this.onPointerDown);\n    pointer.emitter.on('pointermove', this.onPointerMove);\n    pointer.emitter.on('pointerup', this.onPointerUp);\n    if (selector) {\n      pointer.selector = selector;\n    }\n    this.mirror = new ElementMirror();\n    this.autoScroller = new AutoScroller();\n  }\n  destroy() {\n    this.pointer.destroy();\n    // HACK: simulate a pointer-up to end the current drag\n    // TODO: fire 'dragend' directly and stop interaction. discourage use of pointerup event (b/c might not fire)\n    this.onPointerUp({});\n  }\n  startDelay(ev) {\n    if (typeof this.delay === 'number') {\n      this.delayTimeoutId = setTimeout(() => {\n        this.delayTimeoutId = null;\n        this.handleDelayEnd(ev);\n      }, this.delay); // not assignable to number!\n    } else {\n      this.handleDelayEnd(ev);\n    }\n  }\n  handleDelayEnd(ev) {\n    this.isDelayEnded = true;\n    this.tryStartDrag(ev);\n  }\n  handleDistanceSurpassed(ev) {\n    this.isDistanceSurpassed = true;\n    this.tryStartDrag(ev);\n  }\n  tryStartDrag(ev) {\n    if (this.isDelayEnded && this.isDistanceSurpassed) {\n      if (!this.pointer.wasTouchScroll || this.touchScrollAllowed) {\n        this.isDragging = true;\n        this.mirrorNeedsRevert = false;\n        this.autoScroller.start(ev.pageX, ev.pageY, this.containerEl);\n        this.emitter.trigger('dragstart', ev);\n        if (this.touchScrollAllowed === false) {\n          this.pointer.cancelTouchScroll();\n        }\n      }\n    }\n  }\n  tryStopDrag(ev) {\n    // .stop() is ALWAYS asynchronous, which we NEED because we want all pointerup events\n    // that come from the document to fire beforehand. much more convenient this way.\n    this.mirror.stop(this.mirrorNeedsRevert, this.stopDrag.bind(this, ev));\n  }\n  stopDrag(ev) {\n    this.isDragging = false;\n    this.emitter.trigger('dragend', ev);\n  }\n  // fill in the implementations...\n  setIgnoreMove(bool) {\n    this.pointer.shouldIgnoreMove = bool;\n  }\n  setMirrorIsVisible(bool) {\n    this.mirror.setIsVisible(bool);\n  }\n  setMirrorNeedsRevert(bool) {\n    this.mirrorNeedsRevert = bool;\n  }\n  setAutoScrollEnabled(bool) {\n    this.autoScroller.isEnabled = bool;\n  }\n}\n\n/*\nWhen this class is instantiated, it records the offset of an element (relative to the document topleft),\nand continues to monitor scrolling, updating the cached coordinates if it needs to.\nDoes not access the DOM after instantiation, so highly performant.\n\nAlso keeps track of all scrolling/overflow:hidden containers that are parents of the given element\nand an determine if a given point is inside the combined clipping rectangle.\n*/\nclass OffsetTracker {\n  constructor(el) {\n    this.el = el;\n    this.origRect = computeRect(el);\n    // will work fine for divs that have overflow:hidden\n    this.scrollCaches = getClippingParents(el).map(scrollEl => new ElementScrollGeomCache(scrollEl, true));\n  }\n  destroy() {\n    for (let scrollCache of this.scrollCaches) {\n      scrollCache.destroy();\n    }\n  }\n  computeLeft() {\n    let left = this.origRect.left;\n    for (let scrollCache of this.scrollCaches) {\n      left += scrollCache.origScrollLeft - scrollCache.getScrollLeft();\n    }\n    return left;\n  }\n  computeTop() {\n    let top = this.origRect.top;\n    for (let scrollCache of this.scrollCaches) {\n      top += scrollCache.origScrollTop - scrollCache.getScrollTop();\n    }\n    return top;\n  }\n  isWithinClipping(pageX, pageY) {\n    let point = {\n      left: pageX,\n      top: pageY\n    };\n    for (let scrollCache of this.scrollCaches) {\n      if (!isIgnoredClipping(scrollCache.getEventTarget()) && !pointInsideRect(point, scrollCache.clientRect)) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n// certain clipping containers should never constrain interactions, like <html> and <body>\n// https://github.com/fullcalendar/fullcalendar/issues/3615\nfunction isIgnoredClipping(node) {\n  let tagName = node.tagName;\n  return tagName === 'HTML' || tagName === 'BODY';\n}\n\n/*\nTracks movement over multiple droppable areas (aka \"hits\")\nthat exist in one or more DateComponents.\nRelies on an existing draggable.\n\nemits:\n- pointerdown\n- dragstart\n- hitchange - fires initially, even if not over a hit\n- pointerup\n- (hitchange - again, to null, if ended over a hit)\n- dragend\n*/\nclass HitDragging {\n  constructor(dragging, droppableStore) {\n    // options that can be set by caller\n    this.useSubjectCenter = false;\n    this.requireInitial = true; // if doesn't start out on a hit, won't emit any events\n    this.disablePointCheck = false;\n    this.initialHit = null;\n    this.movingHit = null;\n    this.finalHit = null; // won't ever be populated if shouldIgnoreMove\n    this.handlePointerDown = ev => {\n      let {\n        dragging\n      } = this;\n      this.initialHit = null;\n      this.movingHit = null;\n      this.finalHit = null;\n      this.prepareHits();\n      this.processFirstCoord(ev);\n      if (this.initialHit || !this.requireInitial) {\n        dragging.setIgnoreMove(false);\n        // TODO: fire this before computing processFirstCoord, so listeners can cancel. this gets fired by almost every handler :(\n        this.emitter.trigger('pointerdown', ev);\n      } else {\n        dragging.setIgnoreMove(true);\n      }\n    };\n    this.handleDragStart = ev => {\n      this.emitter.trigger('dragstart', ev);\n      this.handleMove(ev, true); // force = fire even if initially null\n    };\n    this.handleDragMove = ev => {\n      this.emitter.trigger('dragmove', ev);\n      this.handleMove(ev);\n    };\n    this.handlePointerUp = ev => {\n      this.releaseHits();\n      this.emitter.trigger('pointerup', ev);\n    };\n    this.handleDragEnd = ev => {\n      if (this.movingHit) {\n        this.emitter.trigger('hitupdate', null, true, ev);\n      }\n      this.finalHit = this.movingHit;\n      this.movingHit = null;\n      this.emitter.trigger('dragend', ev);\n    };\n    this.droppableStore = droppableStore;\n    dragging.emitter.on('pointerdown', this.handlePointerDown);\n    dragging.emitter.on('dragstart', this.handleDragStart);\n    dragging.emitter.on('dragmove', this.handleDragMove);\n    dragging.emitter.on('pointerup', this.handlePointerUp);\n    dragging.emitter.on('dragend', this.handleDragEnd);\n    this.dragging = dragging;\n    this.emitter = new Emitter();\n  }\n  // sets initialHit\n  // sets coordAdjust\n  processFirstCoord(ev) {\n    let origPoint = {\n      left: ev.pageX,\n      top: ev.pageY\n    };\n    let adjustedPoint = origPoint;\n    let subjectEl = ev.subjectEl;\n    let subjectRect;\n    if (subjectEl instanceof HTMLElement) {\n      // i.e. not a Document/ShadowRoot\n      subjectRect = computeRect(subjectEl);\n      adjustedPoint = constrainPoint(adjustedPoint, subjectRect);\n    }\n    let initialHit = this.initialHit = this.queryHitForOffset(adjustedPoint.left, adjustedPoint.top);\n    if (initialHit) {\n      if (this.useSubjectCenter && subjectRect) {\n        let slicedSubjectRect = intersectRects(subjectRect, initialHit.rect);\n        if (slicedSubjectRect) {\n          adjustedPoint = getRectCenter(slicedSubjectRect);\n        }\n      }\n      this.coordAdjust = diffPoints(adjustedPoint, origPoint);\n    } else {\n      this.coordAdjust = {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n  handleMove(ev, forceHandle) {\n    let hit = this.queryHitForOffset(ev.pageX + this.coordAdjust.left, ev.pageY + this.coordAdjust.top);\n    if (forceHandle || !isHitsEqual(this.movingHit, hit)) {\n      this.movingHit = hit;\n      this.emitter.trigger('hitupdate', hit, false, ev);\n    }\n  }\n  prepareHits() {\n    this.offsetTrackers = mapHash(this.droppableStore, interactionSettings => {\n      interactionSettings.component.prepareHits();\n      return new OffsetTracker(interactionSettings.el);\n    });\n  }\n  releaseHits() {\n    let {\n      offsetTrackers\n    } = this;\n    for (let id in offsetTrackers) {\n      offsetTrackers[id].destroy();\n    }\n    this.offsetTrackers = {};\n  }\n  queryHitForOffset(offsetLeft, offsetTop) {\n    let {\n      droppableStore,\n      offsetTrackers\n    } = this;\n    let bestHit = null;\n    for (let id in droppableStore) {\n      let component = droppableStore[id].component;\n      let offsetTracker = offsetTrackers[id];\n      if (offsetTracker &&\n      // wasn't destroyed mid-drag\n      offsetTracker.isWithinClipping(offsetLeft, offsetTop)) {\n        let originLeft = offsetTracker.computeLeft();\n        let originTop = offsetTracker.computeTop();\n        let positionLeft = offsetLeft - originLeft;\n        let positionTop = offsetTop - originTop;\n        let {\n          origRect\n        } = offsetTracker;\n        let width = origRect.right - origRect.left;\n        let height = origRect.bottom - origRect.top;\n        if (\n        // must be within the element's bounds\n        positionLeft >= 0 && positionLeft < width && positionTop >= 0 && positionTop < height) {\n          let hit = component.queryHit(positionLeft, positionTop, width, height);\n          if (hit &&\n          // make sure the hit is within activeRange, meaning it's not a dead cell\n          rangeContainsRange(hit.dateProfile.activeRange, hit.dateSpan.range) && (\n          // Ensure the component we are querying for the hit is accessibly my the pointer\n          // Prevents obscured calendars (ex: under a modal dialog) from accepting hit\n          // https://github.com/fullcalendar/fullcalendar/issues/5026\n          this.disablePointCheck || offsetTracker.el.contains(offsetTracker.el.getRootNode().elementFromPoint(\n          // add-back origins to get coordinate relative to top-left of window viewport\n          positionLeft + originLeft - window.scrollX, positionTop + originTop - window.scrollY))) && (!bestHit || hit.layer > bestHit.layer)) {\n            hit.componentId = id;\n            hit.context = component.context;\n            // TODO: better way to re-orient rectangle\n            hit.rect.left += originLeft;\n            hit.rect.right += originLeft;\n            hit.rect.top += originTop;\n            hit.rect.bottom += originTop;\n            bestHit = hit;\n          }\n        }\n      }\n    }\n    return bestHit;\n  }\n}\nfunction isHitsEqual(hit0, hit1) {\n  if (!hit0 && !hit1) {\n    return true;\n  }\n  if (Boolean(hit0) !== Boolean(hit1)) {\n    return false;\n  }\n  return isDateSpansEqual(hit0.dateSpan, hit1.dateSpan);\n}\nfunction buildDatePointApiWithContext(dateSpan, context) {\n  let props = {};\n  for (let transform of context.pluginHooks.datePointTransforms) {\n    Object.assign(props, transform(dateSpan, context));\n  }\n  Object.assign(props, buildDatePointApi(dateSpan, context.dateEnv));\n  return props;\n}\nfunction buildDatePointApi(span, dateEnv) {\n  return {\n    date: dateEnv.toDate(span.range.start),\n    dateStr: dateEnv.formatIso(span.range.start, {\n      omitTime: span.allDay\n    }),\n    allDay: span.allDay\n  };\n}\n\n/*\nMonitors when the user clicks on a specific date/time of a component.\nA pointerdown+pointerup on the same \"hit\" constitutes a click.\n*/\nclass DateClicking extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.handlePointerDown = pev => {\n      let {\n        dragging\n      } = this;\n      let downEl = pev.origEvent.target;\n      // do this in pointerdown (not dragend) because DOM might be mutated by the time dragend is fired\n      dragging.setIgnoreMove(!this.component.isValidDateDownEl(downEl));\n    };\n    // won't even fire if moving was ignored\n    this.handleDragEnd = ev => {\n      let {\n        component\n      } = this;\n      let {\n        pointer\n      } = this.dragging;\n      if (!pointer.wasTouchScroll) {\n        let {\n          initialHit,\n          finalHit\n        } = this.hitDragging;\n        if (initialHit && finalHit && isHitsEqual(initialHit, finalHit)) {\n          let {\n            context\n          } = component;\n          let arg = Object.assign(Object.assign({}, buildDatePointApiWithContext(initialHit.dateSpan, context)), {\n            dayEl: initialHit.dayEl,\n            jsEvent: ev.origEvent,\n            view: context.viewApi || context.calendarApi.view\n          });\n          context.emitter.trigger('dateClick', arg);\n        }\n      }\n    };\n    // we DO want to watch pointer moves because otherwise finalHit won't get populated\n    this.dragging = new FeaturefulElementDragging(settings.el);\n    this.dragging.autoScroller.isEnabled = false;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\n\n/*\nTracks when the user selects a portion of time of a component,\nconstituted by a drag over date cells, with a possible delay at the beginning of the drag.\n*/\nclass DateSelecting extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.dragSelection = null;\n    this.handlePointerDown = ev => {\n      let {\n        component,\n        dragging\n      } = this;\n      let {\n        options\n      } = component.context;\n      let canSelect = options.selectable && component.isValidDateDownEl(ev.origEvent.target);\n      // don't bother to watch expensive moves if component won't do selection\n      dragging.setIgnoreMove(!canSelect);\n      // if touch, require user to hold down\n      dragging.delay = ev.isTouch ? getComponentTouchDelay$1(component) : null;\n    };\n    this.handleDragStart = ev => {\n      this.component.context.calendarApi.unselect(ev); // unselect previous selections\n    };\n    this.handleHitUpdate = (hit, isFinal) => {\n      let {\n        context\n      } = this.component;\n      let dragSelection = null;\n      let isInvalid = false;\n      if (hit) {\n        let initialHit = this.hitDragging.initialHit;\n        let disallowed = hit.componentId === initialHit.componentId && this.isHitComboAllowed && !this.isHitComboAllowed(initialHit, hit);\n        if (!disallowed) {\n          dragSelection = joinHitsIntoSelection(initialHit, hit, context.pluginHooks.dateSelectionTransformers);\n        }\n        if (!dragSelection || !isDateSelectionValid(dragSelection, hit.dateProfile, context)) {\n          isInvalid = true;\n          dragSelection = null;\n        }\n      }\n      if (dragSelection) {\n        context.dispatch({\n          type: 'SELECT_DATES',\n          selection: dragSelection\n        });\n      } else if (!isFinal) {\n        // only unselect if moved away while dragging\n        context.dispatch({\n          type: 'UNSELECT_DATES'\n        });\n      }\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        this.dragSelection = dragSelection; // only clear if moved away from all hits while dragging\n      }\n    };\n    this.handlePointerUp = pev => {\n      if (this.dragSelection) {\n        // selection is already rendered, so just need to report selection\n        triggerDateSelect(this.dragSelection, pev, this.component.context);\n        this.dragSelection = null;\n      }\n    };\n    let {\n      component\n    } = settings;\n    let {\n      options\n    } = component.context;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.touchScrollAllowed = false;\n    dragging.minDistance = options.selectMinDistance || 0;\n    dragging.autoScroller.isEnabled = options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('pointerup', this.handlePointerUp);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\nfunction getComponentTouchDelay$1(component) {\n  let {\n    options\n  } = component.context;\n  let delay = options.selectLongPressDelay;\n  if (delay == null) {\n    delay = options.longPressDelay;\n  }\n  return delay;\n}\nfunction joinHitsIntoSelection(hit0, hit1, dateSelectionTransformers) {\n  let dateSpan0 = hit0.dateSpan;\n  let dateSpan1 = hit1.dateSpan;\n  let ms = [dateSpan0.range.start, dateSpan0.range.end, dateSpan1.range.start, dateSpan1.range.end];\n  ms.sort(compareNumbers);\n  let props = {};\n  for (let transformer of dateSelectionTransformers) {\n    let res = transformer(hit0, hit1);\n    if (res === false) {\n      return null;\n    }\n    if (res) {\n      Object.assign(props, res);\n    }\n  }\n  props.range = {\n    start: ms[0],\n    end: ms[3]\n  };\n  props.allDay = dateSpan0.allDay;\n  return props;\n}\nclass EventDragging extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // internal state\n    this.subjectEl = null;\n    this.subjectSeg = null; // the seg being selected/dragged\n    this.isDragging = false;\n    this.eventRange = null;\n    this.relevantEvents = null; // the events being dragged\n    this.receivingContext = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n    this.handlePointerDown = ev => {\n      let origTarget = ev.origEvent.target;\n      let {\n        component,\n        dragging\n      } = this;\n      let {\n        mirror\n      } = dragging;\n      let {\n        options\n      } = component.context;\n      let initialContext = component.context;\n      this.subjectEl = ev.subjectEl;\n      let subjectSeg = this.subjectSeg = getElSeg(ev.subjectEl);\n      let eventRange = this.eventRange = subjectSeg.eventRange;\n      let eventInstanceId = eventRange.instance.instanceId;\n      this.relevantEvents = getRelevantEvents(initialContext.getCurrentData().eventStore, eventInstanceId);\n      dragging.minDistance = ev.isTouch ? 0 : options.eventDragMinDistance;\n      dragging.delay =\n      // only do a touch delay if touch and this event hasn't been selected yet\n      ev.isTouch && eventInstanceId !== component.props.eventSelection ? getComponentTouchDelay(component) : null;\n      if (options.fixedMirrorParent) {\n        mirror.parentNode = options.fixedMirrorParent;\n      } else {\n        mirror.parentNode = elementClosest(origTarget, '.fc');\n      }\n      mirror.revertDuration = options.dragRevertDuration;\n      let isValid = component.isValidSegDownEl(origTarget) && !elementClosest(origTarget, '.fc-event-resizer'); // NOT on a resizer\n      dragging.setIgnoreMove(!isValid);\n      // disable dragging for elements that are resizable (ie, selectable)\n      // but are not draggable\n      this.isDragging = isValid && ev.subjectEl.classList.contains('fc-event-draggable');\n    };\n    this.handleDragStart = ev => {\n      let initialContext = this.component.context;\n      let eventRange = this.eventRange;\n      let eventInstanceId = eventRange.instance.instanceId;\n      if (ev.isTouch) {\n        // need to select a different event?\n        if (eventInstanceId !== this.component.props.eventSelection) {\n          initialContext.dispatch({\n            type: 'SELECT_EVENT',\n            eventInstanceId\n          });\n        }\n      } else {\n        // if now using mouse, but was previous touch interaction, clear selected event\n        initialContext.dispatch({\n          type: 'UNSELECT_EVENT'\n        });\n      }\n      if (this.isDragging) {\n        initialContext.calendarApi.unselect(ev); // unselect *date* selection\n        initialContext.emitter.trigger('eventDragStart', {\n          el: this.subjectEl,\n          event: new EventImpl(initialContext, eventRange.def, eventRange.instance),\n          jsEvent: ev.origEvent,\n          view: initialContext.viewApi\n        });\n      }\n    };\n    this.handleHitUpdate = (hit, isFinal) => {\n      if (!this.isDragging) {\n        return;\n      }\n      let relevantEvents = this.relevantEvents;\n      let initialHit = this.hitDragging.initialHit;\n      let initialContext = this.component.context;\n      // states based on new hit\n      let receivingContext = null;\n      let mutation = null;\n      let mutatedRelevantEvents = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: relevantEvents,\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: true\n      };\n      if (hit) {\n        receivingContext = hit.context;\n        let receivingOptions = receivingContext.options;\n        if (initialContext === receivingContext || receivingOptions.editable && receivingOptions.droppable) {\n          mutation = computeEventMutation(initialHit, hit, this.eventRange.instance.range.start, receivingContext.getCurrentData().pluginHooks.eventDragMutationMassagers);\n          if (mutation) {\n            mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, receivingContext.getCurrentData().eventUiBases, mutation, receivingContext);\n            interaction.mutatedEvents = mutatedRelevantEvents;\n            if (!isInteractionValid(interaction, hit.dateProfile, receivingContext)) {\n              isInvalid = true;\n              mutation = null;\n              mutatedRelevantEvents = null;\n              interaction.mutatedEvents = createEmptyEventStore();\n            }\n          }\n        } else {\n          receivingContext = null;\n        }\n      }\n      this.displayDrag(receivingContext, interaction);\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        if (initialContext === receivingContext &&\n        // TODO: write test for this\n        isHitsEqual(initialHit, hit)) {\n          mutation = null;\n        }\n        this.dragging.setMirrorNeedsRevert(!mutation);\n        // render the mirror if no already-rendered mirror\n        // TODO: wish we could somehow wait for dispatch to guarantee render\n        this.dragging.setMirrorIsVisible(!hit || !this.subjectEl.getRootNode().querySelector('.fc-event-mirror'));\n        // assign states based on new hit\n        this.receivingContext = receivingContext;\n        this.validMutation = mutation;\n        this.mutatedRelevantEvents = mutatedRelevantEvents;\n      }\n    };\n    this.handlePointerUp = () => {\n      if (!this.isDragging) {\n        this.cleanup(); // because handleDragEnd won't fire\n      }\n    };\n    this.handleDragEnd = ev => {\n      if (this.isDragging) {\n        let initialContext = this.component.context;\n        let initialView = initialContext.viewApi;\n        let {\n          receivingContext,\n          validMutation\n        } = this;\n        let eventDef = this.eventRange.def;\n        let eventInstance = this.eventRange.instance;\n        let eventApi = new EventImpl(initialContext, eventDef, eventInstance);\n        let relevantEvents = this.relevantEvents;\n        let mutatedRelevantEvents = this.mutatedRelevantEvents;\n        let {\n          finalHit\n        } = this.hitDragging;\n        this.clearDrag(); // must happen after revert animation\n        initialContext.emitter.trigger('eventDragStop', {\n          el: this.subjectEl,\n          event: eventApi,\n          jsEvent: ev.origEvent,\n          view: initialView\n        });\n        if (validMutation) {\n          // dropped within same calendar\n          if (receivingContext === initialContext) {\n            let updatedEventApi = new EventImpl(initialContext, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n            initialContext.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: mutatedRelevantEvents\n            });\n            let eventChangeArg = {\n              oldEvent: eventApi,\n              event: updatedEventApi,\n              relatedEvents: buildEventApis(mutatedRelevantEvents, initialContext, eventInstance),\n              revert() {\n                initialContext.dispatch({\n                  type: 'MERGE_EVENTS',\n                  eventStore: relevantEvents // the pre-change data\n                });\n              }\n            };\n            let transformed = {};\n            for (let transformer of initialContext.getCurrentData().pluginHooks.eventDropTransformers) {\n              Object.assign(transformed, transformer(validMutation, initialContext));\n            }\n            initialContext.emitter.trigger('eventDrop', Object.assign(Object.assign(Object.assign({}, eventChangeArg), transformed), {\n              el: ev.subjectEl,\n              delta: validMutation.datesDelta,\n              jsEvent: ev.origEvent,\n              view: initialView\n            }));\n            initialContext.emitter.trigger('eventChange', eventChangeArg);\n            // dropped in different calendar\n          } else if (receivingContext) {\n            let eventRemoveArg = {\n              event: eventApi,\n              relatedEvents: buildEventApis(relevantEvents, initialContext, eventInstance),\n              revert() {\n                initialContext.dispatch({\n                  type: 'MERGE_EVENTS',\n                  eventStore: relevantEvents\n                });\n              }\n            };\n            initialContext.emitter.trigger('eventLeave', Object.assign(Object.assign({}, eventRemoveArg), {\n              draggedEl: ev.subjectEl,\n              view: initialView\n            }));\n            initialContext.dispatch({\n              type: 'REMOVE_EVENTS',\n              eventStore: relevantEvents\n            });\n            initialContext.emitter.trigger('eventRemove', eventRemoveArg);\n            let addedEventDef = mutatedRelevantEvents.defs[eventDef.defId];\n            let addedEventInstance = mutatedRelevantEvents.instances[eventInstance.instanceId];\n            let addedEventApi = new EventImpl(receivingContext, addedEventDef, addedEventInstance);\n            receivingContext.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: mutatedRelevantEvents\n            });\n            let eventAddArg = {\n              event: addedEventApi,\n              relatedEvents: buildEventApis(mutatedRelevantEvents, receivingContext, addedEventInstance),\n              revert() {\n                receivingContext.dispatch({\n                  type: 'REMOVE_EVENTS',\n                  eventStore: mutatedRelevantEvents\n                });\n              }\n            };\n            receivingContext.emitter.trigger('eventAdd', eventAddArg);\n            if (ev.isTouch) {\n              receivingContext.dispatch({\n                type: 'SELECT_EVENT',\n                eventInstanceId: eventInstance.instanceId\n              });\n            }\n            receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), {\n              draggedEl: ev.subjectEl,\n              jsEvent: ev.origEvent,\n              view: finalHit.context.viewApi\n            }));\n            receivingContext.emitter.trigger('eventReceive', Object.assign(Object.assign({}, eventAddArg), {\n              draggedEl: ev.subjectEl,\n              view: finalHit.context.viewApi\n            }));\n          }\n        } else {\n          initialContext.emitter.trigger('_noEventDrop');\n        }\n      }\n      this.cleanup();\n    };\n    let {\n      component\n    } = this;\n    let {\n      options\n    } = component.context;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.pointer.selector = EventDragging.SELECTOR;\n    dragging.touchScrollAllowed = false;\n    dragging.autoScroller.isEnabled = options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsStore);\n    hitDragging.useSubjectCenter = settings.useEventCenter;\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('pointerup', this.handlePointerUp);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n  // render a drag state on the next receivingCalendar\n  displayDrag(nextContext, state) {\n    let initialContext = this.component.context;\n    let prevContext = this.receivingContext;\n    // does the previous calendar need to be cleared?\n    if (prevContext && prevContext !== nextContext) {\n      // does the initial calendar need to be cleared?\n      // if so, don't clear all the way. we still need to to hide the affectedEvents\n      if (prevContext === initialContext) {\n        prevContext.dispatch({\n          type: 'SET_EVENT_DRAG',\n          state: {\n            affectedEvents: state.affectedEvents,\n            mutatedEvents: createEmptyEventStore(),\n            isEvent: true\n          }\n        });\n        // completely clear the old calendar if it wasn't the initial\n      } else {\n        prevContext.dispatch({\n          type: 'UNSET_EVENT_DRAG'\n        });\n      }\n    }\n    if (nextContext) {\n      nextContext.dispatch({\n        type: 'SET_EVENT_DRAG',\n        state\n      });\n    }\n  }\n  clearDrag() {\n    let initialCalendar = this.component.context;\n    let {\n      receivingContext\n    } = this;\n    if (receivingContext) {\n      receivingContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n    // the initial calendar might have an dummy drag state from displayDrag\n    if (initialCalendar !== receivingContext) {\n      initialCalendar.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n  }\n  cleanup() {\n    this.subjectSeg = null;\n    this.isDragging = false;\n    this.eventRange = null;\n    this.relevantEvents = null;\n    this.receivingContext = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n  }\n}\n// TODO: test this in IE11\n// QUESTION: why do we need it on the resizable???\nEventDragging.SELECTOR = '.fc-event-draggable, .fc-event-resizable';\nfunction computeEventMutation(hit0, hit1, eventInstanceStart, massagers) {\n  let dateSpan0 = hit0.dateSpan;\n  let dateSpan1 = hit1.dateSpan;\n  let date0 = dateSpan0.range.start;\n  let date1 = dateSpan1.range.start;\n  let standardProps = {};\n  if (dateSpan0.allDay !== dateSpan1.allDay) {\n    standardProps.allDay = dateSpan1.allDay;\n    standardProps.hasEnd = hit1.context.options.allDayMaintainDuration;\n    if (dateSpan1.allDay) {\n      // means date1 is already start-of-day,\n      // but date0 needs to be converted\n      date0 = startOfDay(eventInstanceStart);\n    } else {\n      // Moving from allDate->timed\n      // Doesn't matter where on the event the drag began, mutate the event's start-date to date1\n      date0 = eventInstanceStart;\n    }\n  }\n  let delta = diffDates(date0, date1, hit0.context.dateEnv, hit0.componentId === hit1.componentId ? hit0.largeUnit : null);\n  if (delta.milliseconds) {\n    // has hours/minutes/seconds\n    standardProps.allDay = false;\n  }\n  let mutation = {\n    datesDelta: delta,\n    standardProps\n  };\n  for (let massager of massagers) {\n    massager(mutation, hit0, hit1);\n  }\n  return mutation;\n}\nfunction getComponentTouchDelay(component) {\n  let {\n    options\n  } = component.context;\n  let delay = options.eventLongPressDelay;\n  if (delay == null) {\n    delay = options.longPressDelay;\n  }\n  return delay;\n}\nclass EventResizing extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // internal state\n    this.draggingSegEl = null;\n    this.draggingSeg = null; // TODO: rename to resizingSeg? subjectSeg?\n    this.eventRange = null;\n    this.relevantEvents = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n    this.handlePointerDown = ev => {\n      let {\n        component\n      } = this;\n      let segEl = this.querySegEl(ev);\n      let seg = getElSeg(segEl);\n      let eventRange = this.eventRange = seg.eventRange;\n      this.dragging.minDistance = component.context.options.eventDragMinDistance;\n      // if touch, need to be working with a selected event\n      this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(ev.origEvent.target) || ev.isTouch && this.component.props.eventSelection !== eventRange.instance.instanceId);\n    };\n    this.handleDragStart = ev => {\n      let {\n        context\n      } = this.component;\n      let eventRange = this.eventRange;\n      this.relevantEvents = getRelevantEvents(context.getCurrentData().eventStore, this.eventRange.instance.instanceId);\n      let segEl = this.querySegEl(ev);\n      this.draggingSegEl = segEl;\n      this.draggingSeg = getElSeg(segEl);\n      context.calendarApi.unselect();\n      context.emitter.trigger('eventResizeStart', {\n        el: segEl,\n        event: new EventImpl(context, eventRange.def, eventRange.instance),\n        jsEvent: ev.origEvent,\n        view: context.viewApi\n      });\n    };\n    this.handleHitUpdate = (hit, isFinal, ev) => {\n      let {\n        context\n      } = this.component;\n      let relevantEvents = this.relevantEvents;\n      let initialHit = this.hitDragging.initialHit;\n      let eventInstance = this.eventRange.instance;\n      let mutation = null;\n      let mutatedRelevantEvents = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: relevantEvents,\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: true\n      };\n      if (hit) {\n        let disallowed = hit.componentId === initialHit.componentId && this.isHitComboAllowed && !this.isHitComboAllowed(initialHit, hit);\n        if (!disallowed) {\n          mutation = computeMutation(initialHit, hit, ev.subjectEl.classList.contains('fc-event-resizer-start'), eventInstance.range);\n        }\n      }\n      if (mutation) {\n        mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, context.getCurrentData().eventUiBases, mutation, context);\n        interaction.mutatedEvents = mutatedRelevantEvents;\n        if (!isInteractionValid(interaction, hit.dateProfile, context)) {\n          isInvalid = true;\n          mutation = null;\n          mutatedRelevantEvents = null;\n          interaction.mutatedEvents = null;\n        }\n      }\n      if (mutatedRelevantEvents) {\n        context.dispatch({\n          type: 'SET_EVENT_RESIZE',\n          state: interaction\n        });\n      } else {\n        context.dispatch({\n          type: 'UNSET_EVENT_RESIZE'\n        });\n      }\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        if (mutation && isHitsEqual(initialHit, hit)) {\n          mutation = null;\n        }\n        this.validMutation = mutation;\n        this.mutatedRelevantEvents = mutatedRelevantEvents;\n      }\n    };\n    this.handleDragEnd = ev => {\n      let {\n        context\n      } = this.component;\n      let eventDef = this.eventRange.def;\n      let eventInstance = this.eventRange.instance;\n      let eventApi = new EventImpl(context, eventDef, eventInstance);\n      let relevantEvents = this.relevantEvents;\n      let mutatedRelevantEvents = this.mutatedRelevantEvents;\n      context.emitter.trigger('eventResizeStop', {\n        el: this.draggingSegEl,\n        event: eventApi,\n        jsEvent: ev.origEvent,\n        view: context.viewApi\n      });\n      if (this.validMutation) {\n        let updatedEventApi = new EventImpl(context, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n        context.dispatch({\n          type: 'MERGE_EVENTS',\n          eventStore: mutatedRelevantEvents\n        });\n        let eventChangeArg = {\n          oldEvent: eventApi,\n          event: updatedEventApi,\n          relatedEvents: buildEventApis(mutatedRelevantEvents, context, eventInstance),\n          revert() {\n            context.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: relevantEvents // the pre-change events\n            });\n          }\n        };\n        context.emitter.trigger('eventResize', Object.assign(Object.assign({}, eventChangeArg), {\n          el: this.draggingSegEl,\n          startDelta: this.validMutation.startDelta || createDuration(0),\n          endDelta: this.validMutation.endDelta || createDuration(0),\n          jsEvent: ev.origEvent,\n          view: context.viewApi\n        }));\n        context.emitter.trigger('eventChange', eventChangeArg);\n      } else {\n        context.emitter.trigger('_noEventResize');\n      }\n      // reset all internal state\n      this.draggingSeg = null;\n      this.relevantEvents = null;\n      this.validMutation = null;\n      // okay to keep eventInstance around. useful to set it in handlePointerDown\n    };\n    let {\n      component\n    } = settings;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.pointer.selector = '.fc-event-resizer';\n    dragging.touchScrollAllowed = false;\n    dragging.autoScroller.isEnabled = component.context.options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n  querySegEl(ev) {\n    return elementClosest(ev.subjectEl, '.fc-event');\n  }\n}\nfunction computeMutation(hit0, hit1, isFromStart, instanceRange) {\n  let dateEnv = hit0.context.dateEnv;\n  let date0 = hit0.dateSpan.range.start;\n  let date1 = hit1.dateSpan.range.start;\n  let delta = diffDates(date0, date1, dateEnv, hit0.largeUnit);\n  if (isFromStart) {\n    if (dateEnv.add(instanceRange.start, delta) < instanceRange.end) {\n      return {\n        startDelta: delta\n      };\n    }\n  } else if (dateEnv.add(instanceRange.end, delta) > instanceRange.start) {\n    return {\n      endDelta: delta\n    };\n  }\n  return null;\n}\nclass UnselectAuto {\n  constructor(context) {\n    this.context = context;\n    this.isRecentPointerDateSelect = false; // wish we could use a selector to detect date selection, but uses hit system\n    this.matchesCancel = false;\n    this.matchesEvent = false;\n    this.onSelect = selectInfo => {\n      if (selectInfo.jsEvent) {\n        this.isRecentPointerDateSelect = true;\n      }\n    };\n    this.onDocumentPointerDown = pev => {\n      let unselectCancel = this.context.options.unselectCancel;\n      let downEl = getEventTargetViaRoot(pev.origEvent);\n      this.matchesCancel = !!elementClosest(downEl, unselectCancel);\n      this.matchesEvent = !!elementClosest(downEl, EventDragging.SELECTOR); // interaction started on an event?\n    };\n    this.onDocumentPointerUp = pev => {\n      let {\n        context\n      } = this;\n      let {\n        documentPointer\n      } = this;\n      let calendarState = context.getCurrentData();\n      // touch-scrolling should never unfocus any type of selection\n      if (!documentPointer.wasTouchScroll) {\n        if (calendarState.dateSelection &&\n        // an existing date selection?\n        !this.isRecentPointerDateSelect // a new pointer-initiated date selection since last onDocumentPointerUp?\n        ) {\n          let unselectAuto = context.options.unselectAuto;\n          if (unselectAuto && (!unselectAuto || !this.matchesCancel)) {\n            context.calendarApi.unselect(pev);\n          }\n        }\n        if (calendarState.eventSelection &&\n        // an existing event selected?\n        !this.matchesEvent // interaction DIDN'T start on an event\n        ) {\n          context.dispatch({\n            type: 'UNSELECT_EVENT'\n          });\n        }\n      }\n      this.isRecentPointerDateSelect = false;\n    };\n    let documentPointer = this.documentPointer = new PointerDragging(document);\n    documentPointer.shouldIgnoreMove = true;\n    documentPointer.shouldWatchScroll = false;\n    documentPointer.emitter.on('pointerdown', this.onDocumentPointerDown);\n    documentPointer.emitter.on('pointerup', this.onDocumentPointerUp);\n    /*\n    TODO: better way to know about whether there was a selection with the pointer\n    */\n    context.emitter.on('select', this.onSelect);\n  }\n  destroy() {\n    this.context.emitter.off('select', this.onSelect);\n    this.documentPointer.destroy();\n  }\n}\nconst OPTION_REFINERS = {\n  fixedMirrorParent: identity\n};\nconst LISTENER_REFINERS = {\n  dateClick: identity,\n  eventDragStart: identity,\n  eventDragStop: identity,\n  eventDrop: identity,\n  eventResizeStart: identity,\n  eventResizeStop: identity,\n  eventResize: identity,\n  drop: identity,\n  eventReceive: identity,\n  eventLeave: identity\n};\n\n/*\nGiven an already instantiated draggable object for one-or-more elements,\nInterprets any dragging as an attempt to drag an events that lives outside\nof a calendar onto a calendar.\n*/\nclass ExternalElementDragging {\n  constructor(dragging, suppliedDragMeta) {\n    this.receivingContext = null;\n    this.droppableEvent = null; // will exist for all drags, even if create:false\n    this.suppliedDragMeta = null;\n    this.dragMeta = null;\n    this.handleDragStart = ev => {\n      this.dragMeta = this.buildDragMeta(ev.subjectEl);\n    };\n    this.handleHitUpdate = (hit, isFinal, ev) => {\n      let {\n        dragging\n      } = this.hitDragging;\n      let receivingContext = null;\n      let droppableEvent = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: createEmptyEventStore(),\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: this.dragMeta.create\n      };\n      if (hit) {\n        receivingContext = hit.context;\n        if (this.canDropElOnCalendar(ev.subjectEl, receivingContext)) {\n          droppableEvent = computeEventForDateSpan(hit.dateSpan, this.dragMeta, receivingContext);\n          interaction.mutatedEvents = eventTupleToStore(droppableEvent);\n          isInvalid = !isInteractionValid(interaction, hit.dateProfile, receivingContext);\n          if (isInvalid) {\n            interaction.mutatedEvents = createEmptyEventStore();\n            droppableEvent = null;\n          }\n        }\n      }\n      this.displayDrag(receivingContext, interaction);\n      // show mirror if no already-rendered mirror element OR if we are shutting down the mirror (?)\n      // TODO: wish we could somehow wait for dispatch to guarantee render\n      dragging.setMirrorIsVisible(isFinal || !droppableEvent || !document.querySelector('.fc-event-mirror'));\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        dragging.setMirrorNeedsRevert(!droppableEvent);\n        this.receivingContext = receivingContext;\n        this.droppableEvent = droppableEvent;\n      }\n    };\n    this.handleDragEnd = pev => {\n      let {\n        receivingContext,\n        droppableEvent\n      } = this;\n      this.clearDrag();\n      if (receivingContext && droppableEvent) {\n        let finalHit = this.hitDragging.finalHit;\n        let finalView = finalHit.context.viewApi;\n        let dragMeta = this.dragMeta;\n        receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), {\n          draggedEl: pev.subjectEl,\n          jsEvent: pev.origEvent,\n          view: finalView\n        }));\n        if (dragMeta.create) {\n          let addingEvents = eventTupleToStore(droppableEvent);\n          receivingContext.dispatch({\n            type: 'MERGE_EVENTS',\n            eventStore: addingEvents\n          });\n          if (pev.isTouch) {\n            receivingContext.dispatch({\n              type: 'SELECT_EVENT',\n              eventInstanceId: droppableEvent.instance.instanceId\n            });\n          }\n          // signal that an external event landed\n          receivingContext.emitter.trigger('eventReceive', {\n            event: new EventImpl(receivingContext, droppableEvent.def, droppableEvent.instance),\n            relatedEvents: [],\n            revert() {\n              receivingContext.dispatch({\n                type: 'REMOVE_EVENTS',\n                eventStore: addingEvents\n              });\n            },\n            draggedEl: pev.subjectEl,\n            view: finalView\n          });\n        }\n      }\n      this.receivingContext = null;\n      this.droppableEvent = null;\n    };\n    let hitDragging = this.hitDragging = new HitDragging(dragging, interactionSettingsStore);\n    hitDragging.requireInitial = false; // will start outside of a component\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n    this.suppliedDragMeta = suppliedDragMeta;\n  }\n  buildDragMeta(subjectEl) {\n    if (typeof this.suppliedDragMeta === 'object') {\n      return parseDragMeta(this.suppliedDragMeta);\n    }\n    if (typeof this.suppliedDragMeta === 'function') {\n      return parseDragMeta(this.suppliedDragMeta(subjectEl));\n    }\n    return getDragMetaFromEl(subjectEl);\n  }\n  displayDrag(nextContext, state) {\n    let prevContext = this.receivingContext;\n    if (prevContext && prevContext !== nextContext) {\n      prevContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n    if (nextContext) {\n      nextContext.dispatch({\n        type: 'SET_EVENT_DRAG',\n        state\n      });\n    }\n  }\n  clearDrag() {\n    if (this.receivingContext) {\n      this.receivingContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n  }\n  canDropElOnCalendar(el, receivingContext) {\n    let dropAccept = receivingContext.options.dropAccept;\n    if (typeof dropAccept === 'function') {\n      return dropAccept.call(receivingContext.calendarApi, el);\n    }\n    if (typeof dropAccept === 'string' && dropAccept) {\n      return Boolean(elementMatches(el, dropAccept));\n    }\n    return true;\n  }\n}\n// Utils for computing event store from the DragMeta\n// ----------------------------------------------------------------------------------------------------\nfunction computeEventForDateSpan(dateSpan, dragMeta, context) {\n  let defProps = Object.assign({}, dragMeta.leftoverProps);\n  for (let transform of context.pluginHooks.externalDefTransforms) {\n    Object.assign(defProps, transform(dateSpan, dragMeta));\n  }\n  let {\n    refined,\n    extra\n  } = refineEventDef(defProps, context);\n  let def = parseEventDef(refined, extra, dragMeta.sourceId, dateSpan.allDay, context.options.forceEventDuration || Boolean(dragMeta.duration),\n  // hasEnd\n  context);\n  let start = dateSpan.range.start;\n  // only rely on time info if drop zone is all-day,\n  // otherwise, we already know the time\n  if (dateSpan.allDay && dragMeta.startTime) {\n    start = context.dateEnv.add(start, dragMeta.startTime);\n  }\n  let end = dragMeta.duration ? context.dateEnv.add(start, dragMeta.duration) : getDefaultEventEnd(dateSpan.allDay, start, context);\n  let instance = createEventInstance(def.defId, {\n    start,\n    end\n  });\n  return {\n    def,\n    instance\n  };\n}\n// Utils for extracting data from element\n// ----------------------------------------------------------------------------------------------------\nfunction getDragMetaFromEl(el) {\n  let str = getEmbeddedElData(el, 'event');\n  let obj = str ? JSON.parse(str) : {\n    create: false\n  }; // if no embedded data, assume no event creation\n  return parseDragMeta(obj);\n}\nconfig.dataAttrPrefix = '';\nfunction getEmbeddedElData(el, name) {\n  let prefix = config.dataAttrPrefix;\n  let prefixedName = (prefix ? prefix + '-' : '') + name;\n  return el.getAttribute('data-' + prefixedName) || '';\n}\n\n/*\nMakes an element (that is *external* to any calendar) draggable.\nCan pass in data that determines how an event will be created when dropped onto a calendar.\nLeverages FullCalendar's internal drag-n-drop functionality WITHOUT a third-party drag system.\n*/\nclass ExternalDraggable {\n  constructor(el, settings = {}) {\n    this.handlePointerDown = ev => {\n      let {\n        dragging\n      } = this;\n      let {\n        minDistance,\n        longPressDelay\n      } = this.settings;\n      dragging.minDistance = minDistance != null ? minDistance : ev.isTouch ? 0 : BASE_OPTION_DEFAULTS.eventDragMinDistance;\n      dragging.delay = ev.isTouch ?\n      // TODO: eventually read eventLongPressDelay instead vvv\n      longPressDelay != null ? longPressDelay : BASE_OPTION_DEFAULTS.longPressDelay : 0;\n    };\n    this.handleDragStart = ev => {\n      if (ev.isTouch && this.dragging.delay && ev.subjectEl.classList.contains('fc-event')) {\n        this.dragging.mirror.getMirrorEl().classList.add('fc-event-selected');\n      }\n    };\n    this.settings = settings;\n    let dragging = this.dragging = new FeaturefulElementDragging(el);\n    dragging.touchScrollAllowed = false;\n    if (settings.itemSelector != null) {\n      dragging.pointer.selector = settings.itemSelector;\n    }\n    if (settings.appendTo != null) {\n      dragging.mirror.parentNode = settings.appendTo; // TODO: write tests\n    }\n    dragging.emitter.on('pointerdown', this.handlePointerDown);\n    dragging.emitter.on('dragstart', this.handleDragStart);\n    new ExternalElementDragging(dragging, settings.eventData); // eslint-disable-line no-new\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\n\n/*\nDetects when a *THIRD-PARTY* drag-n-drop system interacts with elements.\nThe third-party system is responsible for drawing the visuals effects of the drag.\nThis class simply monitors for pointer movements and fires events.\nIt also has the ability to hide the moving element (the \"mirror\") during the drag.\n*/\nclass InferredElementDragging extends ElementDragging {\n  constructor(containerEl) {\n    super(containerEl);\n    this.shouldIgnoreMove = false;\n    this.mirrorSelector = '';\n    this.currentMirrorEl = null;\n    this.handlePointerDown = ev => {\n      this.emitter.trigger('pointerdown', ev);\n      if (!this.shouldIgnoreMove) {\n        // fire dragstart right away. does not support delay or min-distance\n        this.emitter.trigger('dragstart', ev);\n      }\n    };\n    this.handlePointerMove = ev => {\n      if (!this.shouldIgnoreMove) {\n        this.emitter.trigger('dragmove', ev);\n      }\n    };\n    this.handlePointerUp = ev => {\n      this.emitter.trigger('pointerup', ev);\n      if (!this.shouldIgnoreMove) {\n        // fire dragend right away. does not support a revert animation\n        this.emitter.trigger('dragend', ev);\n      }\n    };\n    let pointer = this.pointer = new PointerDragging(containerEl);\n    pointer.emitter.on('pointerdown', this.handlePointerDown);\n    pointer.emitter.on('pointermove', this.handlePointerMove);\n    pointer.emitter.on('pointerup', this.handlePointerUp);\n  }\n  destroy() {\n    this.pointer.destroy();\n  }\n  setIgnoreMove(bool) {\n    this.shouldIgnoreMove = bool;\n  }\n  setMirrorIsVisible(bool) {\n    if (bool) {\n      // restore a previously hidden element.\n      // use the reference in case the selector class has already been removed.\n      if (this.currentMirrorEl) {\n        this.currentMirrorEl.style.visibility = '';\n        this.currentMirrorEl = null;\n      }\n    } else {\n      let mirrorEl = this.mirrorSelector\n      // TODO: somehow query FullCalendars WITHIN shadow-roots\n      ? document.querySelector(this.mirrorSelector) : null;\n      if (mirrorEl) {\n        this.currentMirrorEl = mirrorEl;\n        mirrorEl.style.visibility = 'hidden';\n      }\n    }\n  }\n}\n\n/*\nBridges third-party drag-n-drop systems with FullCalendar.\nMust be instantiated and destroyed by caller.\n*/\nclass ThirdPartyDraggable {\n  constructor(containerOrSettings, settings) {\n    let containerEl = document;\n    if (\n    // wish we could just test instanceof EventTarget, but doesn't work in IE11\n    containerOrSettings === document || containerOrSettings instanceof Element) {\n      containerEl = containerOrSettings;\n      settings = settings || {};\n    } else {\n      settings = containerOrSettings || {};\n    }\n    let dragging = this.dragging = new InferredElementDragging(containerEl);\n    if (typeof settings.itemSelector === 'string') {\n      dragging.pointer.selector = settings.itemSelector;\n    } else if (containerEl === document) {\n      dragging.pointer.selector = '[data-event]';\n    }\n    if (typeof settings.mirrorSelector === 'string') {\n      dragging.mirrorSelector = settings.mirrorSelector;\n    }\n    let externalDragging = new ExternalElementDragging(dragging, settings.eventData);\n    // The hit-detection system requires that the dnd-mirror-element be pointer-events:none,\n    // but this can't be guaranteed for third-party draggables, so disable\n    externalDragging.hitDragging.disablePointCheck = true;\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\nvar index = createPlugin({\n  name: '@fullcalendar/interaction',\n  componentInteractions: [DateClicking, DateSelecting, EventDragging, EventResizing],\n  calendarInteractions: [UnselectAuto],\n  elementDraggingImpl: FeaturefulElementDragging,\n  optionRefiners: OPTION_REFINERS,\n  listenerRefiners: LISTENER_REFINERS\n});\nexport { ExternalDraggable as Draggable, ThirdPartyDraggable, index as default };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,OAAO,uBAAuB;AAC9B,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,6BAA6B;AAcjC,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,aAAa;AACvB,SAAK,YAAY;AAEjB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AAEzB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAGtB,SAAK,kBAAkB,QAAM;AAC3B,UAAI,CAAC,KAAK,kBAAkB,KAAK,qBAAqB,EAAE,KAAK,KAAK,SAAS,EAAE,GAAG;AAC9E,YAAI,MAAM,KAAK,qBAAqB,IAAI,IAAI;AAC5C,aAAK,QAAQ,QAAQ,eAAe,GAAG;AACvC,aAAK,gBAAgB,GAAG;AACxB,YAAI,CAAC,KAAK,kBAAkB;AAC1B,mBAAS,iBAAiB,aAAa,KAAK,eAAe;AAAA,QAC7D;AACA,iBAAS,iBAAiB,WAAW,KAAK,aAAa;AAAA,MACzD;AAAA,IACF;AACA,SAAK,kBAAkB,QAAM;AAC3B,UAAI,MAAM,KAAK,qBAAqB,EAAE;AACtC,WAAK,aAAa,GAAG;AACrB,WAAK,QAAQ,QAAQ,eAAe,GAAG;AAAA,IACzC;AACA,SAAK,gBAAgB,QAAM;AACzB,eAAS,oBAAoB,aAAa,KAAK,eAAe;AAC9D,eAAS,oBAAoB,WAAW,KAAK,aAAa;AAC1D,WAAK,QAAQ,QAAQ,aAAa,KAAK,qBAAqB,EAAE,CAAC;AAC/D,WAAK,QAAQ;AAAA,IACf;AAGA,SAAK,mBAAmB,QAAM;AAC5B,UAAI,KAAK,SAAS,EAAE,GAAG;AACrB,aAAK,kBAAkB;AACvB,YAAI,MAAM,KAAK,qBAAqB,IAAI,IAAI;AAC5C,aAAK,QAAQ,QAAQ,eAAe,GAAG;AACvC,aAAK,gBAAgB,GAAG;AAGxB,YAAI,WAAW,GAAG;AAClB,YAAI,CAAC,KAAK,kBAAkB;AAC1B,mBAAS,iBAAiB,aAAa,KAAK,eAAe;AAAA,QAC7D;AACA,iBAAS,iBAAiB,YAAY,KAAK,cAAc;AACzD,iBAAS,iBAAiB,eAAe,KAAK,cAAc;AAI5D,eAAO,iBAAiB,UAAU,KAAK,mBAAmB,IAAI;AAAA,MAChE;AAAA,IACF;AACA,SAAK,kBAAkB,QAAM;AAC3B,UAAI,MAAM,KAAK,qBAAqB,EAAE;AACtC,WAAK,aAAa,GAAG;AACrB,WAAK,QAAQ,QAAQ,eAAe,GAAG;AAAA,IACzC;AACA,SAAK,iBAAiB,QAAM;AAC1B,UAAI,KAAK,YAAY;AAEnB,YAAI,WAAW,GAAG;AAClB,iBAAS,oBAAoB,aAAa,KAAK,eAAe;AAC9D,iBAAS,oBAAoB,YAAY,KAAK,cAAc;AAC5D,iBAAS,oBAAoB,eAAe,KAAK,cAAc;AAC/D,eAAO,oBAAoB,UAAU,KAAK,mBAAmB,IAAI;AACjE,aAAK,QAAQ,QAAQ,aAAa,KAAK,qBAAqB,EAAE,CAAC;AAC/D,aAAK,QAAQ;AACb,aAAK,kBAAkB;AACvB,2BAAmB;AAAA,MACrB;AAAA,IACF;AACA,SAAK,oBAAoB,MAAM;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,eAAe,QAAM;AACxB,UAAI,CAAC,KAAK,kBAAkB;AAC1B,YAAI,QAAQ,OAAO,UAAU,KAAK,cAAc,KAAK;AACrD,YAAI,QAAQ,OAAO,UAAU,KAAK,cAAc,KAAK;AACrD,aAAK,QAAQ,QAAQ,eAAe;AAAA,UAClC,WAAW;AAAA,UACX,SAAS,KAAK;AAAA,UACd,WAAW,KAAK;AAAA,UAChB;AAAA,UACA;AAAA,UACA,QAAQ,QAAQ,KAAK;AAAA,UACrB,QAAQ,QAAQ,KAAK;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,cAAc;AACnB,SAAK,UAAU,IAAI,QAAQ;AAC3B,gBAAY,iBAAiB,aAAa,KAAK,eAAe;AAC9D,gBAAY,iBAAiB,cAAc,KAAK,kBAAkB;AAAA,MAChE,SAAS;AAAA,IACX,CAAC;AACD,oBAAgB;AAAA,EAClB;AAAA,EACA,UAAU;AACR,SAAK,YAAY,oBAAoB,aAAa,KAAK,eAAe;AACtE,SAAK,YAAY,oBAAoB,cAAc,KAAK,kBAAkB;AAAA,MACxE,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB;AAAA,EACpB;AAAA,EACA,SAAS,IAAI;AACX,QAAI,YAAY,KAAK,eAAe,EAAE;AACtC,QAAI,SAAS,GAAG;AAChB,QAAI,cAAc,CAAC,KAAK,kBAAkB,eAAe,QAAQ,KAAK,cAAc,IAAI;AACtF,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,iCAA6B;AAC7B,SAAK,aAAa;AAClB,SAAK,YAAY;AAEjB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,eAAe,IAAI;AACjB,QAAI,KAAK,UAAU;AACjB,aAAO,eAAe,GAAG,QAAQ,KAAK,QAAQ;AAAA,IAChD;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,WAAO,oBAAoB,KAAK;AAAA,EAClC;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,YAAY;AACnB,mCAA6B;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,gBAAgB,IAAI;AAClB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,aAAa,EAAE;AACpB,aAAO,iBAAiB,UAAU,KAAK,cAAc,IAAI;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,aAAa,IAAI;AACf,QAAI,KAAK,mBAAmB;AAC1B,WAAK,YAAY,GAAG;AACpB,WAAK,YAAY,GAAG;AACpB,WAAK,cAAc,OAAO;AAC1B,WAAK,cAAc,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,mBAAmB;AAC1B,aAAO,oBAAoB,UAAU,KAAK,cAAc,IAAI;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,qBAAqB,IAAI,SAAS;AAChC,QAAI,SAAS;AACb,QAAI,SAAS;AAEb,QAAI,SAAS;AACX,WAAK,YAAY,GAAG;AACpB,WAAK,YAAY,GAAG;AAAA,IACtB,OAAO;AACL,eAAS,GAAG,QAAQ,KAAK;AACzB,eAAS,GAAG,QAAQ,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB,OAAO,GAAG;AAAA,MACV,OAAO,GAAG;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB,IAAI,SAAS;AAChC,QAAI,UAAU,GAAG;AACjB,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACb,QAAI,SAAS;AAGb,QAAI,WAAW,QAAQ,QAAQ;AAC7B,cAAQ,QAAQ,CAAC,EAAE;AACnB,cAAQ,QAAQ,CAAC,EAAE;AAAA,IACrB,OAAO;AACL,cAAQ,GAAG;AACX,cAAQ,GAAG;AAAA,IACb;AAEA,QAAI,SAAS;AACX,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,eAAS,QAAQ,KAAK;AACtB,eAAS,QAAQ,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,IAAI;AAChC,SAAO,GAAG,WAAW,KAAK,CAAC,GAAG;AAChC;AAGA,SAAS,qBAAqB;AAC5B,sBAAoB;AACpB,aAAW,MAAM;AACf,wBAAoB;AAAA,EACtB,GAAG,OAAO,oBAAoB;AAChC;AAGA,SAAS,kBAAkB;AACzB,iBAAe;AACf,MAAI,gBAAgB,GAAG;AACrB,WAAO,iBAAiB,aAAa,mBAAmB;AAAA,MACtD,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACF;AACA,SAAS,oBAAoB;AAC3B,iBAAe;AACf,MAAI,CAAC,aAAa;AAChB,WAAO,oBAAoB,aAAa,mBAAmB;AAAA,MACzD,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACF;AACA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,4BAA4B;AAC9B,OAAG,eAAe;AAAA,EACpB;AACF;AAOA,IAAM,gBAAN,MAAoB;AAAA,EAClB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,eAAe;AAEpB,SAAK,aAAa,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,MAAM,UAAU,OAAO,OAAO;AAC5B,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK,SAAS,sBAAsB;AACxD,SAAK,cAAc,QAAQ,OAAO;AAClC,SAAK,cAAc,QAAQ,OAAO;AAClC,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW,OAAO,OAAO;AACvB,SAAK,SAAS,QAAQ,OAAO,UAAU,KAAK;AAC5C,SAAK,SAAS,QAAQ,OAAO,UAAU,KAAK;AAC5C,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,QAAI,MAAM;AACR,UAAI,CAAC,KAAK,WAAW;AACnB,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS,MAAM,UAAU;AAAA,QAChC;AACA,aAAK,YAAY;AACjB,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF,WAAW,KAAK,WAAW;AACzB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,MAAM,UAAU;AAAA,MAChC;AACA,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAEA,KAAK,sBAAsB,UAAU;AACnC,QAAI,OAAO,MAAM;AACf,WAAK,QAAQ;AACb,eAAS;AAAA,IACX;AACA,QAAI,wBAAwB,KAAK,YAAY,KAAK,aAAa,KAAK;AAAA,KAEpE,KAAK,UAAU,KAAK,SAClB;AACA,WAAK,kBAAkB,MAAM,KAAK,cAAc;AAAA,IAClD,OAAO;AACL,iBAAW,MAAM,CAAC;AAAA,IACpB;AAAA,EACF;AAAA,EACA,kBAAkB,UAAU,gBAAgB;AAC1C,QAAI,WAAW,KAAK;AACpB,QAAI,oBAAoB,KAAK,SAAS,sBAAsB;AAC5D,aAAS,MAAM,aAAa,SAAS,iBAAiB,aAAkB,iBAAiB;AACzF,eAAW,UAAU;AAAA,MACnB,MAAM,kBAAkB;AAAA,MACxB,KAAK,kBAAkB;AAAA,IACzB,CAAC;AACD,uBAAmB,UAAU,MAAM;AACjC,eAAS,MAAM,aAAa;AAC5B,eAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,QAAQ;AAC3B,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,YAAY,KAAK,WAAW;AACnC,iBAAW,KAAK,YAAY,GAAG;AAAA,QAC7B,MAAM,KAAK,aAAa,OAAO,KAAK;AAAA,QACpC,KAAK,KAAK,aAAa,MAAM,KAAK;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,eAAe,KAAK;AACxB,QAAI,WAAW,KAAK;AACpB,QAAI,CAAC,UAAU;AACb,iBAAW,KAAK,WAAW,KAAK,SAAS,UAAU,IAAI;AAGvD,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,mBAAmB;AAClC,eAAS,MAAM,gBAAgB;AAC/B,eAAS,UAAU,IAAI,mBAAmB;AAC1C,iBAAW,UAAU;AAAA,QACnB,UAAU;AAAA,QACV,QAAQ,KAAK;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,OAAO,aAAa,QAAQ,aAAa;AAAA,QACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,QAC3C,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC;AACD,WAAK,WAAW,YAAY,QAAQ;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AACF;AAUA,IAAM,kBAAN,cAA8B,iBAAiB;AAAA,EAC7C,YAAY,kBAAkB,eAAe;AAC3C,UAAM;AACN,SAAK,eAAe,MAAM;AACxB,WAAK,YAAY,KAAK,iBAAiB,aAAa;AACpD,WAAK,aAAa,KAAK,iBAAiB,cAAc;AACtD,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,YAAY,KAAK,gBAAgB,iBAAiB,aAAa;AACpE,SAAK,aAAa,KAAK,iBAAiB,iBAAiB,cAAc;AACvE,SAAK,cAAc,iBAAiB,eAAe;AACnD,SAAK,eAAe,iBAAiB,gBAAgB;AACrD,SAAK,cAAc,iBAAiB,eAAe;AACnD,SAAK,eAAe,iBAAiB,gBAAgB;AACrD,SAAK,aAAa,KAAK,kBAAkB;AACzC,QAAI,KAAK,eAAe;AACtB,WAAK,eAAe,EAAE,iBAAiB,UAAU,KAAK,YAAY;AAAA,IACpE;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,KAAK,eAAe;AACtB,WAAK,eAAe,EAAE,oBAAoB,UAAU,KAAK,YAAY;AAAA,IACvE;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,KAAK;AAChB,SAAK,iBAAiB,aAAa,GAAG;AACtC,QAAI,CAAC,KAAK,eAAe;AAGvB,WAAK,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,gBAAgB,CAAC,GAAG,CAAC;AAClE,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,cAAc,KAAK;AACjB,SAAK,iBAAiB,cAAc,GAAG;AACvC,QAAI,CAAC,KAAK,eAAe;AAGvB,WAAK,aAAa,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,iBAAiB,CAAC,GAAG,CAAC;AACpE,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB;AAAA,EAAC;AACxB;AACA,IAAM,yBAAN,cAAqC,gBAAgB;AAAA,EACnD,YAAY,IAAI,eAAe;AAC7B,UAAM,IAAI,wBAAwB,EAAE,GAAG,aAAa;AAAA,EACtD;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,oBAAoB;AAClB,WAAO,iBAAiB,KAAK,iBAAiB,EAAE;AAAA,EAClD;AACF;AACA,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EAClD,YAAY,eAAe;AACzB,UAAM,IAAI,uBAAuB,GAAG,aAAa;AAAA,EACnD;AAAA,EACA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAClB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,aAAa,KAAK;AAAA,MAC9B,KAAK,KAAK;AAAA,MACV,QAAQ,KAAK,YAAY,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,qBAAqB;AACnB,SAAK,aAAa,KAAK,kBAAkB;AAAA,EAC3C;AACF;AAKA,IAAM,UAAU,OAAO,gBAAgB,aAAa,YAAY,MAAM,KAAK;AAO3E,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AAEZ,SAAK,YAAY;AACjB,SAAK,cAAc,CAAC,QAAQ,cAAc;AAC1C,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAEnB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,eAAe;AAEpB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,UAAU,MAAM;AACnB,UAAI,KAAK,aAAa;AAEpB,YAAI,OAAO,KAAK,gBAAgB,KAAK,iBAAiB,OAAO,SAAS,KAAK,iBAAiB,OAAO,OAAO;AAC1G,YAAI,MAAM;AACR,cAAI,MAAM,QAAQ;AAClB,eAAK,WAAW,OAAO,MAAM,KAAK,kBAAkB,GAAI;AACxD,eAAK,iBAAiB,GAAG;AAAA,QAC3B,OAAO;AACL,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,OAAO,OAAO,eAAe;AACjC,QAAI,KAAK,WAAW;AAClB,WAAK,eAAe,KAAK,YAAY,aAAa;AAClD,WAAK,iBAAiB;AACtB,WAAK,iBAAiB;AACtB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AACtB,WAAK,WAAW,OAAO,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,WAAW,OAAO,OAAO;AACvB,QAAI,KAAK,WAAW;AAClB,UAAI,iBAAiB,QAAQ,OAAO;AACpC,UAAI,iBAAiB,QAAQ,OAAO;AACpC,UAAI,SAAS,KAAK,mBAAmB,OAAO,IAAI,iBAAiB,KAAK;AACtE,UAAI,SAAS,KAAK,mBAAmB,OAAO,IAAI,iBAAiB,KAAK;AACtE,UAAI,SAAS,GAAG;AACd,aAAK,cAAc;AAAA,MACrB,WAAW,SAAS,GAAG;AACrB,aAAK,gBAAgB;AAAA,MACvB;AACA,UAAI,SAAS,GAAG;AACd,aAAK,gBAAgB;AAAA,MACvB,WAAW,SAAS,GAAG;AACrB,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,iBAAiB;AACtB,WAAK,iBAAiB;AACtB,UAAI,CAAC,KAAK,aAAa;AACrB,aAAK,cAAc;AACnB,aAAK,iBAAiB,QAAQ,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,KAAK,WAAW;AAClB,WAAK,cAAc;AACnB,eAAS,eAAe,KAAK,cAAc;AACzC,oBAAY,QAAQ;AAAA,MACtB;AACA,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,iBAAiB,KAAK;AACpB,SAAK,iBAAiB;AACtB,0BAAsB,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,WAAW,MAAM,SAAS;AACxB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,cAAc,gBAAgB,KAAK;AACvC,QAAI;AAAA;AAAA,MAEJ,cAAc,eAAe,gBAAgB;AAAA,MAE7C,KAAK,cAAc;AAAA;AACnB,QAAI,OAAO;AACX,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,eAAO;AAAA,MAET,KAAK;AACH,oBAAY,cAAc,YAAY,cAAc,IAAI,WAAW,IAAI;AACvE;AAAA,MACF,KAAK;AACH,eAAO;AAAA,MAET,KAAK;AACH,oBAAY,aAAa,YAAY,aAAa,IAAI,WAAW,IAAI;AACrE;AAAA,IACJ;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,MAAM,KAAK;AACzB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,WAAW;AACf,QAAI,eAAe,KAAK,gBAAgB,CAAC;AACzC,aAAS,eAAe,cAAc;AACpC,UAAI,OAAO,YAAY;AACvB,UAAI,WAAW,OAAO,KAAK;AAC3B,UAAI,YAAY,KAAK,QAAQ;AAC7B,UAAI,UAAU,MAAM,KAAK;AACzB,UAAI,aAAa,KAAK,SAAS;AAE/B,UAAI,YAAY,KAAK,aAAa,KAAK,WAAW,KAAK,cAAc,GAAG;AACtE,YAAI,WAAW,iBAAiB,KAAK,eAAe,YAAY,YAAY,MAAM,CAAC,YAAY,SAAS,WAAW,UAAU;AAC3H,qBAAW;AAAA,YACT;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AACA,YAAI,cAAc,iBAAiB,KAAK,iBAAiB,YAAY,cAAc,MAAM,CAAC,YAAY,SAAS,WAAW,aAAa;AACrI,qBAAW;AAAA,YACT;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AAKA,YAAI,YAAY,iBAAiB,KAAK,iBAAiB,YAAY,cAAc,MAAM,CAAC,YAAY,SAAS,WAAW,WAAW;AACjI,qBAAW;AAAA,YACT;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AACA,YAAI,aAAa,iBAAiB,KAAK,kBAAkB,YAAY,eAAe,MAAM,CAAC,YAAY,SAAS,WAAW,YAAY;AACrI,qBAAW;AAAA,YACT;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,eAAe;AACzB,WAAO,KAAK,eAAe,aAAa,EAAE,IAAI,QAAM;AAClD,UAAI,OAAO,QAAQ;AACjB,eAAO,IAAI,sBAAsB,KAAK;AAAA,MACxC;AACA,aAAO,IAAI,uBAAuB,IAAI,KAAK;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,eAAe,eAAe;AAC5B,QAAI,MAAM,CAAC;AACX,aAAS,SAAS,KAAK,aAAa;AAClC,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,KAAK,KAAK;AAAA,MAChB,OAAO;AAKL,YAAI,KAAK,GAAG,MAAM,UAAU,MAAM,KAAK,cAAc,YAAY,EAAE,iBAAiB,KAAK,CAAC,CAAC;AAAA,MAC7F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAQA,IAAM,4BAAN,cAAwC,gBAAgB;AAAA,EACtD,YAAY,aAAa,UAAU;AACjC,UAAM,WAAW;AACjB,SAAK,cAAc;AAGnB,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,QAAM;AACzB,UAAI,CAAC,KAAK,YAAY;AAEpB,aAAK,gBAAgB;AACrB,aAAK,eAAe;AACpB,aAAK,sBAAsB;AAC3B,yBAAiB,SAAS,IAAI;AAC9B,2BAAmB,SAAS,IAAI;AAIhC,YAAI,CAAC,GAAG,SAAS;AACf,aAAG,UAAU,eAAe;AAAA,QAC9B;AACA,aAAK,QAAQ,QAAQ,eAAe,EAAE;AACtC,YAAI,KAAK;AAAA,QAET,CAAC,KAAK,QAAQ,kBAAkB;AAE9B,eAAK,OAAO,aAAa,KAAK;AAC9B,eAAK,OAAO,MAAM,GAAG,WAAW,GAAG,OAAO,GAAG,KAAK;AAClD,eAAK,WAAW,EAAE;AAClB,cAAI,CAAC,KAAK,aAAa;AACrB,iBAAK,wBAAwB,EAAE;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB,QAAM;AACzB,UAAI,KAAK,eAAe;AACtB,aAAK,QAAQ,QAAQ,eAAe,EAAE;AACtC,YAAI,CAAC,KAAK,qBAAqB;AAC7B,cAAI,cAAc,KAAK;AACvB,cAAI;AACJ,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,uBAAa,SAAS,SAAS,SAAS;AACxC,cAAI,cAAc,cAAc,aAAa;AAE3C,iBAAK,wBAAwB,EAAE;AAAA,UACjC;AAAA,QACF;AACA,YAAI,KAAK,YAAY;AAEnB,cAAI,GAAG,UAAU,SAAS,UAAU;AAClC,iBAAK,OAAO,WAAW,GAAG,OAAO,GAAG,KAAK;AACzC,iBAAK,aAAa,WAAW,GAAG,OAAO,GAAG,KAAK;AAAA,UACjD;AACA,eAAK,QAAQ,QAAQ,YAAY,EAAE;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AACA,SAAK,cAAc,QAAM;AACvB,UAAI,KAAK,eAAe;AACtB,aAAK,gBAAgB;AACrB,uBAAe,SAAS,IAAI;AAC5B,yBAAiB,SAAS,IAAI;AAC9B,aAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,YAAI,KAAK,YAAY;AACnB,eAAK,aAAa,KAAK;AACvB,eAAK,YAAY,EAAE;AAAA,QACrB;AACA,YAAI,KAAK,gBAAgB;AACvB,uBAAa,KAAK,cAAc;AAChC,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,QAAI,UAAU,KAAK,UAAU,IAAI,gBAAgB,WAAW;AAC5D,YAAQ,QAAQ,GAAG,eAAe,KAAK,aAAa;AACpD,YAAQ,QAAQ,GAAG,eAAe,KAAK,aAAa;AACpD,YAAQ,QAAQ,GAAG,aAAa,KAAK,WAAW;AAChD,QAAI,UAAU;AACZ,cAAQ,WAAW;AAAA,IACrB;AACA,SAAK,SAAS,IAAI,cAAc;AAChC,SAAK,eAAe,IAAI,aAAa;AAAA,EACvC;AAAA,EACA,UAAU;AACR,SAAK,QAAQ,QAAQ;AAGrB,SAAK,YAAY,CAAC,CAAC;AAAA,EACrB;AAAA,EACA,WAAW,IAAI;AACb,QAAI,OAAO,KAAK,UAAU,UAAU;AAClC,WAAK,iBAAiB,WAAW,MAAM;AACrC,aAAK,iBAAiB;AACtB,aAAK,eAAe,EAAE;AAAA,MACxB,GAAG,KAAK,KAAK;AAAA,IACf,OAAO;AACL,WAAK,eAAe,EAAE;AAAA,IACxB;AAAA,EACF;AAAA,EACA,eAAe,IAAI;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa,EAAE;AAAA,EACtB;AAAA,EACA,wBAAwB,IAAI;AAC1B,SAAK,sBAAsB;AAC3B,SAAK,aAAa,EAAE;AAAA,EACtB;AAAA,EACA,aAAa,IAAI;AACf,QAAI,KAAK,gBAAgB,KAAK,qBAAqB;AACjD,UAAI,CAAC,KAAK,QAAQ,kBAAkB,KAAK,oBAAoB;AAC3D,aAAK,aAAa;AAClB,aAAK,oBAAoB;AACzB,aAAK,aAAa,MAAM,GAAG,OAAO,GAAG,OAAO,KAAK,WAAW;AAC5D,aAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,YAAI,KAAK,uBAAuB,OAAO;AACrC,eAAK,QAAQ,kBAAkB;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,IAAI;AAGd,SAAK,OAAO,KAAK,KAAK,mBAAmB,KAAK,SAAS,KAAK,MAAM,EAAE,CAAC;AAAA,EACvE;AAAA,EACA,SAAS,IAAI;AACX,SAAK,aAAa;AAClB,SAAK,QAAQ,QAAQ,WAAW,EAAE;AAAA,EACpC;AAAA;AAAA,EAEA,cAAc,MAAM;AAClB,SAAK,QAAQ,mBAAmB;AAAA,EAClC;AAAA,EACA,mBAAmB,MAAM;AACvB,SAAK,OAAO,aAAa,IAAI;AAAA,EAC/B;AAAA,EACA,qBAAqB,MAAM;AACzB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,qBAAqB,MAAM;AACzB,SAAK,aAAa,YAAY;AAAA,EAChC;AACF;AAUA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,WAAW,YAAY,EAAE;AAE9B,SAAK,eAAe,mBAAmB,EAAE,EAAE,IAAI,cAAY,IAAI,uBAAuB,UAAU,IAAI,CAAC;AAAA,EACvG;AAAA,EACA,UAAU;AACR,aAAS,eAAe,KAAK,cAAc;AACzC,kBAAY,QAAQ;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,OAAO,KAAK,SAAS;AACzB,aAAS,eAAe,KAAK,cAAc;AACzC,cAAQ,YAAY,iBAAiB,YAAY,cAAc;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,QAAI,MAAM,KAAK,SAAS;AACxB,aAAS,eAAe,KAAK,cAAc;AACzC,aAAO,YAAY,gBAAgB,YAAY,aAAa;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC7B,QAAI,QAAQ;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,aAAS,eAAe,KAAK,cAAc;AACzC,UAAI,CAAC,kBAAkB,YAAY,eAAe,CAAC,KAAK,CAAC,gBAAgB,OAAO,YAAY,UAAU,GAAG;AACvG,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,UAAU,KAAK;AACnB,SAAO,YAAY,UAAU,YAAY;AAC3C;AAeA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,UAAU,gBAAgB;AAEpC,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,oBAAoB,QAAM;AAC7B,UAAI;AAAA,QACF,UAAAA;AAAA,MACF,IAAI;AACJ,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,kBAAkB,EAAE;AACzB,UAAI,KAAK,cAAc,CAAC,KAAK,gBAAgB;AAC3C,QAAAA,UAAS,cAAc,KAAK;AAE5B,aAAK,QAAQ,QAAQ,eAAe,EAAE;AAAA,MACxC,OAAO;AACL,QAAAA,UAAS,cAAc,IAAI;AAAA,MAC7B;AAAA,IACF;AACA,SAAK,kBAAkB,QAAM;AAC3B,WAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,WAAK,WAAW,IAAI,IAAI;AAAA,IAC1B;AACA,SAAK,iBAAiB,QAAM;AAC1B,WAAK,QAAQ,QAAQ,YAAY,EAAE;AACnC,WAAK,WAAW,EAAE;AAAA,IACpB;AACA,SAAK,kBAAkB,QAAM;AAC3B,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,aAAa,EAAE;AAAA,IACtC;AACA,SAAK,gBAAgB,QAAM;AACzB,UAAI,KAAK,WAAW;AAClB,aAAK,QAAQ,QAAQ,aAAa,MAAM,MAAM,EAAE;AAAA,MAClD;AACA,WAAK,WAAW,KAAK;AACrB,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,WAAW,EAAE;AAAA,IACpC;AACA,SAAK,iBAAiB;AACtB,aAAS,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACzD,aAAS,QAAQ,GAAG,aAAa,KAAK,eAAe;AACrD,aAAS,QAAQ,GAAG,YAAY,KAAK,cAAc;AACnD,aAAS,QAAQ,GAAG,aAAa,KAAK,eAAe;AACrD,aAAS,QAAQ,GAAG,WAAW,KAAK,aAAa;AACjD,SAAK,WAAW;AAChB,SAAK,UAAU,IAAI,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA,EAGA,kBAAkB,IAAI;AACpB,QAAI,YAAY;AAAA,MACd,MAAM,GAAG;AAAA,MACT,KAAK,GAAG;AAAA,IACV;AACA,QAAI,gBAAgB;AACpB,QAAI,YAAY,GAAG;AACnB,QAAI;AACJ,QAAI,qBAAqB,aAAa;AAEpC,oBAAc,YAAY,SAAS;AACnC,sBAAgB,eAAe,eAAe,WAAW;AAAA,IAC3D;AACA,QAAI,aAAa,KAAK,aAAa,KAAK,kBAAkB,cAAc,MAAM,cAAc,GAAG;AAC/F,QAAI,YAAY;AACd,UAAI,KAAK,oBAAoB,aAAa;AACxC,YAAI,oBAAoB,eAAe,aAAa,WAAW,IAAI;AACnE,YAAI,mBAAmB;AACrB,0BAAgB,cAAc,iBAAiB;AAAA,QACjD;AAAA,MACF;AACA,WAAK,cAAc,WAAW,eAAe,SAAS;AAAA,IACxD,OAAO;AACL,WAAK,cAAc;AAAA,QACjB,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,IAAI,aAAa;AAC1B,QAAI,MAAM,KAAK,kBAAkB,GAAG,QAAQ,KAAK,YAAY,MAAM,GAAG,QAAQ,KAAK,YAAY,GAAG;AAClG,QAAI,eAAe,CAAC,YAAY,KAAK,WAAW,GAAG,GAAG;AACpD,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,aAAa,KAAK,OAAO,EAAE;AAAA,IAClD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,QAAQ,KAAK,gBAAgB,yBAAuB;AACxE,0BAAoB,UAAU,YAAY;AAC1C,aAAO,IAAI,cAAc,oBAAoB,EAAE;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,aAAS,MAAM,gBAAgB;AAC7B,qBAAe,EAAE,EAAE,QAAQ;AAAA,IAC7B;AACA,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,kBAAkB,YAAY,WAAW;AACvC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU;AACd,aAAS,MAAM,gBAAgB;AAC7B,UAAI,YAAY,eAAe,EAAE,EAAE;AACnC,UAAI,gBAAgB,eAAe,EAAE;AACrC,UAAI;AAAA,MAEJ,cAAc,iBAAiB,YAAY,SAAS,GAAG;AACrD,YAAI,aAAa,cAAc,YAAY;AAC3C,YAAI,YAAY,cAAc,WAAW;AACzC,YAAI,eAAe,aAAa;AAChC,YAAI,cAAc,YAAY;AAC9B,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,YAAI,QAAQ,SAAS,QAAQ,SAAS;AACtC,YAAI,SAAS,SAAS,SAAS,SAAS;AACxC;AAAA;AAAA,UAEA,gBAAgB,KAAK,eAAe,SAAS,eAAe,KAAK,cAAc;AAAA,UAAQ;AACrF,cAAI,MAAM,UAAU,SAAS,cAAc,aAAa,OAAO,MAAM;AACrE,cAAI;AAAA,UAEJ,mBAAmB,IAAI,YAAY,aAAa,IAAI,SAAS,KAAK;AAAA;AAAA;AAAA,WAIlE,KAAK,qBAAqB,cAAc,GAAG,SAAS,cAAc,GAAG,YAAY,EAAE;AAAA;AAAA,YAEnF,eAAe,aAAa,OAAO;AAAA,YAAS,cAAc,YAAY,OAAO;AAAA,UAAO,CAAC,OAAO,CAAC,WAAW,IAAI,QAAQ,QAAQ,QAAQ;AAClI,gBAAI,cAAc;AAClB,gBAAI,UAAU,UAAU;AAExB,gBAAI,KAAK,QAAQ;AACjB,gBAAI,KAAK,SAAS;AAClB,gBAAI,KAAK,OAAO;AAChB,gBAAI,KAAK,UAAU;AACnB,sBAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,IAAI,MAAM,QAAQ,IAAI,GAAG;AACnC,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,KAAK,UAAU,KAAK,QAAQ;AACtD;AACA,SAAS,6BAA6B,UAAU,SAAS;AACvD,MAAI,QAAQ,CAAC;AACb,WAAS,aAAa,QAAQ,YAAY,qBAAqB;AAC7D,WAAO,OAAO,OAAO,UAAU,UAAU,OAAO,CAAC;AAAA,EACnD;AACA,SAAO,OAAO,OAAO,kBAAkB,UAAU,QAAQ,OAAO,CAAC;AACjE,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM,SAAS;AACxC,SAAO;AAAA,IACL,MAAM,QAAQ,OAAO,KAAK,MAAM,KAAK;AAAA,IACrC,SAAS,QAAQ,UAAU,KAAK,MAAM,OAAO;AAAA,MAC3C,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,IACD,QAAQ,KAAK;AAAA,EACf;AACF;AAMA,IAAM,eAAN,cAA2B,YAAY;AAAA,EACrC,YAAY,UAAU;AACpB,UAAM,QAAQ;AACd,SAAK,oBAAoB,SAAO;AAC9B,UAAI;AAAA,QACF;AAAA,MACF,IAAI;AACJ,UAAI,SAAS,IAAI,UAAU;AAE3B,eAAS,cAAc,CAAC,KAAK,UAAU,kBAAkB,MAAM,CAAC;AAAA,IAClE;AAEA,SAAK,gBAAgB,QAAM;AACzB,UAAI;AAAA,QACF;AAAA,MACF,IAAI;AACJ,UAAI;AAAA,QACF;AAAA,MACF,IAAI,KAAK;AACT,UAAI,CAAC,QAAQ,gBAAgB;AAC3B,YAAI;AAAA,UACF;AAAA,UACA;AAAA,QACF,IAAI,KAAK;AACT,YAAI,cAAc,YAAY,YAAY,YAAY,QAAQ,GAAG;AAC/D,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,cAAI,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,6BAA6B,WAAW,UAAU,OAAO,CAAC,GAAG;AAAA,YACrG,OAAO,WAAW;AAAA,YAClB,SAAS,GAAG;AAAA,YACZ,MAAM,QAAQ,WAAW,QAAQ,YAAY;AAAA,UAC/C,CAAC;AACD,kBAAQ,QAAQ,QAAQ,aAAa,GAAG;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAEA,SAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACzD,SAAK,SAAS,aAAa,YAAY;AACvC,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,2BAA2B,QAAQ,CAAC;AACxG,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AAAA,EACtD;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AACF;AAMA,IAAM,gBAAN,cAA4B,YAAY;AAAA,EACtC,YAAY,UAAU;AACpB,UAAM,QAAQ;AACd,SAAK,gBAAgB;AACrB,SAAK,oBAAoB,QAAM;AAC7B,UAAI;AAAA,QACF,WAAAC;AAAA,QACA,UAAAD;AAAA,MACF,IAAI;AACJ,UAAI;AAAA,QACF,SAAAE;AAAA,MACF,IAAID,WAAU;AACd,UAAI,YAAYC,SAAQ,cAAcD,WAAU,kBAAkB,GAAG,UAAU,MAAM;AAErF,MAAAD,UAAS,cAAc,CAAC,SAAS;AAEjC,MAAAA,UAAS,QAAQ,GAAG,UAAU,yBAAyBC,UAAS,IAAI;AAAA,IACtE;AACA,SAAK,kBAAkB,QAAM;AAC3B,WAAK,UAAU,QAAQ,YAAY,SAAS,EAAE;AAAA,IAChD;AACA,SAAK,kBAAkB,CAAC,KAAK,YAAY;AACvC,UAAI;AAAA,QACF;AAAA,MACF,IAAI,KAAK;AACT,UAAI,gBAAgB;AACpB,UAAI,YAAY;AAChB,UAAI,KAAK;AACP,YAAI,aAAa,KAAK,YAAY;AAClC,YAAI,aAAa,IAAI,gBAAgB,WAAW,eAAe,KAAK,qBAAqB,CAAC,KAAK,kBAAkB,YAAY,GAAG;AAChI,YAAI,CAAC,YAAY;AACf,0BAAgB,sBAAsB,YAAY,KAAK,QAAQ,YAAY,yBAAyB;AAAA,QACtG;AACA,YAAI,CAAC,iBAAiB,CAAC,qBAAqB,eAAe,IAAI,aAAa,OAAO,GAAG;AACpF,sBAAY;AACZ,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,UAAI,eAAe;AACjB,gBAAQ,SAAS;AAAA,UACf,MAAM;AAAA,UACN,WAAW;AAAA,QACb,CAAC;AAAA,MACH,WAAW,CAAC,SAAS;AAEnB,gBAAQ,SAAS;AAAA,UACf,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,CAAC,WAAW;AACd,qBAAa;AAAA,MACf,OAAO;AACL,sBAAc;AAAA,MAChB;AACA,UAAI,CAAC,SAAS;AACZ,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AACA,SAAK,kBAAkB,SAAO;AAC5B,UAAI,KAAK,eAAe;AAEtB,0BAAkB,KAAK,eAAe,KAAK,KAAK,UAAU,OAAO;AACjE,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AACA,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI,UAAU;AACd,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACxE,aAAS,qBAAqB;AAC9B,aAAS,cAAc,QAAQ,qBAAqB;AACpD,aAAS,aAAa,YAAY,QAAQ;AAC1C,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,2BAA2B,QAAQ,CAAC;AACxG,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AAAA,EAC1D;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AACF;AACA,SAAS,yBAAyB,WAAW;AAC3C,MAAI;AAAA,IACF;AAAA,EACF,IAAI,UAAU;AACd,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,MAAM;AACjB,YAAQ,QAAQ;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,MAAM,MAAM,2BAA2B;AACpE,MAAI,YAAY,KAAK;AACrB,MAAI,YAAY,KAAK;AACrB,MAAI,KAAK,CAAC,UAAU,MAAM,OAAO,UAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,MAAM,GAAG;AAChG,KAAG,KAAK,cAAc;AACtB,MAAI,QAAQ,CAAC;AACb,WAAS,eAAe,2BAA2B;AACjD,QAAI,MAAM,YAAY,MAAM,IAAI;AAChC,QAAI,QAAQ,OAAO;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK;AACP,aAAO,OAAO,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ,OAAO,GAAG,CAAC;AAAA,IACX,KAAK,GAAG,CAAC;AAAA,EACX;AACA,QAAM,SAAS,UAAU;AACzB,SAAO;AACT;AACA,IAAM,gBAAN,MAAM,uBAAsB,YAAY;AAAA,EACtC,YAAY,UAAU;AACpB,UAAM,QAAQ;AAEd,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB,QAAM;AAC7B,UAAI,aAAa,GAAG,UAAU;AAC9B,UAAI;AAAA,QACF,WAAAA;AAAA,QACA,UAAAD;AAAA,MACF,IAAI;AACJ,UAAI;AAAA,QACF;AAAA,MACF,IAAIA;AACJ,UAAI;AAAA,QACF,SAAAE;AAAA,MACF,IAAID,WAAU;AACd,UAAI,iBAAiBA,WAAU;AAC/B,WAAK,YAAY,GAAG;AACpB,UAAI,aAAa,KAAK,aAAa,SAAS,GAAG,SAAS;AACxD,UAAI,aAAa,KAAK,aAAa,WAAW;AAC9C,UAAI,kBAAkB,WAAW,SAAS;AAC1C,WAAK,iBAAiB,kBAAkB,eAAe,eAAe,EAAE,YAAY,eAAe;AACnG,MAAAD,UAAS,cAAc,GAAG,UAAU,IAAIE,SAAQ;AAChD,MAAAF,UAAS;AAAA,MAET,GAAG,WAAW,oBAAoBC,WAAU,MAAM,iBAAiB,uBAAuBA,UAAS,IAAI;AACvG,UAAIC,SAAQ,mBAAmB;AAC7B,eAAO,aAAaA,SAAQ;AAAA,MAC9B,OAAO;AACL,eAAO,aAAa,eAAe,YAAY,KAAK;AAAA,MACtD;AACA,aAAO,iBAAiBA,SAAQ;AAChC,UAAI,UAAUD,WAAU,iBAAiB,UAAU,KAAK,CAAC,eAAe,YAAY,mBAAmB;AACvG,MAAAD,UAAS,cAAc,CAAC,OAAO;AAG/B,WAAK,aAAa,WAAW,GAAG,UAAU,UAAU,SAAS,oBAAoB;AAAA,IACnF;AACA,SAAK,kBAAkB,QAAM;AAC3B,UAAI,iBAAiB,KAAK,UAAU;AACpC,UAAI,aAAa,KAAK;AACtB,UAAI,kBAAkB,WAAW,SAAS;AAC1C,UAAI,GAAG,SAAS;AAEd,YAAI,oBAAoB,KAAK,UAAU,MAAM,gBAAgB;AAC3D,yBAAe,SAAS;AAAA,YACtB,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AAEL,uBAAe,SAAS;AAAA,UACtB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,KAAK,YAAY;AACnB,uBAAe,YAAY,SAAS,EAAE;AACtC,uBAAe,QAAQ,QAAQ,kBAAkB;AAAA,UAC/C,IAAI,KAAK;AAAA,UACT,OAAO,IAAI,UAAU,gBAAgB,WAAW,KAAK,WAAW,QAAQ;AAAA,UACxE,SAAS,GAAG;AAAA,UACZ,MAAM,eAAe;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAK,kBAAkB,CAAC,KAAK,YAAY;AACvC,UAAI,CAAC,KAAK,YAAY;AACpB;AAAA,MACF;AACA,UAAI,iBAAiB,KAAK;AAC1B,UAAI,aAAa,KAAK,YAAY;AAClC,UAAI,iBAAiB,KAAK,UAAU;AAEpC,UAAI,mBAAmB;AACvB,UAAI,WAAW;AACf,UAAI,wBAAwB;AAC5B,UAAI,YAAY;AAChB,UAAI,cAAc;AAAA,QAChB,gBAAgB;AAAA,QAChB,eAAe,sBAAsB;AAAA,QACrC,SAAS;AAAA,MACX;AACA,UAAI,KAAK;AACP,2BAAmB,IAAI;AACvB,YAAI,mBAAmB,iBAAiB;AACxC,YAAI,mBAAmB,oBAAoB,iBAAiB,YAAY,iBAAiB,WAAW;AAClG,qBAAW,qBAAqB,YAAY,KAAK,KAAK,WAAW,SAAS,MAAM,OAAO,iBAAiB,eAAe,EAAE,YAAY,0BAA0B;AAC/J,cAAI,UAAU;AACZ,oCAAwB,0BAA0B,gBAAgB,iBAAiB,eAAe,EAAE,cAAc,UAAU,gBAAgB;AAC5I,wBAAY,gBAAgB;AAC5B,gBAAI,CAAC,mBAAmB,aAAa,IAAI,aAAa,gBAAgB,GAAG;AACvE,0BAAY;AACZ,yBAAW;AACX,sCAAwB;AACxB,0BAAY,gBAAgB,sBAAsB;AAAA,YACpD;AAAA,UACF;AAAA,QACF,OAAO;AACL,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,WAAK,YAAY,kBAAkB,WAAW;AAC9C,UAAI,CAAC,WAAW;AACd,qBAAa;AAAA,MACf,OAAO;AACL,sBAAc;AAAA,MAChB;AACA,UAAI,CAAC,SAAS;AACZ,YAAI,mBAAmB;AAAA,QAEvB,YAAY,YAAY,GAAG,GAAG;AAC5B,qBAAW;AAAA,QACb;AACA,aAAK,SAAS,qBAAqB,CAAC,QAAQ;AAG5C,aAAK,SAAS,mBAAmB,CAAC,OAAO,CAAC,KAAK,UAAU,YAAY,EAAE,cAAc,kBAAkB,CAAC;AAExG,aAAK,mBAAmB;AACxB,aAAK,gBAAgB;AACrB,aAAK,wBAAwB;AAAA,MAC/B;AAAA,IACF;AACA,SAAK,kBAAkB,MAAM;AAC3B,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AACA,SAAK,gBAAgB,QAAM;AACzB,UAAI,KAAK,YAAY;AACnB,YAAI,iBAAiB,KAAK,UAAU;AACpC,YAAI,cAAc,eAAe;AACjC,YAAI;AAAA,UACF;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,WAAW,KAAK,WAAW;AAC/B,YAAI,gBAAgB,KAAK,WAAW;AACpC,YAAI,WAAW,IAAI,UAAU,gBAAgB,UAAU,aAAa;AACpE,YAAI,iBAAiB,KAAK;AAC1B,YAAI,wBAAwB,KAAK;AACjC,YAAI;AAAA,UACF;AAAA,QACF,IAAI,KAAK;AACT,aAAK,UAAU;AACf,uBAAe,QAAQ,QAAQ,iBAAiB;AAAA,UAC9C,IAAI,KAAK;AAAA,UACT,OAAO;AAAA,UACP,SAAS,GAAG;AAAA,UACZ,MAAM;AAAA,QACR,CAAC;AACD,YAAI,eAAe;AAEjB,cAAI,qBAAqB,gBAAgB;AACvC,gBAAI,kBAAkB,IAAI,UAAU,gBAAgB,sBAAsB,KAAK,SAAS,KAAK,GAAG,gBAAgB,sBAAsB,UAAU,cAAc,UAAU,IAAI,IAAI;AAChL,2BAAe,SAAS;AAAA,cACtB,MAAM;AAAA,cACN,YAAY;AAAA,YACd,CAAC;AACD,gBAAI,iBAAiB;AAAA,cACnB,UAAU;AAAA,cACV,OAAO;AAAA,cACP,eAAe,eAAe,uBAAuB,gBAAgB,aAAa;AAAA,cAClF,SAAS;AACP,+BAAe,SAAS;AAAA,kBACtB,MAAM;AAAA,kBACN,YAAY;AAAA;AAAA,gBACd,CAAC;AAAA,cACH;AAAA,YACF;AACA,gBAAI,cAAc,CAAC;AACnB,qBAAS,eAAe,eAAe,eAAe,EAAE,YAAY,uBAAuB;AACzF,qBAAO,OAAO,aAAa,YAAY,eAAe,cAAc,CAAC;AAAA,YACvE;AACA,2BAAe,QAAQ,QAAQ,aAAa,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,WAAW,GAAG;AAAA,cACvH,IAAI,GAAG;AAAA,cACP,OAAO,cAAc;AAAA,cACrB,SAAS,GAAG;AAAA,cACZ,MAAM;AAAA,YACR,CAAC,CAAC;AACF,2BAAe,QAAQ,QAAQ,eAAe,cAAc;AAAA,UAE9D,WAAW,kBAAkB;AAC3B,gBAAI,iBAAiB;AAAA,cACnB,OAAO;AAAA,cACP,eAAe,eAAe,gBAAgB,gBAAgB,aAAa;AAAA,cAC3E,SAAS;AACP,+BAAe,SAAS;AAAA,kBACtB,MAAM;AAAA,kBACN,YAAY;AAAA,gBACd,CAAC;AAAA,cACH;AAAA,YACF;AACA,2BAAe,QAAQ,QAAQ,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG;AAAA,cAC5F,WAAW,GAAG;AAAA,cACd,MAAM;AAAA,YACR,CAAC,CAAC;AACF,2BAAe,SAAS;AAAA,cACtB,MAAM;AAAA,cACN,YAAY;AAAA,YACd,CAAC;AACD,2BAAe,QAAQ,QAAQ,eAAe,cAAc;AAC5D,gBAAI,gBAAgB,sBAAsB,KAAK,SAAS,KAAK;AAC7D,gBAAI,qBAAqB,sBAAsB,UAAU,cAAc,UAAU;AACjF,gBAAI,gBAAgB,IAAI,UAAU,kBAAkB,eAAe,kBAAkB;AACrF,6BAAiB,SAAS;AAAA,cACxB,MAAM;AAAA,cACN,YAAY;AAAA,YACd,CAAC;AACD,gBAAI,cAAc;AAAA,cAChB,OAAO;AAAA,cACP,eAAe,eAAe,uBAAuB,kBAAkB,kBAAkB;AAAA,cACzF,SAAS;AACP,iCAAiB,SAAS;AAAA,kBACxB,MAAM;AAAA,kBACN,YAAY;AAAA,gBACd,CAAC;AAAA,cACH;AAAA,YACF;AACA,6BAAiB,QAAQ,QAAQ,YAAY,WAAW;AACxD,gBAAI,GAAG,SAAS;AACd,+BAAiB,SAAS;AAAA,gBACxB,MAAM;AAAA,gBACN,iBAAiB,cAAc;AAAA,cACjC,CAAC;AAAA,YACH;AACA,6BAAiB,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,6BAA6B,SAAS,UAAU,gBAAgB,CAAC,GAAG;AAAA,cAC3I,WAAW,GAAG;AAAA,cACd,SAAS,GAAG;AAAA,cACZ,MAAM,SAAS,QAAQ;AAAA,YACzB,CAAC,CAAC;AACF,6BAAiB,QAAQ,QAAQ,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG;AAAA,cAC7F,WAAW,GAAG;AAAA,cACd,MAAM,SAAS,QAAQ;AAAA,YACzB,CAAC,CAAC;AAAA,UACJ;AAAA,QACF,OAAO;AACL,yBAAe,QAAQ,QAAQ,cAAc;AAAA,QAC/C;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AACA,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI,UAAU;AACd,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACxE,aAAS,QAAQ,WAAW,eAAc;AAC1C,aAAS,qBAAqB;AAC9B,aAAS,aAAa,YAAY,QAAQ;AAC1C,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,wBAAwB;AAC5F,gBAAY,mBAAmB,SAAS;AACxC,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AAAA,EACtD;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA;AAAA,EAEA,YAAY,aAAa,OAAO;AAC9B,QAAI,iBAAiB,KAAK,UAAU;AACpC,QAAI,cAAc,KAAK;AAEvB,QAAI,eAAe,gBAAgB,aAAa;AAG9C,UAAI,gBAAgB,gBAAgB;AAClC,oBAAY,SAAS;AAAA,UACnB,MAAM;AAAA,UACN,OAAO;AAAA,YACL,gBAAgB,MAAM;AAAA,YACtB,eAAe,sBAAsB;AAAA,YACrC,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MAEH,OAAO;AACL,oBAAY,SAAS;AAAA,UACnB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,aAAa;AACf,kBAAY,SAAS;AAAA,QACnB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,kBAAkB,KAAK,UAAU;AACrC,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,kBAAkB;AACpB,uBAAiB,SAAS;AAAA,QACxB,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,QAAI,oBAAoB,kBAAkB;AACxC,sBAAgB,SAAS;AAAA,QACvB,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAAA,EAC/B;AACF;AAGA,cAAc,WAAW;AACzB,SAAS,qBAAqB,MAAM,MAAM,oBAAoB,WAAW;AACvE,MAAI,YAAY,KAAK;AACrB,MAAI,YAAY,KAAK;AACrB,MAAI,QAAQ,UAAU,MAAM;AAC5B,MAAI,QAAQ,UAAU,MAAM;AAC5B,MAAI,gBAAgB,CAAC;AACrB,MAAI,UAAU,WAAW,UAAU,QAAQ;AACzC,kBAAc,SAAS,UAAU;AACjC,kBAAc,SAAS,KAAK,QAAQ,QAAQ;AAC5C,QAAI,UAAU,QAAQ;AAGpB,cAAQ,WAAW,kBAAkB;AAAA,IACvC,OAAO;AAGL,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,QAAQ,UAAU,OAAO,OAAO,KAAK,QAAQ,SAAS,KAAK,gBAAgB,KAAK,cAAc,KAAK,YAAY,IAAI;AACvH,MAAI,MAAM,cAAc;AAEtB,kBAAc,SAAS;AAAA,EACzB;AACA,MAAI,WAAW;AAAA,IACb,YAAY;AAAA,IACZ;AAAA,EACF;AACA,WAAS,YAAY,WAAW;AAC9B,aAAS,UAAU,MAAM,IAAI;AAAA,EAC/B;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,WAAW;AACzC,MAAI;AAAA,IACF;AAAA,EACF,IAAI,UAAU;AACd,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,MAAM;AACjB,YAAQ,QAAQ;AAAA,EAClB;AACA,SAAO;AACT;AACA,IAAM,gBAAN,cAA4B,YAAY;AAAA,EACtC,YAAY,UAAU;AACpB,UAAM,QAAQ;AAEd,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB,QAAM;AAC7B,UAAI;AAAA,QACF,WAAAC;AAAA,MACF,IAAI;AACJ,UAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,UAAI,MAAM,SAAS,KAAK;AACxB,UAAI,aAAa,KAAK,aAAa,IAAI;AACvC,WAAK,SAAS,cAAcA,WAAU,QAAQ,QAAQ;AAEtD,WAAK,SAAS,cAAc,CAAC,KAAK,UAAU,iBAAiB,GAAG,UAAU,MAAM,KAAK,GAAG,WAAW,KAAK,UAAU,MAAM,mBAAmB,WAAW,SAAS,UAAU;AAAA,IAC3K;AACA,SAAK,kBAAkB,QAAM;AAC3B,UAAI;AAAA,QACF;AAAA,MACF,IAAI,KAAK;AACT,UAAI,aAAa,KAAK;AACtB,WAAK,iBAAiB,kBAAkB,QAAQ,eAAe,EAAE,YAAY,KAAK,WAAW,SAAS,UAAU;AAChH,UAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,WAAK,gBAAgB;AACrB,WAAK,cAAc,SAAS,KAAK;AACjC,cAAQ,YAAY,SAAS;AAC7B,cAAQ,QAAQ,QAAQ,oBAAoB;AAAA,QAC1C,IAAI;AAAA,QACJ,OAAO,IAAI,UAAU,SAAS,WAAW,KAAK,WAAW,QAAQ;AAAA,QACjE,SAAS,GAAG;AAAA,QACZ,MAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,IACH;AACA,SAAK,kBAAkB,CAAC,KAAK,SAAS,OAAO;AAC3C,UAAI;AAAA,QACF;AAAA,MACF,IAAI,KAAK;AACT,UAAI,iBAAiB,KAAK;AAC1B,UAAI,aAAa,KAAK,YAAY;AAClC,UAAI,gBAAgB,KAAK,WAAW;AACpC,UAAI,WAAW;AACf,UAAI,wBAAwB;AAC5B,UAAI,YAAY;AAChB,UAAI,cAAc;AAAA,QAChB,gBAAgB;AAAA,QAChB,eAAe,sBAAsB;AAAA,QACrC,SAAS;AAAA,MACX;AACA,UAAI,KAAK;AACP,YAAI,aAAa,IAAI,gBAAgB,WAAW,eAAe,KAAK,qBAAqB,CAAC,KAAK,kBAAkB,YAAY,GAAG;AAChI,YAAI,CAAC,YAAY;AACf,qBAAW,gBAAgB,YAAY,KAAK,GAAG,UAAU,UAAU,SAAS,wBAAwB,GAAG,cAAc,KAAK;AAAA,QAC5H;AAAA,MACF;AACA,UAAI,UAAU;AACZ,gCAAwB,0BAA0B,gBAAgB,QAAQ,eAAe,EAAE,cAAc,UAAU,OAAO;AAC1H,oBAAY,gBAAgB;AAC5B,YAAI,CAAC,mBAAmB,aAAa,IAAI,aAAa,OAAO,GAAG;AAC9D,sBAAY;AACZ,qBAAW;AACX,kCAAwB;AACxB,sBAAY,gBAAgB;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,uBAAuB;AACzB,gBAAQ,SAAS;AAAA,UACf,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,SAAS;AAAA,UACf,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,UAAI,CAAC,WAAW;AACd,qBAAa;AAAA,MACf,OAAO;AACL,sBAAc;AAAA,MAChB;AACA,UAAI,CAAC,SAAS;AACZ,YAAI,YAAY,YAAY,YAAY,GAAG,GAAG;AAC5C,qBAAW;AAAA,QACb;AACA,aAAK,gBAAgB;AACrB,aAAK,wBAAwB;AAAA,MAC/B;AAAA,IACF;AACA,SAAK,gBAAgB,QAAM;AACzB,UAAI;AAAA,QACF;AAAA,MACF,IAAI,KAAK;AACT,UAAI,WAAW,KAAK,WAAW;AAC/B,UAAI,gBAAgB,KAAK,WAAW;AACpC,UAAI,WAAW,IAAI,UAAU,SAAS,UAAU,aAAa;AAC7D,UAAI,iBAAiB,KAAK;AAC1B,UAAI,wBAAwB,KAAK;AACjC,cAAQ,QAAQ,QAAQ,mBAAmB;AAAA,QACzC,IAAI,KAAK;AAAA,QACT,OAAO;AAAA,QACP,SAAS,GAAG;AAAA,QACZ,MAAM,QAAQ;AAAA,MAChB,CAAC;AACD,UAAI,KAAK,eAAe;AACtB,YAAI,kBAAkB,IAAI,UAAU,SAAS,sBAAsB,KAAK,SAAS,KAAK,GAAG,gBAAgB,sBAAsB,UAAU,cAAc,UAAU,IAAI,IAAI;AACzK,gBAAQ,SAAS;AAAA,UACf,MAAM;AAAA,UACN,YAAY;AAAA,QACd,CAAC;AACD,YAAI,iBAAiB;AAAA,UACnB,UAAU;AAAA,UACV,OAAO;AAAA,UACP,eAAe,eAAe,uBAAuB,SAAS,aAAa;AAAA,UAC3E,SAAS;AACP,oBAAQ,SAAS;AAAA,cACf,MAAM;AAAA,cACN,YAAY;AAAA;AAAA,YACd,CAAC;AAAA,UACH;AAAA,QACF;AACA,gBAAQ,QAAQ,QAAQ,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG;AAAA,UACtF,IAAI,KAAK;AAAA,UACT,YAAY,KAAK,cAAc,cAAc,eAAe,CAAC;AAAA,UAC7D,UAAU,KAAK,cAAc,YAAY,eAAe,CAAC;AAAA,UACzD,SAAS,GAAG;AAAA,UACZ,MAAM,QAAQ;AAAA,QAChB,CAAC,CAAC;AACF,gBAAQ,QAAQ,QAAQ,eAAe,cAAc;AAAA,MACvD,OAAO;AACL,gBAAQ,QAAQ,QAAQ,gBAAgB;AAAA,MAC1C;AAEA,WAAK,cAAc;AACnB,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AAAA,IAEvB;AACA,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,SAAS,EAAE;AACxE,aAAS,QAAQ,WAAW;AAC5B,aAAS,qBAAqB;AAC9B,aAAS,aAAa,YAAY,UAAU,QAAQ,QAAQ;AAC5D,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,KAAK,UAAU,2BAA2B,QAAQ,CAAC;AACxG,gBAAY,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AAC5D,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AAAA,EACtD;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EACA,WAAW,IAAI;AACb,WAAO,eAAe,GAAG,WAAW,WAAW;AAAA,EACjD;AACF;AACA,SAAS,gBAAgB,MAAM,MAAM,aAAa,eAAe;AAC/D,MAAI,UAAU,KAAK,QAAQ;AAC3B,MAAI,QAAQ,KAAK,SAAS,MAAM;AAChC,MAAI,QAAQ,KAAK,SAAS,MAAM;AAChC,MAAI,QAAQ,UAAU,OAAO,OAAO,SAAS,KAAK,SAAS;AAC3D,MAAI,aAAa;AACf,QAAI,QAAQ,IAAI,cAAc,OAAO,KAAK,IAAI,cAAc,KAAK;AAC/D,aAAO;AAAA,QACL,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,WAAW,QAAQ,IAAI,cAAc,KAAK,KAAK,IAAI,cAAc,OAAO;AACtE,WAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,4BAA4B;AACjC,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,WAAW,gBAAc;AAC5B,UAAI,WAAW,SAAS;AACtB,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AACA,SAAK,wBAAwB,SAAO;AAClC,UAAI,iBAAiB,KAAK,QAAQ,QAAQ;AAC1C,UAAI,SAAS,sBAAsB,IAAI,SAAS;AAChD,WAAK,gBAAgB,CAAC,CAAC,eAAe,QAAQ,cAAc;AAC5D,WAAK,eAAe,CAAC,CAAC,eAAe,QAAQ,cAAc,QAAQ;AAAA,IACrE;AACA,SAAK,sBAAsB,SAAO;AAChC,UAAI;AAAA,QACF,SAAAE;AAAA,MACF,IAAI;AACJ,UAAI;AAAA,QACF,iBAAAC;AAAA,MACF,IAAI;AACJ,UAAI,gBAAgBD,SAAQ,eAAe;AAE3C,UAAI,CAACC,iBAAgB,gBAAgB;AACnC,YAAI,cAAc;AAAA,QAElB,CAAC,KAAK,2BACJ;AACA,cAAI,eAAeD,SAAQ,QAAQ;AACnC,cAAI,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,gBAAgB;AAC1D,YAAAA,SAAQ,YAAY,SAAS,GAAG;AAAA,UAClC;AAAA,QACF;AACA,YAAI,cAAc;AAAA,QAElB,CAAC,KAAK,cACJ;AACA,UAAAA,SAAQ,SAAS;AAAA,YACf,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,4BAA4B;AAAA,IACnC;AACA,QAAI,kBAAkB,KAAK,kBAAkB,IAAI,gBAAgB,QAAQ;AACzE,oBAAgB,mBAAmB;AACnC,oBAAgB,oBAAoB;AACpC,oBAAgB,QAAQ,GAAG,eAAe,KAAK,qBAAqB;AACpE,oBAAgB,QAAQ,GAAG,aAAa,KAAK,mBAAmB;AAIhE,YAAQ,QAAQ,GAAG,UAAU,KAAK,QAAQ;AAAA,EAC5C;AAAA,EACA,UAAU;AACR,SAAK,QAAQ,QAAQ,IAAI,UAAU,KAAK,QAAQ;AAChD,SAAK,gBAAgB,QAAQ;AAAA,EAC/B;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,mBAAmB;AACrB;AACA,IAAM,oBAAoB;AAAA,EACxB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,cAAc;AAAA,EACd,YAAY;AACd;AAOA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,UAAU,kBAAkB;AACtC,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,kBAAkB,QAAM;AAC3B,WAAK,WAAW,KAAK,cAAc,GAAG,SAAS;AAAA,IACjD;AACA,SAAK,kBAAkB,CAAC,KAAK,SAAS,OAAO;AAC3C,UAAI;AAAA,QACF,UAAAH;AAAA,MACF,IAAI,KAAK;AACT,UAAI,mBAAmB;AACvB,UAAI,iBAAiB;AACrB,UAAI,YAAY;AAChB,UAAI,cAAc;AAAA,QAChB,gBAAgB,sBAAsB;AAAA,QACtC,eAAe,sBAAsB;AAAA,QACrC,SAAS,KAAK,SAAS;AAAA,MACzB;AACA,UAAI,KAAK;AACP,2BAAmB,IAAI;AACvB,YAAI,KAAK,oBAAoB,GAAG,WAAW,gBAAgB,GAAG;AAC5D,2BAAiB,wBAAwB,IAAI,UAAU,KAAK,UAAU,gBAAgB;AACtF,sBAAY,gBAAgB,kBAAkB,cAAc;AAC5D,sBAAY,CAAC,mBAAmB,aAAa,IAAI,aAAa,gBAAgB;AAC9E,cAAI,WAAW;AACb,wBAAY,gBAAgB,sBAAsB;AAClD,6BAAiB;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,WAAK,YAAY,kBAAkB,WAAW;AAG9C,MAAAA,UAAS,mBAAmB,WAAW,CAAC,kBAAkB,CAAC,SAAS,cAAc,kBAAkB,CAAC;AACrG,UAAI,CAAC,WAAW;AACd,qBAAa;AAAA,MACf,OAAO;AACL,sBAAc;AAAA,MAChB;AACA,UAAI,CAAC,SAAS;AACZ,QAAAA,UAAS,qBAAqB,CAAC,cAAc;AAC7C,aAAK,mBAAmB;AACxB,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACF;AACA,SAAK,gBAAgB,SAAO;AAC1B,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,UAAU;AACf,UAAI,oBAAoB,gBAAgB;AACtC,YAAI,WAAW,KAAK,YAAY;AAChC,YAAI,YAAY,SAAS,QAAQ;AACjC,YAAI,WAAW,KAAK;AACpB,yBAAiB,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,6BAA6B,SAAS,UAAU,gBAAgB,CAAC,GAAG;AAAA,UAC3I,WAAW,IAAI;AAAA,UACf,SAAS,IAAI;AAAA,UACb,MAAM;AAAA,QACR,CAAC,CAAC;AACF,YAAI,SAAS,QAAQ;AACnB,cAAI,eAAe,kBAAkB,cAAc;AACnD,2BAAiB,SAAS;AAAA,YACxB,MAAM;AAAA,YACN,YAAY;AAAA,UACd,CAAC;AACD,cAAI,IAAI,SAAS;AACf,6BAAiB,SAAS;AAAA,cACxB,MAAM;AAAA,cACN,iBAAiB,eAAe,SAAS;AAAA,YAC3C,CAAC;AAAA,UACH;AAEA,2BAAiB,QAAQ,QAAQ,gBAAgB;AAAA,YAC/C,OAAO,IAAI,UAAU,kBAAkB,eAAe,KAAK,eAAe,QAAQ;AAAA,YAClF,eAAe,CAAC;AAAA,YAChB,SAAS;AACP,+BAAiB,SAAS;AAAA,gBACxB,MAAM;AAAA,gBACN,YAAY;AAAA,cACd,CAAC;AAAA,YACH;AAAA,YACA,WAAW,IAAI;AAAA,YACf,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,mBAAmB;AACxB,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,cAAc,KAAK,cAAc,IAAI,YAAY,UAAU,wBAAwB;AACvF,gBAAY,iBAAiB;AAC7B,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,aAAa,KAAK,eAAe;AACxD,gBAAY,QAAQ,GAAG,WAAW,KAAK,aAAa;AACpD,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,cAAc,WAAW;AACvB,QAAI,OAAO,KAAK,qBAAqB,UAAU;AAC7C,aAAO,cAAc,KAAK,gBAAgB;AAAA,IAC5C;AACA,QAAI,OAAO,KAAK,qBAAqB,YAAY;AAC/C,aAAO,cAAc,KAAK,iBAAiB,SAAS,CAAC;AAAA,IACvD;AACA,WAAO,kBAAkB,SAAS;AAAA,EACpC;AAAA,EACA,YAAY,aAAa,OAAO;AAC9B,QAAI,cAAc,KAAK;AACvB,QAAI,eAAe,gBAAgB,aAAa;AAC9C,kBAAY,SAAS;AAAA,QACnB,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AACA,QAAI,aAAa;AACf,kBAAY,SAAS;AAAA,QACnB,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,SAAS;AAAA,QAC7B,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,oBAAoB,IAAI,kBAAkB;AACxC,QAAI,aAAa,iBAAiB,QAAQ;AAC1C,QAAI,OAAO,eAAe,YAAY;AACpC,aAAO,WAAW,KAAK,iBAAiB,aAAa,EAAE;AAAA,IACzD;AACA,QAAI,OAAO,eAAe,YAAY,YAAY;AAChD,aAAO,QAAQ,eAAe,IAAI,UAAU,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,wBAAwB,UAAU,UAAU,SAAS;AAC5D,MAAI,WAAW,OAAO,OAAO,CAAC,GAAG,SAAS,aAAa;AACvD,WAAS,aAAa,QAAQ,YAAY,uBAAuB;AAC/D,WAAO,OAAO,UAAU,UAAU,UAAU,QAAQ,CAAC;AAAA,EACvD;AACA,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,eAAe,UAAU,OAAO;AACpC,MAAI,MAAM;AAAA,IAAc;AAAA,IAAS;AAAA,IAAO,SAAS;AAAA,IAAU,SAAS;AAAA,IAAQ,QAAQ,QAAQ,sBAAsB,QAAQ,SAAS,QAAQ;AAAA;AAAA,IAE3I;AAAA,EAAO;AACP,MAAI,QAAQ,SAAS,MAAM;AAG3B,MAAI,SAAS,UAAU,SAAS,WAAW;AACzC,YAAQ,QAAQ,QAAQ,IAAI,OAAO,SAAS,SAAS;AAAA,EACvD;AACA,MAAI,MAAM,SAAS,WAAW,QAAQ,QAAQ,IAAI,OAAO,SAAS,QAAQ,IAAI,mBAAmB,SAAS,QAAQ,OAAO,OAAO;AAChI,MAAI,WAAW,oBAAoB,IAAI,OAAO;AAAA,IAC5C;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,kBAAkB,IAAI;AAC7B,MAAI,MAAM,kBAAkB,IAAI,OAAO;AACvC,MAAI,MAAM,MAAM,KAAK,MAAM,GAAG,IAAI;AAAA,IAChC,QAAQ;AAAA,EACV;AACA,SAAO,cAAc,GAAG;AAC1B;AACA,OAAO,iBAAiB;AACxB,SAAS,kBAAkB,IAAI,MAAM;AACnC,MAAI,SAAS,OAAO;AACpB,MAAI,gBAAgB,SAAS,SAAS,MAAM,MAAM;AAClD,SAAO,GAAG,aAAa,UAAU,YAAY,KAAK;AACpD;AAOA,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,IAAI,WAAW,CAAC,GAAG;AAC7B,SAAK,oBAAoB,QAAM;AAC7B,UAAI;AAAA,QACF,UAAAA;AAAA,MACF,IAAI;AACJ,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,MAAAA,UAAS,cAAc,eAAe,OAAO,cAAc,GAAG,UAAU,IAAI,qBAAqB;AACjG,MAAAA,UAAS,QAAQ,GAAG;AAAA;AAAA,QAEpB,kBAAkB,OAAO,iBAAiB,qBAAqB;AAAA,UAAiB;AAAA,IAClF;AACA,SAAK,kBAAkB,QAAM;AAC3B,UAAI,GAAG,WAAW,KAAK,SAAS,SAAS,GAAG,UAAU,UAAU,SAAS,UAAU,GAAG;AACpF,aAAK,SAAS,OAAO,YAAY,EAAE,UAAU,IAAI,mBAAmB;AAAA,MACtE;AAAA,IACF;AACA,SAAK,WAAW;AAChB,QAAI,WAAW,KAAK,WAAW,IAAI,0BAA0B,EAAE;AAC/D,aAAS,qBAAqB;AAC9B,QAAI,SAAS,gBAAgB,MAAM;AACjC,eAAS,QAAQ,WAAW,SAAS;AAAA,IACvC;AACA,QAAI,SAAS,YAAY,MAAM;AAC7B,eAAS,OAAO,aAAa,SAAS;AAAA,IACxC;AACA,aAAS,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACzD,aAAS,QAAQ,GAAG,aAAa,KAAK,eAAe;AACrD,QAAI,wBAAwB,UAAU,SAAS,SAAS;AAAA,EAC1D;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AACF;AAQA,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EACpD,YAAY,aAAa;AACvB,UAAM,WAAW;AACjB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB,QAAM;AAC7B,WAAK,QAAQ,QAAQ,eAAe,EAAE;AACtC,UAAI,CAAC,KAAK,kBAAkB;AAE1B,aAAK,QAAQ,QAAQ,aAAa,EAAE;AAAA,MACtC;AAAA,IACF;AACA,SAAK,oBAAoB,QAAM;AAC7B,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,QAAQ,QAAQ,YAAY,EAAE;AAAA,MACrC;AAAA,IACF;AACA,SAAK,kBAAkB,QAAM;AAC3B,WAAK,QAAQ,QAAQ,aAAa,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB;AAE1B,aAAK,QAAQ,QAAQ,WAAW,EAAE;AAAA,MACpC;AAAA,IACF;AACA,QAAI,UAAU,KAAK,UAAU,IAAI,gBAAgB,WAAW;AAC5D,YAAQ,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACxD,YAAQ,QAAQ,GAAG,eAAe,KAAK,iBAAiB;AACxD,YAAQ,QAAQ,GAAG,aAAa,KAAK,eAAe;AAAA,EACtD;AAAA,EACA,UAAU;AACR,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EACA,cAAc,MAAM;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,mBAAmB,MAAM;AACvB,QAAI,MAAM;AAGR,UAAI,KAAK,iBAAiB;AACxB,aAAK,gBAAgB,MAAM,aAAa;AACxC,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF,OAAO;AACL,UAAI,WAAW,KAAK,iBAElB,SAAS,cAAc,KAAK,cAAc,IAAI;AAChD,UAAI,UAAU;AACZ,aAAK,kBAAkB;AACvB,iBAAS,MAAM,aAAa;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AACF;AAMA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,qBAAqB,UAAU;AACzC,QAAI,cAAc;AAClB;AAAA;AAAA,MAEA,wBAAwB,YAAY,+BAA+B;AAAA,MAAS;AAC1E,oBAAc;AACd,iBAAW,YAAY,CAAC;AAAA,IAC1B,OAAO;AACL,iBAAW,uBAAuB,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,KAAK,WAAW,IAAI,wBAAwB,WAAW;AACtE,QAAI,OAAO,SAAS,iBAAiB,UAAU;AAC7C,eAAS,QAAQ,WAAW,SAAS;AAAA,IACvC,WAAW,gBAAgB,UAAU;AACnC,eAAS,QAAQ,WAAW;AAAA,IAC9B;AACA,QAAI,OAAO,SAAS,mBAAmB,UAAU;AAC/C,eAAS,iBAAiB,SAAS;AAAA,IACrC;AACA,QAAI,mBAAmB,IAAI,wBAAwB,UAAU,SAAS,SAAS;AAG/E,qBAAiB,YAAY,oBAAoB;AAAA,EACnD;AAAA,EACA,UAAU;AACR,SAAK,SAAS,QAAQ;AAAA,EACxB;AACF;AACA,IAAI,QAAQ,aAAa;AAAA,EACvB,MAAM;AAAA,EACN,uBAAuB,CAAC,cAAc,eAAe,eAAe,aAAa;AAAA,EACjF,sBAAsB,CAAC,YAAY;AAAA,EACnC,qBAAqB;AAAA,EACrB,gBAAgB;AAAA,EAChB,kBAAkB;AACpB,CAAC;", "names": ["dragging", "component", "options", "context", "documentPointer"]}