<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div>
      <h2 class="mb-0" style="cursor: pointer">
        <strong>群組</strong>
      </h2>
    </div>
  </c-col>
</c-row>
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'my_team'"
            (click)="setShowCard('my_team')"
            class="custom-link m"
            >我的團隊</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'my_team'"
            class="custom-link active"
            >我的團隊</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'enterprise_team'"
            (click)="setShowCard('enterprise_team')"
            class="custom-link m"
            >企業團隊</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'enterprise_team'"
            class="custom-link active"
            >企業團隊</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>
<!-- 法人列表 -->
<div>
  <c-card
    class="mb-4"
    *ngIf="my_team_list.length > 0 && showCard == 'my_team'"
  >
    <c-card-header
      class="d-flex justify-content-between align-items-center flex-wrap"
    >
      <!-- 左側：建立法人 -->
      <div class="d-flex align-items-center gap-2">
        <button
          class="btn shadow-sm bg-white"
          style="
            border: none;
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            font-size: 1.5rem;
            line-height: 1;
          "
          title="建立法人"
          (click)="create_team()"
        >
          +
        </button>
      </div>
      <div class="input-group shadow-sm" style="max-width: 300px">
        <span class="input-group-text bg-white border-end-0">
          <svg cIcon name="cilSearch" size="lg" title="搜尋我的團隊"></svg>
        </span>
        <input
          cInput
          type="text"
          class="form-control border-start-0"
          placeholder="搜尋"
          [(ngModel)]="searchMyTeamText"
        />
      </div>
    </c-card-header>

    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex justify-content-between align-items-center"
        *ngFor="let team of filteredMyTeam(); let i = index"
      >

        <div
          class="flex-grow-1"
          style="cursor: pointer"
          [routerLink]="['/team', team.team_code, 'dashboard']"
        >
          <div class="fw-bold">{{ team.team_name }}</div>
          <div class="text-muted small">
            {{ team.team_code }}
          </div>
        </div>


        <c-dropdown>
          <button
            cDropdownToggle
            [caret]="false"
            class="btn bg-white d-flex justify-content-center align-items-center"
            style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="black"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="5" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="12" cy="19" r="2" />
            </svg>
          </button>
          <ul cDropdownMenu>
            <a
              cDropdownItem
              class="text-danger"
              style="cursor: pointer"
            >
              離開
            </a>
          </ul>
        </c-dropdown>
      </div>
    </div>
  </c-card>
</div>
<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="my_team_list.length == 0 && showCard == 'my_team'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未加入企業團隊</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="create_team()"
            >此</span
          >創建或<span
          class="fw-bold text-primary"
          style="cursor: pointer"
          (click)="setShowCard('enterprise_team')"
          >加入</span
        >開始體驗IMA-One
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>
<div>
  <c-card
    class="mb-4"
    *ngIf="enterprise_team_list.length > 0 && showCard == 'enterprise_team'"
  >
    <c-card-header
      class="d-flex justify-content-between align-items-center flex-wrap"
    >
    <strong>列表</strong>
      <div class="d-flex align-items-center gap-2">
      </div>
      <div class="input-group shadow-sm" style="max-width: 300px">
        <span class="input-group-text bg-white border-end-0">
          <svg cIcon name="cilSearch" size="lg" title="搜尋我的團隊"></svg>
        </span>
        <input
          cInput
          type="text"
          class="form-control border-start-0"
          placeholder="搜尋"
          [(ngModel)]="searchEnterpriseTeamText"
        />
      </div>
    </c-card-header>

    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex justify-content-between align-items-center"
        *ngFor="let team of filteredEnterpriseTeam(); let i = index"
      >

        <div
          class="flex-grow-1"
          style="cursor: pointer"
          [routerLink]="['/team', team.team_code, 'dashboard']"
        >
          <div class="fw-bold">{{ team.team_name }}</div>
          <div class="text-muted small">
            {{ team.team_code }}
          </div>
        </div>


        <c-dropdown>
          <button
            cDropdownToggle
            [caret]="false"
            class="btn bg-white d-flex justify-content-center align-items-center"
            style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="black"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="5" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="12" cy="19" r="2" />
            </svg>
          </button>
          <ul cDropdownMenu *ngIf="isNotInMyTeamList(team.team_code)">
            <a cDropdownItem
            style="cursor: pointer"
            (click)="apply_entering_team(team.team_code)">
              申請加入
            </a>
          </ul>
        </c-dropdown>
      </div>
    </div>
  </c-card>
</div>
<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="enterprise_team_list.length == 0 && showCard == 'enterprise_team'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未創建企業團隊</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="create_team()"
            >此</span
          >創建開始體驗IMA-One
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>
