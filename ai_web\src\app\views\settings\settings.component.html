<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div>
      <h2 class="mb-0" style="cursor: pointer">
        <strong>個人設定</strong>
      </h2>
    </div>
  </c-col>
</c-row>

<!-- 分頁標籤 -->
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2" *ngFor="let tab of tabs">
          <a
            cNavLink
            *ngIf="showCard !== tab.key"
            (click)="setShowCard(tab.key)"
            class="custom-link"
            >{{ tab.label }}</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === tab.key"
            class="custom-link active"
            >{{ tab.label }}</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>
<!-- 個人資訊卡片 -->
<c-card *ngIf="showCard === 'info'" class="mb-4">
  <c-card-header>
    <strong>個人資訊</strong>
  </c-card-header>
  <c-card-body class="row g-3">
    <!-- 頭像區塊（置中 + Email 在上） -->
    <div class="col-12 mb-4 d-flex flex-column align-items-center">
      <div class="mb-2 text-muted small">{{ user.email }}</div>
      <div class="avatar-container">
        <img
          [src]="previewImage || profile_picture || 'assets/images/avatars/default-user.png'"
          class="avatar-img"
          alt="預覽"
          (click)="avatarInput.click()"
        />
        <div class="avatar-hover-text">點擊更換圖片</div>
        <input #avatarInput type="file" (change)="onFileSelected($event)" accept="image/*" hidden />
      </div>
    </div>

    <div class="col-md-6">
      <label class="form-label">姓氏</label>
      <input cInput class="form-control" [(ngModel)]="user.lastname" />
    </div>
    <div class="col-md-6">
      <label class="form-label">名字</label>
      <input cInput class="form-control" [(ngModel)]="user.firstname" />
    </div>
    <div class="col-md-6">
      <label class="form-label">聯絡電話</label>
      <input cInput class="form-control" [(ngModel)]="user.phone" maxlength="10" minlength="10" pattern="[0-9]*" placeholder="0233445678"/>
    </div>
    <div class="col-12">
      <label class="form-label">通知設定</label>
      <div class="form-check form-switch" *ngIf="user.notification_method">
        <input
          class="form-check-input"
          type="checkbox"
          [(ngModel)]="user.notification_method.mail" />
        <label class="form-check-label">Mail 通知</label>
      </div>
    </div>
  </c-card-body>

  <c-card-footer class="text-end">
    <button cButton class="btn btn-primary" variant="outline" (click)="update_self_settings()">
      儲存變更
    </button>
  </c-card-footer>
</c-card>



<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="!user.payment_method  && showCard == 'payment'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未設定個人付款方式</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="togglePaymentModal()"
            >此</span
          >設定付款方式
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>
<div class="border rounded shadow-sm p-3 bg-white" *ngIf="user.payment_credit_card && user.payment_method == 'credit'  && showCard == 'payment'">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h6 class="mb-0 fw-bold text-primary">付款方式：信用卡</h6>
    <button
      cButton
      class="btn btn-sm btn-primary text-white"
      (click)="togglePaymentModal()"
    >
      變更付款方式
    </button>
  </div>

  <div class="row g-3">
    <div class="col-12 col-md-6">
      <div class="text-muted small">持卡人姓名</div>
      <div class="fw-semibold">{{ user.payment_credit_card.name }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">卡號</div>
      <div class="fw-semibold">
        **** **** **** {{ user.payment_credit_card.number.slice(-4) }}
      </div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">有效期限</div>
      <div class="fw-semibold">
        {{ user.payment_credit_card.expMonth }}/{{ user.payment_credit_card.expYear }}
      </div>
    </div>
  </div>
</div>

<div
  class="border rounded shadow-sm p-3 bg-white"
  *ngIf="user.payment_bank && user.payment_method == 'bank' && showCard == 'payment'"
>
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h6 class="mb-0 fw-bold text-primary">付款方式：銀行帳戶</h6>
    <button
      cButton
      class="btn btn-sm btn-primary text-white"
      (click)="togglePaymentModal()"
    >
      變更付款方式
    </button>
  </div>

  <div class="row g-3">
    <div class="col-12 col-md-6">
      <div class="text-muted small">戶名</div>
      <div class="fw-semibold">{{ user.payment_bank.name }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">總行代碼</div>
      <div class="fw-semibold">{{ user.payment_bank.bankCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">分行代碼</div>
      <div class="fw-semibold">{{ user.payment_bank.branchCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">帳號</div>
      <div class="fw-semibold">{{ user.payment_bank.accountNumber }}</div>
    </div>
  </div>
</div>



<!-- 收款方式 -->
<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="!user.payout_bank  && showCard == 'payout'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未設定個人收款帳戶</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="togglePayoutModal()"
            >此</span
          >設定收款帳戶
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>

<div
  class="border rounded shadow-sm p-3 bg-white"
  *ngIf="user.payout_bank && showCard == 'payout'"
>
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h6 class="mb-0 fw-bold text-primary">收款帳戶資料</h6>
    <button
      cButton
      class="btn btn-sm btn-primary text-white"
      (click)="togglePayoutModal()"
    >
      變更收款帳戶
    </button>
  </div>

  <div class="row g-3">
    <div class="col-12 col-md-6">
      <div class="text-muted small">戶名</div>
      <div class="fw-semibold">{{ user.payout_bank.name }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">總行代碼</div>
      <div class="fw-semibold">{{ user.payout_bank.bankCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">分行代碼</div>
      <div class="fw-semibold">{{ user.payout_bank.branchCode }}</div>
    </div>
    <div class="col-12 col-md-6">
      <div class="text-muted small">帳號</div>
      <div class="fw-semibold">{{ user.payout_bank.accountNumber }}</div>
    </div>
  </div>
</div>

<c-modal [visible]="paymentModalVisible" size="lg">
  <c-modal-header>
    <h5 cModalTitle>設定付款方式</h5>
    <button cButtonClose (click)="togglePaymentModal()"></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mb-3">
      <label class="form-label fw-bold d-block">選擇付款方式</label>
      <div class="d-flex gap-4">
        <div class="form-check">
          <input
            class="form-check-input"
            type="radio"
            name="paymentMethod"
            id="credit"
            value="credit"
            [(ngModel)]="paymentMethod"
          />
          <label class="form-check-label fw-semibold" for="credit">
            信用卡付款
          </label>
        </div>
        <div class="form-check">
          <input
            class="form-check-input"
            type="radio"
            name="paymentMethod"
            id="bank"
            value="bank"
            [(ngModel)]="paymentMethod"
          />
          <label class="form-check-label fw-semibold" for="bank">
            銀行帳戶匯款
          </label>
        </div>
      </div>
    </div>

    <!-- 信用卡 -->
    <div *ngIf="paymentMethod === 'credit'" class="mt-4">
      <h6 class="fw-bold mb-3">信用卡資料</h6>
      <div class="mb-3">
        <label class="form-label">卡號 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入 16 位數卡號"
          [(ngModel)]="creditCard.number"
        />
      </div>
      <div class="mb-3">
        <label class="form-label">持卡人姓名 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="如信用卡上所示"
          [(ngModel)]="creditCard.name"
        />
      </div>
      <div class="row g-3">
        <div class="col">
          <label class="form-label">有效月 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="MM"
            [(ngModel)]="creditCard.expMonth"
          />
        </div>
        <div class="col">
          <label class="form-label">有效年 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="YYYY"
            [(ngModel)]="creditCard.expYear"
          />
        </div>
        <div class="col">
          <label class="form-label">安全碼 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="CVV"
            [(ngModel)]="creditCard.cvv"
          />
        </div>
      </div>
    </div>

    <!-- 銀行帳戶 -->
    <div *ngIf="paymentMethod === 'bank'" class="mt-4">
      <h6 class="fw-bold mb-3">銀行帳戶資料</h6>
      <div class="mb-3">
        <label class="form-label">戶名 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入帳戶戶名"
          [(ngModel)]="bankAccount.name"
        />
      </div>
      <div class="row g-3">
        <div class="col">
          <label class="form-label">總行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 004"
            [(ngModel)]="bankAccount.bankCode"
          />
        </div>
        <div class="col">
          <label class="form-label">分行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 1234"
            [(ngModel)]="bankAccount.branchCode"
          />
        </div>
      </div>
      <div class="mt-3">
        <label class="form-label">帳號 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入完整7-14碼帳號"
          [(ngModel)]="bankAccount.accountNumber"
        />
      </div>
    </div>
  </c-modal-body>

  <c-modal-footer class="flex-column align-items-start gap-2">

    <div class="form-check ms-2">
      <input
        class="form-check-input"
        type="checkbox"
        id="confirmDataCorrect"
        [(ngModel)]="confirmDataCorrect"
      />
      <label class="form-check-label" for="confirmDataCorrect">
        我已確認付款資料正確無誤並同意<a
        href="/terms/create_user"
        target="_blank"
        rel="noopener noreferrer"
      >
      平台使用條款
      </a>
      </label>
    </div>

    <div class="w-100 d-flex justify-content-end gap-2">
      <button
        cButton
        variant="ghost"
        (click)="togglePaymentModal()"
      >
        取消
      </button>
      <button
        cButton
        color="primary"
        class="text-white"
        (click)="savePayment()"
        [disabled]="isFormInvalid() || !confirmDataCorrect"
      >
        確認儲存
      </button>
    </div>

  </c-modal-footer>

</c-modal>



<c-modal [visible]="payoutModalVisible" size="lg">
  <c-modal-header>
    <h5 cModalTitle>設定收款帳戶</h5>
    <button cButtonClose (click)="togglePayoutModal()"></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mt-2">
      <h6 class="fw-bold mb-3">銀行帳戶資料</h6>
      <div class="mb-3">
        <label class="form-label">戶名 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入帳戶戶名"
          [(ngModel)]="bankAccount.name"
        />
      </div>
      <div class="row g-3">
        <div class="col">
          <label class="form-label">總行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 004"
            [(ngModel)]="bankAccount.bankCode"
          />
        </div>
        <div class="col">
          <label class="form-label">分行代號 <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            placeholder="如 1234"
            [(ngModel)]="bankAccount.branchCode"
          />
        </div>
      </div>
      <div class="mt-3">
        <label class="form-label">帳號 <span class="text-danger">*</span></label>
        <input
          type="text"
          class="form-control"
          placeholder="請輸入完整7-14碼帳號"
          [(ngModel)]="bankAccount.accountNumber"
        />
      </div>
    </div>
  </c-modal-body>

  <c-modal-footer class="flex-column align-items-start gap-2">

    <div class="form-check ms-2">
      <input
        class="form-check-input"
        type="checkbox"
        id="confirmDataCorrect"
        [(ngModel)]="confirmDataCorrect"
      />
      <label class="form-check-label" for="confirmDataCorrect">
        我已確認收款資料正確無誤並同意<a
        href="/terms/create_user"
        target="_blank"
        rel="noopener noreferrer"
      >
      平台使用條款
      </a>
      </label>
    </div>

    <div class="w-100 d-flex justify-content-end gap-2">
      <button
        cButton
        variant="ghost"
        (click)="togglePayoutModal()"
      >
        取消
      </button>
      <button
        cButton
        color="primary"
        class="text-white"
        (click)="savePayout()"
        [disabled]="isFormInvalid('payout') || !confirmDataCorrect"
      >
        確認儲存
      </button>
    </div>

  </c-modal-footer>

</c-modal>
