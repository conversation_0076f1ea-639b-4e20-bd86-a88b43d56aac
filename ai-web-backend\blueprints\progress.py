from flask import Blueprint, request, jsonify
import redis, json
from accessories import get_user_info, update_json_in_mongo, mongo
import time

progress_page = Blueprint('progress', __name__)

@progress_page.route('/Create_Progress/<token>', methods=['GET'])
def Create_Progress(token):
    collection = mongo.db["event"]
    filter_cond = {
    "$or": [
        {
            "Execution Status": "未送件",
            "Case_Handler": get_user_info(token, 'firstname')
        },
        {
            "Execution Status": "待接案"
        }
    ]
    }
    projection = {"place_id": 1, "_id": 0, "Execution Status": 1}  
    docs = list(collection.find(filter_cond, projection))
    projects = [
        {
            "name":   d.get("place_id"),
            "status": d.get("Execution Status")
        }
        for d in docs
    ]
    result = {
        "statuses": ["待接案", "未送件"],
        "projects": projects
    }
    return jsonify(result), 200

@progress_page.route('/Update_Progress/<token>', methods=['POST'])
def Update_Progress(token):
    data = request.get_json(force=True)
    place_id = data.get('place_id')
    new_status   = data.get('status')
    first_name = get_user_info(token, 'firstname')
    doc = mongo.db.event.find_one({"place_id":place_id})
    _id = doc["_id"]

    update_json_in_mongo(
        {"Execution Status": new_status, "Case_Handler": first_name},
        collection_name="event",
        doc_name=_id,
        save_history=True  # 可選要不要留歷史紀錄
    )
    return 0
'''
    return jsonify({
        'statuses': statuses,
        'projects': projects
    }), 200'''