from flask import Blueprint, send_from_directory, current_app, request
from accessories import sqldb
import os
from flask import request, Blueprint, jsonify, current_app, send_file
from sqlalchemy import text
from io import BytesIO

download_page = Blueprint('download', __name__)

@download_page.route('/ht1/<ht1_version>', methods=['GET'])
def ht1(ht1_version):
    directory = os.path.join(current_app.config['HT1_ADDRESS'], ht1_version)
    file_name = 'main'
    if os.path.isfile(os.path.join(directory, file_name)):
        return send_from_directory(directory, file_name, as_attachment=True)
    else:
        return "File not found", 404
    
@download_page.route('/delta_api', methods=['OPTIONS', 'POST'])
def delta_api():
    data = request.get_json()
    device_id = data.get("device_id")
    start_date = data.get("start_date")
    end_date = data.get("end_date")
    order = data.get("order")
    limit = data.get("limit")
    engine = sqldb.get_engine(current_app, bind='197')
    if limit > 10000:
        return jsonify({"message": "out of search limit"}), 404
    
    if device_id not in ['10518604922_010se', '10518604922_020se', 
                         '10518604922_110se', '10518604922_120se', 
                         '10518604922_130se', '10518604922_140se', 
                         '10518604922_150se', '10518604922_160se', 
                         '10518604922_310se', '07398939003_010se',
                         '07398939003_110se', '07398939003_210se',
                         '07398939003_310se', '07398939003_320se',
                         '07398939003_330st', '07398939003_331st',
                         '08871617156_010se']: ## list here
        return jsonify({"message": "device id not valid"}), 404

    with engine.connect() as connection:
        if start_date == '' or end_date == '':
            query = f"""
            SELECT * 
            FROM ima_thing.{device_id} 
            ORDER BY id {order} 
            LIMIT {limit};
        """
        else:
            query = f"""
                SELECT * 
                FROM ima_thing.{device_id} 
                WHERE id BETWEEN '{start_date}' AND '{end_date}' 
                ORDER BY id {order} 
                LIMIT {limit};
            """
        table = connection.execute(text(query))
        if not table.rowcount:
            return jsonify({"message": "No matching tables found!"}), 404

        # 初始化 result
        result = {column: [] for column in table.keys()}

        # 遍歷每一列數據
        for row in table:
            for column, value in zip(table.keys(), row):
                if isinstance(value, bytes):
                    result[column].append(value.decode('utf-8'))
                else:
                    result[column].append(value)

        # 將結果轉換成所需格式
        output = [{column: values} for column, values in result.items()]
    return jsonify({
        "data": output
    }), 200