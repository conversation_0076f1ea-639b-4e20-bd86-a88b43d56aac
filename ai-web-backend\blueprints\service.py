from flask import request, Blueprint, jsonify, current_app, send_file, send_from_directory, abort, url_for
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required, role_required
from accessories import mongo,update_json_in_mongo,save_json_to_mongo,sqldb,crawler_redis_client
from sqlalchemy import text
from flask_sqlalchemy import SQLAlchemy
from bson.objectid import ObjectId
from datetime import datetime
import json
service_page = Blueprint('service', __name__)

@service_page.route('/get_place_id_service_list/<place_id>', methods=['OPTIONS', 'GET'])
@roles_required(['project leader'])
def get_place_id_service_list(token,place_id):   
    place_id_info = mongo.db.place_id.find_one({"place_id": place_id})
    if place_id_info == None:
        place_id_service_list = None
    else:
        place_id_service_list = place_id_info.get('service_list')
    return jsonify({
        "token": get_refresh_token(token),
        "place_id_service_list": place_id_service_list}), 200
@service_page.route('/get_service_info/<service_id>', methods=['OPTIONS', 'GET'])
@roles_required(['project leader'])
def get_service_info(token,service_id):
    service = mongo.db.service.find_one({"service_id": service_id},{"_id": 0})
    return jsonify({"token": get_refresh_token(token),'data':service}), 200
@service_page.route('/create_service', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_service(token):   
    data=request.get_json()
    print(data)
    place_id = data.get('place_id')
    service_name = data.get('servicename')
    service_type = data.get('servicetype')
    project_code = data.get('project_code')
    place_id_info = mongo.db.place_id.find_one({"place_id": place_id})
    if not place_id_info:
        print(456)
        service_id = f"s{place_id}{format(0,'03X')}"
        save_data={
            'place_id':place_id,
            'service_list':[service_id]
        }
        save_json_to_mongo(data_dict= save_data,collection_name='place_id',document_name=ObjectId())
    else:
        print(456)
        created_service = place_id_info.get('service_list',[])
        max_hex = 0
        prefix = f"s{place_id}"
        for s in created_service:
            if len(s) == len(prefix) + 3:
                try:
                    hex_part = s[-3:]
                    dec_val = int(hex_part, 16)
                    if dec_val > max_hex:
                        max_hex = dec_val
                        print(max_hex)
                except ValueError:
                    continue  
        service_id = f's{place_id}{format(max_hex + 1, "03X")}'
        created_service.append(service_id)
        update_json_in_mongo(data={'service_list':created_service},collection_name='place_id',doc_name= place_id_info.get('_id'))
    print(service_id)
    project =mongo.db.project.find_one({"project_code": project_code})
    project_service_list = project.get('service_list',[])
    project_service_list.append(service_id)
    update_json_in_mongo(data={'service_list':project_service_list},collection_name='project',doc_name=project.get('_id'))
    save_json_to_mongo(data_dict={'service_name':service_name,'service_id':service_id,'service_type': service_type},collection_name='service',document_name=ObjectId())
    return jsonify({"token": get_refresh_token(token)}), 200
@service_page.route('/get_show_service_dict', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_show_service_dict(token):
    data=request.get_json()
    result = []
    for service_id in data:
        service = mongo.db.service.find_one({"service_id": service_id})
        result.append({'service_name':service.get('service_name'),'service_id':service.get('service_id'),'service_type':service.get('service_type')})
    return jsonify({"token": get_refresh_token(token),'result':result}), 200
@service_page.route('/get_requester_info/<email>', methods=['OPTIONS', 'GET'])
@roles_required(['project leader'])
def get_requester_info(token,email):
    user = mongo.db.user.find_one({"email": email})
    data = {user.get('firstname')},{user.get('lastname')}
    return jsonify({"token": get_refresh_token(token),'data':data}), 200
@service_page.route('/get_tpc_bill_info/<place_id>', methods=['OPTIONS', 'GET'])
@roles_required(['project leader'])
def get_tpc_bill_info(token,place_id):
    print(place_id)
    name = f"{get_user_info(token,'firstname')},{get_user_info(token,'lastname')}"
    sql = text("""
    SELECT *
    FROM tpc_bills
    WHERE place_id = :place_id
    AND bill_year_month = (
        SELECT MAX(bill_year_month)
        FROM tpc_bills
        WHERE place_id = :place_id
    )
    """)
    result = sqldb.session.execute(sql, {"place_id": place_id})
    rows = result.mappings().all()  # ✅ 將每筆 row 變成 dict-like 物件
    data = [dict(row) for row in rows]
    if data == []:
        return jsonify({'error': 'Failed to fetch data'}), 500
    data = data[0]
    print(data)

    return jsonify({"token": get_refresh_token(token),'data':data}), 200
@service_page.route('/electricity_plan_change_request_submit', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def electricity_plan_change_request_submit(token):
    event_id = datetime.now().strftime("%Y%m%d_%H%M%S") + f"{int(datetime.now().microsecond / 1000):03d}"
    while True:
        event_id_list = [doc["event_id"] for doc in mongo.db.events.find({}, {"event_id": 1, "_id": 0})]
        if event_id  not in event_id_list :
            break
        event_id = datetime.now().strftime("%Y%m%d_%H%M%S") + f"{int(datetime.now().microsecond / 1000):03d}"
    data = request.get_json()  # 取得 JSON 資料
    place_id=data.get('place_id_ebpps_customer_address')[:11]
    event_data ={
        "event_id":event_id,
        "event_type": "electricity_plan_change",
        "project_code": f"{data.get('project_code')}",
        "project_name": f"{data.get('project_name')}",
        "service_id": f"{data.get('service_id')}",
        "role": {
            "project_leader": f"{get_user_info(token,'email')}",
        },
        "original_type": {
            "type": f"{data.get('electricity_type')}",
            "time": f"{data.get('time_type')}",
            "contract_peak": f"{data.get('contract_peak')}",
            "contract_semipeak": f"{data.get('contract_semipeak')}",
            "contract_offpeak": f"{data.get('contract_offpeak')}",
            "contract_saturday": f"{data.get('contract_saturday')}"
        },
        "changed_type": {
            "type": f"{data.get('new_power_type')}",
            "time": f"{data.get('new_time_tariff')}",
            "contract_peak": f"{data.get('new_peak_capacity')}",
            "contract_semipeak": f"{data.get('new_halfpeak_capacity')}",
            "contract_offpeak": f"{data.get('new_contract_offpeak')}",
            "contract_saturday": f"{data.get('new_saturday_capacity')}"
        },
          "place_id_info": {
    "company_id_name": f"{data.get('company_id_name')}",
    "current_usage": f"{data.get('current_usage')}",
    "place_id_ebpps_customer_address": f"{data.get('place_id_ebpps_customer_address')}",
    "bill_address": f"{data.get('bill_address')}",
        },
        "create_time" : datetime.now(),
        "last_updated": datetime.now(),
        "place_id":place_id,
        "stage": "application",
        "status": "in_process",
    }
    save_json_to_mongo(event_data,'events',ObjectId())
    service = mongo.db.service.find_one({'service_id':f"{data.get('service_id')}"})
    event_list = service.get('events_list',[])
    event_list.append(f"{data.get('service_id')}")
    update_json_in_mongo({'events_list':event_list },'service',service.get('_id'))
    place_id=data.get('place_id_ebpps_customer_address')[:11]
    company_id = data.get('company_id_name')[:8]
    redis_data ={ "Title": "XOB1用電變更(設備不變)",
        "部門": "IMA_One",
        "電號用電地址": f"{place_id}",
        "統編戶名": f"{company_id}",
        "現場用途": f"{data.get('current_usage')}",
        "原用電種類": f"{data.get('electricity_type')}",
        "欲變更用電種類": f"{data.get('new_power_type')}",
        "原時間電價": f"{data.get('time_type')}",
        "欲變更時間電價": f"{data.get('new_time_tariff')}",
        "原尖峰契約容量": f"{data.get('contract_peak')}",
        "欲變更尖峰契約容量": f"{data.get('new_peak_capacity')}",
        "原半尖峰契約容量": f"{data.get('contract_semipeak')}",
        "欲變更半尖峰契約容量": f"{data.get('new_halfpeak_capacity')}",
        "原週六契約容量": f"{data.get('contract_saturday')}",
        "欲變更週六契約容量": f"{data.get('new_saturday_capacity')}", 
        "契約移入移出說明": f"{data.get('contract_note')}"}
    crawler_redis_client.rpush('fill_in_bizform',json.dumps(redis_data))
    return jsonify({"token": get_refresh_token(token)}), 200
@service_page.route('/get_event_info_list/<service_id>', methods=['OPTIONS', 'GET'])
@roles_required(['project leader'])
def get_event_list_info(token,service_id):
    result = list(mongo.db.events.find({"service_id":service_id},{"_id": 0}))
    print(result)
    return jsonify({"token": get_refresh_token(token),'data':result}), 200    
@service_page.route('/get_not_acceptance_number_event_info_list', methods=['OPTIONS', 'GET'])
@roles_required(['TPC_Officer'])
def get_not_acceptance_number_event_info_list(token):
    result = list(mongo.db.events.find({
    "$and": [
        {"Acceptance_Number": {"$exists": False}},  # "Acceptance_Number" 字段不存在
        {"role.TPC_Officer": {"$exists": False}}   # "role.TPC_Officer" 字段不存在
    ]
    }, {"_id": 0}))
    return jsonify({"token": get_refresh_token(token),'data':result}), 200    

@service_page.route('/take_case/<event_id>', methods=['OPTIONS', 'GET'])
@roles_required(['TPC_Officer'])
def take_case(token,event_id):
    print(event_id)
    event = mongo.db.events.find_one({"event_id": event_id})
    print(event)
    event['role']['TPC_Officer'] =f"{get_user_info(token,'email')}"
    update_json_in_mongo(event,"events",event.get('_id'))
    return jsonify({"token": get_refresh_token(token)}), 200    
'''{
  "_id": ObjectId(),
  "event_id": time(year/month/day/hour/minute/second/milisecond) (檢查唯一),
  "event_type": "TPC electricity type application",
  "service_id" : "service_id"
  "role": {
    "project leader": "email",
    "TPC_Officer": "email"
  },
  "original_type": {
    "type": "低壓需量",
    "time": "二段式",
    "contract_peak": 49,
    "contract_semipeak": 10,
    "contract_offpeak": 0,
    "contract_saturday": 0
  },
  "changed_type": {
    "type": "低壓需量",
    "time": "三段式",
    "contract_peak": 45,
    "contract_semipeak": 0,
    "contract_offpeak": 0,
    "contract_saturday": 0
  },
  "place_id_info": {
    "統編戶名(英文)": "42838254今時科技股份有限公司",
    "現場用途(英文)": "辦公室",
    ...
  },
  "stage": "電力公司受理(英文)",
  "status": "In Progress"
  "create_time": datetime,
  "last_updated": datetime
  "Acceptance_Number": 
  "Case_Handler": 
  "Order_Acceptance_Date": 
  'Execution Status":
  "Process Form Number":
  "place_id":
}

pending
in_process
done
canceled
error


*/
'''



'''
    {
        "Title": "XOB1用電變更(設備不變)",
        "部門": "pi",
        "電號用電地址": "09087035207",
        "統編戶名": "53389851",
        "現場用途": "糕餅",
        "原用電種類": "低壓需量",
        "欲變更用電種類": "低壓需量",
        "原時間電價": "三段式",
        "欲變更時間電價": "三段式",
        "原尖峰契約容量": "27",
        "欲變更尖峰契約容量": "24",
        "原半尖峰契約容量": "4",
        "欲變更半尖峰契約容量": "8",
        "原週六契約容量": "0",
        "欲變更週六契約容量": "0", 
        "契約移入移出說明": "移轉3尖峰至4半尖峰"
    }
'''