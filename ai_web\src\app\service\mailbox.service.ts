import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { BehaviorSubject,Observable, of } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class MailboxService {
  private unreadMailSubject = new BehaviorSubject<boolean>(false);
  unreadMail$ = this.unreadMailSubject.asObservable(); // 讓其他元件訂閱它
  constructor(private http: HttpClient) { }

  loadMails(page: number = 1, pageSize: number = 10): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/mailbox/load_mails`, {   page, page_size: pageSize}, { headers });
  }
  responceMail(apiUrl: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}${apiUrl}`, {}, { headers });
  }
  viewMailDetails(mail_id:number | string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/mailbox/view_mail_details/${mail_id}`, { mail_id }, { headers });
  }
  getUnreadMailCount(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/mailbox/unread_mails_count`, {  }, { headers });
  }
  updateUnreadMailStatus(status: boolean): void {
    this.unreadMailSubject.next(status); // 發送未讀狀態變更
  }
}
