import { GroupService } from './../../../service/group.service';
import { Component, OnInit } from '@angular/core';
import { GatewayService } from '../../../service/gateway.service';
import { ProjectService } from '../../../service/project.service';
import { DashboardService } from '../../../service/dashboard.service';
import { SharedModule, enterprise, team } from '../../../shared/shared.module';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';




@Component({
  selector: 'app-list',
  standalone: true,
  imports: [ SharedModule ],
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.scss'],
})
export class OverviewComponent implements OnInit {
  searchETText = '';
  searchTeamText = '';
  create_enterprise_modal_visible = false;
  showCard = 'enterprise';
  enterprise_list: enterprise[] = [];
  team_list: team[] = [];
  team_enterprise_name_list: string[] = [];
  apply_team_visible: boolean = false;
  inputTeamId: string = '';
  constructor(
    private router: Router,
    private GroupService: GroupService,
    private ProjectService: ProjectService,
    private DashboardService: DashboardService
  ) {
  }

  ngOnInit(): void {
    this.GroupService.get_user_enterprise().subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        for (let enterprise of data.enterprises){
          this.enterprise_list.push({
            taxId: enterprise.taxId,
            name: enterprise.name,
            address: enterprise.address,
            telephone: enterprise.telephone
        });
      }

      },
      (error) => {
        console.error('Error fetching user enterprises:', error);
      }
    );
    this.GroupService.get_user_team().subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        for (let team of data.teams){
          this.team_list.push({
            taxId: team.taxId,
            team_name: team.team_name,
            team_code: team.team_code,
            accounts: []
          });
          this.GroupService.searchEnterpriseId(team.taxId).subscribe(
            (data) => {
              localStorage.setItem('token', data.token);
              this.team_enterprise_name_list.push(data.enterprise_name);
            },
            (error) => {

            }
          )
      }

      },
      (error) => {
        console.error('Error fetching user enterprises:', error);
      }
    );
  }
  setShowCard(input: string, card_index: number = 0): void {
    this.showCard = input;
  }
  create_enterprise(){
    this.router.navigate(['/groups/create_enterprise']);
  }
  filteredEnterprise() {
    return this.enterprise_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchETText.toLowerCase())
    );
  }
  toggle_apply_team(){
    this.apply_team_visible = !this.apply_team_visible;
  }
  apply_entering_team(){
    this.GroupService.apply_entering_team(this.inputTeamId).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.toggle_apply_team();
      },
      (error) => {

      }
    )
  }
}
