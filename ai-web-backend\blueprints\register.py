from werkzeug.security import generate_password_hash
from flask_mail import Message
from flask import jsonify, request, redirect, url_for, Blueprint, current_app
import uuid
from accessories import mail, redis_client, mongo, save_json_to_mongo
from bson.objectid import ObjectId
register_page = Blueprint('register_page', __name__)

@register_page.route('/register', methods=['POST'])
def register():
    def send_verification_email(email, verification_link):
      try:
        msg = Message("Please verify your email", recipients=[email])
        msg.body = f"Click the link to verify your email: {verification_link}"
        mail.send(msg)
      except Exception:
          return jsonify({"error": "Email sending failed."}), 409

    data = request.json
    firstname = data.get('firstname')
    lastname = data.get('lastname')
    password = data.get('password')
    email = data.get('email')
    confirmPassword = data.get('confirmPassword')
    existing_user = mongo.db.user.find_one({"email": email})
    if existing_user:
        return jsonify({"error": "Email already exists."}), 409

    for key in redis_client.scan_iter("register:*"):
        try:
            user_data = redis_client.hgetall(key)
            if user_data:
                redis_email = user_data.get(b'email')
                if redis_email and redis_email.decode() == email:
                    return jsonify({"error": "Email already pending verification."}), 409
        except Exception:
            continue

    if firstname == '' or lastname == '' or password == '' or email == '' or password != confirmPassword:
        return jsonify({"error": "Invalid user info."}), 409
    
    token = str(uuid.uuid4())
    redis_key = f"register:{token}"

    redis_client.hset(redis_key, mapping={
        'firstname': firstname,
        'lastname': lastname,
        'email': email,
        'password': generate_password_hash(password)
    })
    redis_client.expire(redis_key, 3600)

    verification_link = url_for('register_page.verify_email', token=token, _external=True)

    send_verification_email(email, verification_link)

    return jsonify({"message": "Verification email sent."}), 200

@register_page.route('/verify/<token>', methods=['GET'])
def verify_email(token):
    user_data = redis_client.hgetall(f"register:{token}")

    if user_data and not mongo.db.user.find_one({"email": user_data.get(b'email').decode('utf-8')}):
        firstname = user_data.get(b'firstname').decode('utf-8')
        lastname = user_data.get(b'lastname').decode('utf-8')
        password = user_data.get(b'password').decode('utf-8')
        email = user_data.get(b'email').decode('utf-8')

        save_json_to_mongo({
                            "firstname": firstname,
                            "lastname": lastname,
                            "password": password,
                            "email": email,
                            "role": ['user'],
                            "project": [],
                            }, 'user', ObjectId())

        redis_client.delete(token)
        return redirect(current_app.config['DOMAIN_NAME']+'/register-success')

    else:
        return redirect(current_app.config['DOMAIN_NAME']+'/register-failed')