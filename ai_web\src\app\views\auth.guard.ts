import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../service/auth.service';
import { Observable, of } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const token = localStorage.getItem('token');
    const currentRoute = state.url;
    if (!token) {
      this.router.navigate(['/login']);
      return of(false);
    }

    return this.authService.verifyAccess(currentRoute).pipe(
      tap(isAuth => {
        if (!isAuth) {
          this.router.navigate(['/login']);
        }
      }),
      catchError((error) => {
        console.error('Error during authentication:', error);
        this.router.navigate(['/login']);
        return of(false);
      })
    );
  }
}


@Injectable({
  providedIn: 'root'
})
export class EnterpriseAuthGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const token = localStorage.getItem('token');
    const currentRoute = state.url;
    const taxId = currentRoute.split("/")[2];
    if (!token) {
      this.router.navigate(['/login']);
      return of(false);
    }

    return this.authService.verifyEnterpriseAccess(taxId, currentRoute).pipe(
      tap(isAuth => {
        if (!isAuth) {
          this.router.navigate(['/login']);
        }
      }),
      catchError((error) => {
        console.error('Error during authentication:', error);
        this.router.navigate(['/login']);
        return of(false);
      })
    );
  }
}


@Injectable({
  providedIn: 'root'
})
export class TeamAuthGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const token = localStorage.getItem('token');
    const currentRoute = state.url;
    const team_code = currentRoute.split("/")[2];
    if (!token) {
      this.router.navigate(['/login']);
      return of(false);
    }

    return this.authService.verifyTeamAccess(team_code, currentRoute).pipe(
      tap(isAuth => {
        if (!isAuth) {
          this.router.navigate(['/login']);
        }
      }),
      catchError((error) => {
        console.error('Error during authentication:', error);
        this.router.navigate(['/login']);
        return of(false);
      })
    );
  }
}
