<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div >
      <h2
        class="mb-0"
        style="cursor: pointer"
      >
      <a href="/groups/create_enterprise" style="text-decoration: none; color: inherit;">
        <strong>建立或加入企業法人</strong>
      </a>
      </h2>
    </div>
  </c-col>
</c-row>
<c-card class="mb-2">
  <c-card-header>
    <strong>尋找</strong>
  </c-card-header>
  <c-card-body>
    <c-row class="align-items-center mb-3">
      <c-col [sm]="3">
        <label cLabel class="mx-2 fw-bold">統一編號</label>
      </c-col>
      <c-col [sm]="6">
        <input
          cFormControl
          aria-label="統一編號"
          required
          [(ngModel)]="new_enterprise.taxId"
          maxlength="8"
          [disabled]="step > 0"
          placeholder="請輸入統一編號"
        />
      </c-col>
    </c-row>
  </c-card-body>
  <c-card-footer *ngIf="step == 0">
    <button cButton variant="outline" color="primary" style="float: right" (click)="searchEnterprise()">
      尋找
    </button>
  </c-card-footer>

</c-card>

<c-card *ngIf="step > 0" class="mb-2">
  <c-card-header >
    <strong>企業法人資料</strong>
  </c-card-header>
  <c-card-body>
    <c-row class="align-items-center mb-3">
      <c-col [sm]="3">
        <label cLabel class="mx-2 fw-bold">狀態</label>
      </c-col>
      <c-col [sm]="6">
        <span class="badge" [ngClass]="{'bg-info': registered, 'bg-success': !registered}">
          {{ registered ? '已建立' : '未建立' }}
        </span>
      </c-col>
    </c-row>
    <c-row class="align-items-center mb-3">
      <c-col [sm]="3">
        <label cLabel class="mx-2 fw-bold">登記名稱</label>
      </c-col>
      <c-col [sm]="6">
        <input
          cFormControl
          required
          [(ngModel)]="new_enterprise.name"
          [disabled]="step > 1 || registered"
          placeholder="請輸入登記名稱"
        />
      </c-col>
    </c-row>
    <c-row class="align-items-center mb-3">
      <c-col [sm]="3">
        <label cLabel class="mx-2 fw-bold">登記地址</label>
      </c-col>
      <c-col [sm]="6">
        <input
          cFormControl
          required
          [(ngModel)]="new_enterprise.address"
          [disabled]="step > 1 || registered"
          placeholder="請輸入登記地址"
        />
      </c-col>
    </c-row>
  </c-card-body>
</c-card>
<c-card *ngIf="step == 1 || step == 2" class="mb-2">
  <c-card-header >
    <strong>設定</strong>
  </c-card-header>
  <c-card-body>
    <c-row class="align-items-center mb-3">
      <c-col [sm]="3">
        <label cLabel class="mx-2 fw-bold">聯絡電話</label>
      </c-col>
      <c-col [sm]="6">
        <input
          cFormControl
          required
          [(ngModel)]="new_enterprise.telephone"
          maxlength="10"
          placeholder="請輸入聯絡電話"
        />
      </c-col>
    </c-row>

  </c-card-body>
</c-card>
<button *ngIf="step == 1 || step == 2" cButton variant="outline" color="primary" style="float: right" (click)="toggle_createEnterprise()">
  完成
</button>
<button
  *ngIf="step == 3"
  [disabled]="isEnterpriseExist()"
  cButton
  variant="outline"
  color="primary"
  style="float: right"
  (click)="toggle_applyEnterprise()"
>
  申請加入
</button>


<c-modal [visible]="create_enterprise_modal_visible" size="xl">
  <c-modal-header>
    <h5 cModalTitle>建立企業法人</h5>
    <button cButtonClose (click)="toggle_createEnterprise()"></button>
  </c-modal-header>

  <c-modal-body>
    <p>請確認下列資料無誤，並點擊「完成」以建立企業法人。</p>

    <ul class="list-group mb-3">
      <li class="list-group-item">
        <strong>統一編號：</strong> {{ new_enterprise.taxId }}
      </li>
      <li class="list-group-item">
        <strong>登記名稱：</strong> {{ new_enterprise.name }}
      </li>
      <li class="list-group-item">
        <strong>登記地址：</strong> {{ new_enterprise.address }}
      </li>
      <li class="list-group-item">
        <strong>聯絡電話：</strong> {{ new_enterprise.telephone }}
      </li>
    </ul>

    <c-form-check class="mt-3">
      <input
        cFormCheckInput
        type="checkbox"
        id="agreeTerms"
        [(ngModel)]="agreeTerms"
        name="agreeTerms"
      />
      <label cFormCheckLabel for="agreeTerms">
        我已閱讀並同意
        <a
          href="/terms/create_enterprise"
          target="_blank"
          rel="noopener noreferrer"
        >
          平台使用條款
        </a>
      </label>
    </c-form-check>
  </c-modal-body>

  <c-modal-footer>
    <button cButton variant="ghost" (click)="toggle_createEnterprise()">取消</button>
    <button
      cButton
      class="text-white mx-2"
      [disabled]="!agreeTerms"
      (click)="createEnterprise()"
    >
      確認
    </button>
  </c-modal-footer>
</c-modal>



<c-modal [visible]="apply_enterprise_modal_visible" size="xl">
  <c-modal-header>
    <h5 cModalTitle>申請加入企業法人</h5>
    <button cButtonClose (click)="toggle_applyEnterprise()"></button>
  </c-modal-header>

  <c-modal-body>
    <p>確認申請加入<strong>{{ new_enterprise.name }}</strong>？</p>
    <ul class="list-group mb-3">
      <li class="list-group-item">
        <strong>統一編號：</strong> {{ new_enterprise.taxId }}
      </li>
      <li class="list-group-item">
        <strong>登記名稱：</strong> {{ new_enterprise.name }}
      </li>
      <li class="list-group-item">
        <strong>登記地址：</strong> {{ new_enterprise.address }}
      </li>
    </ul>

    <c-form-check class="mt-3">
      <input
        cFormCheckInput
        type="checkbox"
        id="agreeTerms"
        [(ngModel)]="agreeTerms"
        name="agreeTerms"
      />
      <label cFormCheckLabel for="agreeTerms">
        我已閱讀並同意
        <a
          href="/terms/create_enterprise"
          target="_blank"
          rel="noopener noreferrer"
        >
          平台使用條款
        </a>
      </label>
    </c-form-check>
  </c-modal-body>

  <c-modal-footer>
    <button cButton variant="ghost" (click)="toggle_createEnterprise()">取消</button>
    <button
      cButton
      class="text-white mx-2"
      [disabled]="!agreeTerms"
      (click)="applyEnteringEnterprise()"
    >
      確認
    </button>
  </c-modal-footer>
</c-modal>
