<c-row class="align-items-center mb-4 px-3">
  <div *ngIf="project">
    <h2 class="mb-0" [routerLink]="['/project', project_code]" style="cursor: pointer;"><strong>{{project.project_name}} - 設定</strong></h2>
  </div>
</c-row>
<div>
  <c-card class="mb-4">
    <c-card-header class="d-flex justify-content-between align-items-center">
      <strong>專案碼：{{project_code}}</strong>
      <div class="d-flex align-items-center gap-2" style="max-width: 300px">
        <button
          class="btn shadow-sm bg-white"
          style="
            border: none;
            width: 2.25rem;
            height: 2.25rem;
            padding: 0;
            font-size: 1.25rem;
            line-height: 1;
          "
          (click)="toggle_invite_collab_modal()"
        >
          +
        </button>
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg [cIcon]="icons.cilSearch" size="xl" title="搜尋電號"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋成員名稱..."
            [(ngModel)]="searchMemberText"
          />
        </div>
      </div>
    </c-card-header>
    <c-card-body>
      <c-row *ngFor="let member of member_list; let i = index" class="mb-3">
        <c-col>
          <c-card>
            <c-card-body class="d-flex justify-content-between align-items-center">
              <div>
                <strong>{{ member.name }}</strong> <br />
                <small class="text-muted">{{ member.email }}</small> <br />
                <small class="text-muted">{{ member.role }}</small>
              </div>
            </c-card-body>
          </c-card>
        </c-col>
      </c-row>
      <c-row class="mb-3">
        <c-col>
          <c-card
            class="d-flex justify-content-center align-items-center"
            style="height: 100px; cursor: pointer;"
            (click)="toggle_invite_collab_modal()"
            (mouseenter)="hovering = true"
            (mouseleave)="hovering = false"
            [ngStyle]="hovering ? {
              transform: 'scale(1.03)',
              boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
            } : {}"
          >
            <c-card-body class="text-center">
              <span style="font-size: 2rem; color: #4CAF50;">＋</span>
              <div style="font-size: 0.9rem; color: #4CAF50;">邀請協作</div>
            </c-card-body>
          </c-card>
        </c-col>
      </c-row>

    </c-card-body>
  </c-card>
</div>

<c-modal #verticallyCenteredModal alignment="top" #scrollableLongContentModal id="liveDemoModal" [visible]="invite_collab_visible" backdrop="static" size="lg">
  <c-modal-header>
    <h5 cModalTitle>邀請協作</h5>
    <button (click)="toggle_invite_collab_modal()" cButtonClose></button>
  </c-modal-header>
    <c-modal-body>
      <c-input-group class="mb-4">
        <label [sm]="2" cCol cLabel="col" class="mx-4">
          協作者信箱
        </label>
        <input aria-label="gmail" cFormControl [(ngModel)]="collab_email" placeholder = "<EMAIL>" required/>
      </c-input-group>

      <button cButton color="primary" class="btn btn-primary" (click)="send_project_collaboration(collab_email)"  style="float: right;" variant="outline">傳送邀請</button>
    </c-modal-body>
</c-modal>
