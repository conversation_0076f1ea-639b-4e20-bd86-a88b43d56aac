import { TeamService } from './../../../service/team.service';
import { Component } from '@angular/core';
import { SharedModule, appItem, team } from '../../../shared/shared.module';
import { allApps } from '../applications';
import { SharedService } from '../../../service/shared.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OnInit } from '@angular/core';

@Component({
  selector: 'app-application-info',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './application-info.component.html',
  styleUrl: './application-info.component.scss'
})
export class ApplicationInfoComponent implements OnInit {
  appId: number;
  app: appItem | undefined;
  hasBoundPaymentAccount: boolean = false;
  hasBoundPayoutAccount: boolean = false;
  showTermsModal = false;
  termsAccepted = false;
  team: team;
  // 留言區星等
  commentRating: number = 0;
  hoverRating: number = 0;

  // 留言表單
  commentForm: FormGroup;

  constructor(
    private sharedService: SharedService,
    private fb: FormBuilder,
    private teamService: TeamService
  ) {
    this.appId = Number(this.sharedService.getRouteId('appId'));
    this.app = allApps.find(app => app.id === this.appId);

    this.commentForm = this.fb.group({
      comment: ['', [Validators.required, Validators.maxLength(200)]]
    });
  }
  ngOnInit() {
    this.teamService.get_team_info(this.sharedService.getRouteId('team_code')).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.team = data.data;

      },
      (error) => {

      }
    )
    this.teamService.check_enterprise_payment(this.sharedService.getRouteId('team_code')).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.hasBoundPaymentAccount = data.data;
      },
      (error) => {

      }
    )
    this.teamService.check_enterprise_payout(this.sharedService.getRouteId('team_code')).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.hasBoundPayoutAccount = data.data;
      },
      (error) => {

      }
    )
  }

  // 平均星等四捨五入（讓模板呼叫，不用Math.）
  getRoundedRating(): number {
    return Math.round(this.app?.rating_avg || 0);
  }

  // 留言送出
  submitComment() {
    if (this.commentForm.valid && this.commentRating && this.app) {
      const comment = this.commentForm.value.comment;
      if (!this.app.comments) this.app.comments = [];
      this.app.comments.unshift({
        user: '目前用戶',
        date: new Date().toISOString().slice(0, 10),
        text: comment,
        rating: this.commentRating
      });
      this.commentForm.reset();
      this.commentRating = 0;
      this.hoverRating = 0;
    }
  }

  // 取得應用
  getApp() {
    this.showTermsModal = true;
    this.termsAccepted = false;
  }

  // 開通應用
  confirmAppActivation() {
    this.teamService.activate_application(this.team.team_code, this.appId).subscribe(
      (data) => {
        localStorage.setItem('token', data.token)
        this.showTermsModal = false;
        alert(data.msg);
      },
      (error) => {
        alert(error.error.msg);
      }
    )
  }

  // 關閉 Modal
  closeModal() {
    this.showTermsModal = false;
  }

}
