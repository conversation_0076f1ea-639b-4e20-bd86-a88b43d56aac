import { EnterpriseService } from './../../../service/enterprise.service';
import { ProjectService } from '../../../service/project.service';
import { UserService } from '../../../service/user.service';
import { Component, inject } from '@angular/core';
import { SharedModule, enterprise } from "../../../shared/shared.module"
import { RouterLink, RouterOutlet, ActivatedRoute, Router } from '@angular/router';
import { NgScrollbar } from 'ngx-scrollbar';
import { CommonModule } from '@angular/common';
import { ProgressComponent } from '@coreui/angular';
import { OnInit } from '@angular/core';
import {
  ContainerComponent,
  ColorModeService,
  ShadowOnScrollDirective,
  SidebarBrandComponent,
  SidebarComponent,
  SidebarHeaderComponent,
  SidebarNavComponent
} from '@coreui/angular';
import { EnterpriseFooterComponent, EnterpriseHeaderComponent } from '.';
import { navItems } from './_nav';

@Component({
  selector: 'app-dashboard',
  templateUrl: './enterprise-layout.component.html',
  styleUrls: ['./enterprise-layout.component.scss'],
  standalone: true,
  imports: [
    SidebarComponent,
    SidebarHeaderComponent,
    SidebarBrandComponent,
    RouterLink,
    NgScrollbar,
    SidebarNavComponent,
    EnterpriseHeaderComponent,
    ShadowOnScrollDirective,
    ContainerComponent,
    RouterOutlet,
    EnterpriseFooterComponent,
    CommonModule,
    ProgressComponent,
    SharedModule
  ]
})
export class EnterpriseLayoutComponent implements OnInit {
  public navItems : any;
  public userRoles: string[] = [];
  enterprise: enterprise = {
    taxId: '',
    name: '',
    address: '',
    telephone: '',
    applications: []
  };
  taxId: string = '';
  readonly #colorModeService = inject(ColorModeService);
  readonly colorMode = this.#colorModeService.colorMode;
  constructor(private userService: UserService, private ProjectService: ProjectService, private route: ActivatedRoute,
    private router: Router, private EnterpriseService: EnterpriseService
  ) {
    this.taxId = this.route.snapshot.paramMap.get('taxId') || '';
  }
  ngOnInit() {
    this.EnterpriseService.get_enterprise_info(this.taxId).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.enterprise = data.data;
        this.EnterpriseService.get_user_enterprise_roles(this.enterprise.taxId).subscribe(
          (data) => {
            this.userRoles = data.roles;
            localStorage.setItem('token', data.token);
            this.filterNavItems();
          },
          (error) => {
            this.userRoles = [];
          }
        );
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }


  filterNavItems() {
    this.navItems = navItems
      .filter(item => this.isRoleAllowed(item))  // 先過濾主項目
      .map(item => {
        if (item.children) {
          // 過濾每個 item 的 children
          item.children = item.children.filter(child => this.isRoleAllowed(child));
        }
        if (item.url?.includes(':taxId') || false) {
          return {
            ...item,
            url: item.url.replace(':taxId', this.taxId)
          };
        }

        return item;
      });
  }

  isRoleAllowed(item: any): boolean {
    if (!item.roles || !Array.isArray(item.roles)) {
      return false;
    }
    // 判斷主項目或子項目的角色是否包含當前用戶的角色
    const allowed = item.roles.some((role: string) => this.userRoles.includes(role));
    return allowed;
  }


  onScrollbarUpdate($event: any) {
    // 可以根據需要處理滾動條事件
  }
}

