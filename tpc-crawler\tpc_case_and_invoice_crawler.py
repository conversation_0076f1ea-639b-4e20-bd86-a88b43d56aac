from playwright.async_api import async_playwright
from pymongo import MongoClient
import time, os, random, datetime, logging, requests, re, sys, json
from datetime import datetime, date
from redis import Redis 
from PIL import Image
from io import BytesIO
import pytesseract
import asyncio
import config
import base64
import io
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import undetected_chromedriver as uc
import os
import shutil
import subprocess
import sys
import time
from pyvirtualdisplay import Display
import ddddocr
import contextlib

MONGO_URI = "mongodb://*************:27017/"
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 0
CASE_INQUIRY_KEY = "case_inquiry_crawler_key"
MAX_RETRIES = 5

redis_client = Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
MONGO_URI = "mongodb://*************:27017/"
mongodb = MongoClient(MONGO_URI)
db = mongodb["web"]
collection = db["project_case_acceptance"]

field_mapping = {
    "LL_fmno": "Application_Number",
    "LL_applydate": "Application_Date",
    "LL_item": "Application_Item",
    "LL_custname": "Account_Name",
    "LL_address": "ebpps_customer_address",
    "LL_custno": "place_id",
    "LL_pay": "Payable_Amount",
    "LL_condition": "Payment_Status",
    "LL_unpaid": "Outstanding_Payment",
    "LL_status": "STATUS",
 
}


all_fields = [
    "Application_Number", "Payable_Amount",
    "Application_Date", "Payment_Status",
    "Application_Item", "Outstanding_Payment",
    "Account_Name", "Invoice",  # Invoice 無 ID
    "STATUS", "ebpps_customer_address",
    "place_id"
]
redis_queue_key = "invoice_sql_queue"
# 排除不要的欄位與類型
excluded_titles = {"序號", "買受人註記欄", "選擇", "動作"}
excluded_invoice_types = {
    "得抵扣之進貨及費用", "得抵扣之固定資產",
    "不得抵扣之進貨及費用", "不得抵扣之固定資產"
}

# 欄位對照表（中文 → 英文）
key_map = {
    "發票號碼": "invoice_number", "發票日期": "invoice_date",
    "銷售額合計": "total_sales_amount", "應稅銷售額": "taxable_sales_amount",
    "零稅銷售額": "zero_tax_sales_amount", "免稅銷售額": "tax_exempt_sales_amount",
    "營業稅": "vat_amount", "總計": "total_amount", "課稅別": "tax_type",
    "發票狀態": "invoice_status", "最後異動時間": "last_modified_time",
    "載具類別編號": "carrier_type_code", "載具編碼": "carrier_code",
    "格式代號": "format_code", "進/銷項": "item_type",
    "發票類別": "invoice_category", "買方統編": "buyer_id",
    "買方名稱": "buyer_name", "賣方統編": "seller_id",
    "賣方名稱": "seller_name", "寄送日期": "send_date",
    "傳送方名稱": "sender_name", "重複開立或上傳": "duplicate_flag"
}

def process_invoice_to_redis(driver):
    rows = driver.find_elements(By.XPATH, "//tr")

    for row in rows:
        cells = row.find_elements(By.TAG_NAME, "td")
        if not cells or not any(cell.get_attribute("data-title") == "發票號碼" for cell in cells):
            continue

        raw_data = {}
        for cell in cells:
            title = cell.get_attribute("data-title")
            if not title or title in excluded_titles:
                continue
            text = cell.text.strip()
            if not text:
                links = cell.find_elements(By.TAG_NAME, "a")
                if links:
                    text = links[0].text.strip()
            raw_data[title] = text

        if raw_data.get("發票日期") == "請選擇..." or raw_data.get("發票類別") in excluded_invoice_types:
            continue

        english_data = {key_map.get(k, k): v for k, v in raw_data.items()}

        # 推入 Redis queue
        redis_client.rpush(redis_queue_key, json.dumps(english_data))
        print(f"📤 已推入 Redis：{english_data.get('invoice_number')}")
        
def wait():
    time.sleep(random.uniform(2, 5))
    
def check_page(driver):
   
    body = driver.find_element(By.TAG_NAME, "body").text
    if "戶名不符" in body:
        return True

    

def case_inquiry(driver, thress_name,pl_number,case_number):
    try:
        driver.get("https://service.taipower.com.tw/wapp/newnas/nawp090.aspx")
        print("網頁載入成功")
    except Exception as e:
        print("⚠️ 網頁載入錯誤:", e)
        return False

    try:
        print("正在輸入戶名")
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, "custname"))
        )
        driver.find_element(By.ID, "custname").send_keys(thress_name)
        wait()
    except Exception as e:
        print("⚠️ 輸入戶名錯誤:", e)
        return False

    try:
        select_element = driver.find_element(By.ID, "dist")
        dropdown = Select(select_element)
        dropdown.select_by_value(pl_number)
        wait()
    except Exception as e:
        print("⚠️ 選擇區處錯誤:", e)
        return False

    try:
        input_box = driver.find_element(By.ID, "cpsno")
        input_box.clear()
        input_box.send_keys(case_number)
        wait()
    except Exception as e:
        print("⚠️ 輸入案件編號錯誤:", e)
        return False

    try:
        submit_btn = driver.find_element(By.ID, "Button2")
        submit_btn.click()
        wait()
    except Exception as e:
        print("⚠️ 送出表單錯誤:", e)
        return False

    try:
        if check_page(driver):
            print("目前沒有該筆資料或輸入錯誤")
            redis_client.rpush('case_inquiry_crawler_key', json.dumps({"thress_name": thress_name, "pl_number":pl_number,"case_number": case_number}))
            wait()
            return False
    except Exception as e:
        print("⚠️ 檢查頁面錯誤:", e)
        return False

    try:
        print("找到資料並且以插入mongodb並且以插入mongodb")
        result = {}
        for span_id, field_name in field_mapping.items():
            try:
                value = driver.find_element(By.ID, span_id).text.strip()
            except:
                value = ""
            result[field_name] = value

        for field in all_fields:
            if field not in result:
                result[field] = ""
                
                
        redis_client.rpush("case_inquiry_result_queue", json.dumps(result, ensure_ascii=False))
        print("✅ 資料已推入 Redis")

# 通知後端來取
        resp = requests.post("http://localhost:5000/crawler/receive_case_result")
        print("✅ 後端回應：", resp.status_code)
        wait()
        ##繳費情況是代繳的話轉移到下一個
        #不需繳款的話就可直接結案
        return True
    except Exception as e:
        print("⚠️ 資料處理與儲存錯誤:", e)
        return False
    
##def get_case_inquiry_data():
   ##     redis_client = Redis(host='localhost', port=6379, db=0, decode_responses=True)
    ##return redis_client.llen("case_inquiry_crawler_key") > 0

def ensure_ddddocr_model():
    import ddddocr
    model_path = os.path.join(os.path.dirname(ddddocr.__file__), 'common_old.onnx')
    
    if not os.path.exists(model_path):
        print(f"⚠️ 找不到模型檔，正在下載到 {model_path} ...")
        url = "https://github.com/sml2h3/ddddocr/releases/download/v1.4/common_old.onnx"
        response = requests.get(url)
        if response.status_code == 200:
            with open(model_path, 'wb') as f:
                f.write(response.content)
            print("✅ 模型下載完成")
        else:
            raise RuntimeError(f"❌ 模型下載失敗，HTTP 狀態碼: {response.status_code}")

# ===== 在初始化 OCR 前呼叫 =====


def create_driver():
    check_and_install_env()
    time.sleep(5)
    print("✅ 正在下載與驗證驗證碼模型")
    ensure_ddddocr_model()
    time.sleep(5)
    print("✅ 正在下載ddddocr模型完成")
    print("✅ 正在建立瀏覽器")
    display = Display(backend="xvfb", size=(1920, 1080))
    display.start()

    # ✅ 建立 Chrome 選項
    options = Options()
    options.add_argument("--headless=new")  # ✅ 新版 headless 模式
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-application-cache")
    options.add_argument("--disk-cache-size=1")
    options.add_argument("--disable-cache")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--lang=zh-TW")
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                         "AppleWebKit/537.36 (KHTML, like Gecko) "
                         "Chrome/123.0.0.0 Safari/537.36")
    options.add_experimental_option("prefs", {
        "intl.accept_languages": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"
    })

    # ✅ 啟動 Chrome 瀏覽器
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    driver.set_page_load_timeout(60)

    return driver, display

def call_tpc_case_inquiry():
    ##result = collection.find_one({"STATUS": "Pending_Progress_Inquiry"})
    result = redis_client.lpop("case_inquiry_crawler_key")
    if not result:
        print("❌ 查無此受理號碼")
        return

    print("✅ 找到資料：")
    driver = None
    display = None
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            driver, display = create_driver()
            ##region_id, application_number = result["Application_Number"].split("-")
            ##case_inquiry(driver, result["Account_Name"][:3], region_id, application_number)
            case_inquiry(driver, *(lambda d: (d["thress_name"], d["pl_number"], d["case_number"]))(json.loads(result)))
            break  # ✅ 成功則跳出 retry 迴圈
        except Exception as e:
            print(f"⚠️ 第 {attempt} 次開啟失敗：{e}")
            if attempt == MAX_RETRIES:
                print("❌ 最多嘗試次數已達上限，結束。")
        finally:
            try:
                print("✅ 無法執行正在關閉瀏覽器")
                if driver:
                    driver.quit()
                if display:
                    display.stop()
            except:
                pass

def call_tpc_invoice1_inquiry():
    result = redis_client.rpop('invoice_inquiry_crawler_key')
    if not result:
        print("❌ 查無此受理號碼")
        return

    print("✅ 找到資料：")
    driver = None
    display = None
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            driver, display = create_driver()
            ##tpc_crawler_invoice_1(driver, result["place_id"], result["Business_ID"], result["Invoice_YearMonth"])
            tpc_crawler_invoice_1(driver, *(lambda d: (d["place_id"], d["tax_ID"], d["invoice_year_month"]))(json.loads(result)))
            break
        except Exception as e:
            print(f"⚠️ 第 {attempt} 次開啟失敗：{e}")
            if attempt == MAX_RETRIES:
                print("❌ 最多嘗試次數已達上限，結束。")
        finally:
            try:
                print("✅ 無法執行正在關閉瀏覽器")
                if driver:
                    driver.quit()
                if display:
                    display.stop()
            except:
                pass
def get_tpc_case_crawler_request():
    length = redis_client.llen("case_inquiry_crawler_key")
    if  length > 0:
        return True
    else:
        return False
def get_tpc_invoice_crawler_request1():
    length = redis_client.llen("invoice_inquiry_crawler_key")
    if  length > 0:
        return True
    else:
        return False
def get_tpc_invoice_crawler_request2():
    length = redis_client.llen("invoice_data_queue")
    if  length > 0:
        return True
    else:
        return False

def call_tpc_invoice2_inquiry():
    result = redis_client.lpop("invoice_data_queue")
    if result is None:
        print("📭 Redis queue 已空，結束處理")
        return

    print("✅ 找到資料：")
    driver = None
    display = None
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            driver, display = create_driver()
           # tpc_crawler_invoice_2(driver, result["mouth_number"], result["start_value"], result["end_value"])
            tpc_crawler_invoice_2(driver, *(lambda d: (d["start_time"], d["end_time"]))(json.loads(result)))
            break
        except Exception as e:
            print(f"⚠️ 第 {attempt} 次開啟失敗：{e}")
            if attempt == MAX_RETRIES:
                print("❌ 最多嘗試次數已達上限，結束。")
        finally:
            try:
                print("✅ 無法執行正在關閉瀏覽器")
                if driver:
                    driver.quit()
                if display:
                    display.stop()
            except:
                pass



def install_package(pkg_name):
    print(f"🔧 正在安裝 {pkg_name} ...")
    try:
        subprocess.run(['sudo', 'apt', 'update'], check=True)
        subprocess.run(['sudo', 'apt', 'install', '-y', pkg_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 安裝 {pkg_name} 失敗: {e}")
        sys.exit(1)

def check_and_install_env():
 
    chrome_path = shutil.which("google-chrome") or shutil.which("chromium-browser")
    if not chrome_path:
        print("❗ 未找到 Chrome，將嘗試安裝 chromium-browser")
        install_package("chromium-browser")
        chrome_path = shutil.which("chromium-browser")
        if not chrome_path:
            print("❌ Chromium 安裝失敗，請手動安裝")
            sys.exit(1)
        else:
            print(f"✅ 已安裝 Chromium: {chrome_path}")
    else:
        print(f"✅ Chrome 存在: {chrome_path}")


    if not shutil.which("xvfb-run"):
        print("❗ 未找到 xvfb，將安裝...")
        install_package("xvfb")
    else:
        print("✅ xvfb 已安裝")


def tsleep():
    seconds = random.uniform(2, 5)
    time.sleep(seconds)
def crawler_download_invoices(driver):
    """
    下載發票資料的函數
    
    Args:
        driver: Selenium WebDriver 實例
    """
    # 找出所有 checkbox（排除 id=checkbox-all 的）
    checkboxes = driver.find_elements(By.XPATH, "//input[@type='checkbox' and starts-with(@id, 'checkbox-') and not(@id='checkbox-all')]")

    print(f"✅ 找到 {len(checkboxes)} 筆發票資料，開始逐一下載...")

    for i, checkbox in enumerate(checkboxes):
        try:
            checkbox_id = checkbox.get_attribute("id")

            # 滾動到 checkbox 可見
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
            WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, checkbox_id)))

            # 點選 checkbox
            checkbox.click()
            time.sleep(0.5)

            # 點擊下載按鈕
            download_btn = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[@title='下載電子發票證明聯(5.7)']"))
            )
            download_btn.click()

            print(f"📥 第 {i+1} 筆已點擊下載。")
            time.sleep(5)  # 可視下載時間調整

            # 取消勾選
            checkbox.click()
            time.sleep(1)

        except Exception as e:
            print(f"⚠️ 第 {i+1} 筆發生錯誤：{e}")
            continue

    print("✅ 所有項目處理完成！")
def tpc_crawler_invoice_2(driver,start_value, end_value):
    """
    台電發票爬蟲主函數
    
    Args:
        driver: Selenium WebDriver 實例
        mouth_number: 月份數字
        start_value: 開始日期元素ID ex.2025-06-01
        end_value: 結束日期元素ID ex.2025-06-10
    """
    tsleep()
    driver.get("https://www.einvoice.nat.gov.tw/accounts/login/mw?login_challenge=9fa6134d5f6d4cf680865faced614db1")
    wait = WebDriverWait(driver, 10)
    # 等待該連結可見並點擊
    tsleep()
    link = wait.until(EC.element_to_be_clickable(
        (By.CSS_SELECTOR, 'a.nav-link[title="營業人/扣繳單位"]')
    ))
    link.click()
    tsleep()
    # 輸入統一編號
    try:
        ban_input = driver.find_element(By.ID, "ban")
        ban_input.clear()
        ban_input.send_keys("********")
        print("✅ 成功輸入統一編號")
    except Exception as e:
        print("❌ 無法找到或輸入 ban 欄位:", e)
    tsleep()
    # 輸入帳號
    try:
        ban_input = driver.find_element(By.ID, "user_id")
        ban_input.clear()
        ban_input.send_keys("imapione")
        print("✅ 帳號")
    except Exception as e:
        print("❌ 無法找到或輸入 帳號 欄位:", e)
    tsleep()
    # 輸入密碼
    try:
        ban_input = driver.find_element(By.ID, "user_password")
        ban_input.clear()
        ban_input.send_keys("one@********")
        print("✅ 密碼")
    except Exception as e:
        print("❌ 無法找到或輸入 密碼 欄位:", e)
    tsleep()
    # 處理驗證碼圖片
    img_element = driver.find_element(By.CSS_SELECTOR, "span.code_num img")
    img_src = img_element.get_attribute("src")
    base64_data = img_src.split(",")[1]

    # 解碼成 PIL 圖片，轉為白底黑字
    image_data = base64.b64decode(base64_data)
    image = Image.open(BytesIO(image_data)).convert("RGBA")

    # 替換背景為白色
    white_bg = Image.new("RGBA", image.size, (255, 255, 255, 255))
    white_image = Image.alpha_composite(white_bg, image).convert("RGB")

    with contextlib.redirect_stdout(io.StringIO()):
        ocr = ddddocr.DdddOcr()
        buf = BytesIO()
        white_image.save(buf, format="PNG")
        result = ocr.classification(buf.getvalue())

    print("📌 ddddocr 辨識結果:", result)
    tsleep()
    # 輸入驗證碼
    try:
        ban_input = driver.find_element(By.ID, "captcha")
        ban_input.clear()
        ban_input.send_keys(result)
        print("✅ 成功輸入驗證碼")
    except Exception as e:
        print("❌ 無法找到或輸入驗證碼欄位:", e)
    tsleep()
    # 點擊登入按鈕
    wait = WebDriverWait(driver, 10)
    login_btn = wait.until(EC.element_to_be_clickable((By.ID, "submitBtn")))
    login_btn.click()

    wait = WebDriverWait(driver, 10)

    # 點擊功能選單
    try:
        menu_link = wait.until(EC.element_to_be_clickable((By.ID, "headingFunctionB2B_MENU")))
        menu_link.click()
        print("✅ 成功點擊功能選單")
    except Exception as e:
        print("❌ 無法找到或點擊 menu 欄位:，重新進到登入頁面", e)
        tpc_crawler_invoice_2(driver,mouth_number,start_value,end_value)
    tsleep()
    
    # 點擊下載選單
    tsleep()
    try:
        download_menu = wait.until(EC.element_to_be_clickable((By.ID, "headingFunctionB2BC_SINGLE_QRY_DOWN")))
        download_menu.click()
        print("✅ 成功點擊下載選單")
    except Exception as e:
        print("❌ 無法找到或點擊下載選單:", e)
    tsleep()
    
    # 點擊發票查詢/列印/下載
    tsleep()
    try:
        target = wait.until(EC.element_to_be_clickable(
            (By.XPATH, "//span[contains(text(), '發票查詢/列印/下載')]")
        ))
        target.click()
        print("✅ 成功點擊發票查詢/列印/下載")
    except Exception as e:
        print("❌ 無法找到或點擊發票查詢/列印/下載:", e)
    tsleep()
    
    # 設定頁面縮放
    try:
        driver.execute_script("document.body.style.zoom='30%'")
        print("✅ 成功設定頁面縮放")
    except Exception as e:
        print("❌ 無法設定頁面縮放:", e)
    
    # 選擇查詢類型
    try:
        select_element = Select(driver.find_element("id", "input01"))
        select_element.select_by_value("D")
        print("✅ 成功選擇查詢類型")
    except Exception as e:
        print("❌ 無法選擇查詢類型:", e)
    
    

    try:
          # 從 start_value 中提取月份並加上"月"，去除前導零
        month = start_value[5:7]
        mouth_number = str(int(month)) + "月"
        date_input = driver.find_element(By.ID, "dp-input-date02")
        date_input.click()
        month_button = driver.find_element(By.CSS_SELECTOR, 'button[data-test="month-toggle-overlay-0"]')
        month_button.click()
        march_button = driver.find_element(By.XPATH, f'//div[contains(@class, "dp__overlay_cell") and text()="{mouth_number}"]')
        march_button.click()
        print(f"✅ 成功選擇月份: {mouth_number}")
        time.sleep(3)
        start_date = driver.find_element(By.ID, start_value)
        start_date.click()
        time.sleep(3)

        end_date = driver.find_element(By.ID, end_value)
        end_date.click()
        print(f"✅ 成功選擇結束日期: {end_value}")
    except Exception as e:
        print(f"❌ 無法選擇開始日期 {start_value}:", e)
    tsleep()

    try:
        query_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[@title='查詢']"))
        )
        query_button.click()
        print("✅ 成功點擊查詢按鈕")
    except Exception as e:
        print("❌ 無法點擊查詢按鈕:", e)
    
    # 等待表格載入完成
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_all_elements_located((By.TAG_NAME, "tr"))
        )
        print("✅ 表格載入完成")
    except Exception as e:
        print("❌ 表格載入失敗:", e)
    
    # 持續檢查是否有下一頁按鈕
    while True:
        try:
            next_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[@title='下一頁']"))
            )
            
            # 檢查下一頁按鈕是否可用（不是 disabled）
            if next_button.is_enabled():
                print("🔄 正在處理當頁內容，...")
                process_invoice_to_redis(driver)
                tsleep()
                crawler_download_invoices(driver)
                next_button.click()
            else:
                process_invoice_to_redis(driver)
                print("✅ 抓取最後一樣資料")
                crawler_download_invoices(driver)
                print("✅ 已到最後一頁，處理完成！")
                try:
                    resp = requests.post("http://localhost:5000/crawler/insert_invoice_to_mongo", timeout=5)
                    print("✅ 後端回應：", resp.status_code, resp.json())
                except Exception as e:
                    print("❌ 呼叫後端失敗：", e)
                break
                
        except Exception as e:
            print("✅ 沒有下一頁或已到最後一頁，處理完成！")
            break

def extract_and_save_invoice_data(driver,place_id):
    # 抓取表格資料列
    rows = driver.find_elements(By.XPATH, "//tbody/tr")

    data = {}
    for row in rows:
        cells = row.find_elements(By.TAG_NAME, "td")
        
        # 排除品項表格（例如第一欄為數字序號 "1", "2"）和電費明細
        if len(cells) >= 4 and cells[0].text.strip().isdigit() and cells[1].text.strip() == "電費":
            continue
        
        # 一列含兩對 key-value
        if len(cells) >= 2:
            key1 = cells[0].text.strip()
            val1 = cells[1].text.strip()
            data[key1] = val1
        if len(cells) >= 4:
            key2 = cells[2].text.strip()
            val2 = cells[3].text.strip()
            data[key2] = val2

    print("✅ 擷取結果（已排除電費明細）：")
    for k, v in data.items():
        print(f"{k}: {v}")

    # 中英欄位對照
    key_map = {
        "發票號碼": "invoice_number",
        "賣方統一編號": "seller_id",
        "發票期別": "invoice_period",
        "買方統一編號": "buyer_id",
        "總計金額": "total_amount",
        "稅額": "tax_amount",
        "課稅別": "tax_type",
        "發票日期": "invoice_date",
        "隨機碼": "random_code",
        "電號": "meter_id",
        "稅前金額": "amount_before_tax"
    }

    english_data = {key_map.get(k, k): v for k, v in data.items()}
    
    invoice_number = english_data.get("invoice_number")
    if invoice_number:
        # 將發票資料推送到 Redis 佇列
        result = {
            "place_id": place_id,
            "invoice_data": english_data,
        }
        
        redis_client.rpush("invoice_data_queue", json.dumps(result, ensure_ascii=False))
        print(f"✅ Invoice {invoice_number} 資料已推送到 Redis 佇列")
        
        # 通知後端來取資料
    else:
        print("❌ 發票號碼遺失，無法儲存。")
        
def tpc_crawler_invoice_1(driver,custno,businessid,period):
    try:
        print("🚀 開始執行台電發票爬蟲...")
        
        # 導航到目標網頁
        try:
            print("📡 正在導航到台電發票查詢頁面...")
            driver.get("https://service.taipower.com.tw/einvoice/search_2")
            print("✅ 頁面導航成功")
        except Exception as e:
            print(f"❌ 頁面導航失敗：{str(e)}")
            raise
        tsleep()
        # 輸入客戶編號
        try:
            print("📝 正在輸入客戶編號...")
            custno_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "custno"))
            )
            custno_input.clear()
            custno_input.send_keys(custno)
            print("✅ 客戶編號輸入完成")
        except Exception as e:
            print(f"❌ 客戶編號輸入失敗：{str(e)}")
            raise
        tsleep()
        # 輸入統一編號
        try:
            print("📝 正在輸入統一編號...")
            business_id_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "businessid"))
            )
            business_id_input.clear()
            business_id_input.send_keys(businessid)
            print("✅ 統一編號輸入完成")
        except Exception as e:
            print(f"❌ 統一編號輸入失敗：{str(e)}")
            raise
        
        # 輸入期別
        try:
            print("📝 正在輸入期別...")
            period_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "period"))
            )
            period_input.clear()
            period_input.send_keys(period)
            print("✅ 期別輸入完成")
        except Exception as e:
            print(f"❌ 期別輸入失敗：{str(e)}")
            raise
        tsleep()
        # 處理驗證碼
        try:
            print("🔍 正在處理驗證碼...")
            img_element = driver.find_element(By.ID, "captcha_img")
            
          
            base64_data = driver.execute_script("""
                const img = arguments[0];
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                return canvas.toDataURL('image/png').split(',')[1];
            """, img_element)
            
            # 解碼並白底處理
            image_data = base64.b64decode(base64_data)
            image = Image.open(BytesIO(image_data)).convert("RGBA")
            white_bg = Image.new("RGBA", image.size, (255, 255, 255, 255))
            white_image = Image.alpha_composite(white_bg, image).convert("RGB")
            
            # 丟入 ddddocr
            buf = BytesIO()
            white_image.save(buf, format="PNG")
            with contextlib.redirect_stdout(io.StringIO()):
                ocr = ddddocr.DdddOcr()
            result = ocr.classification(buf.getvalue())
            
            print(f"✅ 驗證碼識別結果：{result}")
        except Exception as e:
            print(f"❌ 驗證碼處理失敗：{str(e)}")
            raise
        tsleep()
        # 輸入驗證碼
        try:
            captcha_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "picpassword"))
            )
            captcha_input.clear()
            captcha_input.send_keys(result)
            print("✅ 驗證碼輸入完成")
        except Exception as e:
            print(f"❌ 驗證碼輸入失敗：{str(e)}")
            raise
        
        # 記錄原始視窗
        try:
            original_windows = driver.window_handles
            print("✅ 原始視窗記錄完成")
        except Exception as e:
            print(f"❌ 記錄原始視窗失敗：{str(e)}")
            raise
        
        # 點擊查詢按鈕
        try:
            print("🔘 正在點擊查詢按鈕...")
            driver.find_element(By.XPATH, "//button[text()='點我查詢']").click()
            print("✅ 查詢按鈕點擊成功")
        except Exception as e:
            print(f"❌ 查詢按鈕點擊失敗：{str(e)}")
            raise
        
        # 等待新分頁出現
        try:
            print("⏳ 等待新分頁開啟...")
            WebDriverWait(driver, 10).until(lambda d: len(d.window_handles) > len(original_windows))
            print("✅ 新分頁開啟成功")
        
     
        except Exception as e:
            print(f"❌ 等待新分頁失敗：{str(e)}")
            raise
        tsleep()
        # 切換到新分頁
        try:
            new_window = [w for w in driver.window_handles if w not in original_windows][0]
            driver.switch_to.window(new_window)
            print("✅ 已切換到新分頁")
        except Exception as e:
            print(f"❌ 切換分頁失敗：{str(e)}")
            raise
        
        # 等待表格出現
        try:
            print("⏳ 等待表格載入...")
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "tbody"))
            )
            print("✅ 表格載入成功")
        except Exception as e:
            print(f"❌ 表格載入失敗：{str(e)}")
            tpc_crawler_invoice_1(driver,custno,businessid,period)
            print("✅ 驗證碼錯誤重新嘗試開啟頁面...")
            raise
        tsleep()
        # 擷取所有連結
        try:
            print("🔗 正在擷取發票連結...")
            a_tags = driver.find_elements(By.XPATH, "//tbody//a[@href]")
            hrefs = [a.get_attribute("href") for a in a_tags if a.get_attribute("href")]
            print(f"✅ 共找到 {len(hrefs)} 個發票連結")
        except Exception as e:
            print(f"❌ 擷取發票連結失敗：{str(e)}")
            raise
        
        # 處理每個連結
        for i, url in enumerate(hrefs, 1):
            try:
                print(f"📄 正在處理第 {i}/{len(hrefs)} 個發票連結...")
                driver.get(url)
                time.sleep(3)
                extract_and_save_invoice_data(driver)
                print(f"✅ 第 {i} 個發票處理完成")
            except Exception as e:
                print(f"❌ 處理第 {i} 個發票時發生錯誤：{str(e)}")
                continue
        
        print("🎉 所有發票處理完成！")
        tsleep()
        try:
                resp = requests.post("http://localhost:5000/crawler/receive_invoice_data")
                print("✅ 後端回應：", resp.status_code)
        except Exception as e:
                print(f"⚠️ 後端通知失敗：{str(e)}")
    except Exception as e:
        print(f"❌ 爬蟲執行過程中發生錯誤：{str(e)}")
        raise

        
def tpc_case_and_invoice_crawler_thread():  ##找案件 發票需求
    while True:
        tpc_case_crawler_request = get_tpc_case_crawler_request()
        if tpc_case_crawler_request:
            call_tpc_case_inquiry()
            time.sleep(random.uniform(2, 5))
        tpc_invoice_crawler_request1 = get_tpc_invoice_crawler_request1()
        if tpc_invoice_crawler_request1:
            call_tpc_invoice1_inquiry()
            time.sleep(random.uniform(2, 5))
        tpc_invoice_crawler_request2 = get_tpc_invoice_crawler_request2()
        if tpc_invoice_crawler_request2:
            call_tpc_invoice2_inquiry()
        time.sleep(random.uniform(2, 5))  # 等待一段時間後再檢查




# ############################################################
# MongoDB 測試資料插入腳本
# 用途：建立測試用的案件資料，用於測試不同狀態的爬蟲功能
# ############################################################

# from pymongo import MongoClient

# ############################################################
# 狀態對應說明
# ############################################################
# Pending_Progress_Inquiry   → 待進度查詢（案件進度查詢）
# Pending_Invoice_Check      → 待查詢是否開發票（發票查詢）
# Pending_Invoice_Issuance   → 待發票開立（發票開立）

# ############################################################
# MongoDB 連線設定
# ############################################################
# mongo_client = MongoClient("mongodb://localhost:27017/")  # 若有帳密請補在 URI 中
# db = mongo_client["web"]
# collection = db["project_case_acceptance"]

# ############################################################
# 測試資料一：進度查詢測試資料
# 用途：測試案件進度查詢功能
# ############################################################
# data1 = {
#     "Application_Number": "00-********",  # 受理號碼
#     "Application_Date": "2025-06-26",     # 申請日期
#     "Account_Name": "今時科技",           # 申請人姓名
#     "STATUS": "Pending_Progress_Inquiry", # 狀態：待進度查詢
# }
# collection.update_one(
#     {"Application_Number": data1["Application_Number"]},  # 以受理號碼為查詢條件
#     {"$set": data1},                                      # 更新或插入資料
#     upsert=True                                           # 如果不存在則插入
# )
# print(f"✅ 資料已儲存：{data1['Application_Number']}")

# ############################################################
# 測試資料二：發票查詢測試資料
# 用途：測試發票查詢功能（以 place_id + YearMonth 為條件）
# ############################################################
# data2 = {
#     "place_id": "***********",           # 區處代號
#     "Business_ID": "********",           # 統一編號
#     "Invoice_YearMonth": "11405",        # 發票年月
#     "STATUS": "Pending_Invoice_Check",   # 狀態：待查詢是否開發票
# }
# collection.update_one(
#     {
#         "place_id": data2["place_id"],                    # 以區處代號為查詢條件
#         "Invoice_YearMonth": data2["Invoice_YearMonth"]   # 以發票年月為查詢條件
#     },
#     {"$set": data2},                                      # 更新或插入資料
#     upsert=True                                           # 如果不存在則插入
# )
# print(f"✅ 資料已儲存：place_id={data2['place_id']}, 月份={data2['Invoice_YearMonth']}")

# ############################################################
# 測試資料三：發票開立測試資料
# 用途：測試發票開立功能（以日期範圍為條件）
# ############################################################
# data3 = {
#     "mouth_number": "6月",                                # 月份名稱
#     "start_value": "2025-06-01",                         # 開始日期
#     "end_value": "2025-06-05",                           # 結束日期
#     "STATUS": "Pending_Invoice_Issuance",                # 狀態：待發票開立
# }
# collection.update_one(
#     {
#         "start_value": data3["start_value"],             # 以開始日期為查詢條件
#         "end_value": data3["end_value"]                  # 以結束日期為查詢條件
#     },
#     {"$set": data3},                                     # 更新或插入資料
#     upsert=True                                          # 如果不存在則插入
# )
# print(f"✅ 資料已儲存：{data3['mouth_number']} {data3['start_value']}~{data3['end_value']}")
