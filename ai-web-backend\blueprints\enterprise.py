from flask import request, Blueprint, jsonify
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo, type_name_trans
from blueprints.api import get_user_info, get_refresh_token, verify_token, enterprise_role_required, generate_secure_random_string
from bson.objectid import ObjectId

enterprise_page = Blueprint('enterprise', __name__)

@enterprise_page.route('/get_enterprise_info', methods=['OPTIONS', 'POST', 'GET'])
@enterprise_role_required(['member'])
def get_enterprise_info(groupId, token):
    enterprise_info = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    credit_card = enterprise_info.get("credit_card", {})
    if credit_card:
        credit_card["number"] = "**** **** **** " + credit_card["number"][-4:]
        credit_card.pop("cvv", None)  # 刪掉 cvv
    enterprise_info["credit_card"] = credit_card
    return jsonify({"token": get_refresh_token(token),
                    "data": enterprise_info}), 200

@enterprise_page.route('/update_enterprise_info', methods=['OPTIONS', 'POST', 'GET'])
@enterprise_role_required(['admin'])
def update_enterprise_info(groupId, token):
    data = request.get_json()
    enterprise_name = data.get("enterprise").get("name")
    enterprise_address = data.get("enterprise").get("address")
    enterprise_telephone = data.get("enterprise").get("telephone")
    enterprise_info = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    enterprise_info["name"] = enterprise_name
    enterprise_info["address"] = enterprise_address
    enterprise_info["telephone"] = enterprise_telephone
    update_json_in_mongo(enterprise_info, 'enterprise', enterprise_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200


@enterprise_page.route('/get_user_enterprise_roles', methods=['OPTIONS', 'POST', 'GET'])
@enterprise_role_required(['member'])
def get_user_enterprise_roles(groupId, token):
    enterprise_info = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    user_email = get_user_info(token, "email")
    roles = []
    for account in enterprise_info.get("accounts"):
        if account.get("email") == user_email:
            roles = account.get("roles")
    return jsonify({"token": get_refresh_token(token),
                    "roles": roles}), 200


@enterprise_page.route('/edit_member_auth', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['admin'])
def edit_member_auth(groupId, token):
    data = request.get_json()
    applicant = data.get("applicant")
    roles = data.get('roles')
    enterprise = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    original_roles = []
    admin_count = 0
    for idx, i in enumerate(enterprise.get("accounts")):
        if i.get("email") == applicant:
            original_roles = enterprise.get("accounts")[idx]["roles"]
            enterprise.get("accounts")[idx]["roles"] = roles
        if 'admin' in enterprise.get("accounts")[idx]["roles"]:
            admin_count += 1
    
    if applicant == get_user_info(token, "email") and 'admin' not in roles and admin_count == 0:
        return jsonify({"msg": "cannot remove last admin",
                        "roles": original_roles}), 400
    update_json_in_mongo(enterprise, 'enterprise', enterprise_dbId)
    return jsonify({"token": get_refresh_token(token),}), 200


@enterprise_page.route('/reply_entering_application', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['admin'])
def reply_entering_application(groupId, token):
    data = request.get_json()
    applicant_email = data.get("applicant").get("email")
    reply = data.get("reply")
    enterprise = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    for i in enterprise.get("accounts"):
        if i.get("email") == applicant_email:
            return jsonify({"msg": "application repeated!"}), 400
    account_list = enterprise.get("accounts")
    if reply:
        account_list.append({
            "email": applicant_email,
            "roles": ['member']
        })
        enterprise["accounts"] = account_list
        applicant = mongo.db.user.find_one({"email": applicant_email}, {"_id": 0})
        applicant_dbId = mongo.db.user.find_one({"email": applicant_email}).get("_id")
        applicant_enterprise_list = applicant.get("enterprise") or []
        if groupId not in applicant_enterprise_list:
            applicant_enterprise_list.append(groupId)
        applicant['enterprise'] = applicant_enterprise_list
        update_json_in_mongo(applicant, 'user', applicant_dbId)
    
    for i in enterprise.get("applications"):
        if i.get("email") == applicant_email:
            enterprise['applications'].remove(i)
    update_json_in_mongo(enterprise, 'enterprise', enterprise_dbId)
    
    return jsonify({"token": get_refresh_token(token),}), 200


@enterprise_page.route('/remove_member', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['admin'])
def remove_member(groupId, token):
    data = request.get_json()
    member = data.get("member").get("email")
    if member == get_user_info(token, 'email'):
        return jsonify({"msg": 'Cannot remove account!'}), 400
    enterprise = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    account_list = enterprise.get("accounts")
    for i in account_list:
        if member == i.get("email"):
            account_list.remove(i)
            break
    enterprise["accounts"] = account_list
    user = mongo.db.user.find_one({"email": member}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": member}).get("_id")
    user_enterprise_list = user.get("enterprise")
    if groupId in user_enterprise_list:
        user_enterprise_list.remove(groupId)
    user["enterprise"] = user_enterprise_list
    update_json_in_mongo(user, 'user', user_dbId)
    update_json_in_mongo(enterprise, 'enterprise', enterprise_dbId)
    return jsonify({"token": get_refresh_token(token),}), 200




@enterprise_page.route('/get_member_info', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['member'])
def get_member_info(groupId, token):
    data = request.get_json()
    
    return jsonify({"token": get_refresh_token(token),}), 200


@enterprise_page.route('/create_team', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['member'])
def create_team(groupId, token):
    data = request.get_json()
    team_name = data.get("team_name")
    enterprise_info = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    user = mongo.db.user.find_one({"email": get_user_info(token, 'email')}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": get_user_info(token, 'email')}).get("_id")
    team_code = generate_secure_random_string(8)
    while mongo.db.team.find_one({"team_code": team_code}):
        team_code = generate_secure_random_string(8)
    ## update enterprise, team, user
    enterprise_team = enterprise_info.get("teams", [])
    enterprise_team.append(team_code)
    enterprise_info["teams"] = enterprise_team
    team = {
        "taxId": groupId,
        "team_code": team_code,
        "team_name": team_name,
        "accounts": [
            {
                "email": get_user_info(token, "email"),
                "roles": ['admin', 'member']
            }
        ]
    }
    user_team = user.get("team", [])
    user_team.append(team_code)
    user["team"] = user_team
    save_json_to_mongo(team, 'team', ObjectId())
    update_json_in_mongo(enterprise_info, 'enterprise', enterprise_dbId)
    update_json_in_mongo(user, 'user', user_dbId)
    return jsonify({"token": get_refresh_token(token),}), 200

@enterprise_page.route('/get_enterprise_team', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['member'])
def get_enterprise_team(groupId, token):
    enterprise_team = mongo.db.enterprise.find_one({"taxId": groupId}).get("teams")
    teams = []
    if enterprise_team:
        for team_code in enterprise_team:
            team = mongo.db.team.find_one({'team_code': team_code})
            if team:
                teams.append({
                    'team_code': team.get('team_code'),
                    'team_name': team.get('team_name'),
                    'taxId': team.get('taxId')
                })

    return jsonify({"token": get_refresh_token(token),
                    "enterprise_team": teams}), 200

@enterprise_page.route('/update_enterprise_payment', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['member'])
def update_enterprise_payment(groupId, token):
    data = request.get_json()
    payment_method = data.get("payment_method")
    
    enterprise_info = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    if payment_method not in ['credit', 'bank']:
        return jsonify({"msg": 'invalid payment method'}), 400
    enterprise_info['payment_method'] = payment_method
    if payment_method == 'credit':
        enterprise_info["payment_credit_card"] = data.get("credit_card")
    else:
        enterprise_info["payment_bank"] = data.get("bank")
    update_json_in_mongo(enterprise_info, 'enterprise', enterprise_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200

@enterprise_page.route('/update_enterprise_payout', methods=['OPTIONS', 'POST'])
@enterprise_role_required(['member'])
def update_enterprise_payout(groupId, token):
    data = request.get_json()
    enterprise_info = mongo.db.enterprise.find_one({"taxId": groupId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": groupId}).get("_id")
    enterprise_info["payout_bank"] = data.get("bank")
    update_json_in_mongo(enterprise_info, 'enterprise', enterprise_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200
