<c-header class="toolbar-container bg-white shadow-sm py-2 px-4 custom-header d-flex justify-content-between align-items-center"
[ngStyle]="{ 'margin-left': isSidebarOpen ? '60px' : '0px' ,
  'width': isSidebarOpen ? 'calc(100% - 60px)' : '100%'
}">
  <!-- 左側容器 -->
  <div style="width: 48px;" class="d-flex justify-content-start">
    <button
      cOffcanvasToggle="sideBar"
      (click)="toggleSidebar()"
      class="btn d-flex align-items-center text-white"
    >
      <svg cIcon name="cilMenu" size="lg"></svg>
    </button>
  </div>

  <!-- 中間 logo -->
  <div class="position-absolute start-50 translate-middle-x text-white fw-bold fs-5">
    <img src="assets/imalogo_white.ico" alt="Your Logo" class="sidebar-brand-full mx-1" style="cursor: pointer" height="32" width="32" [routerLink]="['/project', project_code, 'plot']">
  </div>

  <!-- 右側容器 -->
  <div style="width: 48px;" class="d-flex justify-content-start">
    <button
      cOffcanvasToggle="sideBar"
      (click)="toggleEditLock()"
      class="btn d-flex align-items-center text-white"
    >
    <ng-container *ngIf="editLock">
      <svg cIcon name="cilLockLocked"  size="lg"></svg>
    </ng-container>

    <ng-container *ngIf="!editLock">
      <svg cIcon name="cilLockUnlocked"  size="lg"></svg>
    </ng-container>



    </button>
  </div>
</c-header>


<div class="sidebar-wrapper" [ngStyle]="{ 'display': isSidebarOpen ? 'flex' : 'none'}">
  <div class="sidebar-icon-bar">
    <!-- project dropdown -->
    <c-dropdown direction="dropend" variant="btn-group" [visible]="activeMenu === 'project'">
      <button
        cButton
        color="#345830"
        class="text-white"
        cDropdownToggle
        [caret]="false"
        [class.active]="activeMenu === 'project'"
        (click)="activeMenu = activeMenu === 'project' ? null : 'project'">
        <svg cIcon name="cil3d"></svg>
      </button>
      <ul cDropdownMenu>
        <li><button cDropdownItem (click)="toggle_modal('plot_list')">視圖</button></li>
        <li><button cDropdownItem (click)="toggle_modal('place_id_list')">電號</button></li>
      </ul>
    </c-dropdown>

    <!-- component dropdown -->
    <c-dropdown direction="dropend" variant="btn-group" [visible]="activeMenu === 'component'">
      <button
        cButton
        color="#345830"
        class="text-white"
        cDropdownToggle
        [caret]="false"
        [class.active]="activeMenu === 'component'"
        (click)="activeMenu = activeMenu === 'component' ? null : 'component'">
        <svg cIcon name="cil-layers"></svg>
      </button>
      <ul cDropdownMenu>
        <li><button cDropdownItem (click)="toggle_modal('link_list')">連結</button></li>
        <li><button cDropdownItem (click)="toggle_modal('object_list')">元件</button></li>
      </ul>
    </c-dropdown>

    <!-- view dropdown -->
    <c-dropdown direction="dropend" variant="btn-group" [visible]="activeMenu === 'view'">
      <button
        cButton
        color="#345830"
        class="text-white"
        cDropdownToggle
        [caret]="false"
        [class.active]="activeMenu === 'view'"
        (click)="activeMenu = activeMenu === 'view' ? null : 'view'">
        <svg cIcon name="cil-zoom"></svg>
      </button>
      <ul cDropdownMenu>
        <li><button cDropdownItem (click)="openDeviceDashboardView()">儀表板預覽</button></li>
      </ul>
    </c-dropdown>

    <!-- export dropdown -->
    <c-dropdown direction="dropend" variant="btn-group" [visible]="activeMenu === 'export'">
      <button
        cButton
        color="#345830"
        class="text-white"
        cDropdownToggle
        [caret]="false"
        [class.active]="activeMenu === 'export'"
        (click)="activeMenu = activeMenu === 'export' ? null : 'export'">
        <svg cIcon name="cil-cloud-download"></svg>
      </button>
      <ul cDropdownMenu>
        <li><button cDropdownItem (click)="exportPNG()">圖像(.png)</button></li>
        <li><button cDropdownItem (click)="exportJPG()">圖像(.jpg)</button></li>
        <li><button cDropdownItem (click)="exportBOM()">點位表</button></li>
      </ul>
    </c-dropdown>

    <!-- settings dropdown -->
    <c-dropdown direction="dropend" variant="btn-group" [visible]="activeMenu === 'settings'">
      <button
        cButton
        color="#345830"
        class="text-white"
        cDropdownToggle
        [caret]="false"
        [class.active]="activeMenu === 'settings'"
        (click)="activeMenu = activeMenu === 'settings' ? null : 'settings'">
        <svg cIcon name="cil-settings"></svg>
      </button>
      <ul cDropdownMenu>
        <li><button cDropdownItem (click)="toggle_modal('setting')">視圖設定</button></li>
      </ul>
    </c-dropdown>
    <div class="sidebar-help-button p-2 d-flex justify-content-center">
      <button
        (click)="toggle_modal('help')"
        class="btn d-flex align-items-center text-white"
      >
        ？
      </button>
    </div>
  </div>

</div>





<c-modal
  id="helpModal"
  [visible]="helpModalVisible"
  alignment="center"
  size="lg"
>
  <c-modal-header>
    <h5 cModalTitle>幫助</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <!-- 搜尋欄 -->
    <div class="input-group shadow-sm">
      <span class="input-group-text bg-white border-end-0">
        <svg cIcon name="cil-search" size="xl" title="搜尋元件"></svg>
      </span>
      <input
        type="text"
        [(ngModel)]="accordionSearch"
        class="form-control"
        placeholder="搜尋操作說明..."
      />
    </div>
    <div class="accordion-search mb-3">


    </div>

    <c-accordion>
      <!-- 新增與刪除 -->
      <c-accordion-item #item0="cAccordionItem" [visible]="true" *ngIf="!accordionSearch || '新增與刪除'.toLowerCase().includes(accordionSearch.toLowerCase())">
        <ng-template cTemplateId="accordionHeaderTemplate">
          <button (click)="item0.toggleItem()" [collapsed]="!item0.visible" cAccordionButton>
            新增與刪除
          </button>
        </ng-template>
        <ng-template cTemplateId="accordionBodyTemplate">
          <div class="accordion-body">
            <ul>
              <li><strong>新增文字方塊：</strong> 在畫布上雙擊可建立可編輯的文字方塊。</li>
              <li><strong>新增元件：</strong> 在空白處長按 0.5 秒，可選擇「新增元件」。</li>
              <li><strong>移除背景圖：</strong> 在空白處長按 0.5 秒，可選擇「移除背景圖」。</li>
              <li><strong>上傳背景圖：</strong> 拖曳圖片檔至畫布自動上傳為背景圖。</li>
            </ul>
          </div>
        </ng-template>
      </c-accordion-item>

      <!-- 專案操作 -->
      <c-accordion-item #item1="cAccordionItem" [visible]="false" *ngIf="!accordionSearch || '專案操作'.toLowerCase().includes(accordionSearch.toLowerCase())">
        <ng-template cTemplateId="accordionHeaderTemplate">
          <button (click)="item1.toggleItem()" [collapsed]="!item1.visible" cAccordionButton>
            專案操作
          </button>
        </ng-template>
        <ng-template cTemplateId="accordionBodyTemplate">
          <div class="accordion-body">
            <ul>
              <li><strong>新增/切換/移除視圖：</strong> 點擊左側「<svg cIcon name="cil3d"></svg>」→「視圖」可進行管理。</li>
              <li><strong>新增/編輯/移除電號：</strong> 點擊左側「<svg cIcon name="cil3d"></svg>」→「電號」可進行設定。</li>
            </ul>
          </div>
        </ng-template>
      </c-accordion-item>

      <!-- 元件功能 -->
      <c-accordion-item #item2="cAccordionItem" [visible]="false" *ngIf="!accordionSearch || '元件及連結'.toLowerCase().includes(accordionSearch.toLowerCase())">
        <ng-template cTemplateId="accordionHeaderTemplate">
          <button (click)="item2.toggleItem()" [collapsed]="!item2.visible" cAccordionButton>
            元件及連結
          </button>
        </ng-template>
        <ng-template cTemplateId="accordionBodyTemplate">
          <div class="accordion-body">
            <ul>
              <li><strong>查找元件列表：</strong> 點選左側「<svg cIcon name="cil-layers"></svg>」→「元件」可開啟總表搜尋。</li>
              <li><strong>編輯元件內容：</strong> 點選 元件圖標 → 左下角「<svg cIcon name="cil-menu"></svg>」可編輯元件內容。</li>
              <li><strong>查找連結：</strong> 點選左側「<svg cIcon name="cil-layers"></svg>」→「連結」可開啟總表搜尋。</li>
            </ul>
          </div>
        </ng-template>
      </c-accordion-item>

      <!-- 視圖與輸出 -->
      <c-accordion-item #item3="cAccordionItem" [visible]="false" *ngIf="!accordionSearch || '視圖與輸出'.toLowerCase().includes(accordionSearch.toLowerCase())">
        <ng-template cTemplateId="accordionHeaderTemplate">
          <button (click)="item3.toggleItem()" [collapsed]="!item3.visible" cAccordionButton>
            視圖與輸出
          </button>
        </ng-template>
        <ng-template cTemplateId="accordionBodyTemplate">
          <div class="accordion-body">
            <ul>
              <li><strong>預覽儀表板：</strong> 點擊「<svg cIcon name="cil-zoom"></svg>」→「儀表板」可預覽目前視圖。</li>
              <li><strong>匯出圖檔/點位表：</strong> 點擊「<svg cIcon name="cil-cloud-download"></svg>」可輸出 PNG、JPG 或點位表。</li>
            </ul>
          </div>
        </ng-template>
      </c-accordion-item>

      <!-- 操作與選取 -->
      <c-accordion-item #item4="cAccordionItem" [visible]="false" *ngIf="!accordionSearch || '操作與選取'.toLowerCase().includes(accordionSearch.toLowerCase())">
        <ng-template cTemplateId="accordionHeaderTemplate">
          <button (click)="item4.toggleItem()" [collapsed]="!item4.visible" cAccordionButton>
            操作與選取
          </button>
        </ng-template>
        <ng-template cTemplateId="accordionBodyTemplate">
          <div class="accordion-body">
            <ul>
              <li><strong>拖曳移動畫面：</strong> 點擊空白區域後拖曳即可平移畫布。</li>
              <li><strong>多選元件：</strong> 按住 <kbd>Ctrl</kbd> 或 <kbd>⌘</kbd> 並框選。</li>
            </ul>
          </div>
        </ng-template>
      </c-accordion-item>

      <!-- 側邊工具列 -->
      <c-accordion-item #item5="cAccordionItem" [visible]="false" *ngIf="!accordionSearch || '視圖設定'.toLowerCase().includes(accordionSearch.toLowerCase())">
        <ng-template cTemplateId="accordionHeaderTemplate">
          <button (click)="item5.toggleItem()" [collapsed]="!item5.visible" cAccordionButton>
            視圖設定
          </button>
        </ng-template>
        <ng-template cTemplateId="accordionBodyTemplate">
          <div class="accordion-body">
            <ul>
              <li><strong>開啟設定頁面：</strong> 點選「<svg cIcon name="cil-settings"></svg>」後選擇「視圖設定」進入設定頁面</li>
            </ul>
          </div>
        </ng-template>
      </c-accordion-item>


    <!-- 離開 -->
    <c-accordion-item #item6="cAccordionItem" [visible]="false" *ngIf="!accordionSearch || '離開'.toLowerCase().includes(accordionSearch.toLowerCase())">
      <ng-template cTemplateId="accordionHeaderTemplate">
        <button (click)="item6.toggleItem()" [collapsed]="!item6.visible" cAccordionButton>
          離開編輯
        </button>
      </ng-template>
      <ng-template cTemplateId="accordionBodyTemplate">
        <div class="accordion-body">
          <ul>
            <li>點擊畫面正上方圖標即可離開</li>
          </ul>
        </div>
      </ng-template>
    </c-accordion-item>
  </c-accordion>

  </c-modal-body>
</c-modal>




<div
  id="paper"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event)"
  [ngStyle]="{ 'left': isSidebarOpen ? '60px' : '0px' }"
></div>

<div class="fixed-text"  *ngIf="!pending_save">已同步</div>
<div class="fixed-text" *ngIf="pending_save">同步中...</div>
<div class="fixed-text2" [ngStyle]="{ 'left': isSidebarOpen ? '70px' : '10px'}">{{ projectplot_name }}</div>

<c-modal
  #verticallyCenteredModal
  alignment="center"
  #scrollableLongContentModal
  id="liveDemoModal"
  [visible]="create_object_visible"
  backdrop="static"
>
  <c-modal-header >
    <h5 cModalTitle>
      創建元件
    </h5>
    <button (click)="cancel_create_object()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <!-- Object Type 下拉選單 -->
    <c-row class="mb-3">
      <c-col>
        <label cLabel class="mx-2">選擇元件類型</label>
      </c-col>
      <c-col>
        <select
          [(ngModel)]="editing_object_type"
          cSelect
          class="mt-1"
          [disabled]="editing_object_type.length>0"
        >
          <option *ngFor="let type of object_type_list" [value]="type_name_trans(type)">
            {{ type }}
          </option>
        </select>
      </c-col>
    </c-row>
    <c-row class="mb-3" *ngIf="editing_object_type=='Gateway'">
      <c-col>
        <label cLabel class="mx-2" style="color: red">注意：閘道新增後將無法移除</label>
      </c-col>
    </c-row>
    <div *ngIf="editing_object_type.length > 0" class="nav-container mb-3">
      <c-tabs-list layout="fill" variant="tabs">
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'select'"
            (click)="switchActiveTab('select')"
            style="cursor: pointer"
            >選擇</a
          >
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeTab === 'create'"
            (click)="switchActiveTab('create')"
            style="cursor: pointer"
            >新增</a
          >
        </li>
      </c-tabs-list>
    </div>


    <!-- Tab Content -->
    <div *ngIf="editing_object_type.length > 0" class="fw-bold text-dark mb-1 list-group list-group-flush">
      <div *ngIf="activeTab === 'select'">
        <c-col>
          <c-row class="mb-3 mt-2">
            <c-col>
              <label cCol cLabel="col" class="mx-2"> 選擇{{ type_name_trans(editing_object_type, true) }} </label>
            </c-col>
            <c-col>
              <select
            aria-label="Default select example"
            [(ngModel)]="selected_object_setting_index"
            cSelect
            class="mt-2 mb-3"
          >
            <option
              *ngFor="let object of filter_object_list(editing_object_type); let i = index"
              [value]="i"
            >
              {{ object.name }}({{ object.serialNo }})
            </option>
          </select>
            </c-col>
          </c-row>

          <button
            cButton
            color="primary"
            variant="outline"
            (click)="create_object()"
            class="float-end"
          >
            選擇
          </button>
        </c-col>
      </div>
      <div *ngIf="activeTab === 'create'">
        <c-col class="pl-4">
          <c-row class="mb-3 mt-2">
            <c-col>
              <label cCol cLabel="col" class="mx-2"> 所屬電號 </label>
            </c-col>
            <c-col>
              <select
                [(ngModel)]="new_place_id"
                (change)="filter_serialNo()"
                cSelect
              >
                <option *ngFor="let place_id of place_id_list">
                  {{ place_id }}
                </option>
              </select>
            </c-col>
          </c-row>
          <c-row class="mb-3">
            <c-col>
              <label cCol cLabel="col" class="mx-2"> 元件暱稱 </label>
            </c-col>
            <c-col>
              <input
                aria-label="元件名稱"
                cFormControl
                [(ngModel)]="new_object_name"
                required
              />
            </c-col>
          </c-row>

          <button
            cButton
            color="primary"
            variant="outline"
            (click)="create_object_and_setting()"
            class="float-end"
          >
            新建
          </button>
        </c-col>
      </div>
    </div>
  </c-modal-body>
</c-modal>


<c-modal
  id="liveDemoModal"
  [visible]="create_link_visible"
  scrollable
  size="lg"
>
  <c-modal-header>
    <h5 cModalTitle>新增連結圖標</h5>
    <button (click)="cancel_create_link()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <c-row class="mb-3">
      <c-col [sm]="2">
        <label cCol cLabel="col" for="project" class="mx-2"> 連結類型 </label>
      </c-col>
      <c-col [sm]="3">
        <input cFormControl [(ngModel)]="new_link_source_name" disabled required />
      </c-col>
      <c-col [sm]="1">
        <label cCol cLabel="col" class="my-2"> &#8646; </label>
      </c-col>
      <c-col [sm]="2">
        <select
          [(ngModel)]="new_link_type"
          (change)="check_link_exist()"
          cSelect
        >
          <option
            *ngFor="let link_type of connectableLinkTypeList"
            [value]="link_type"
          >
            {{ type_name_trans(link_type, true) }}
          </option>
        </select>
      </c-col>
      <c-col [sm]="1">
        <label cCol cLabel="col" class="my-2"> &#8646; </label>
      </c-col>
      <c-col [sm]="3">
        <input cFormControl [(ngModel)]="new_link_target_name" disabled required />
      </c-col>
    </c-row>
    <ng-container *ngIf="new_link_type.length > 0">
      <c-row class="mb-3">
        <c-col [sm]="2">
          <label cCol cLabel="col" for="project" class="mx-2"> 連結名稱 </label>
        </c-col>
        <c-col [sm]="10">
          <input
            cCol
            cLabel="col"
            cFormControl
            id="name"
            type="text"
            *ngIf="!repeated_link"
            value="{{ new_link_name }}"
            [(ngModel)]="new_link_name"
          />
          <input
            cCol
            cLabel="col"
            cFormControl
            id="name"
            type="text"
            *ngIf="repeated_link"
            disabled
            value="{{ new_link_name }}"
            [(ngModel)]="new_link_name"
          />
        </c-col>
      </c-row>
    </ng-container>
  </c-modal-body>
  <c-modal-footer
    *ngIf="new_link_source.length > 0 || new_link_name.length > 0"
  >
    <button
      cButton
      variant="outline"
      color="primary"
      *ngIf="new_link_type.length > 0 && !repeated_link"
      (click)="create_link()"
    >
      新增
    </button>
    <button
      cButton
      variant="outline"
      color="primary"
      *ngIf="new_link_type.length > 0 && repeated_link"
      (click)="select_link()"
    >
      選擇
    </button>
  </c-modal-footer>
</c-modal>

<c-modal id="liveDemoModal" [visible]="place_id_visible">
  <c-modal-header>
    <h5 cModalTitle>電號</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <div class="input-group shadow-sm">
      <span class="input-group-text bg-white border-end-0">
        <svg cIcon name="cil-zoom"></svg>
      </span>
      <input
        cInput
        type="text"
        class="form-control border-start-0"
        placeholder="搜尋電號..."
        [(ngModel)]="searchPlaceIdText"
      />
    </div>
    <ul class="list-group">
      <li
        class="list-group-item list-group-item-action"
        *ngFor="let place_id of filteredPlaceId()"
        style="cursor: pointer;"
      >
        {{ place_id }}
      </li>
    </ul>
  </c-modal-body>
</c-modal>


<c-modal id="liveDemoModal" [visible]="plot_list_visible">
  <c-modal-header>
    <h5 cModalTitle>視圖</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <div class="input-group shadow-sm">
      <span class="input-group-text bg-white border-end-0">
        <svg cIcon name="cil-zoom"></svg>
      </span>
      <input
        cInput
        type="text"
        class="form-control border-start-0"
        placeholder="搜尋規劃圖名稱..."
        [(ngModel)]="searchPlotText"
      />
    </div>
    <ul class="list-group">
      <li
        class="list-group-item list-group-item-action"
        *ngFor="let plot of filteredPlots()"
        (click)="navigateToEditPage(plot)"
        style="cursor: pointer;"
      >
        {{ plot }}
      </li>
    </ul>
  </c-modal-body>
</c-modal>


<c-modal id="liveDemoModal" [visible]="link_list_visible">
  <c-modal-header>
    <h5 cModalTitle>連結</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <div class="input-group shadow-sm">
      <span class="input-group-text bg-white border-end-0">
        <svg cIcon name="cil-zoom"></svg>
      </span>
      <input
        cInput
        type="text"
        class="form-control border-start-0"
        placeholder="搜尋連結名稱..."
        [(ngModel)]="searchLinkText"
      />
    </div>
    <ul class="list-group">
      <li
        class="list-group-item list-group-item-action"
        *ngFor="let link of filteredLinks()"
        style="cursor: pointer;"
      >
        {{type_name_trans(link.type, true)}}: {{link.name ? link.name : '(未命名)'}}
      </li>
    </ul>
  </c-modal-body>
</c-modal>

<c-modal id="liveDemoModal" [visible]="object_list_visible">
  <c-modal-header>
    <h5 cModalTitle>元件</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <div class="input-group shadow-sm">
      <span class="input-group-text bg-white border-end-0">
        <svg cIcon name="cil-zoom"></svg>
      </span>
      <input
        cInput
        type="text"
        class="form-control border-start-0"
        placeholder="搜尋元件名稱..."
        [(ngModel)]="searchObjectText"
      />
    </div>
    <ul class="list-group">
      <li
        class="list-group-item list-group-item-action"
        *ngFor="let object of filteredObjects(); let i = index"
        style="cursor: pointer;"
      >
        {{type_name_trans(object.type, true)}}: {{ object.name }}
      </li>
    </ul>
  </c-modal-body>
</c-modal>


<c-modal id="liveDemoModal" [visible]="object_visible" *ngIf="editingObject">
  <c-modal-header>
    <h5 cModalTitle>編輯元件</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>

    <c-row class="mb-3">
      <c-col [sm]="4">
        <label cCol cLabel="col" class="mx-2"> 元件名稱 </label>
      </c-col>
      <c-col [sm]="8">
        <input
          cFormControl
          [(ngModel)]="editingObject.name"
          required
          aria-label="元件名稱"
        />
      </c-col>
    </c-row>
    <button cButton variant="outline" color="primary" (click)="toggle_modal('gateway_config', editingObject.gateway_id || '')" *ngIf="editingObject.type === 'Gateway'">
      編輯閘道組態
    </button>
  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="primary" (click)="saveObject()">
      完成
    </button>
  </c-modal-footer>
</c-modal>

<c-modal id="liveDemoModal" [visible]="object_style_visible" *ngIf="editing_cell">
  <c-modal-header>
    <h5 cModalTitle>編輯元件樣式</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <!-- Tab 選項 -->
    <div class="nav-container mb-3">
      <c-tabs-list layout="fill" variant="tabs">
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeObjectTab === 'config'"
            (click)="switchActiveObjectTab('config')"
            style="cursor: pointer"
          >
            儀表板
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeObjectTab === 'attr'"
            (click)="switchActiveObjectTab('attr')"
            style="cursor: pointer"
          >
            屬性
          </a>
        </li>

      </c-tabs-list>
    </div>

    <!-- Tab Content -->
    <div *ngIf="activeObjectTab === 'config'">
      <!-- 儀表板序列號 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">序列號</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            disabled
            [(ngModel)]="editing_cell_dashboard_serialNo"
            required
            aria-label="序列號"
          />
        </c-col>
      </c-row>


      <!-- 儀表板名稱 -->
      <c-row class="mb-3" *ngIf="editing_cell_dashboard_visible && editing_cell_dashboard_name_visible">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">儀表板名稱</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            [(ngModel)]="editing_cell_dashboard_name"
            required
            aria-label="名稱"
          />
        </c-col>
      </c-row>



      <!-- 表格名稱 -->
      <c-row class="mb-3" *ngIf="editing_cell_dashboard_visible">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">設備ID</label>
        </c-col>
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">{{editing_cell_dashboard_serialNo.split('-')[1]}}_</label>
        </c-col>
        <c-col [sm]="4">
          <input
            cFormControl
            [(ngModel)]="editing_cell_dashboard_TableName"
            required
            aria-label="表格名稱"
            placeholder="010se"
          />
        </c-col>
      </c-row>


      <!-- 表格欄位 -->
      <c-row class="mb-3" *ngIf="editing_cell_dashboard_visible">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">欄位名稱</label>
        </c-col>
        <c-col [sm]="8">
          <select
            cFormControl
            [(ngModel)]="editing_cell_dashboard_TableColumn"
            aria-label="表格欄位"
            cSelect
          >
            <option value="">(None)</option>
            <option value="alert">alert</option>
            <option value="current">current</option>
            <option value="current_a">current_a</option>
            <option value="current_b">current_b</option>
            <option value="current_c">current_c</option>
            <option value="error">error</option>
            <option value="flow">flow</option>
            <option value="humidity">humidity</option>
            <option value="hz">hz</option>
            <option value="invstatus">invstatus</option>
            <option value="kva">kva</option>
            <option value="kvah">kvah</option>
            <option value="kvar">kvar</option>
            <option value="kvarh">kvarh</option>
            <option value="kw">kw</option>
            <option value="kwh">kwh</option>
            <option value="on_off">on_off</option>
            <option value="pf">pf</option>
            <option value="pressure">pressure</option>
            <option value="reset">reset</option>
            <option value="set_hz">set_hz</option>
            <option value="temperature">temperature</option>
            <option value="value">value</option>
            <option value="voltage">voltage</option>
            <option value="ByPass">ByPass</option>
            <option value="Consumption">Consumption</option>
            <option value="DIn01">DIn01</option>
            <option value="DIn02">DIn02</option>
            <option value="DIn03">DIn03</option>
            <option value="DIn04">DIn04</option>
            <option value="DOut01">DOut01</option>
            <option value="DOut02">DOut02</option>
            <option value="Direction">Direction</option>
            <option value="Revconsumption">Revconsumption</option>
            <option value="Threads">Threads</option>
          </select>
        </c-col>
      </c-row>

      <!-- 浮點數位數 -->
      <c-row class="mb-3" *ngIf="editing_cell_dashboard_visible">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">小數點位數</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="number"
            (input)="filterAndFillZero($event)"
            max="5"
            min="0"
            [(ngModel)]="editing_cell_dashboard_data_floatNumber"
            required
            aria-label="小數點位數"
          />
        </c-col>
      </c-row>


      <!-- 時間格式 -->
      <c-row class="mb-3" *ngIf="editing_cell_dashboard_visible && editing_cell_dashboard_time_visible">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">時間格式</label>
        </c-col>
        <c-col [sm]="8">
          <select
            cFormControl
            [(ngModel)]="editing_cell_dashboard_time"
            aria-label="時間格式"
            cSelect
          >
            <option value="%m:%d;%H:%M">MM:DD;HH:mm</option>
            <option value="%H:%M">HH:mm</option>
          </select>
        </c-col>
      </c-row>
      <!-- 是否顯示在檢視模式 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">顯示於儀表板</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true" sizing="lg">
            <input
              cFormCheckInput
              type="checkbox"
              [(ngModel)]="editing_cell_dashboard_visible"
              aria-label="顯示於檢視模式"
            />
          </c-form-check>
        </c-col>
      </c-row>
      <!-- 是否顯示名稱 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">顯示名稱</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true" sizing="lg">
            <input
              cFormCheckInput
              type="checkbox"
              [(ngModel)]="editing_cell_dashboard_name_visible"
              aria-label="顯示名稱"
            />
          </c-form-check>
        </c-col>
      </c-row>
      <!-- 是否顯示表格欄位 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">顯示欄位名稱</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true" sizing="lg">
            <input
              cFormCheckInput
              type="checkbox"
              [(ngModel)]="editing_cell_dashboard_TableColumn_visible"
              aria-label="顯示表格欄位"
            />
          </c-form-check>
        </c-col>
      </c-row>



      <!-- 是否顯示時間 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">顯示時間</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true" sizing="lg">
            <input
              cFormCheckInput
              type="checkbox"
              [(ngModel)]="editing_cell_dashboard_time_visible"
              aria-label="顯示時間"
            />
          </c-form-check>
        </c-col>
      </c-row>
    </div>
    <div *ngIf="activeObjectTab === 'attr'">
      <!-- 動態填滿開關 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">動態填滿</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true" sizing="lg">
            <input cFormCheckInput type="checkbox" [(ngModel)]="editing_cell_attr_dynamic_fillColor_available" />
          </c-form-check>
        </c-col>
      </c-row>

      <!-- 動態填滿設定：表格樣式 -->
      <ng-container *ngIf="editing_cell_attr_dynamic_fillColor_available">
        <c-row>
          <c-col>
            <table class="table table-bordered align-middle mb-2 text-center">
              <thead class="table-light">
                <tr>
                  <th style="width: 25%">閾值</th>
                  <th style="width: 25%">顏色</th>
                  <th style="width: 25%">透明</th>
                  <th style="width: 25%">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of editing_cell_attr_dynamic_fillColor; let i = index">
                  <!-- 閾值欄 -->
                  <td>
                    <input
                      type="number"
                      class="form-control form-control-sm text-center"
                      [(ngModel)]="item.threshold"
                      *ngIf="i !== 0"
                    />
                  </td>

                  <!-- 顏色欄 -->
                  <td>
                    <input
                      type="color"
                      class="form-control form-control-color"
                      [(ngModel)]="item.color"
                      [disabled]="item.transparent"
                    />
                  </td>

                  <!-- 透明欄 -->
                  <td>
                    <c-form-check [switch]="true" sizing="lg" class="d-flex justify-content-center">
                      <input cFormCheckInput type="checkbox" [(ngModel)]="item.transparent" />
                    </c-form-check>
                  </td>

                  <!-- 操作欄 -->
                  <td>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm"
                      (click)="removeColorThreshold(i)"
                      *ngIf="editing_cell_attr_dynamic_fillColor.length > 1"
                    >
                      刪除
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 新增按鈕 -->
            <div class="text-end">
              <button
                type="button"
                class="btn btn-outline-primary btn-sm mb-3"
                (click)="addColorThreshold()"
                [disabled]="editing_cell_attr_dynamic_fillColor.length >= 10"
              >
                新增填色區段
              </button>
            </div>
          </c-col>
        </c-row>
      </ng-container>



      <!-- 背景顏色 -->
      <c-row class="mb-3" *ngIf="!editing_cell_attr_dynamic_fillColor_available">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">填滿</label>
        </c-col>
        <c-col [sm]="2">
          <input
            cFormControl
            type="color"
            [(ngModel)]="editing_cell_attr_fillColor.color"
            [disabled]="editing_cell_attr_fillColor.transparent"
            required
          />
        </c-col>
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">透明</label>
        </c-col>
        <c-col [sm]="2">
          <c-form-check [switch]="true" sizing="lg">
            <input cFormCheckInput type="checkbox" [(ngModel)]="editing_cell_attr_fillColor.transparent"/>
          </c-form-check>
        </c-col>
      </c-row>

      <!-- 邊框寬度 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">邊框寬度</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="number"
            (input)="filterAndFillZero($event)"
            [(ngModel)]="editing_cell_attr_strokeWidth"
            required
            aria-label="寬度"
          />
        </c-col>
      </c-row>

      <!-- 邊框顏色 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">邊框顏色</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="color"
            [(ngModel)]="editing_cell_attr_strokeColor"
            required
            aria-label="邊框顏色"
          />
        </c-col>
      </c-row>

      <!-- 字體大小 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">字體大小</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="number"
            (input)="filterAndFillZero($event)"
            [(ngModel)]="editing_cell_attr_fontSize"
            required
            aria-label="字體大小"
          />
        </c-col>
      </c-row>

      <!-- 字體顏色 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">字體顏色</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="color"
            [(ngModel)]="editing_cell_attr_fontColor"
            required
            aria-label="字體顏色"
          />
        </c-col>
      </c-row>
    </div>
  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="primary" (click)="saveCellStyle()">
      完成
    </button>
  </c-modal-footer>
</c-modal>


<c-modal id="liveDemoModal" [visible]="link_visible" *ngIf="editingLink">
  <c-modal-header>
    <h5 cModalTitle>編輯連結</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <c-row class="mb-3">
      <c-col [sm]="4">
        <label cCol cLabel="col" class="mx-2"> 連結名稱 </label>
      </c-col>
      <c-col [sm]="8">
        <input
          cFormControl
          [(ngModel)]="editingLink.name"
          required
          aria-label="元件名稱"
        />
      </c-col>
    </c-row>
  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="primary" (click)="saveLink()">
      完成
    </button>
  </c-modal-footer>
</c-modal>

<c-modal id="liveDemoModal" [visible]="link_style_visible">
  <c-modal-header>
    <h5 cModalTitle>編輯連結樣式</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <!-- Tab 選項 -->
    <div class="nav-container mb-3">
      <c-tabs-list layout="fill" variant="tabs">
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeObjectTab === 'config'"
            (click)="switchActiveObjectTab('config')"
            style="cursor: pointer"
          >
            儀表板
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeObjectTab === 'attr'"
            (click)="switchActiveObjectTab('attr')"
            style="cursor: pointer"
          >
            屬性
          </a>
        </li>

      </c-tabs-list>
    </div>

    <!-- Tab Content -->
    <div *ngIf="activeObjectTab === 'config'">
      <!-- 儀表板序列號 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">序列號</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            disabled
            [(ngModel)]="editing_link_dashboard_serialNo"
            required
            aria-label="序列號"
          />
        </c-col>
      </c-row>

      <!-- 是否顯示在檢視模式 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">顯示於儀表板</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true">
            <input
              cFormCheckInput
              type="checkbox"
              [(ngModel)]="editing_link_dashboard_visible"
              aria-label="顯示於檢視模式"
            />
          </c-form-check>
        </c-col>
      </c-row>



    </div>
    <div *ngIf="activeObjectTab === 'attr'">
      <!-- 線條顏色 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">線條顏色</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="color"
            [(ngModel)]="editing_link_attr_strokeColor"
            required
          />
        </c-col>
      </c-row>
      <!-- 線條寬度 -->
       <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">線條寬度</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="number"
            (input)="filterAndFillZero($event)"
            [(ngModel)]="editing_link_attr_strokeWidth"
            required
            aria-label="寬度"
          />
        </c-col>
        </c-row>
    </div>
  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="primary" (click)="saveLinkStyle()">
      完成
    </button>
  </c-modal-footer>
</c-modal>



<c-modal id="liveDemoModal" [visible]="textBlock_style_visible">
  <c-modal-header>
    <h5 cModalTitle>編輯文字方塊樣式</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <!-- Tab 選項 -->
    <div class="nav-container mb-3">
      <c-tabs-list layout="fill" variant="tabs">
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeObjectTab === 'config'"
            (click)="switchActiveObjectTab('config')"
            style="cursor: pointer"
          >
            儀表板
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeObjectTab === 'attr'"
            (click)="switchActiveObjectTab('attr')"
            style="cursor: pointer"
          >
            屬性
          </a>
        </li>

      </c-tabs-list>
    </div>

    <!-- Tab Content -->
    <div *ngIf="activeObjectTab === 'config'">
      <!-- 是否顯示在檢視模式 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">顯示於儀表板</label>
        </c-col>
        <c-col [sm]="8">
          <c-form-check [switch]="true" sizing="lg">
            <input
              cFormCheckInput
              type="checkbox"
              [(ngModel)]="editing_textBlock_dashboard_visible"
              aria-label="顯示於檢視模式"
            />
          </c-form-check>
        </c-col>
      </c-row>
    </div>
    <div *ngIf="activeObjectTab === 'attr'">
      <!-- 背景顏色 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">填滿</label>
        </c-col>
        <c-col [sm]="2">
          <input
            cFormControl
            type="color"
            [(ngModel)]="editing_textBlock_attr_fillColor.color"
            [disabled]="editing_textBlock_attr_fillColor.transparent"
            required
          />
        </c-col>
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">透明</label>
        </c-col>
        <c-col [sm]="2">
          <c-form-check [switch]="true" sizing="lg">
            <input cFormCheckInput type="checkbox" [(ngModel)]="editing_textBlock_attr_fillColor.transparent"/>
          </c-form-check>
        </c-col>
      </c-row>
      <!-- 線條顏色 -->
      <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">外框顏色</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="color"
            [(ngModel)]="editing_textBlock_attr_strokeColor"
            required
          />
        </c-col>
      </c-row>
      <!-- 線條寬度 -->
       <c-row class="mb-3">
        <c-col [sm]="4">
          <label cCol cLabel="col" class="mx-2">外框寬度</label>
        </c-col>
        <c-col [sm]="8">
          <input
            cFormControl
            type="number"
            (input)="filterAndFillZero($event)"
            [(ngModel)]="editing_textBlock_attr_strokeWidth"
            required
            aria-label="寬度"
          />
        </c-col>
        </c-row>
        <c-row class="mb-3">
          <c-col [sm]="4">
            <label cCol cLabel="col" class="mx-2">字體大小</label>
          </c-col>
          <c-col [sm]="8">
            <input
              cFormControl
              type="number"
              (input)="filterAndFillZero($event)"
              [(ngModel)]="editing_textBlock_attr_fontSize"
              required
              aria-label="字體大小"
            />
          </c-col>
        </c-row>

        <!-- 字體顏色 -->
        <c-row class="mb-3">
          <c-col [sm]="4">
            <label cCol cLabel="col" class="mx-2">字體顏色</label>
          </c-col>
          <c-col [sm]="8">
            <input
              cFormControl
              type="color"
              [(ngModel)]="editing_textBlock_attr_fontColor"
              required
              aria-label="字體顏色"
            />
          </c-col>
        </c-row>
    </div>
  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="primary" (click)="saveTextBlockStyle()">
      完成
    </button>
  </c-modal-footer>
</c-modal>


<input type="file" #bgFileInput style="display: none;" accept="image/*" />





<c-modal id="liveDemoModal" [visible]="setting_visible">
  <c-modal-header>
    <h5 cModalTitle>編輯視圖</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
     <c-row class="mb-3">
      <c-col [sm]="4">
        <label cCol cLabel="col" class="mx-2"><strong>儀表板</strong></label>
      </c-col>
      <c-col [sm]="8">

      </c-col>
    </c-row>
    <c-row class="mb-3 align-items-center">
      <c-col [xs]="12" [sm]="4" class="d-flex align-items-center mb-2 mb-sm-0">
        <label cLabel for="customRange2" class="mx-2 mb-0">背景Alpha: {{ editing_plot_background_alpha | number:'1.2-2' }}</label>
      </c-col>
      <c-col [xs]="12" [sm]="8">
        <input
          cFormControl
          [(ngModel)]="editing_plot_background_alpha"
          id="customRange2"
          max="1"
          min="0"
          step="0.01"
          type="range"
          class="w-100"
        />
      </c-col>
    </c-row>
    <!-- 視圖名稱是否顯示在檢視模式 -->
    <c-row class="mb-3">
      <c-col [sm]="4">
        <label cCol cLabel="col" class="mx-2">顯示視圖名稱</label>
      </c-col>
      <c-col [sm]="8">
        <c-form-check [switch]="true" sizing="lg">
          <input
            cFormCheckInput
            type="checkbox"
            [(ngModel)]="editing_plot_plotname_visible"
          />
        </c-form-check>
      </c-col>
    </c-row>
    <!-- 最近更新時間是否顯示在檢視模式 -->
    <c-row class="mb-3">
      <c-col [sm]="4">
        <label cCol cLabel="col" class="mx-2">顯示最近更新時間</label>
      </c-col>
      <c-col [sm]="8">
        <c-form-check [switch]="true" sizing="lg">
          <input
            cFormCheckInput
            type="checkbox"
            [(ngModel)]="editing_plot_updatedTime_visible"
          />
        </c-form-check>
      </c-col>
    </c-row>
     <!-- 檢視模式更新週期 -->
     <c-row class="mb-3">
      <c-col [sm]="4">
        <label cCol cLabel="col" class="mx-2">更新週期: {{editing_plot_updatedTime_period}}s</label>
      </c-col>
      <c-col [sm]="8">
        <input
          cFormControl
          [(ngModel)]="editing_plot_updatedTime_period"
          id="customRange2"
          max="300"
          min="30"
          step="1"
          type="range"
          class="w-100"
        />
      </c-col>
    </c-row>
  </c-modal-body>

  <c-modal-footer>
    <button cButton variant="outline" color="primary" (click)="saveSetting()">
      完成
    </button>
  </c-modal-footer>
</c-modal>



<c-modal id="liveDemoModal" [visible]="remove_object_visible">
  <c-modal-header>
    <h5 cModalTitle>移除物件</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <strong>確認移除圖標及元件設定？</strong>
    <br>
    <a style="color: red">警告：設定一旦移除將無法復原</a>
  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="danger" (click)="removeObject()">
      移除
    </button>
  </c-modal-footer>
</c-modal>


<c-modal id="liveDemoModal" [visible]="remove_link_visible">
  <c-modal-header>
    <h5 cModalTitle>移除連結</h5>
    <button (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <strong>確認移除圖標及連結設定？</strong>
    <br>
    <a style="color: red">警告：設定一旦移除將無法復原</a>

  </c-modal-body>
  <c-modal-footer>
    <button cButton variant="outline" color="danger" (click)="removeLink()">
      移除
    </button>
  </c-modal-footer>
</c-modal>




<c-modal id="liveDemoModal" [visible]="gateway_config_visible" size="xl" scrollable backdrop="static">
  <c-modal-header>
    <h5 cModalTitle>編輯閘道組態</h5>
    <button *ngIf="gateway_config_getRemoteState" (click)="toggle_modal()" cButtonClose></button>
  </c-modal-header>
  <c-modal-body>
    <div *ngIf="!gateway_config_getRemoteState"
      class="d-flex flex-column justify-content-center align-items-center"
      style="height: 10vh;">
      <c-spinner color="primary"></c-spinner>
      <div class="mt-2 text-muted">連線中...</div>
    </div>
    <c-row class="mb-3" *ngIf="gateway_config_getRemoteState">
      <c-col [sm]="8">
        <label cCol cLabel="col" class="mx-2">遠端狀態：</label>
        <label cCol cLabel="col" class="mx-2" style="color: green" *ngIf="gateway_config_connected">連線中</label>
        <label cCol cLabel="col" class="mx-2" style="color: red" *ngIf="!gateway_config_connected">無法連線</label>
      </c-col>
      <c-col [sm]="4">
        <button cButton variant="outline" color="primary" class="float-end" (click)="save_gateway_config()" *ngIf="!gateway_config_connected">
          儲存至伺服器
        </button>
        <button cButton variant="outline" color="primary" class="float-end" (click)="save_gateway_config()" *ngIf="gateway_config_connected">
          發佈至遠端閘道
        </button>
      </c-col>
    </c-row>
    <ng-container *ngIf="gateway_config_getRemoteState">
    <!-- Tab 選項 -->
    <div class="nav-container mb-3">
      <c-tabs-list layout="fill" variant="tabs">
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeGatewayTab === 'Host'"
            (click)="switchActiveGatewayTab('Host')"
            style="cursor: pointer"
          >
            主機
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeGatewayTab === 'Communication'"
            (click)="switchActiveGatewayTab('Communication')"
            style="cursor: pointer"
          >
            通訊
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeGatewayTab === 'Driver'"
            (click)="switchActiveGatewayTab('Driver')"
            style="cursor: pointer"
          >
            設備
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link"
            [class.active]="activeGatewayTab === 'Logic'"
            (click)="switchActiveGatewayTab('Logic')"
            style="cursor: pointer"
          >
            邏輯
          </a>
        </li>
      </c-tabs-list>
    </div>
    <div *ngIf="activeGatewayTab === 'Host'">
      <!-- 背景顏色 -->
      <c-row class="mb-3 mx-2">
        <c-col [sm]="4">
          <label cCol cLabel="col">電號</label>
        </c-col>
        <c-col [sm]="2">
          <label cCol cLabel="col">{{gateway_config.Host.HostName}}</label>
        </c-col>
      </c-row>
      <!-- 序列號 -->
      <c-row class="mb-3 mx-2">
        <c-col [sm]="4">
          <label cCol cLabel="col" >序列號</label>
        </c-col>
        <c-col [sm]="2">
          <label cCol cLabel="col" >{{gateway_config.Host.SerialNo}}</label>
        </c-col>
      </c-row>
      <!-- 特殊電號列表 -->
      <c-row class="mb-3 mx-2">
        <c-col [sm]="4">
          <label cCol cLabel="col" for="Identical_placeID_list">
            特殊電號設備列表
          </label>
        </c-col>

        <c-col [sm]="8" class="text-end">
          <button
            cButton
            variant="outline"
            color="primary"
            (click)="add_Identical_placeID_device()"
          >
            新增特殊電號設備列表
          </button>
        </c-col>
      </c-row>

      <div class="table-responsive" *ngIf="gateway_config.Host.Identical_placeID_list.length > 0">
        <table
          [striped]="true"
          bordered
          cTable
          class="table-sm align-middle text-center w-100 mx-2"
        >
          <thead >
            <tr>
              <th scope="col">Index</th>
              <th scope="col">設備485地址<br /><small>(0-9, a-z)</small></th>
              <th scope="col">對應電號</th>
              <th scope="col">功能</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let device of gateway_config.Host.Identical_placeID_list; let i = index"
            >
              <td>{{ i }}</td>
              <td>
                <input
                  aria-label="設備485地址"
                  maxlength="1"
                  class="form-control"
                  [(ngModel)]="device.slaveAddress"
                  name="slaveAddress-{{ i }}"
                  placeholder="485序列號"
                  required
                  style="min-width: 80px;"
                />
              </td>
              <td>
                <input
                  aria-label="對應電號"
                  maxlength="11"
                  class="form-control"
                  [(ngModel)]="device.placeID"
                  name="placeID-{{ i }}"
                  placeholder="對應電號"
                  required
                  style="min-width: 120px;"
                />
              </td>
              <td>
                <button
                  cButton
                  variant="outline"
                  color="danger"
                  (click)="remove_Identical_placeID_device(i)"
                >
                  刪除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <c-row class="mb-3 mx-2" >
        <label [sm]="4" cCol cLabel="col" for="Version">
          版本
        </label>
        <c-col [sm]="6">
          <input cFormControl id="Version" plaintext readonly type="text" value={{gateway_config.Host.Version}} />
        </c-col>
        <c-col [sm]="2">
          <button cButton variant="outline" color="primary" (click)="toggle_update_view()" *ngIf="gateway_config_update_available_boolean" style="float: right;" >更新</button>
        </c-col>

      </c-row>
      <c-row class="mb-3 mx-2" *ngIf="gateway_config_connected">
        <label [sm]="4" cCol cLabel="col" for="Version">
          SSH連線
        </label>
        <c-col [sm]="4">
          <button cButton variant="outline" color="secondary" (click)="create_SSH()" style="float: left;" >建立</button>
        </c-col>
        <c-col [sm]="4">
          <label cCol cLabel="col" for="SSH message">
            {{gateway_config_ssh_msg}}
            </label>
        </c-col>
      </c-row>
      <c-row class="mb-3 mx-2" *ngIf="gateway_config_connected">
        <label [sm]="4" cCol cLabel="col"  for="Version" *ngIf="compareVersion(gateway_config.Host.Version, '1.0.7')">
          MAC位址
        </label>
        <c-col [sm]="4" *ngIf="compareVersion(gateway_config.Host.Version, '1.0.7')">
          <button cButton variant="outline" color="secondary" (click)="get_MACAddress()" style="float: left;" >取得</button>
        </c-col>
        <c-col [sm]="4">
          <label cCol cLabel="col" for="MAC message">
            {{gateway_config_MAC_msg}}
            </label>
        </c-col>
      </c-row>
      <c-row class="mb-3 mx-2" *ngIf="compareVersion(gateway_config.Host.Version, '1.0.7')">
        <label [sm]="4" cCol cLabel="col" for="Version">
          資料紀錄週期(s)
        </label>
        <c-col [sm]="8">
          <select [(ngModel)]="gateway_config.Host.PostDataPeriod" (ngModelChange)="gateway_config.Host.PostDataPeriod = +$event" cSelect>
            <option [value]="60">60</option>
            <option [value]="30">30</option>
            <option [value]="20">20</option>
            <option [value]="15">15</option>
            <option [value]="12">12</option>
            <option [value]="10">10</option>
            <option [value]="6">6</option>
            <option [value]="5">5</option>
          </select>
        </c-col>
      </c-row>
    </div>
    <div *ngIf="activeGatewayTab === 'Communication'">
      <!-- Wi-Fi 設定 -->
      <c-row class="mb-3">
        <label [sm]="4" cCol cLabel="col"><strong>Wi-Fi 設定</strong></label>
      </c-row>
      <div class="row gy-3 mb-3">
        <div class="col-12" *ngFor="let WiFi of gateway_config.Communication.WiFi; let i = index">
          <div class="card p-3 shadow-sm">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">Wi-Fi 設定 #{{ i + 1 }}</h6>
              <button cButton variant="outline" color="danger" (click)="remove_wifi(i)" size="sm">刪除</button>
            </div>

            <div class="mb-2">
              <label class="form-label">SSID</label>
              <input class="form-control" [(ngModel)]="WiFi.ssid" name="ssid-{{i}}" placeholder="Wi-Fi名稱" required />
            </div>

            <div class="mb-2" *ngIf="WiFi.key_mgmt === 'WPA-EAP'">
              <label class="form-label">帳號</label>
              <input class="form-control" [(ngModel)]="WiFi.identity" name="identity-{{i}}" placeholder="帳號" required />
            </div>

            <div class="mb-2" *ngIf="WiFi.key_mgmt !== 'NONE'">
              <label class="form-label">密碼</label>
              <input class="form-control" [(ngModel)]="WiFi.psk" name="psk-{{i}}" placeholder="密碼" required />
            </div>

            <div class="mb-2">
              <label class="form-label d-block">加密方式</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" [(ngModel)]="WiFi.key_mgmt" [value]="'WPA-PSK'" name="key_mgmt-{{i}}" />
                <label class="form-check-label">WPA-PSK</label>
              </div>
              <div class="form-check" *ngIf="compareVersion(gateway_config.Host.Version, '1.0.8')">
                <input class="form-check-input" type="radio" [(ngModel)]="WiFi.key_mgmt" [value]="'WPA-EAP'" name="key_mgmt-{{i}}" />
                <label class="form-check-label">WPA-EAP</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" [(ngModel)]="WiFi.key_mgmt" [value]="'NONE'" name="key_mgmt-{{i}}" />
                <label class="form-check-label">NONE</label>
              </div>
            </div>

            <div class="mb-2">
              <label class="form-label">優先度</label>
              <input type="number" class="form-control" (input)="filterAndFillZero($event)"
                [(ngModel)]="WiFi.priority" name="priority-{{i}}" placeholder="優先度" required />
            </div>
          </div>
        </div>
      </div>


      <c-row class="mb-3">
        <c-col>
          <button cButton variant="outline" color="primary" (click)="add_wifi()" class="float-end">
            新增 Wi-Fi
          </button>
        </c-col>
      </c-row>

      <!-- 上傳伺服器設定 -->
      <c-row class="mb-3">
        <label [sm]="4" cCol cLabel="col"><strong>上傳伺服器 設定</strong></label>
      </c-row>
      <div class="row gy-3 mb-3">
        <div class="col-12" *ngFor="let server of gateway_config.Communication.PostServer; let i = index">
          <div class="card p-3 shadow-sm">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">伺服器設定 #{{ i + 1 }}</h6>
            </div>

            <div class="mb-2">
              <label class="form-label">伺服器地址</label>
              <input class="form-control" [(ngModel)]="server.ServerAddress" name="ServerAddress-{{i}}" disabled />
            </div>

            <div class="mb-2">
              <label class="form-label">伺服器端口</label>
              <input type="number" (input)="filterAndFillZero($event)"class="form-control" [(ngModel)]="server.ServerPort" name="ServerPort-{{i}}" disabled />
            </div>

            <div class="mb-2">
              <label class="form-label">重送等待 (秒)</label>
              <input type="number" (input)="filterAndFillZero($event)" class="form-control" [(ngModel)]="server.Wait" name="Wait-{{i}}" disabled />
            </div>
          </div>
        </div>
      </div>

      <c-row class="mb-3">
        <label [sm]="4" cCol cLabel="col"><strong>固定IP 設定</strong></label>
      </c-row>
      <div class="row gy-3">
        <!-- 固定 IP 設定 -->
        <div class="col-12 mb-3">
          <div class="card p-3 shadow-sm">
            <div class="mb-2">
              <label class="form-label">IP</label>
              <input class="form-control" [(ngModel)]="gateway_config.Communication.FixedIP.IP" name="FixedIP.IP" placeholder="IP" />
            </div>

            <div class="mb-2">
              <label class="form-label">Routers</label>
              <input class="form-control" [(ngModel)]="gateway_config.Communication.FixedIP.Routers" name="FixedIP.Routers" placeholder="Routers" />
            </div>
          </div>
        </div>
      </div>
      <c-row class="mb-3">
        <label [sm]="4" cCol cLabel="col"><strong>行動網路 設定</strong></label>
      </c-row>
      <!-- SIM 卡號碼 -->
      <div class="col-12 mb-3">
        <div class="card p-3 shadow-sm">
          <h6 class="mb-3">SIM 卡號碼</h6>

          <div class="mb-2">
            <label class="form-label">Phone Number</label>
            <input class="form-control" [(ngModel)]="gateway_config.Communication.PhoneNumber" name="PhoneNumber" placeholder="Phone Number" />
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="activeGatewayTab === 'Driver'">
      <div class="row gy-3">
        <div
          class="col-12"
          *ngFor="let driver of gateway_config.Modbus.Driver; let i = index"
        >
          <div class="card p-3 shadow-sm position-relative">
            <h6 class="mb-3">驅動程式 #{{ i + 1 }}</h6>

            <!-- 端口名稱 -->
            <div class="mb-2">
              <label class="form-label">端口名稱</label>
              <input
                type="text"
                class="form-control"
                disabled
                [(ngModel)]="driver.PortAddress"
              />
            </div>

            <!-- RS485 ID -->
            <div class="mb-2">
              <label class="form-label">RS485 ID</label>
              <input
                type="number"
                class="form-control"
                (input)="filterAndFillZero($event)"
                [(ngModel)]="driver.SlaveAddress"
              />
            </div>

            <!-- 驅動程式 -->
            <div class="mb-2">
              <label class="form-label">驅動程式</label>
              <select
                class="form-select"
                [(ngModel)]="driver.DriverName"
                (change)="change_driver(i)"
              >
                <option
                  *ngFor="let option of driver_list"
                  [value]="option.value"
                >
                  {{ option.label }} ({{ option.value }})
                </option>
              </select>
            </div>

            <!-- 操作按鈕 -->
            <div class="d-flex justify-content-end">
              <c-dropdown>
                <button
                  cDropdownToggle
                  [caret]="false"
                  class="btn bg-white d-flex justify-content-center align-items-center"
                  style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="black"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12" cy="5" r="2" />
                    <circle cx="12" cy="12" r="2" />
                    <circle cx="12" cy="19" r="2" />
                  </svg>
                </button>
                <ul cDropdownMenu>
                  <li>
                    <a
                      cDropdownItem
                      style="cursor: pointer"
                      (click)="toggle_pin_config_modal(i)"
                      *ngIf="is_ai(gateway_config.Modbus.Driver[i].DriverName)"
                      >編輯腳位</a
                    >
                  </li>
                  <li>
                    <a
                      cDropdownItem
                      style="cursor: pointer"
                      (click)="toggle_get_driver_pin_modal(i)"
                      *ngIf="gateway_config_connected && compareVersion(gateway_config.Host.Version, '1.0.7')"
                      >測試腳位</a
                    >
                  </li>
                  <li>
                    <a
                      cDropdownItem
                      style="cursor: pointer"
                      (click)="remove_driver(i)"
                      class="text-danger"
                      >刪除驅動程式</a
                    >
                  </li>
                </ul>
              </c-dropdown>
            </div>
          </div>
        </div>

        <!-- 新增驅動程式按鈕 -->
        <div class="col-12 text-end">
          <button
            cButton
            variant="outline"
            color="primary"
            class="mt-2"
            (click)="add_Driver()"
          >
            新增驅動程式
          </button>
        </div>
      </div>
    </div>
    <div *ngIf="activeGatewayTab === 'Logic'">
      <div class="row my-3">
        <div
          class="col-12"
          *ngFor="let logic of gateway_config.Strategy.Logics; let i = index"
        >
          <div class="card p-3 shadow-sm mb-3">
            <h6 class="mb-3">邏輯模組 #{{ i }}</h6>

            <!-- 邏輯類型 -->
            <div class="mb-2">
              <label class="form-label">邏輯類型</label>
              <select
                class="form-select"
                [(ngModel)]="gateway_config.Strategy.Logics[i].Type"
                disabled
              >
                <option value="CondCtrlByT2">邏輯控制模組 (CondCtrlByT2)</option>
                <option value="DataTransByRedis">區網內變數傳遞模組 (DataTransByRedis)</option>
              </select>
            </div>

            <!-- 執行週期 -->
            <div class="mb-2">
              <label class="form-label">執行週期</label>
              <input
                type="number"
                class="form-control"
                (input)="filterAndFillZero($event)"
                [(ngModel)]="gateway_config.Strategy.Logics[i].ExecutionPrd"
                name="ExecutionPrd"
                placeholder="1"
              />
            </div>

            <!-- 操作按鈕 -->
            <div class="d-flex justify-content-end mt-3">
              <button
                cButton
                variant="outline"
                color="primary"
                class="me-2"
                (click)="toggle_edit_logic_modal(i)"
              >
                編輯
              </button>
              <button
                cButton
                variant="outline"
                color="danger"
                (click)="remove_logic(i)"
              >
                刪除
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 新增邏輯模組按鈕 -->
      <div class="col-12 text-end">
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="toggle_add_logic_modal()"
        >
          新增邏輯模組
        </button>
      </div>
    </div>
    </ng-container>
  </c-modal-body>
</c-modal>

<c-modal #verticallyCenteredModal alignment="top" #scrollableLongContentModal id="liveDemoModal" [visible]="gateway_config_update_visible" backdrop="static" size="lg">
  <c-modal-header>
    <h5 cModalTitle>提交更新請求</h5>
    <button (click)="toggle_update_view()" cButtonClose></button>
  </c-modal-header>
    <c-modal-body>
      <c-row class="mb-3">
        <label cCol cLabel="col">
          <strong>確認請求內容：</strong>
        </label>
      </c-row>
      <c-row class="mb-3">
        <label [sm]="2" cCol cLabel="col" >
          設備
        </label>
        <label [sm]="10" cCol cLabel="col">
          {{editing_gateway_id}}
        </label>
      </c-row>
      <c-row class="mb-3">
        <label [sm]="2" cCol cLabel="col" >
          原始版本
        </label>
        <label [sm]="4" cCol cLabel="col">
          {{gateway_config.Host.Version}}
        </label>
        <label [sm]="2" cCol cLabel="col" >
          =>
        </label>
        <label [sm]="2" cCol cLabel="col" >
          目標版本
        </label>
        <label [sm]="2" cCol cLabel="col">
          {{gateway_config_latest_version}}
        </label>
      </c-row>
      <c-row class="mb-3">
        <label cCol cLabel="col" >
          *請確保網路連線狀況穩定<br>
          *版本發行說明請見系統公告郵件
        </label>
      </c-row>
      <button cButton variant="outline" color="primary" (click)="update_gateway()"  style="float: right;" class="mb-3">發佈至遠端</button>
   </c-modal-body>
</c-modal>

<c-modal
#verticallyCenteredModal
alignment="top"
#scrollableLongContentModal
id="liveDemoModal"
[visible]="gateway_config_get_driver_pin_visible"
backdrop="static"
size="lg"
class="custom-modal">

<!-- Modal Header -->
<c-modal-header class="d-flex justify-content-between align-items-center">
  <h5 cModalTitle class="font-weight-bold">測試設備腳位</h5>
  <button
    (click)="toggle_get_driver_pin_modal(0)"
    cButtonClose
    class="btn-close"
    aria-label="Close">
  </button>
</c-modal-header>

<!-- Modal Body -->
<c-modal-body class="p-4">
  <div class="row mb-3">
    <!-- Pin Address Select -->
    <div class="col-md-6">
      <label for="pinAddr" class="form-label">選擇腳位地址</label>
      <select [(ngModel)]="gateway_config_testing_driver_pin_pinAddr" cSelect id="pinAddr" class="form-select">
        <option *ngFor="let Pin of getAllowedInputPinNames(gateway_config_testing_driver_pin_slaveAddr); let i = index" [value]="i">
          {{ i }}
        </option>
      </select>
    </div>

    <!-- Pin Name Select -->
    <div class="col-md-6">
      <label for="pinName" class="form-label">選擇腳位名稱</label>
      <select
        [(ngModel)]="gateway_config_testing_driver_pin_pinName"
        cSelect
        id="pinName"
        class="form-select">
        <option *ngFor="let name of getAllowedInputPinNames(gateway_config_testing_driver_pin_slaveAddr)[gateway_config_testing_driver_pin_pinAddr]; let i = index" [value]="name">
          {{ name }}
        </option>
      </select>
    </div>
  </div>

  <!-- Result Display as Disabled Field -->
  <div class="mb-3" *ngIf="gateway_config_get_driver_pin_result.split('/')[0].length>0">
    <label for="result" class="form-label">結果</label>

    <div class="row">
      <!-- Value Display -->
      <div class="col-4">
        <label class="form-label">值</label>
        <input
          type="text"
          id="value"
          class="form-control"
          [value]="gateway_config_get_driver_pin_result.split('/')[0]"
          disabled
          aria-label="Value"
          readonly
        />
      </div>

      <!-- Status Code Display -->
      <div class="col-4">
        <label class="form-label">狀態碼</label>
        <input
          type="text"
          id="statusCode"
          class="form-control"
          [value]="gateway_config_get_driver_pin_result.split('/')[1]"
          disabled
          aria-label="Status Code"
          readonly
        />
      </div>

      <!-- Access Time Display -->
      <div class="col-4">
        <label class="form-label">訪問時間</label>
        <input
          type="text"
          id="accessTime"
          class="form-control"
          [value]="gateway_config_get_driver_pin_result.split('/')[2]"
          disabled
          aria-label="Access Time"
          readonly
        />
      </div>
    </div>
  </div>


  <!-- Test Button -->
  <div class="text-end">
    <button
      cButton
      variant="outline"
      color="primary"
      (click)="get_remote_driver_pin_data()"
      class="mb-3">
      取得遠端即時數值
    </button>
  </div>
</c-modal-body>
</c-modal>


<c-modal
#verticallyCenteredModal
alignment="top"
#scrollableLongContentModal
id="liveDemoModal"
[visible]="gateway_config_mac_modal_visible"
backdrop="static"
size="lg"
class="custom-modal">

<c-modal-header class="d-flex justify-content-between align-items-center">
<h5 cModalTitle class="font-weight-bold text-primary">設備MAC地址</h5>
<button
  (click)="toggle_mac_modal()"
  cButtonClose
  class="btn-close"
  aria-label="Close">
</button>
</c-modal-header>

<c-modal-body class="p-4">
<div *ngIf="gateway_config_MAC_list.length > 0; else noData">
  <ul class="list-group">
    <li *ngFor="let mac of gateway_config_MAC_list" class="list-group-item d-flex justify-content-between align-items-center">
      <div><strong>{{ mac.interface }}</strong></div>
      <div>{{ mac.address }}</div>
    </li>
  </ul>
</div>
<ng-template #noData>
  <p class="text-muted text-center">尚無資料</p>
</ng-template>
</c-modal-body>

</c-modal>

<c-modal
  #verticallyCenteredModal
  alignment="center"
  #scrollableLongContentModal
  id="liveDemoModal"
  [visible]="gateway_config_pin_config_visible"
  backdrop="static"
  size="xl"
>
  <ng-container *ngIf="gateway_config_pin_config_visible">
    <c-modal-header>
      <h5 cModalTitle>編輯腳位</h5>
      <button (click)="toggle_pin_config_modal(0)" cButtonClose></button>
    </c-modal-header>

    <c-modal-body>
      <!-- 桌面版 Table -->
      <div class="table-responsive d-none d-md-block">
        <table cTable [striped]="true" bordered class="mb-3">
          <thead>
            <tr>
              <th scope="col">Pin Address</th>
              <th scope="col">設備類型</th>
              <th scope="col">Pin Name</th>
              <th scope="col">讀取函式</th>
              <th scope="col">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              *ngFor="let pin of gateway_config.Modbus.Driver[gateway_config_editing_pin_config].PinConfig; let i = index"
            >
              <td>{{ pin.PinIdx }}</td>
              <td>S{{ pin.SensorType }}</td>
              <td>{{ pin.Definition[0].RenameAs }}</td>
              <td>{{ pin.Definition[0].Read }}</td>
              <td>
                <button
                  cButton
                  variant="outline"
                  color="primary"
                  class="mx-1"
                  (click)="toggle_edit_pin_modal(gateway_config_editing_pin_config, i)"
                >
                  編輯
                </button>
                <button
                  cButton
                  variant="outline"
                  color="danger"
                  class="mx-1"
                  (click)="reset_pin_definition(gateway_config_editing_pin_config, i)"
                >
                  重置
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 手機版卡片樣式 -->
      <div class="d-block d-md-none">
        <div
          class="card mb-3 shadow-sm"
          *ngFor="let pin of gateway_config.Modbus.Driver[gateway_config_editing_pin_config].PinConfig; let i = index"
        >
          <div class="card-body">
            <p class="mb-1"><strong>Pin Address:</strong> {{ pin.PinIdx }}</p>
            <p class="mb-1"><strong>設備類型:</strong> S{{ pin.SensorType }}</p>
            <p class="mb-1"><strong>Pin Name:</strong> {{ pin.Definition[0].RenameAs }}</p>
            <p class="mb-2"><strong>讀取函式:</strong> {{ pin.Definition[0].Read }}</p>

            <div class="d-flex justify-content-end">
              <button
                cButton
                variant="outline"
                color="primary"
                class="me-2"
                (click)="toggle_edit_pin_modal(gateway_config_editing_pin_config, i)"
              >
                編輯
              </button>
              <button
                cButton
                variant="outline"
                color="danger"
                (click)="reset_pin_definition(gateway_config_editing_pin_config, i)"
              >
                重置
              </button>
            </div>
          </div>
        </div>
      </div>
    </c-modal-body>
  </ng-container>
</c-modal>


<c-modal #verticallyCenteredModal alignment="center" #scrollableLongContentModal id="liveDemoModal" [visible]="gateway_config_edit_pin_def_config_visible" backdrop="static" size="lg">
  <c-modal-header>
    <h5 cModalTitle>編輯腳位定義</h5>
    <button (click)="toggle_edit_pin_modal(gateway_config_editing_pin_config, gateway_config_edit_pin_def_index)" cButtonClose></button>
  </c-modal-header>
    <c-modal-body>
      <c-row class="mb-3">
        <label [sm]="2" cCol cLabel="col">
          設備類型
        </label>
        <c-col [sm]="10">
          <select aria-label="SensorType" [(ngModel)]="gateway_config_editing_pin_def_dtype" (change)="switch_editing_dtype()" cSelect>
            <option value="D">D (數位訊號，Digital)</option>
            <option value="A">A (類比訊號，Analog)</option>
            <option value="T">T (溫度，Temperature)</option>
            <option value="R">R (控制，Regulator)</option>
            <option value="H">H (濕度，Humidity)</option>
            <option value="F">F (流量，Flow)</option>
            <option value="P">P (壓力，Pressure)</option>
        </select>
        </c-col>
      </c-row>
      <c-row class="mb-3">
        <label [sm]="2" cCol cLabel="col">
          物理量範圍
        </label>
        <c-col [sm]="4">
          <input type="number" [(ngModel)]="gateway_config_editing_pin_def_lrange" (input)="filterAndFillZero($event)" cFormControl/>
        </c-col>
        <label [sm]="2" cCol cLabel="col">
          ~
        </label>
        <c-col [sm]="4">
          <input type="number" [(ngModel)]="gateway_config_editing_pin_def_hrange" (input)="filterAndFillZero($event)" cFormControl/>
        </c-col>
      </c-row>
      <c-row class="mb-3">
        <label [sm]="2" cCol cLabel="col">
          ADC精度
        </label>
        <c-col [sm]="8">
          <input type="number" [(ngModel)]="gateway_config_editing_pin_def_adc" (input)="filterAndFillZero($event)" cFormControl/>
        </c-col>
        <label [sm]="2" cCol cLabel="col">
          bit(s)
        </label>
      </c-row>
      <button cButton variant="outline" color="primary" (click)="set_pin_definition(gateway_config_editing_pin_config, gateway_config_edit_pin_def_index)"  style="float: right;">設定腳位定義</button>
   </c-modal-body>
</c-modal>


<c-modal #verticallyCenteredModal alignment="top" #scrollableLongContentModal id="liveDemoModal" [visible]="gateway_config_add_logic_visible" backdrop="static" size="lg">
  <c-modal-header>
    <h5 cModalTitle>新增邏輯模組</h5>
    <button (click)="toggle_add_logic_modal()" cButtonClose></button>
  </c-modal-header>
    <c-modal-body>
      <select aria-label="Default select example" [(ngModel)]="gateway_config_selected_logic_type" class="mb-3" cSelect>
        <option value="CondCtrlByT2">邏輯控制模組 (CondCtrlByT2)</option>
        <option value="DataTransByRedis">區網內變數傳遞模組 (DataTransByRedis)</option>
      </select>
      <button cButton variant="outline" color="primary" (click)="add_Logic(); toggle_add_logic_modal()"  style="float: right;" class="mb-3">建立</button>
   </c-modal-body>
</c-modal>

<c-modal
#modalRef
alignment="top"
id="liveDemoModal"
[visible]="gateway_config_edit_logic_visible"
backdrop="static"
size="xl">
  <c-modal-header>
    <h5 cModalTitle>編輯邏輯模組</h5>
    <button (click)="toggle_edit_logic_modal(0)" cButtonClose></button>
  </c-modal-header>
    <c-modal-body *ngIf="gateway_config.Strategy.Logics?.length || [].length > 0">
      <c-row class="mb-3" *ngIf="gateway_config.Strategy.Logics[gateway_config_editing_logic].Type">
        <label [sm]="6" cCol cLabel="col" for="InputList">
          <strong>邏輯輸入 設定</strong>
        </label>
      </c-row>

      <ng-container *ngFor="let input of gateway_config.Strategy.Logics[gateway_config_editing_logic].InputList; let j = index">
        <c-card class="mb-3">
          <c-card-body>
            <h6 class="mb-3">Pin{{ padNumber(j, 3) }}</h6>

            <c-row class="mb-2">
              <c-col sm="6">
                <label>Port Name</label>
                <input cFormControl [(ngModel)]="input.PortName" disabled placeholder="Server Address" />
              </c-col>
              <c-col sm="6">
                <label>Slave Address</label>
                <select [(ngModel)]="input.SlaveAddress" cSelect>
                  <option *ngFor="let driver of gateway_config.Modbus.Driver" [value]="+driver.SlaveAddress">
                    {{ driver.SlaveAddress }}
                  </option>
                </select>
              </c-col>
            </c-row>

            <c-row class="mb-2">
              <c-col sm="6">
                <label>Pin Address</label>
                <select [(ngModel)]="input.PinAddress" cSelect>
                  <option *ngFor="let pin of getAllowedInputPinNames(input.SlaveAddress); let i = index" [value]="i">
                    {{ i }}
                  </option>
                </select>
              </c-col>
              <c-col sm="6">
                <label>Pin Name</label>
                <select [(ngModel)]="input.PinName" cSelect>
                  <option *ngFor="let name of getAllowedInputPinNames(input.SlaveAddress)[input.PinAddress]; let i = index" [value]="name">
                    {{ name }}
                  </option>
                </select>
              </c-col>
            </c-row>

            <div class="text-end">
              <button cButton variant="outline" color="danger" (click)="remove_LogicInput(gateway_config_editing_logic, j)">
                刪除
              </button>
            </div>
          </c-card-body>
        </c-card>
      </ng-container>

      <c-row class="mb-3">
        <c-col class="text-end">
          <button cButton variant="outline" color="primary" (click)="add_LogicInput(gateway_config_editing_logic)">
            新增邏輯輸入
          </button>
        </c-col>
      </c-row>

      <c-row class="mb-3">
        <label [sm]="6" cCol cLabel="col" for="OutputList">
          <strong>邏輯輸出 設定</strong>
        </label>
      </c-row>

      <ng-container *ngFor="let output of gateway_config.Strategy.Logics[gateway_config_editing_logic].OutputList; let j = index">
        <c-card class="mb-3">
          <c-card-body>
            <h6 class="mb-3">Pout{{ padNumber(j, 3) }}</h6>

            <c-row class="mb-2">
              <c-col sm="6">
                <label>Port Name</label>
                <input cFormControl [(ngModel)]="output.PortName" disabled placeholder="Server Address" />
              </c-col>
              <c-col sm="6">
                <label>Slave Address</label>
                <select [(ngModel)]="output.SlaveAddress" cSelect>
                  <option *ngFor="let driver of gateway_config.Modbus.Driver" [value]="driver.SlaveAddress">
                    {{ driver.SlaveAddress }}
                  </option>
                </select>
              </c-col>
            </c-row>

            <c-row class="mb-2">
              <c-col sm="6">
                <label>Pin Address</label>
                <select [(ngModel)]="output.PinAddress" cSelect>
                  <option *ngFor="let pin of getAllowedOutputPinNames(output.SlaveAddress); let i = index" [value]="i">
                    {{ i }}
                  </option>
                </select>
              </c-col>
              <c-col sm="6">
                <label>Pin Name</label>
                <select [(ngModel)]="output.PinName" cSelect>
                  <option *ngFor="let name of getAllowedOutputPinNames(output.SlaveAddress)[output.PinAddress]; let i = index" [value]="name">
                    {{ name }}
                  </option>
                </select>
              </c-col>
            </c-row>

            <div class="text-end">
              <button cButton variant="outline" color="danger" (click)="remove_LogicOutput(gateway_config_editing_logic, j)">
                刪除
              </button>
            </div>
          </c-card-body>
        </c-card>
      </ng-container>


      <c-row class="mb-3">
        <c-col>
          <button cButton variant="outline"
          color="primary"  *ngIf="gateway_config.Strategy.Logics[gateway_config_editing_logic].Type == 'CondCtrlByT2' && gateway_config.Strategy.Logics[gateway_config_editing_logic].OutputList.length < 1"   (click)="add_LogicOutput(gateway_config_editing_logic)" style="float: right;">新增邏輯輸出</button>
        </c-col>
      </c-row>
      <c-row class="mb-3"  *ngIf="gateway_config.Strategy.Logics[gateway_config_editing_logic].Type">
        <label [sm]="5" cCol cLabel="col" for="Arguments">
          <strong>{{gateway_config.Strategy.Logics[gateway_config_editing_logic].Type}} 設定</strong>
        </label>
      </c-row>
      <div *ngIf="gateway_config.Strategy.Logics[gateway_config_editing_logic].Type==='CondCtrlByT2'" >
        <label [sm]="2" cCol cLabel="col" for="Declaration">
          變數宣告
        </label>

        <c-row>
          <ng-container *ngFor="let Declaration of gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_Declaration; let j = index">
            <c-col sm="12" lg="12">
              <c-card class="mb-3">
                <c-card-body>
                  <div class="mb-2">
                    <label for="VarName-{{j}}">變數名稱</label>
                    <input
                      id="VarName-{{j}}"
                      cFormControl
                      class="form-control form-control-sm"
                      [(ngModel)]="Declaration.VarName"
                      name="VarName-{{j}}"
                      placeholder="Variable Name"
                      required
                    />
                  </div>

                  <div class="mb-2">
                    <label for="Definition-{{j}}">變數定義</label>
                    <input
                      id="Definition-{{j}}"
                      cFormControl
                      class="form-control form-control-sm"
                      [(ngModel)]="Declaration.Definition"
                      name="Definition-{{j}}"
                      placeholder="Definition"
                      required
                    />
                  </div>

                  <div class="mb-2">
                    <label for="RollingFunct-{{j}}">滾動函數</label>
                    <select
                      id="RollingFunct-{{j}}"
                      cSelect
                      class="form-select form-select-sm"
                      [(ngModel)]="Declaration.Rolling_Funct"
                    >
                      <option value="None">None</option>
                      <option value="max">maximum</option>
                      <option value="min">minimum</option>
                      <option value="mean">mean</option>
                    </select>
                  </div>

                  <div class="mb-2">
                    <label for="RollingLen-{{j}}">滾動長度</label>
                    <input
                      id="RollingLen-{{j}}"
                      type="number"
                      class="form-control form-control-sm"
                      cFormControl
                      (input)="filterAndFillZero($event)"
                      [(ngModel)]="Declaration.Rolling_Len"
                      name="Rolling_Len-{{j}}"
                      placeholder="[0, [30,60]]"
                      required
                    />
                  </div>

                  <div class="d-flex justify-content-end">
                    <button
                      cButton
                      variant="outline"
                      color="danger"
                      class="btn btn-sm"
                      (click)="remove_declaration(gateway_config_editing_logic, j)"
                    >
                      刪除
                    </button>
                  </div>
                </c-card-body>
              </c-card>
            </c-col>
          </ng-container>
        </c-row>

        <c-row class="mb-3">
          <c-col>
            <button
              cButton
              variant="outline"
              color="primary"
              class="btn float-end"
              (click)="add_declaration(gateway_config_editing_logic)"
            >
              新增變數宣告
            </button>
          </c-col>
        </c-row>
        <label [sm]="6" cCol cLabel="col" for="Thresholds">
          條件判斷
        </label>

        <c-row>
          <ng-container *ngFor="let Threshold of gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_Thresholds; let j = index">
            <c-col sm="12" lg="12">
              <c-card class="mb-3">
                <c-card-body>
                  <div class="mb-2">
                    <label>優先級</label>
                    <input class="form-control form-control-sm" [value]="j" disabled />
                  </div>

                  <div class="mb-2">
                    <label>條件</label>
                    <input
                      cFormControl
                      class="form-control form-control-sm"
                      [(ngModel)]="Threshold.Condition"
                      name="Condition-{{j}}"
                      placeholder="Condition"
                      required
                    />
                  </div>

                  <div class="mb-2">
                    <label>動作</label>
                    <input
                      cFormControl
                      class="form-control form-control-sm"
                      [(ngModel)]="Threshold.Action"
                      name="Action-{{j}}"
                      placeholder="Action"
                      required
                    />
                  </div>

                  <div class="mb-2">
                    <label>邊界</label>

                    <div *ngFor="let boundary of Threshold.Boundary; let k = index; trackBy: trackByBoundary" class="mb-2">
                      <div class="d-flex align-items-center gap-2">
                        <c-form-check [switch]="true" class="me-2">
                          <input
                            cFormCheckInput
                            type="checkbox"
                            (change)="toggleThresholdBoundaryType(gateway_config_editing_logic, j, k)"
                            [checked]="isBoundaryArray(boundary)"
                          />
                        </c-form-check>

                        <!-- 單一數值 -->
                        <ng-container *ngIf="!isBoundaryArray(boundary)">
                          <input
                            type="number"
                            (input)="filterAndFillZero($event)"
                            class="form-control form-control-sm"
                            cFormControl
                            [(ngModel)]="Threshold.Boundary[k]"
                            placeholder="Boundary"
                            required
                          />
                        </ng-container>

                        <!-- 範圍數值 -->
                        <ng-container *ngIf="isBoundaryArray(boundary)">
                          <input
                            type="number"
                            (input)="filterAndFillZero($event)"
                            class="form-control form-control-sm"
                            cFormControl
                            [(ngModel)]="$any(boundary)[0]"
                            placeholder="Lower"
                            required
                          />
                          <input
                            type="number"
                            (input)="filterAndFillZero($event)"
                            class="form-control form-control-sm"
                            cFormControl
                            [(ngModel)]="$any(boundary)[1]"
                            placeholder="Upper"
                            required
                          />
                        </ng-container>

                        <button
                          cButton
                          variant="outline"
                          color="danger"
                          class="btn btn-sm"
                          (click)="remove_threshold_boundary(gateway_config_editing_logic, j, k)"
                        >
                          -
                        </button>
                      </div>
                    </div>

                    <button
                      cButton
                      variant="outline"
                      color="success"
                      class="btn btn-sm mt-1"
                      (click)="add_threshold_boundary(gateway_config_editing_logic, j)"
                    >
                      + 增加邊界
                    </button>
                  </div>

                  <div class="mb-2">
                    <label>判定間隔</label>
                    <input
                      type="number"
                      (input)="filterAndFillZero($event)"
                      cFormControl
                      class="form-control form-control-sm"
                      [(ngModel)]="Threshold.Interval"
                      name="Interval-{{j}}"
                      placeholder="Interval"
                      required
                    />
                  </div>

                  <div class="d-flex justify-content-end">
                    <button
                      cButton
                      variant="outline"
                      color="danger"
                      class="btn btn-sm"
                      (click)="remove_threshold(gateway_config_editing_logic, j)"
                    >
                      刪除
                    </button>
                  </div>
                </c-card-body>
              </c-card>
            </c-col>
          </ng-container>
        </c-row>

        <c-row class="mb-3">
          <c-col>
            <button
              cButton
              variant="outline"
              color="primary"
              class="btn float-end"
              (click)="add_threshold(gateway_config_editing_logic)"
            >
              新增條件判斷
            </button>
          </c-col>
        </c-row>

        <label [sm]="6" cCol cLabel="col" for="DvcBoundary">
          時間邊界
        </label>

        <c-row>
          <ng-container *ngFor="let TimeBoundary of gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_TimeBoundary; let j = index">
            <c-col sm="12" lg="12">
              <c-card class="mb-3">
                <c-card-body>
                  <!-- 優先級 -->
                  <div class="mb-2">
                    <label>優先級</label>
                    <input class="form-control form-control-sm" [value]="j" disabled />
                  </div>

                  <!-- 星期選擇 -->
                  <div class="mb-2">
                    <label>星期</label>
                    <div class="d-flex flex-wrap gap-1">
                      <ng-container *ngFor="let day of [6,0,1,2,3,4,5]; let idx = index">
                        <c-form-check [inline]="true">
                          <input cFormCheckInput type="checkbox"
                            [ngModel]="isSelected(day, gateway_config_editing_logic, j)"
                            (ngModelChange)="onDayChange($event, day, gateway_config_editing_logic, j)" />
                          <label cFormCheckLabel>{{ ['日','一','二','三','四','五','六'][idx] }}</label>
                        </c-form-check>
                      </ng-container>
                    </div>
                  </div>

                  <!-- 時間區間 -->
                  <div class="mb-2">
                    <label>時間</label>
                    <div class="d-flex align-items-center gap-1 flex-nowrap">
                      <input type="number" class="form-control form-control-sm" style="width: 60px;" placeholder="HH"
                        [(ngModel)]="TimeBoundary.Time[0][0]" (input)="filterAndFillZero($event)" required />
                      <span>:</span>
                      <input type="number" class="form-control form-control-sm" style="width: 60px;" placeholder="MM"
                        [(ngModel)]="TimeBoundary.Time[0][1]" (input)="filterAndFillZero($event)" required />
                      <span> - </span>
                      <input type="number" class="form-control form-control-sm" style="width: 60px;" placeholder="HH"
                        [(ngModel)]="TimeBoundary.Time[1][0]" (input)="filterAndFillZero($event)" required />
                      <span>:</span>
                      <input type="number" class="form-control form-control-sm" style="width: 60px;" placeholder="MM"
                        [(ngModel)]="TimeBoundary.Time[1][1]" (input)="filterAndFillZero($event)" required />
                    </div>
                  </div>

                  <!-- 判定間隔 -->
                  <div class="mb-2">
                    <label>判定間隔</label>
                    <input type="number" (input)="filterAndFillZero($event)" class="form-control form-control-sm"
                      [(ngModel)]="TimeBoundary.Interval" placeholder="Interval" required />
                  </div>

                  <!-- 邊界 -->
                  <div class="mb-2">
                    <label>邊界</label>
                    <ng-container *ngFor="let boundary of TimeBoundary.Boundary; let k = index; trackBy: trackByBoundary">
                      <div class="d-flex align-items-center gap-2 mb-2">
                        <c-form-check [switch]="true" class="me-2">
                          <input type="checkbox" cFormCheckInput
                            (change)="toggleTimeBoundaryType(gateway_config_editing_logic, j, k)"
                            [checked]="isBoundaryArray(boundary)" />
                        </c-form-check>

                        <ng-container *ngIf="!isBoundaryArray(boundary)">
                          <input type="number" class="form-control form-control-sm" placeholder="Boundary"
                            [(ngModel)]="TimeBoundary.Boundary[k]" (input)="filterAndFillZero($event)" required />
                        </ng-container>

                        <ng-container *ngIf="isBoundaryArray(boundary)">
                          <input type="number" class="form-control form-control-sm" placeholder="Lower"
                            [(ngModel)]="$any(boundary)[0]" (input)="filterAndFillZero($event)" required />
                          <input type="number" class="form-control form-control-sm" placeholder="Upper"
                            [(ngModel)]="$any(boundary)[1]" (input)="filterAndFillZero($event)" required />
                        </ng-container>

                        <button cButton size="sm" color="danger" variant="outline"
                          (click)="remove_timeBoundary_boundary(gateway_config_editing_logic, j, k)">-</button>
                      </div>
                    </ng-container>

                    <button cButton size="sm" color="success" variant="outline"
                      (click)="add_timeBoundary_boundary(gateway_config_editing_logic, j)">新增邊界</button>
                  </div>

                  <!-- 刪除按鈕 -->
                  <div class="d-flex justify-content-end">
                    <button cButton size="sm" color="danger" variant="outline"
                      (click)="remove_timeBoundary(gateway_config_editing_logic, j)">刪除</button>
                  </div>
                </c-card-body>
              </c-card>
            </c-col>
          </ng-container>
        </c-row>

        <!-- 新增時間邊界按鈕 -->
        <c-row class="mb-3">
          <c-col class="text-end">
            <button cButton variant="outline" color="primary"
              (click)="add_timeBoundary(gateway_config_editing_logic)">新增時間邊界</button>
          </c-col>
        </c-row>

              <c-input-group class="mb-4">
                <label [sm]="2" cCol cLabel="col" for="DvcBoundary">
                  設備邊界
                </label>
                <c-col [sm]="10">
                  <c-row *ngFor="let boundary of gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary; let j = index; trackBy: trackByBoundary" class="align-items-center mb-2">
                    <c-col class="d-flex align-items-center gap-2">
                      <c-form-check [switch]="true" class="me-2">
                        <input
                          cFormCheckInput
                          checked
                          type="checkbox"
                          (change)="toggleDvcBoundaryType(gateway_config_editing_logic, j)"
                          [checked]="isBoundaryArray(gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary[j])"
                        />
                      </c-form-check>

                      <ng-container *ngIf="!isBoundaryArray(gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary[j])">
                        <input
                          type="number"
                          (input)="filterAndFillZero($event)"
                          aria-label="Boundary"
                          cFormControl
                          [(ngModel)]="gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary[j]"
                          name="Boundary-{{j}}"
                          placeholder="Boundary"
                          class="form-control boundary-input"
                          required/>
                      </ng-container>

                      <ng-container *ngIf="isBoundaryArray(gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary[j])">
                        <input
                          type="number"
                          [ngModel]="getBoundary(j, 0)"
                          (ngModelChange)="setBoundary(j, 0, $event)"
                          name="Boundary-{{j}}-lower"
                          placeholder="Lower Limit"
                          class="form-control boundary-input"
                          required />

                        <input
                          type="number"
                          [ngModel]="getBoundary(j, 1)"
                          (ngModelChange)="setBoundary(j, 1, $event)"
                          name="Boundary-{{j}}-upper"
                          placeholder="Upper Limit"
                          class="form-control boundary-input"
                          required />

                      </ng-container>
                      <button
                        cButton variant="outline"
                        color="danger"
                        (click)="remove_dvcBoundary_boundary(gateway_config_editing_logic, j)">-</button>
                    </c-col>
              </c-row>
              <c-col>
                <button
                  cButton variant="outline"
                  color="success"
                  variant="outline"
                  (click)="add_dvcBoundary_boundary(gateway_config_editing_logic)">
                  +
                </button>
              </c-col>
            </c-col>
          </c-input-group>
        </div>
      <div *ngIf="gateway_config.Strategy.Logics[gateway_config_editing_logic].Type==='DataTransByRedis'" >
        <c-row class="mb-3">
          <label [sm]="4" cCol cLabel="col" for="VarName">
            變數名稱
          </label>
          <c-col [sm]="8">
            <input aria-describedby="basic-addon1"
                aria-label="VarName"
                cFormControl
                [(ngModel)]="gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.DataTransByRedis_VarName" name="VarName" placeholder = "變數名稱"
        />
        </c-col>
        </c-row>
        <c-row class="mb-3">
          <label [sm]="4" cCol cLabel="col" for="VarDefinition">
            變數定義
          </label>
          <c-col [sm]="8">
            <input aria-describedby="basic-addon1"
                aria-label="VarDefinition"
                cFormControl
                [(ngModel)]="gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.DataTransByRedis_VarDefinition" name="VarDefinition" placeholder = "變數定義"
        />
        </c-col>
        </c-row>
        <c-row class="mb-3">
          <label [sm]="4" cCol cLabel="col" for="ServerIP">
            目標設備區網IP
          </label>
          <c-col [sm]="8">
            <input aria-describedby="basic-addon1"
                aria-label="ServerIP"
                cFormControl
                [(ngModel)]="gateway_config.Strategy.Logics[gateway_config_editing_logic].Arguments.DataTransByRedis_ServerIP" name="ServerIP" placeholder = "目標設備區網IP"
        />
        </c-col>
        </c-row>
      </div>

   </c-modal-body>
</c-modal>
