import { default_pin_data, allowed_input_pinName, allowed_output_pinName, driver_list } from './default_pin_data';
import { GatewayService } from './../../../service/gateway.service';
import { ProjectService } from './../../../service/project.service';
import { DashboardService } from './../../../service/dashboard.service';
import {
  OnInit,
  Component,
  ElementRef,
  ViewChild,
  Renderer2,
} from '@angular/core';
import { dia, shapes, ui } from '@joint/plus';
import { forkJoin } from 'rxjs';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { setTheme } from '@joint/plus';
import { linkTools } from '@joint/core';
import { CommonModule } from '@angular/common';
import {
  AccordionButtonDirective,
  AccordionComponent,
  AccordionItemComponent,
  TemplateIdDirective,
  ButtonCloseDirective,
  ButtonDirective,
  CardBodyComponent, CardComponent,
  CardHeaderComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  FormCheckComponent, FormCheckInputDirective, FormCheckLabelDirective,
  HeaderComponent,
  InputGroupComponent,
  InputGroupTextDirective,
  TableModule,
  GridModule,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  FormControlDirective,
  FormSelectDirective,
  OffcanvasToggleDirective,
  Tabs2Module,
  SpinnerComponent,
} from '@coreui/angular';
import {
  type_name_trans,
  serial_no_list,
  object_type_list,
  link_type_list,
  link_connectable_object,
} from '../object_type';
import { IconDirective } from '@coreui/icons-angular';
import * as joint from 'jointjs';
import * as vsmshapes from '@joint/shapes-vsm';
import '@joint/plus/joint-plus.css';

interface color {
  color: string,
  transparent: boolean
}

type IntOrPair = number | [number, number];

interface MAC{
  interface: string;
  address: string;
}

interface Thresholds {
  Condition: string;
  Action: string;
  Boundary: IntOrPair[];
  Interval: number;
}

interface Declaration {
  VarName: string;
  Definition: string;
  Rolling_Funct: string;
  Rolling_Len: number;
}

interface TimeBoundary {
  WeekDay: number[];
  Time: number[][];
  Interval: number;
  Boundary: IntOrPair[];
}

interface CmderServer {
  ServerAddress: string;
  ServerPort: number;
  Wait: number;
}
interface FixedIP {
  IP: string;
  Routers: string;
}

interface WiFi {
  ssid: string;
  identity: string;
  psk: string;
  key_mgmt: string;
  priority: number;
}

interface Identical_placeID_device {
  slaveAddress: string;
  placeID: string;
}

interface Host {
  HostName: string;
  SerialNo: string;
  Identical_placeID_list: Identical_placeID_device[];
  LogLevel: string;
  LogFile: string;
  Version: string;
  CmderServer: CmderServer;
  PostDataPeriod: number;
}

interface Communication {
  WiFi: WiFi[];
  PostServer: CmderServer[];
  FixedIP: FixedIP;
  PhoneNumber: string;
}

interface Pin {
  PortName: string;
  SlaveAddress: number;
  PinAddress: number;
  PinName: string
}
interface PinDefinition {
  Pin: string;
  RenameAs: string;
  Read: string;
  Write: string;
  Register_W: string;
}

interface PinConfig{
  PinIdx: number;
  SensorType: string;
  Definition: PinDefinition[];
}
interface baseLogic {
  Type: string;
  ExecutionPrd: number;
  InputList: Pin[];
  OutputList: Pin[];
  Arguments: {
    CondCtrlByT2_DvcBoundary: IntOrPair[];
    CondCtrlByT2_TimeBoundary: TimeBoundary[];
    CondCtrlByT2_Declaration: Declaration[];
    CondCtrlByT2_Thresholds: Thresholds[];
    CondCtrlByT2_RedisDB: string;
    DataTransByRedis_VarName: string;
    DataTransByRedis_VarDefinition: string;
    DataTransByRedis_ServerIP: string;
    DataTransByRedis_RedisDB: string;
  }
}

interface Strategy {
  Logics: baseLogic[];
}

interface Modbus {
  Driver: { DriverName: string; PortAddress: string; SlaveAddress: number ;PinConfig: PinConfig[];}[];
}

interface Config {
  Host: Host;
  Communication: Communication;
  Strategy: Strategy;
  Modbus: Modbus;
}

const mergedShapes = {
  ...shapes,
  ...vsmshapes,
};

interface plot_object {
  type: string;
  name: string;
  place_id: string;
  serialNo: string;
  gateway_id: string | null;
}

interface link {
  type: string;
  name: string;
  source: string;
  target: string;
  serialNo: string;
}
@Component({
  selector: 'app-device-dashboard-edit',
  standalone: true,
  imports: [
    AccordionButtonDirective,
    AccordionComponent,
    AccordionItemComponent,
    TemplateIdDirective,
    ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent, CardComponent,
    CardHeaderComponent,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    FormCheckComponent, FormCheckInputDirective, FormCheckLabelDirective,
    HeaderComponent,
    InputGroupComponent,
    InputGroupTextDirective,
    TableModule,
    GridModule,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    CommonModule,
    IconDirective,
    FormsModule,
    ReactiveFormsModule,
    FormControlDirective,
    FormSelectDirective,
    OffcanvasToggleDirective,
    Tabs2Module,
    SpinnerComponent,
    RouterModule
  ],
  templateUrl: './device-dashboard-edit.component.html',
  styleUrls: ['./device-dashboard-edit.component.scss'],
})
export class DeviceDashboardEditComponent implements OnInit {
  // Declaration
  @ViewChild('canvas') canvas: ElementRef;
  @ViewChild('bgFileInput') bgFileInput!: ElementRef<HTMLInputElement>;
  project_code: string;
  projectplot_name = '';
  place_id_list: string[] = [];
  gateway_list: string[] = [];
  namespace = shapes;
  graph: dia.Graph;
  commandManager: dia.CommandManager;
  modal_title = '';
  paper: dia.Paper;
  toolbar: ui.Toolbar;
  paperScroller: ui.PaperScroller;
  rect1: shapes.standard.Rectangle;
  stencil: ui.Stencil;
  selection: ui.Selection;
  reader: FileReader;
  imageAddress = '';
  nav: ui.Navigator;
  halo: ui.Halo;
  selection_handles: ui.Selection.Options;
  currentEditingElement: HTMLElement | null = null;
  editing_object_type: string = '';
  creating_link: string = '';
  editing_object_id: string = '';
  editing_link_id: string = '';
  editing_block_id: string = '';
  creating_link_id: string = '';
  input_name: string = '';
  pending_save: boolean = false;
  count_serialNo: string = '';
  new_serialNo: string = '0';
  new_place_id: string = '';
  new_object_name: string = '';
  new_link_type: string = '';
  new_link_serialNo: string = '';
  new_link_source = '';
  new_link_target = '';
  new_link_name = '';

  repeated_link: boolean = false;
  remove_link_serialNo: string = '';
  link_type_list = link_type_list;
  object_type_list = object_type_list;
  connectableSourceList: plot_object[];
  connectableLinkTypeList: string[];
  connectableTargetList: plot_object[];
  projectobject_list: plot_object[] = [];
  projectlink_list: link[] = [];
  selected_object_setting_index: number;
  currentList: any[] = [];
  projectplot_list: string[] = [];
  create_link_visible: boolean = false;
  plot_list_visible: boolean = false;
  link_list_visible: boolean = false;
  object_list_visible: boolean = false;
  object_visible: boolean = false;
  object_style_visible: boolean = false;
  link_visible: boolean = false;
  link_style_visible: boolean = false;
  textBlock_style_visible = false;
  place_id_visible: boolean = false;
  helpModalVisible: boolean = false;
  create_object_visible: boolean = false;
  edit_port_visible: boolean = false;
  setting_visible: boolean = false;
  remove_object_visible: boolean = false;
  remove_link_visible: boolean = false;

  isSidebarOpen: boolean = false;
  activeMenu: string | null = null;
  activeTab: 'select' | 'create' = 'select'; // 預設為選擇設定檔
  activeObjectTab: 'attr' | 'config' = 'config';
  activeGatewayTab: 'Host' | 'Communication' | 'Driver' | 'Logic' = 'Host';
  currentTab: string = '規劃圖'; // 默認顯示規劃圖
  accordionSearch: string = '';
  searchPlotText = '';
  searchObjectText = '';
  searchPlaceIdText = '';
  searchLinkText = '';
  editingObject: plot_object | null = null;
  editingLink: link | null = null;

  editLock: boolean = false;

  new_link_source_name: string = '';
  new_link_target_name: string = '';
  gateway_config_visible: boolean = false;
  editing_gateway_id: string = '';
  editing_cell: any = null;
  editing_link: any = null;
  editing_textBlock: any = null;
  editing_cell_attr_strokeWidth: number = 2;
  editing_cell_attr_strokeColor: string = '#000000';
  editing_cell_attr_fillColor: color = {
    color: '#000000', transparent: false
  };
  editing_cell_attr_fontSize: number = 14;
  editing_cell_attr_fontColor: string = '#000000';
  editing_cell_attr_dynamic_fillColor_available: boolean = false;
  editing_cell_attr_dynamic_fillColor = [
    { threshold: 0.5, color: '#ff0000', transparent: false }
  ];
  editing_cell_dashboard_visible: boolean = false;
  editing_cell_dashboard_serialNo: string = '';
  editing_cell_dashboard_name: string = '';
  editing_cell_dashboard_name_visible: boolean = true;
  editing_cell_dashboard_TableName: string = '';
  editing_cell_dashboard_TableColumn: string = '';
  editing_cell_dashboard_TableColumn_visible: boolean = true;
  editing_cell_dashboard_time: string = '%m:%d;%H:%M';
  editing_cell_dashboard_time_visible: boolean = true;
  editing_cell_dashboard_data_floatNumber: number = 0;

  editing_link_attr_strokeWidth: number = 2
  editing_link_attr_strokeColor: string = '#ffffff';
  editing_link_dashboard_visible: boolean = false;
  editing_link_dashboard_serialNo: string = '';

  editing_textBlock_attr_strokeWidth: number = 2;
  editing_textBlock_attr_strokeColor: string = '#000000';
  editing_textBlock_attr_fillColor: color = {
    color: '#94ECBE', transparent: false
  };
  editing_textBlock_attr_fontSize: number = 14;
  editing_textBlock_attr_fontColor: string = '#000000';
  editing_textBlock_dashboard_visible: boolean = false;

  editing_plot_background_alpha: number = 0.5;
  editing_plot_plotname_visible: boolean = true;
  editing_plot_updatedTime_visible: boolean = true;
  editing_plot_updatedTime_period: number = 60;

  removing_cell: any = null;
  removing_link: any = null;

  gateway_config: Config = {
    Host: {
      HostName: '99999999999',
      SerialNo: '0',
      Identical_placeID_list: [],
      LogLevel: 'INFO',
      LogFile: './log/HT1.log',
      Version: '1.0.0',
      CmderServer: {
        ServerAddress: 'imabox-server3.ima-ems.com',
        ServerPort: 8883,
        Wait: 60,
      },
      PostDataPeriod: 60
    },
    Communication: {
      WiFi: [],
      PostServer: [],
      FixedIP: {
        IP: '',
        Routers: ''
      },
      PhoneNumber: ''
    },
    Strategy: {
      Logics: [],
    },
    Modbus: {
      Driver: [],
    },
  };
  gateway_config_connected: boolean = false;
  gateway_config_getRemoteState: boolean = false;
  gateway_config_update_visible: boolean = false;
  gateway_config_mac_modal_visible: boolean = false;
  gateway_config_get_driver_pin_visible: boolean = false;
  gateway_config_selected_logic: string = '';
  gateway_config_selected_type: string = '';
  gateway_config_MAC_msg: string = '';
  gateway_config_ssh_msg: string = '';
  gateway_config_MAC_list: MAC[] = [];
  gateway_config_checking_version: boolean = true;
  gateway_config_update_available_boolean: boolean = false;
  gateway_config_latest_version: string = '';
  gateway_config_testing_driver_pin_portName: string = '';
  gateway_config_testing_driver_pin_slaveAddr: number = 0;
  gateway_config_testing_driver_pin_pinAddr: number = 0;
  gateway_config_testing_driver_pin_pinName: string = '';
  gateway_config_get_driver_pin_result: string = '';
  gateway_config_selected_logic_type: string = 'CondCtrlByT2';
  gateway_config_editing_pin_def_dtype: string = 'D';
  gateway_config_editing_pin_def_lrange: number = 0;
  gateway_config_editing_pin_def_hrange: number = 1;
  gateway_config_editing_pin_def_adc: number = 8;
  gateway_config_edit_pin_def_index = 0;
  gateway_config_edit_pin_def_config_visible = false;
  gateway_config_editing_pin_config = 0;
  gateway_config_pin_config_visible = false;
  gateway_config_edit_logic_visible = false;
  gateway_config_editing_logic = 0;
  gateway_config_add_logic_visible = false;

  allowed_input_pinName = allowed_input_pinName;
  allowed_output_pinName = allowed_output_pinName;
  driver_list = driver_list;

  constructor(
    private DashboardService: DashboardService,
    private GatewayService: GatewayService,
    private route: ActivatedRoute,
    private el: ElementRef,
    private renderer: Renderer2,
    private ProjectService: ProjectService,
    private router: Router
  ) {
    this.load_project_info();
  }
  load_project_info() {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.projectplot_list = [];
    this.ProjectService.get_projectplotlist(this.project_code).subscribe(
      (data) => {
        for (let i = 0; i < data.projectplot_list.length; i++) {
          this.projectplot_list[i] = data.projectplot_list[i].projectplot_name;
        }
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
    this.projectobject_list = [];
    this.DashboardService.get_projectplot_object(this.project_code).subscribe(
      (data) => {
        for (let i = 0; i < data.object_list.length; i++) {
          this.projectobject_list.push({
            type: data.object_list[i].type,
            name: data.object_list[i].name,
            place_id: data.object_list[i].place_id,
            serialNo: data.object_list[i].serialNo,
            gateway_id: data.object_list[i].gateway_id || null,
          });
        }
        localStorage.setItem('token', data.token);
        this.projectlink_list = [];
        this.DashboardService.get_projectplot_link(this.project_code).subscribe(
          (data) => {
            for (let i = 0; i < data.link_list.length; i++) {
              const translatedType = type_name_trans(data.link_list[i].type) || data.link_list[i].type || '';
              this.projectlink_list.push({
                type: translatedType,
                name: data.link_list[i].name,
                source: data.link_list[i].source,
                target: data.link_list[i].target,
                serialNo: data.link_list[i].serialNo,
              });
            }
            this.filter_invalid_object();
            localStorage.setItem('token', data.token);
          },
          (error) => {
            this.router.navigate(['/404']);
          }
        );
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );

    this.ProjectService.get_project_info(this.project_code).subscribe(
      (data) => {
        this.place_id_list = data.data.place_id;
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  ngOnInit(): void {
    // Initialization and Declaration
    this.namespace = mergedShapes;
    this.graph = new dia.Graph({}, { cellNamespace: this.namespace });
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.projectplot_name = this.route.snapshot.paramMap.get('projectplot_name') || '';
    this.activeTab = 'select'; // 預設為選擇設定檔
    this.selected_object_setting_index = -1;
    this.DashboardService.get_plot(
      this.project_code,
      this.projectplot_name
    ).subscribe(
      (data) => {
        this.load_graph(data.data);
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
        this.toggle_modal('');
      }
    );
    setInterval(() => this.saveData(), 3000);
    this.commandManager = new dia.CommandManager({ graph: this.graph });
    this.paper = new dia.Paper({
      el: document.getElementById('my-paper'),
      model: this.graph,
      width: 2000,
      height: 2000,
      gridSize: 5,
      drawGrid: true,
      snapLinks: true,
      linkPinning: false,
      restrictTranslate: true,
      background: { color: '#F5F5F5' },
      cellViewNamespace: this.namespace,
      defaultRouter: { name: 'orthogonal' },
      defaultConnector: { name: 'straight', args: { cornerType: 'line' } },
      defaultLink: new vsmshapes.VSMShipment({
        attrs: {
          line: {
            stroke: '#000000',
            strokeWidth: 2,
            targetMarker: {
              type: 'path',
              d: '',
            },
          },
        },
      }),
      interactive: (cellView) => {
        return !this.editLock;
      }
    });




    const verticesTool = new linkTools.Vertices();
    const boundaryTool = new linkTools.Boundary();
    const removeButton = new linkTools.Remove({
      distance: 20,
      markup: [
        {
          tagName: 'circle',
          selector: 'button',
          attributes: {
            r: 7,
            fill: '#4A7856', // ← 綠色按鈕背景
            cursor: 'pointer',
          },
        },
        {
          tagName: 'path',
          selector: 'icon',
          attributes: {
            d: 'M -3 -3 L 3 3 M -3 3 L 3 -3',
            stroke: '#FFFFFF', // ← X 的顏色，白色比較清楚
            'stroke-width': 2,
            'stroke-linecap': 'round',
            'pointer-events': 'none',
          },
        },
      ],
      action: (evt, linkView) => {
        this.toggle_modal('remove_link', linkView.el.getAttribute('model-id') || '');
      }
    });
    const editViewTool = new linkTools.Button({
      name: 'editview-button',
      distance: 40,
      markup: [
        {
          tagName: 'g',
          selector: 'edit-button',
          children: [
            {
              tagName: 'circle',
              selector: 'button',
              attributes: {
                r: 7,
                fill: '#4A7856',
                cursor: 'pointer',
              },
            },
            {
              tagName: 'path',
              selector: 'icon',
              attributes: {
                d: 'M244.884,43.885c-0.847-0.89-1.705-1.772-2.577-2.645c-0.096-0.096-0.202-0.179-0.301-0.272 C215.235,14.546,179.822,0,142.169,0C104.194,0,68.493,14.788,41.642,41.641c-55.43,55.431-55.43,145.624,0,201.055 c26.852,26.853,62.553,41.641,100.527,41.641s73.676-14.788,100.527-41.641C297.386,188.006,298.112,99.479,244.884,43.885z M62.854,62.854C84.04,41.668,112.208,30,142.169,30c24.702,0,48.18,7.938,67.536,22.595L52.589,209.674 C19.45,165.758,22.866,102.843,62.854,62.854z',
                fill: '#FFFFFF',
                transform: 'scale(0.03) translate(-145, -145)',
                'pointer-events': 'none',
              },
            },
          ],
        },
      ],
      action: (evt, linkView) => {
        this.toggle_modal('link_style', linkView.el.getAttribute('model-id') || '');
    }});
    const editTool = new linkTools.Button({
      name: 'edit-button',
      distance: 60,
      markup: [
        {
          tagName: 'g',
          selector: 'edit-button',
          children: [
            {
              tagName: 'circle',
              selector: 'button',
              attributes: {
                r: 7,
                fill: '#4A7856',
                cursor: 'pointer',
              },
            },
            {
              tagName: 'path',
              selector: 'line1',
              attributes: {
                d: 'M -4 -3 H 4',
                stroke: '#FFFFFF',
                'stroke-width': 1.5,
                'stroke-linecap': 'round',
                'pointer-events': 'none',
              },
            },
            {
              tagName: 'path',
              selector: 'line2',
              attributes: {
                d: 'M -4 0 H 4',
                stroke: '#FFFFFF',
                'stroke-width': 1.5,
                'stroke-linecap': 'round',
                'pointer-events': 'none',
              },
            },
            {
              tagName: 'path',
              selector: 'line3',
              attributes: {
                d: 'M -4 3 H 4',
                stroke: '#FFFFFF',
                'stroke-width': 1.5,
                'stroke-linecap': 'round',
                'pointer-events': 'none',
              },
            },
          ],
        },
      ],
      action: (evt, linkView) => {
        this.toggle_modal('link', linkView.el.getAttribute('model-id') || '');
        /*
        window.open(
          `/project/link/${this.project_code}/${
            linkView.model.get('config')?.serialNo.split(':')[0]
          }/${linkView.model.get('config')?.serialNo}`,
          '_blank'
        );
        */
      },
    });
    const toolsView = new dia.ToolsView({
      tools: [verticesTool, boundaryTool, editTool, editViewTool, removeButton],
    });


    this.paperScroller = new ui.PaperScroller({
      paper: this.paper,
      scrollWhileDragging: true,
      restrictTranslate: true,
    });
    this.selection = new ui.Selection({
      paper: this.paper,
    });
    this.selection.removeHandle('rotate');
    setTheme('modern');
    document.getElementById('paper')!.appendChild(this.paperScroller.render().el);
    this.paperScroller.centerContent();
    this.paperScroller.center();


    // Operations
    /// drag move
    let lastDistance: number | null = null;

    this.paperScroller.el.addEventListener(
      'touchmove',
      (e: TouchEvent) => {
        if (e.touches.length >= 2) {
          e.preventDefault(); // 阻止預設縮放行為

          const dx = e.touches[0].clientX - e.touches[1].clientX;
          const dy = e.touches[0].clientY - e.touches[1].clientY;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (lastDistance !== null) {
            const delta = distance - lastDistance;

            // 只有超過一定距離變化才進行縮放
            if (Math.abs(delta) > 1) {
              let scaleFactor = 1 + delta / 300; // 放大或縮小的比例，數字越大越平滑

              scaleFactor = Math.max(0.95, Math.min(scaleFactor, 1.05));

              this.paperScroller.zoom(this.paperScroller.zoom() * scaleFactor, {
                max: 5,
                min: 0.2,
                ox: (e.touches[0].clientX + e.touches[1].clientX) / 2,
                oy: (e.touches[0].clientY + e.touches[1].clientY) / 2,
                absolute: true
              });
            }
          }

          lastDistance = distance;
        }
      },
      { passive: false }
    );

    this.paperScroller.el.addEventListener(
      'touchend',
      (e: TouchEvent) => {
        if (e.touches.length < 2) {
          lastDistance = null;
        }
      },
      { passive: true }
    );



    let pinchTimeout: any = null; // 用於存儲節流的計時器
    this.paper.on('paper:pinch', (_evt, ox, oy, scale) => {

      if (pinchTimeout) return; // 如果計時器存在，則跳過此次觸發

      pinchTimeout = setTimeout(() => {
        pinchTimeout = null; // 清除計時器，允許下一次觸發
      }, 10); // 設置觸發間隔為 100 毫秒

      this.paperScroller.zoom(this.paperScroller.zoom() * scale, {
        min: 0.2,
        max: 5,
        ox,
        oy,
        absolute: true,
      });
    });

    /// 連續選取
    this.paper.on('element:pointerup', (elementView, evt) => {
      if (evt.ctrlKey || evt.metaKey) {
        this.selection.collection.add(elementView.model);
      }
    });


    /// 開啟halo
    this.paper.on('cell:pointerup', (cellView) => {

      if (cellView.model.get('type') === 'standard.TextBlock') {
        // textbox here
        const halo = new ui.Halo({
          cellView: cellView,
          type: 'overlay',
        }).render();
        halo.removeHandle('unlink');
        halo.removeHandle('rotate');
        halo.removeHandle('clone');
        halo.removeHandle('fork');
        halo.removeHandle('link');
        halo.addHandle({
          name: 'edit_view',
          position: 'ne',
          icon: this.createViewIcon('#57477A', '#FF17D3'),
          hideOnDrag: true,
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '編輯樣式',
                'data-tooltip-position': 'left'
            }
        }
        });
        halo.on('action:edit_view:pointerdown', (evt) => {
          evt.stopPropagation();
          this.toggle_modal('textBlock_style', cellView.el.getAttribute('model-id') || '');
        });
        halo.changeHandle('remove', {
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '移除文字方塊',
                'data-tooltip-position': 'right'
            }
        }
        });
        halo.changeHandle('resize', {
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '拖曳以調整大小',
                'data-tooltip-position': 'left'
            }
        }
        });
        halo.on('action:remove:pointerdown', (evt) => {
          evt.stopPropagation();
          this.pending_save = true;
        });
      } else if (cellView.model.isElement()) {
        const foundObject = this.projectobject_list.find(
          (obj) => obj.serialNo === cellView.model.get('config')?.serialNo
        );
        // objects here
        const halo = new ui.Halo({
          cellView: cellView,
          type: 'overlay',
          boxContent: foundObject ? `${foundObject.serialNo}` : '',
        }).render();
        halo.removeHandle('unlink');
        halo.removeHandle('rotate');
        halo.removeHandle('clone');
        halo.removeHandle('fork');
        halo.changeHandle('resize', {
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '拖曳以調整大小',
                'data-tooltip-position': 'left'
            }
        }
        });
        halo.changeHandle('remove', {
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '移除元件',
                'data-tooltip-position': 'right'
            }
        }
        });
        halo.changeHandle('link', {
          position: 'e',
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '點擊並拖曳以創建元件連結',
                'data-tooltip-position': 'left'
            }
        }
        });

        halo.addHandle({
          name: 'edit',
          position: 'sw',
          icon: this.createSettingIcon('#57477A', '#FF17D3'),
          hideOnDrag: true,
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '編輯元件',
                'data-tooltip-position': 'right'
            }
        }
        });
        halo.addHandle({
          name: 'edit_view',
          position: 'ne',
          icon: this.createViewIcon('#57477A', '#FF17D3'),
          hideOnDrag: true,
          attrs: {
            '.handle': {
                'data-tooltip-class-name': 'small',
                'data-tooltip': '編輯樣式',
                'data-tooltip-position': 'left'
            }
        }
        });
        halo.on('action:edit_view:pointerdown', (evt) => {
          evt.stopPropagation();
          this.toggle_modal('object_style', cellView.el.getAttribute('model-id') || '');
        });
        halo.on('action:edit:pointerdown', (evt) => {
          evt.stopPropagation();
          this.toggle_modal('object', cellView.el.getAttribute('model-id') || '');
        });

        halo.on('action:remove:pointerdown', (evt: PointerEvent) => {
          this.commandManager.undo();
          this.toggle_modal('remove_object', cellView.el.getAttribute('model-id') || '');
        });


        halo.on('action:link:add', (link) => {
          if (!link.get('target').id) {
            link.remove();
            this.pending_save = true;
          } else {
            const source_id = this.graph
              .getCell(link.get('source').id)
              .get('config')?.serialNo;
            const target_id = this.graph
              .getCell(link.get('target').id)
              .get('config')?.serialNo;
            this.creating_link_id = link.id;
            this.toggle_modal('create_link', `${source_id}:${target_id}`);
            if (this.connectableLinkTypeList.length == 0) {
              this.toggle_modal('');
              this.creating_link_id = '';
              this.creating_link = '';
              link.remove();
              this.pending_save = true;
            }
          }
        });
      }
      // else if model == xxx, then open corresponding halo
      // check if the object is the last one in all project plot before removing the object.

  });


    /// 連續選取
    this.selection.on('selection-box:pointerdown', (elementView, evt) => {
      if (evt.ctrlKey || evt.metaKey) {
        this.selection.collection.remove(elementView.model);
      }
    });

    /// 生成元件事件
    this.graph.on('add', (cell) => {
      if (cell.get('type') === 'standard.Rectangle'){
        this.toggle_modal('create_object');

        this.editing_object_id = cell.id;
      }

    });

    // 圖形更動觸發儲存檔案
    this.graph.on('change', (cell) => {
      this.pending_save = true;
    });

    // 圖形移除觸發儲存檔案
    this.graph.on('remove', () => {
      this.pending_save = true;
    });

    /// 雙擊空白處創建文字方塊
    this.paper.on('blank:pointerdblclick', (event: joint.dia.Event) => {

        const paperCoords = this.paper.clientToLocalPoint({
          x: event.clientX || 0,
          y: event.clientY || 0,
        });

        // 創建文字方塊
        const textBlock = new shapes.standard.TextBlock();

        textBlock.position(paperCoords.x, paperCoords.y);
        textBlock.resize(100, 30); // 設置大小
        textBlock.attr({
          body: {
            fill: 'transparent',
            stroke: 'transparent',
            pointerEvents: 'none',
          },
          label: {
            text: 'textBlock',
            fill: '#000000',
            textAnchor: 'middle',
            yAlignment: 'middle',
            pointerEvents: 'auto',
          },
        });

        // 添加到圖表
        this.graph.addCell(textBlock);
        this.pending_save = true;
      }

    );

    let currentEditingElement: HTMLElement | null = null;

    // 雙擊文字方塊觸發編輯
    this.paper.on('cell:pointerdblclick', function (cellView, evt) {

        const target = evt.target as HTMLElement;
      const cell = cellView.model;

      if (evt.target.nodeName === 'DIV') {
        target.setAttribute('contenteditable', 'true');
        target.style.whiteSpace = 'normal';
        target.style.wordBreak = 'break-word';
        target.style.width = '100%';
        target.style.height = '100%';
        target.focus();
        const range = document.createRange();
        const selection = window.getSelection();
        range.selectNodeContents(target);
        range.collapse(false);
        selection?.removeAllRanges();
        selection?.addRange(range);
        currentEditingElement = evt.target;
        target.addEventListener('blur', () => {
          const newText = target.innerText;
          cell.prop('attrs/label/text', newText);
          target.removeAttribute('contenteditable');
        });
      }
      }
    );

    // 按一鍵關閉文字方塊編輯
    let pressTimer: any = null;
    this.paper.on('blank:pointerdown', (evt) => {
      if (this.editLock) {
        evt.stopImmediatePropagation();
      }
      if (evt.ctrlKey || evt.metaKey) {
        this.selection.startSelecting(evt);
      } else if (lastDistance === null) {
        this.selection.stopSelecting(evt);
        ui.TextEditor.close();

        if (currentEditingElement) {
          currentEditingElement.blur();
          currentEditingElement = null;
        }
        this.paperScroller.startPanning(evt);
        this.selection.collection.reset();
        this.activeMenu = '';

        // ✅ 把 clientX/clientY 轉成 JointJS 的 localPoint
        const localPoint = this.paper.clientToLocalPoint({
          x: evt.clientX || 0,
          y: evt.clientY || 0,
        });

        pressTimer = setTimeout(() => {
          const ct = new ui.ContextToolbar({
            target: {
              x: evt.clientX,
              y: evt.clientY,
            },
            tools: [
              {
                action: 'add-object',
                content: '新增元件'
              },
              {
                action: 'add-bg',
                content: '上傳背景圖'
              },
              {
                action: 'remove-bg',
                content: '移除背景圖'
              },
            ],
            vertical: true,
          });

          ct.on('action:remove-bg', () => {
            this.removeBackground();
            ct.remove();
          });

          ct.on('action:add-bg', () => {
            this.bgFileInput.nativeElement.click(); // 開啟檔案選擇器
            this.bgFileInput.nativeElement.onchange = () => {
              const file = this.bgFileInput.nativeElement.files?.[0];
              if (!file) return;

              if (file.type.startsWith('image/') && file.size < 5 * 1024 * 1024) {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('project_code', this.project_code);
                formData.append('plot_name', this.projectplot_name);

                this.DashboardService.upload_pjplot_background(formData).subscribe(
                  (data) => {
                    this.imageAddress = data.filename;
                    const enhancedData = {
                      ...this.graph.toJSON(),
                      backgroundImage: this.imageAddress,
                    };
                    this.load_graph(enhancedData);
                    localStorage.setItem('token', data.token);
                    this.pending_save = true;
                  },
                  (error) => {
                    alert('上傳失敗，請稍後再試');
                  }
                );
              } else {
                alert('圖片格式錯誤或大小超過 5MB');
              }

              // 清空選取，避免同一張圖無法再次觸發 onchange
              this.bgFileInput.nativeElement.value = '';
            };


          });

          ct.on('action:add-object', () => {
            const rect = new shapes.standard.Rectangle({
              position: {
                x: localPoint.x,
                y: localPoint.y,
              },
              size: { width: 80, height: 60 },
              attrs: {
                body: {
                  stroke: '#000000',
                  rx: 2,
                  ry: 2,
                  fill: '#94ECBE',
                },
                label: {
                  text: '',
                  fill: '#000000',
                },
              },
            });
            this.graph.addCell(rect);
            ct.remove();
          });

          ct.on('all', () => ct.remove());
          ct.render();

        }, 500);
      }
    });


    this.paper.on('blank:pointerup', () => {
      if (pressTimer) {
        clearTimeout(pressTimer);
        pressTimer = null;
      }
    });

    this.paper.on('blank:pointermove', () => {
      if (pressTimer) {
        clearTimeout(pressTimer);
        pressTimer = null;
      }
    });




    // 滑鼠滑入link顯示toolView
    this.paper.on('link:mouseenter', function (linkView) {
      linkView.addTools(toolsView);
    });

    // 滑鼠滑出link關閉toolView
    this.paper.on('link:mouseleave', function (linkView) {
      linkView.removeTools();
    });

    // 右鍵顯示移除背景圖選項
    window.oncontextmenu = (evt) => {
      evt.preventDefault();

    };
    new ui.Tooltip({
      rootTarget: document.body,
      target: '[data-tooltip]',
      padding: 15
    });
  }

  // Functions
  saveData() {
    var plot_valid = true;
    this.graph.getLinks().forEach((link) => {
      const target = link.get('target');
      if (!target || !target.id) {
        plot_valid = false;
      }
    });
    if (
      plot_valid &&
      this.pending_save == true &&
      this.editing_object_id.length == 0 &&
      this.creating_link.length == 0
    ) {
      const graphData = this.graph.toJSON();
      const enhancedData = {
        ...graphData,
        backgroundImage: this.imageAddress,
      };
      this.paper.options.interactive = false;
      this.DashboardService.save_plot(
        this.project_code,
        this.projectplot_name,
        enhancedData
      ).subscribe(
        (data) => {
          this.paper.options.interactive = true;
          this.pending_save = false;
          localStorage.setItem('token', data.token);
        },
        (error) => {
          this.pending_save = true;
          this.router.navigate(['/404']);
        }
      );
    }
  }

  load_graph(graph: any) {
    if (graph.cells) {
      this.graph.fromJSON(graph);
      if (graph.backgroundImage.length > 0) {
        this.DashboardService.get_pjplot_image(
          this.project_code,
          this.projectplot_name
        ).subscribe(
          (data) => {
            const reader = new FileReader();
            reader.onload = (e: ProgressEvent<FileReader>) => {
              const fileURL = URL.createObjectURL(data);
              this.imageAddress = fileURL;
              const img = new Image();
              img.onload = () => {
                const imgWidth = img.width;
                const imgHeight = img.height;
                this.paper.el.style.backgroundImage = `url(${fileURL})`;
                this.paper.el.style.backgroundSize = 'cover';
                this.paper.el.style.backgroundRepeat = 'no-repeat';
                this.paper.el.style.backgroundPosition = 'center';
                this.paper.el.style.backgroundBlendMode = 'overlay';
                this.paper.el.style.backgroundColor =
                  `rgba(255, 255, 255, ${this.graph.get('config')?.backgroundColorAlpha || 0})`;
                this.paper.setDimensions(
                  ((2000 * imgWidth) / imgHeight) * this.paperScroller.zoom(),
                  2000 * this.paperScroller.zoom()
                );
              };
              img.src = fileURL;
            };
            reader.readAsDataURL(data);
          },
          (error) => {
            this.router.navigate(['/404']);
          }
        );
      } else {
        this.paper.el.style.backgroundImage = '';
        this.paper.el.style.backgroundSize = 'cover';
        this.paper.el.style.backgroundRepeat = 'no-repeat';
        this.paper.el.style.backgroundPosition = 'center';
        this.paper.el.style.backgroundBlendMode = 'overlay';
        this.paper.el.style.backgroundColor = '#F5F5F5';
        this.paper.setDimensions(2000, 2000);
      }
    }
  }

  filter_invalid_object() {
    const cells = this.graph.get('cells') || [];
    for (const cell of cells) {
      const serialNo = cell.get('config')?.serialNo;

      if (!serialNo) {
        if (cell.get('type') === 'standard.TextBlock') continue;
        this.graph.removeCells([cell]);
        continue;
      }

      const foundObject = this.projectobject_list.find(
        (obj) => obj.serialNo === serialNo
      );

      if (foundObject) {
        const currentLabel = cell.attr('label/text');
        if (currentLabel !== foundObject.name) {
          cell.attr('label/text', foundObject.name);
        }
      }

      const inObjects = this.projectobject_list.some(
        (obj) => obj.serialNo === serialNo
      );
      const inLinks = this.projectlink_list.some(
        (link) => link.serialNo === serialNo
      );
      var revLinks = false;
      if (serialNo.split(':').length > 1) {
        revLinks = this.projectlink_list.some(
          (link) =>
            link.serialNo ===
            `${serialNo.split(':')[0]}:${serialNo.split(':')[2]}:${
              serialNo.split(':')[1]
            }`
        );
      }

      if (!inObjects && !inLinks && !revLinks) {
        this.graph.removeCells([cell]);
      }

    }
  }

  create_object() {
    this.filter_serialNo();
    if (this.selected_object_setting_index >= 0) {
      const cell = this.graph.getCell(this.editing_object_id);
      if (cell) {
        cell.set(
          'config', {
            serialNo: this.filter_object_list(this.editing_object_type)[
              this.selected_object_setting_index
            ].serialNo,
            showInViewMode: false
          },
        );
        const foundObject = this.projectobject_list.find(
          (obj) => obj.serialNo === this.filter_object_list(this.editing_object_type)[
            this.selected_object_setting_index
          ].serialNo
        );

        if (foundObject) {
          cell.attr('label/text', foundObject.name);
        }
      }
      this.toggle_modal('');
      this.pending_save = true;
      this.editing_object_id = '';
      this.selected_object_setting_index = -1;
    }
  }
  create_object_and_setting() {
    this.filter_serialNo();
    this.DashboardService.create_projectplot_object(
      this.project_code,
      this.editing_object_type,
      this.new_object_name,
      this.new_place_id,
      this.new_serialNo
    ).subscribe(
      (data) => {
        const cell = this.graph.getCell(this.editing_object_id);
        // 如果是gateway，按照序列新增對應serialNo的gateway_id
        if (this.editing_object_type == 'Gateway' && cell){
          this.GatewayService.create_gateway_and_config(this.project_code, this.new_place_id, this.new_serialNo).subscribe(
            (data) => {

              localStorage.setItem('token', data.token);
              this.load_project_info();
            },
            (error) => {
              this.router.navigate(['/404']);
            }
          )
        } else if (cell) {
          this.load_project_info();
        }
        cell.set('config', {
          serialNo: this.new_serialNo,
          showInViewMode: false,
          gateway_id: this.editing_object_type == 'Gateway' ? `${this.new_place_id}_${data.serialNo}` : ''
        })
        this.toggle_modal('');
        this.pending_save = true;
        this.editing_object_id = '';
        localStorage.setItem('token', data.token);
      },
      (error) => {}
    );

  }

  cancel_create_object() {
    this.toggle_modal('');
    this.commandManager.undo();
    this.editing_object_id = '';
  }

  /// 創建設定圖標
  createSettingIcon(color: string, hoverColor: string = '0', size = 24) {
    const strokeWidth = 2;
    const strokeColor = color;
    if (hoverColor === '0') {
      hoverColor = color;
    }

    const lineHeight = size / 6;
    const spacing = size / 4;

    return (
      'data:image/svg+xml;utf8,' +
      encodeURIComponent(`
      <svg xmlns="http://www.w3.org/2000/svg"
           width="${size}" height="${size}"
           viewBox="0 0 ${size} ${size}">
        <style>
          line {
            transition: stroke 0.3s ease;
          }
          line:hover {
            stroke: ${hoverColor};
          }
        </style>
        <!-- 增加透明背景方便點擊 -->
        <rect x="0" y="0" width="${size}" height="${size}" fill="transparent" />
        <line x1="3" y1="${spacing}" x2="${size - 3}" y2="${spacing}" stroke="${strokeColor}" stroke-width="${strokeWidth}" stroke-linecap="round" />
        <line x1="3" y1="${spacing * 2}" x2="${size - 3}" y2="${spacing * 2}" stroke="${strokeColor}" stroke-width="${strokeWidth}" stroke-linecap="round" />
        <line x1="3" y1="${spacing * 3}" x2="${size - 3}" y2="${spacing * 3}" stroke="${strokeColor}" stroke-width="${strokeWidth}" stroke-linecap="round" />
      </svg>
    `)
    );
  }


  createViewIcon(color: string, hoverColor: string = '0', size = 20) {
    const strokeWidth = 0;
    const strokeColor = '#333';
    if (hoverColor === '0') {
      hoverColor = color;
    }
    return (
      'data:image/svg+xml;utf8,' +
      encodeURIComponent(`
      <svg fill="${color}" stroke="${strokeColor}"
           stroke-width="${strokeWidth}"
           width="${size}"
           height="${size}"
           viewBox="0 0 284.336 284.336" version="1.1"
           xmlns="http://www.w3.org/2000/svg"
           xmlns:xlink="http://www.w3.org/1999/xlink"
           xml:space="preserve">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <path d="M244.884,43.885c-0.847-0.89-1.705-1.772-2.577-2.645c-0.096-0.096-0.202-0.179-0.301-0.272 C215.235,14.546,179.822,0,142.169,0C104.194,0,68.493,14.788,41.642,41.641c-55.43,55.431-55.43,145.624,0,201.055 c26.852,26.853,62.553,41.641,100.527,41.641s73.676-14.788,100.527-41.641C297.386,188.006,298.112,99.479,244.884,43.885z M62.854,62.854C84.04,41.668,112.208,30,142.169,30c24.702,0,48.18,7.938,67.536,22.595L52.589,209.674 C19.45,165.758,22.866,102.843,62.854,62.854z"></path>
        </g>
      </svg>
    `)
    );
  }

  /// 拖曳後取得圖片大小
  getImageDimensions(url: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
        });
      };
      img.onerror = (error) => {
        reject('Failed to load image');
      };
      img.src = url;
    });
  }

  /// 拖曳上傳背景圖片
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.renderer.addClass(
      this.el.nativeElement.querySelector('#paper'),
      'drag-over'
    );
  }

  // 拖曳上傳背景圖片
  onDragLeave(event: DragEvent): void {
    this.renderer.removeClass(
      this.el.nativeElement.querySelector('#paper'),
      'drag-over'
    );
  }

  // Drop 背景圖片
  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.renderer.removeClass(
      this.el.nativeElement.querySelector('#paper'),
      'drag-over'
    );

    if (event.dataTransfer?.files.length) {
      const file = event.dataTransfer.files[0];
      if (file.type.startsWith('image/') && file.size < 5 * 1024 * 1024) {
        // 5MB maximum
        const formData = new FormData();
        formData.append('image', file);
        formData.append('project_code', this.project_code);
        formData.append('plot_name', this.projectplot_name);
        this.DashboardService.upload_pjplot_background(formData).subscribe(
          (data) => {
            this.imageAddress = data.filename;
            const enhancedData = {
              ...this.graph.toJSON(),
              backgroundImage: this.imageAddress,
            };
            this.load_graph(enhancedData);
            localStorage.setItem('token', data.token);
            this.pending_save = true;
          },
          (error) => {
            this.router.navigate(['/404']);
          }
        );
      } else {
        alert('Image too large or type invalid');
      }
    }
  }

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  cancel_create_link() {
    this.toggle_modal('');
    this.commandManager.undo();
    this.creating_link_id = '';
    this.creating_link = '';
  }

  filter_serialNo() {
    this.count_serialNo = serial_no_list[0];
    for (let i = 0; i < serial_no_list.length; i++) {
      for (let j = 0; j < this.projectobject_list.length; j++) {
        const po = this.projectobject_list[j];
        const serial = po.serialNo;
        const lastPart = serial.substring(serial.lastIndexOf('-') + 1);
        if (
          po.place_id == this.new_place_id &&
          po.type == this.editing_object_type &&
          lastPart == this.count_serialNo
        ) {
          this.count_serialNo = serial_no_list[i + 1];
        }
      }
    }
    this.new_serialNo = this.editing_object_type + '-' + this.new_place_id + '-' + this.count_serialNo;
  }

  filter_object_list(type: string | null) {
    const ret: plot_object[] = [];
    for (let i = 0; i < this.projectobject_list.length; i++) {
      if (this.projectobject_list[i].type == type) {
        ret.push(this.projectobject_list[i]);
      }
    }
    return ret;
  }
  removeBackground() {
    const formData = new FormData();
    formData.append('project_code', this.project_code);
    formData.append('plot_name', this.projectplot_name);
    this.DashboardService.upload_pjplot_background(formData).subscribe(
      (data) => {
        this.imageAddress = data.filename;
        const enhancedData = {
          ...this.graph.toJSON(),
          backgroundImage: '',
        };
        this.load_graph(enhancedData);
        localStorage.setItem('token', data.token);
        this.pending_save = true;
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  type_name_trans(name: string, rev: boolean = false): string | null {
    return type_name_trans(name, rev);
  }

  create_link() {
    this.new_link_serialNo = `${this.new_link_type}:${this.new_link_source}:${this.new_link_target}`;
    this.DashboardService.create_projectplot_link(
      this.project_code,
      this.new_link_serialNo,
      this.new_link_name
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.select_link();
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  create_place_id() {
    if (this.input_name.length == 11) {
      this.ProjectService.create_place_id(
        this.project_code,
        this.input_name
      ).subscribe(
        (data) => {
          this.load_project_info();
          this.toggle_modal();
          localStorage.setItem('token', data.token);
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
    }
  }
  add_projectplot() {
    this.ProjectService.create_projectplot(
      this.project_code,
      this.input_name
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.toggle_modal();
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  check_link_exist() {
    this.repeated_link = false;
    for (let link of this.projectlink_list) {
      if (
        link.type === this.new_link_type &&
        ((link.source === this.new_link_source &&
          link.target === this.new_link_target) ||
          (link.source === this.new_link_target &&
            link.target === this.new_link_source))
      ) {
        this.new_link_name = link.name;
        this.new_link_serialNo = link.serialNo;
        this.repeated_link = true;
        break;
      }
    }
    this.new_link_serialNo = `${this.new_link_type}:${this.new_link_source}:${this.new_link_target}`;
  }

  refresh_connectable_link_type_list(): void {
    const allLinkTypes: string[] = Object.keys(link_connectable_object);

    const source = this.projectobject_list.find(
      (obj: plot_object) => obj.serialNo === this.new_link_source
    );
    const target = this.projectobject_list.find(
      (obj: plot_object) => obj.serialNo === this.new_link_target
    );

    if (!source || !target) {
      this.connectableLinkTypeList = [];
      return;
    }

    const sourceType = source.type || '';
    const targetType = target.type || '';

    this.connectableLinkTypeList = allLinkTypes.filter((linkType: string) => {
      const validTypes: string[] = link_connectable_object[linkType];
      return validTypes.includes(sourceType) && validTypes.includes(targetType);
    });
  }

  select_link() {
    const cell = this.graph.getCell(this.creating_link_id);
    if (cell) {
      const type = this.new_link_serialNo.split(':')[0];

      // 連線對應的 VSM 類型映射表
      const linkTypeMap: { [key: string]: string } = {
        AirPipe: 'VSMInformationFlow',
        LiquidPipe: 'VSMMaterialFlow',
        MotorCable: 'VSMShipment',
        RS485: 'VSMManualInfo',
        Signal: 'VSMManualInfo',
        EtherNet: 'VSMPullArrow',
      };
      const newType = linkTypeMap[type];
      const source = cell.get('source');
      const target = cell.get('target');
      const vertices = cell.get('vertices');

      // 移除原本的連線
      this.graph.removeCells([cell]);

      try {
        // 建立新的 VSM 連線
        const newLink = new (vsmshapes as any)[newType]({
          source,
          target,
          vertices,
          attrs: {
            line: {
              targetMarker: null,
            },
          },
        });

        newLink.set('config', {
          serialNo: this.new_link_serialNo,
          showInViewMode: false,
        });
        this.graph.addCell(newLink);
        newLink.toBack();
      } catch (error) {
        console.error('Error creating new link:', error);
      }
    }

    this.toggle_modal('');
    this.pending_save = true;
    this.creating_link_id = '';
    this.creating_link = '';
  }

  switchTab(tab: string): void {
    this.currentTab = tab;
  }

  switchActiveTab(tab: 'select' | 'create'): void {
    this.activeTab = tab;
  }

  switchActiveObjectTab(tab: 'attr' | 'config'): void {
    this.activeObjectTab = tab;
  }

  switchActiveGatewayTab(tab: 'Host' | 'Communication' | 'Driver' | 'Logic'): void {
    this.activeGatewayTab = tab;
  }

  navigateToEditPage(item: any): void {
    if (this.currentTab === '電號') {
    } else if (this.currentTab === '規劃圖') {
      const targetUrl = this.router.serializeUrl(
        this.router.createUrlTree(['/project/device-dashboard-edit', this.project_code, item])
      );
      window.location.href = targetUrl;
    } else if (this.currentTab === '設備') {
      window.open(`/project/object/${this.project_code}/${type_name_trans(item.type)}/${item.serialNo}`,'_blank');
    } else if (this.currentTab === '連結') {
      window.open(
        `/project/link/${this.project_code}/${item.serialNo.split(':')[0]}/${item.serialNo}`)
    } else {
      console.warn('Unknown navigation type:', this.currentTab);
    }
  }
  openDeviceDashboardView(): void {
    const url = `/project/device-dashboard-view/${this.project_code}/${this.projectplot_name}`;
    window.open(url, '_blank'); // 在新窗口中打開
  }

  zoomIn(){

  }
  zoomOut(){

  }
  resetView(){

  }
  exportPNG(){

  }
  exportJPG(){

  }
  exportBOM(){

  }

  gateway_config_init(){
    this.gateway_config = {
      Host: {
        HostName: '99999999999',
        SerialNo: '0',
        Identical_placeID_list: [],
        LogLevel: 'INFO',
        LogFile: './log/HT1.log',
        Version: '1.0.0',
        CmderServer: {
          ServerAddress: 'imabox-server3.ima-ems.com',
          ServerPort: 8883,
          Wait: 60,
        },
        PostDataPeriod: 60
      },
      Communication: {
        WiFi: [],
        PostServer: [],
        FixedIP: {
          IP: '',
          Routers: ''
        },
        PhoneNumber: ''
      },
      Strategy: {
        Logics: [],
      },
      Modbus: {
        Driver: [],
      },
    };
    this.gateway_config_connected= false;
    this.gateway_config_getRemoteState = false;
    this.gateway_config_update_visible = false;
    this.gateway_config_mac_modal_visible = false;
    this.gateway_config_get_driver_pin_visible = false;
    this.gateway_config_selected_logic = '';
    this.gateway_config_selected_type = '';
    this.gateway_config_MAC_msg = '';
    this.gateway_config_ssh_msg = '';
    this.gateway_config_MAC_list = [];
    this.gateway_config_checking_version = true;
    this.gateway_config_update_available_boolean = false;
    this.gateway_config_latest_version = '';
    this.gateway_config_testing_driver_pin_portName = '';
    this.gateway_config_testing_driver_pin_slaveAddr = 0;
    this.gateway_config_testing_driver_pin_pinAddr = 0;
    this.gateway_config_testing_driver_pin_pinName = '';
    this.gateway_config_get_driver_pin_result = '';
    this.gateway_config_selected_logic_type = 'CondCtrlByT2';
    this.gateway_config_editing_pin_def_dtype = 'D';
    this.gateway_config_editing_pin_def_lrange = 0;
    this.gateway_config_editing_pin_def_hrange = 1;
    this.gateway_config_editing_pin_def_adc = 8;
    this.gateway_config_edit_pin_def_index = 0;
    this.gateway_config_edit_pin_def_config_visible = false;
    this.gateway_config_editing_pin_config = 0;
    this.gateway_config_pin_config_visible = false;
    this.gateway_config_edit_logic_visible = false;
    this.gateway_config_editing_logic = 0;
    this.gateway_config_add_logic_visible = false;

  }

  save_gateway_config(){
    this.GatewayService.submit_config(this.gateway_config, this.gateway_config_connected).subscribe(
      (response: any) => {
        localStorage.setItem('token', response.token);
        this.toggle_modal('');
        this.load_project_info();
      },
      error => {
        this.router.navigate(['/404']);
      }
    );
  }

  addColorThreshold() {
    if (this.editing_cell_attr_dynamic_fillColor.length < 10) {
      this.editing_cell_attr_dynamic_fillColor.push({
        threshold: 0.5,
        color: '#000000',
        transparent: false
      });
    }
  }

  removeColorThreshold(index: number) {
    this.editing_cell_attr_dynamic_fillColor.splice(index, 1);
  }

  removeObject(){
    this.DashboardService.remove_projectplot_object(
      this.project_code,
      this.removing_cell.get('config')?.serialNo,
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.removing_cell.remove();
        this.toggle_modal();
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )
  }
  removeLink(){
    this.DashboardService.remove_projectplot_link(
      this.project_code,
      this.removing_link.get('config')?.serialNo,
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.removing_link.remove();
        this.toggle_modal();
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )

  }

  saveSetting(){
    this.paper.el.style.backgroundColor = `rgba(255, 255, 255, ${1 - this.editing_plot_background_alpha})`;
    this.graph.set('config', {
      backgroundColorAlpha: 1 - this.editing_plot_background_alpha,
      plotNameVisible: this.editing_plot_plotname_visible,
      updatedTimeVisible: this.editing_plot_updatedTime_visible,
      updatedTimePeriod: this.editing_plot_updatedTime_period
    });
    this.toggle_modal('');
  }

  filteredPlots() {
    return this.projectplot_list.filter((p) =>
      p.toLowerCase().includes(this.searchPlotText.toLowerCase())
    );
  }

  filteredPlaceId() {
    return this.place_id_list.filter((p) =>
      p.toLowerCase().includes(this.searchPlaceIdText.toLowerCase())
    );
  }

  filteredObjects() {
    return this.projectobject_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchObjectText.toLowerCase())
    );
  }

  filteredLinks() {
    return this.projectlink_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchLinkText.toLowerCase())
    );
  }

  saveObject(){
    this.DashboardService.update_projectplot_object(this.project_code, this.editingObject).subscribe(
      (data) => {
        this.toggle_modal('');
        this.load_project_info();
        localStorage.setItem('token', data.token);

      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )
  }

  saveLink(){
    this.DashboardService.update_projectplot_link(this.project_code, this.editingLink).subscribe(
      (data) => {
        this.toggle_modal('');
        this.load_project_info();
        localStorage.setItem('token', data.token);

      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )
  }

  saveCellStyle(){
    this.editing_cell.attr('body/stroke-width', this.editing_cell_attr_strokeWidth);
    this.editing_cell.attr('body/stroke', this.editing_cell_attr_strokeColor);
    this.editing_cell.attr('body/fill', this.editing_cell_attr_fillColor.transparent ? 'transparent' : this.editing_cell_attr_fillColor.color);
    this.editing_cell.attr('label/font-size', this.editing_cell_attr_fontSize);
    this.editing_cell.attr('label/fill', this.editing_cell_attr_fontColor);
    this.editing_cell.set('config', {
      showInViewMode: this.editing_cell_dashboard_visible,
      serialNo: this.editing_cell_dashboard_serialNo,
      showName: this.editing_cell_dashboard_name,
      showNameVisible: this.editing_cell_dashboard_name_visible,
      showTableName: this.editing_cell_dashboard_TableName ? this.editing_cell_dashboard_serialNo.split('-')[1] + '_' + this.editing_cell_dashboard_TableName: '',
      showColumnName: this.editing_cell_dashboard_TableColumn,
      tableColumnVisible: this.editing_cell_dashboard_TableColumn_visible,
      dataFloatNumber: this.editing_cell_dashboard_data_floatNumber,
      timeFormat: this.editing_cell_dashboard_time,
      timeVisible: this.editing_cell_dashboard_time_visible,
      dynamic_fillColor_available: this.editing_cell_attr_dynamic_fillColor_available,
      dynamic_fillColor: this.editing_cell_attr_dynamic_fillColor
    }
  )
  this.toggle_modal('');
  this.pending_save = true;
  }

  saveLinkStyle(){
    this.editing_link.attr('line/strokeWidth', this.editing_link_attr_strokeWidth);
    this.editing_link.attr('line/stroke', this.editing_link_attr_strokeColor);
    this.editing_link.set('config', {
      showInViewMode: this.editing_link_dashboard_visible,
      serialNo: this.editing_link_dashboard_serialNo
    }
  )
  this.toggle_modal('');
  this.pending_save = true;
  }

  saveTextBlockStyle(){
    this.editing_textBlock.attr('body/strokeWidth', this.editing_textBlock_attr_strokeWidth);
    this.editing_textBlock.attr('body/stroke', this.editing_textBlock_attr_strokeColor);
    this.editing_textBlock.attr('body/fill', this.editing_textBlock_attr_fillColor.transparent ? 'transparent' : this.editing_textBlock_attr_fillColor.color);
    this.editing_textBlock.attr('label/style/font-size', `${this.editing_textBlock_attr_fontSize}px`);
    this.editing_textBlock.attr('label/style/color', this.editing_textBlock_attr_fontColor);
    this.editing_textBlock.set('config', {
      showInViewMode: this.editing_textBlock_dashboard_visible
    }
  )
  this.toggle_modal('');
  this.pending_save = true;
  }


  toggle_modal(focus: string = '', args: string = ''){
    this.input_name = '';
    this.editing_object_id = '';
    this.editing_object_type = '';
    this.editing_gateway_id = '';
    this.new_link_source_name = '';
    this.new_link_target_name = '';
    this.new_serialNo = '';
    this.new_place_id = '';
    this.new_object_name = '';
    this.editing_cell = null;
    this.editing_link = null;
    this.removing_cell = null
    this.removing_link = null;
    this.editing_cell_attr_strokeWidth = 2;
    this.editing_cell_attr_strokeColor = '#000000';
    this.editing_cell_attr_fillColor = {
      color: '#94ECBE', transparent: false
    };
    this.editing_cell_attr_fontSize = 14;
    this.editing_cell_attr_fontColor = '#000000';
    this.editing_cell_dashboard_visible = false;
    this.editing_cell_dashboard_serialNo = '';
    this.editing_cell_dashboard_name = '';
    this.editing_cell_dashboard_name_visible = true;
    this.editing_cell_dashboard_TableName = '';
    this.editing_cell_dashboard_TableColumn = '';
    this.editing_cell_dashboard_TableColumn_visible = true;
    this.editing_cell_dashboard_time = '%m:%d;%H:%M';
    this.editing_cell_dashboard_time_visible = true;
    this.editing_link_attr_strokeWidth = 2
    this.editing_link_attr_strokeColor = '#ffffff';
    this.editing_link_dashboard_visible = false;
    this.editing_link_dashboard_serialNo = '';
    this.editing_textBlock_attr_strokeWidth = 2;
    this.editing_textBlock_attr_strokeColor = '#000000';
    this.editing_textBlock_attr_fillColor = {
      color: '#94ECBE', transparent: false
    };
    this.editing_textBlock_attr_fontSize = 14;
    this.editing_textBlock_attr_fontColor = '#000000';
    this.editing_textBlock_dashboard_visible = false;
    this.editingObject = null;
    this.editingLink = null;
    this.editing_textBlock = null;
    this.create_link_visible = false;
    this.place_id_visible = false;
    this.plot_list_visible = false;
    this.link_list_visible = false;
    this.object_list_visible = false;
    this.helpModalVisible = false;
    this.create_object_visible = false;
    this.object_visible = false;
    this.object_style_visible = false;
    this.link_visible = false;
    this.link_style_visible = false;
    this.textBlock_style_visible = false
    this.gateway_config_visible = false;
    this.setting_visible = false;
    this.remove_object_visible = false;
    this.remove_link_visible = false;
    this.gateway_config_update_visible = false;

    if (focus == 'create_object') {
      this.create_object_visible = true;
    } else if (focus == 'create_link') {
      this.creating_link = 'link';
      this.create_link_visible = true;
      this.connectableLinkTypeList = this.link_type_list
        .map((linkType) => type_name_trans(linkType))
        .filter((result) => result !== null) as string[];
      this.new_link_source = args.split(':')[0];
      this.new_link_source_name = this.projectobject_list.find(
        (obj) => obj.serialNo === this.new_link_source)?.name || '';
      this.new_link_type = '';
      this.new_link_name = '';
      this.new_link_target = args.split(':')[1];
      this.new_link_target_name = this.projectobject_list.find(
        (obj) => obj.serialNo === this.new_link_target)?.name || '';
      this.refresh_connectable_link_type_list();
    } else if (focus == 'help'){
      this.helpModalVisible = true;
    } else if (focus == 'place_id_list'){
      this.place_id_visible = true;
    } else if (focus == 'plot_list'){
      this.plot_list_visible = true;
    } else if (focus == 'link_list'){
      this.link_list_visible = true;
    } else if (focus == 'object_list'){
      this.object_list_visible = true;
    } else if (focus == 'object'){
      this.object_visible = true;
      this.editing_object_id = args;
      const foundObject = this.projectobject_list.find(
        (obj) => obj.serialNo === this.graph.getCell(this.editing_object_id).get('config').serialNo);
      if (foundObject){
        this.editingObject = foundObject;
      }

    } else if (focus == 'object_style'){
      this.object_style_visible = true;
      this.editing_object_id = args;
      this.editing_cell = this.graph.getCell(this.editing_object_id);
      console.log(this.editing_cell);
      this.editing_cell_attr_strokeWidth = this.editing_cell.get('attrs').body['stroke-width'] ?? 2;
      this.editing_cell_attr_strokeColor = this.editing_cell.get('attrs').body.stroke || '#000000';
      this.editing_cell_attr_fillColor.color = this.editing_cell.get('attrs').body.fill || 'transparent';
      this.editing_cell_attr_fillColor.transparent = this.editing_cell.get('attrs').body.fill === 'transparent' ? true : false;
      this.editing_cell_attr_dynamic_fillColor_available = this.editing_cell.get('config')?.dynamic_fillColor_available || false;
      this.editing_cell_attr_dynamic_fillColor = this.editing_cell.get('config')?.dynamic_fillColor || [{threshold: 0.5, color: '#94ECBE', transparent: false}];
      this.editing_cell_attr_fontSize = this.editing_cell.get('attrs').label['font-size'] || 14;
      this.editing_cell_attr_fontColor = this.editing_cell.get('attrs').label.fill || '#000000';
      this.editing_cell_dashboard_visible = this.editing_cell.get('config')?.showInViewMode || false;
      this.editing_cell_dashboard_serialNo = this.editing_cell.get('config')?.serialNo || '';
      this.editing_cell_dashboard_name = this.editing_cell.get('config')?.showName || '';
      this.editing_cell_dashboard_name_visible = this.editing_cell.get('config')?.showNameVisible ?? true;
      this.editing_cell_dashboard_TableName = this.editing_cell.get('config').showTableName ? this.editing_cell.get('config').showTableName.split('_')[1] : '';
      this.editing_cell_dashboard_TableColumn = this.editing_cell.get('config')?.showColumnName || '';
      this.editing_cell_dashboard_TableColumn_visible = this.editing_cell.get('config')?.tableColumnVisible ?? true;
      this.editing_cell_dashboard_data_floatNumber = this.editing_cell.get('config')?.dataFloatNumber || 0;
      this.editing_cell_dashboard_time = this.editing_cell.get('config')?.timeFormat || '%m:%d;%H:%M';
      this.editing_cell_dashboard_time_visible = this.editing_cell.get('config')?.timeVisible ?? true;



    } else if (focus == 'link'){
      this.link_visible = true;
      this.editing_link_id = args;
      const foundLink = this.projectlink_list.find(
        (lnk) => lnk.serialNo === this.graph.getCell(this.editing_link_id).get('config').serialNo);
      if (foundLink){
        this.editingLink = foundLink;
      }

    } else if (focus == 'link_style'){
      this.link_style_visible = true;
      this.editing_link_id = args;
      this.editing_link = this.graph.getCell(this.editing_link_id);
      // load editing link style setting
      this.editing_link_attr_strokeWidth = this.editing_link.get('attrs').line['strokeWidth'] ?? 2;
      this.editing_link_attr_strokeColor = this.editing_link.get('attrs').line['stroke'] || '#ffffff';
      this.editing_link_dashboard_visible = this.editing_link.get('config')?.showInViewMode || false;
      this.editing_link_dashboard_serialNo = this.editing_link.get('config')?.serialNo || '';

    } else if (focus == 'textBlock_style'){
      this.textBlock_style_visible = true;
      this.editing_block_id = args;

      this.editing_textBlock = this.graph.getCell(this.editing_block_id);
      this.editing_textBlock_attr_strokeWidth = this.editing_textBlock.get('attrs').body['strokeWidth'] ?? 2;
      this.editing_textBlock_attr_strokeColor = this.editing_textBlock.get('attrs').body['stroke'] || '#000000';
      this.editing_textBlock_attr_fillColor.color = this.editing_textBlock.get('attrs').body.fill || 'transparent';
      this.editing_textBlock_attr_fillColor.transparent = this.editing_textBlock.get('attrs').body.fill === 'transparent' ? true : false;
      this.editing_textBlock_attr_fontSize = parseInt((this.editing_textBlock.get('attrs').label.style['font-size'] || '').replace('px', '')) || 14;
      this.editing_textBlock_attr_fontColor = this.editing_textBlock.get('attrs').label.style['color'] || '#000000';
      this.editing_textBlock_dashboard_visible = this.editing_textBlock.get('config')?.showInViewMode || false;

    } else if (focus == 'gateway_config'){
      this.gateway_config_visible = true;
      this.editing_gateway_id = args;
      this.gateway_config_connected = false;
      this.gateway_config_getRemoteState = false;
      this.load_gateway_config(this.editing_gateway_id);


    } else if (focus == 'setting'){
      this.setting_visible = true;
      const bgColor = window.getComputedStyle(this.paper.el).backgroundColor;
      // bgColor 格式會是像 "rgba(255, 255, 255, 0.5)"

      this.editing_plot_background_alpha = 0.5; // 預設半透明
      const rgbaMatch = bgColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+),?\s*([0-9.]+)?\)/);
      if (rgbaMatch) {
        this.editing_plot_background_alpha = 1 - (rgbaMatch[4] ? parseFloat(rgbaMatch[4]) : 1);
      }
      this.editing_plot_plotname_visible = this.graph.get('config')?.plotNameVisible ?? true;
      this.editing_plot_updatedTime_visible = this.graph.get('config')?.updatedTimeVisible ?? true;
    } else if (focus == 'remove_object') {
      this.removing_cell = this.graph.getCell(args);
      const serialNo = this.removing_cell.get('config')?.serialNo;
      let objectCount = 0;

      const requests = this.projectplot_list.map(plotname =>
        this.DashboardService.get_plot(this.project_code, plotname)
      );

      forkJoin(requests).subscribe(
        responses => {
          for (let response of responses) {
            localStorage.setItem('token', response.token);
            const cells = response.data?.cells || [];
            for (let cell of cells) {
              if (cell['config']?.serialNo === serialNo) {
                objectCount++;
              }
            }
          }
          if (objectCount > 1){
            this.removing_cell.remove();
          } else if (this.removing_cell.get('config')?.serialNo.split("-")[0] != 'Gateway'){
            this.remove_object_visible = true;
          }
        },
        error => {
          this.router.navigate(['/404']);
          console.error('取得 plot 資料失敗', error);
        }
      );

    } else if (focus == 'remove_link') {
      this.removing_link = this.graph.getCell(args);
      const serialNo = this.removing_link.get('config')?.serialNo;
      let objectCount = 0;

      const requests = this.projectplot_list.map(plotname =>
        this.DashboardService.get_plot(this.project_code, plotname)
      );

      forkJoin(requests).subscribe(
        responses => {
          for (let response of responses) {
            localStorage.setItem('token', response.token);
            const cells = response.data?.cells || [];
            for (let cell of cells) {
              if (cell['config']?.serialNo === serialNo) {
                objectCount++;
              }
            }
          }
          if (objectCount <= 1) {
            this.remove_link_visible = true;
          } else {
            this.removing_link.remove();
          }
        },
        error => {
          this.router.navigate(['/404']);
          console.error('取得 plot 資料失敗', error);
        }
      );


    }
  }

  load_gateway_config(gateway_id: string) {
    this.gateway_config_init();
    this.GatewayService.get_remote_config(gateway_id).subscribe(
      (data) => {
        this.gateway_config_connected = true;
        this.gateway_config_getRemoteState = true;
        this.write_config_to_frontend(data.config);
        localStorage.setItem('token', data.token);
        this.GatewayService.update_available(data.config.Host.Version).subscribe(
          (data) => {
            localStorage.setItem('token', data.token);
            this.gateway_config_checking_version = false;
            this.gateway_config_update_available_boolean = data.data;
            this.gateway_config_latest_version = data.latest_version;
          },
          (error) => {
            this.gateway_config_checking_version = false;

          }
        )
      },
      (error) => {
        this.gateway_config_getRemoteState = true;
        this.gateway_config_connected = false;
        this.GatewayService.load_config(gateway_id).subscribe(
          (data) => {
            localStorage.setItem('token', data.token);
            this.write_config_to_frontend(data.config);
          },
          (error) => {
            this.router.navigate(['/404']);
            console.log("Error!");
          }
        );
      }
    )
  }

  write_config_to_frontend(data: any) {
    this.gateway_config_selected_logic = '';
    this.gateway_config_selected_type = '';
    this.gateway_config = {
      Host: {
        HostName: '99999999999',
        SerialNo: '0',
        Identical_placeID_list: [],
        LogLevel: 'INFO',
        LogFile: './log/HT1.log',
        Version: '1.0.0',
        CmderServer: {
          ServerAddress: 'imabox-server3.ima-ems.com',
          ServerPort: 8883,
          Wait: 60,
        },
        PostDataPeriod: 60
      },
      Communication: {
        WiFi: [],
        PostServer: [],
        FixedIP: {
          IP: '',
          Routers: ''
        },
        PhoneNumber: ''
      },
      Strategy: {
        Logics: [],
      },
      Modbus: {
        Driver: [],
      },
    };
    this.gateway_config.Host.HostName = data.Host.HostName;
    this.gateway_config.Host.SerialNo = data.Host.SerialNo;
    if (data?.Host?.Identical_placeID_dict) {
      Object.entries(data.Host.Identical_placeID_dict).forEach(([key, value]) => {
        this.gateway_config.Host.Identical_placeID_list.push({
          slaveAddress: String(key),
          placeID: String(value)
        });
      });
    }
    this.gateway_config.Host.LogLevel = data.Host.LogLevel;
    this.gateway_config.Host.LogFile = data.Host.LogFile;
    this.gateway_config.Host.Version = data.Host.Version;
    this.gateway_config.Host.CmderServer = data.Host.CmderServer;
    this.gateway_config.Host.PostDataPeriod = data.Host.PostDataPeriod || 60;
    if (data.Communication.WiFi.length > 0){
      for (let i = 0; i < data.Communication.WiFi.length; i ++){
        this.gateway_config.Communication.WiFi.push({
          ssid: data.Communication.WiFi[i].ssid,
          identity: data.Communication.WiFi[i].identity || '',
          psk: data.Communication.WiFi[i].psk,
          key_mgmt: data.Communication.WiFi[i].key_mgmt,
          priority: data.Communication.WiFi[i].priority,
        });
      }
    }
    this.gateway_config.Communication.PostServer = data.Communication.PostServer;
    if (data.Communication.FixedIP) {
      this.gateway_config.Communication.FixedIP.IP = data.Communication.FixedIP.IP;
      this.gateway_config.Communication.FixedIP.Routers = data.Communication.FixedIP.Routers;
    } else {
      this.gateway_config.Communication.FixedIP.IP = "";
      this.gateway_config.Communication.FixedIP.Routers = "";
    }
    this.gateway_config.Communication.PhoneNumber = data.Communication.PhoneNumber;
    if (data.Strategy.Logics.length > 0){
      for (let i = 0; i < data.Strategy.Logics.length; i ++) {
        if (data.Strategy.Logics[i].Type == "CondCtrlByT2") {
          this.gateway_config.Strategy.Logics.push({
            ExecutionPrd: data.Strategy.Logics[i].ExecutionPrd,
            Type: data.Strategy.Logics[i].Type,
            InputList: data.Strategy.Logics[i].InputList,
            OutputList: data.Strategy.Logics[i].OutputList,
            Arguments: {
              CondCtrlByT2_DvcBoundary: data.Strategy.Logics[i].Arguments.DvcBoundary,
              CondCtrlByT2_TimeBoundary: data.Strategy.Logics[i].Arguments.TimeBoundary,
              CondCtrlByT2_Declaration: data.Strategy.Logics[i].Arguments.Declaration,
              CondCtrlByT2_Thresholds: data.Strategy.Logics[i].Arguments.Thresholds,
              CondCtrlByT2_RedisDB: data.Strategy.Logics[i].Arguments.RedisDB,
              DataTransByRedis_VarName: '',
              DataTransByRedis_VarDefinition: '',
              DataTransByRedis_ServerIP: '',
              DataTransByRedis_RedisDB: 'IMARedisModuleData',
            }
          })
        }
        else if (data.Strategy.Logics[i].Type == "DataTransByRedis") {
          this.gateway_config.Strategy.Logics.push({
            ExecutionPrd: data.Strategy.Logics[i].ExecutionPrd,
            Type: data.Strategy.Logics[i].Type,
            InputList: data.Strategy.Logics[i].InputList,
            OutputList: data.Strategy.Logics[i].OutputList,
            Arguments: {
              CondCtrlByT2_DvcBoundary: [],
              CondCtrlByT2_TimeBoundary: [],
              CondCtrlByT2_Declaration: [],
              CondCtrlByT2_Thresholds: [],
              CondCtrlByT2_RedisDB: 'IMARedisModuleData',
              DataTransByRedis_VarName: data.Strategy.Logics[i].Arguments.VarName,
              DataTransByRedis_VarDefinition: data.Strategy.Logics[i].Arguments.VarDefinition,
              DataTransByRedis_ServerIP: data.Strategy.Logics[i].Arguments.ServerIP,
              DataTransByRedis_RedisDB: data.Strategy.Logics[i].Arguments.RedisDB,
            }
          })
        }
      }
    }
    if (data.Modbus.Driver.length > 0){
      for (let i = 0; i < data.Modbus.Driver.length; i ++) {
        this.gateway_config.Modbus.Driver.push({
          DriverName: data.Modbus.Driver[i].DriverName,
          PortAddress: data.Modbus.Driver[i].PortAddress,
          SlaveAddress: data.Modbus.Driver[i].SlaveAddress,
          PinConfig: []
        })

        if (data.Modbus.Driver[i].PinConfig){
          if (data.Modbus.Driver[i].PinConfig.length > 0){
            for (let j = 0; j < data.Modbus.Driver[i].PinConfig.length; j ++) {
              this.gateway_config.Modbus.Driver[i].PinConfig[j] = data.Modbus.Driver[i].PinConfig[j]
            }
          }
        }
      }
    }
  }
  remove_Identical_placeID_device(index: number) {
    this.gateway_config.Host.Identical_placeID_list.splice(index, 1);
  }
  add_Identical_placeID_device() {
    this.gateway_config.Host.Identical_placeID_list.push({
      slaveAddress: '0',
      placeID: '',
    });
  }
  compareVersion(targetVersion: string, specifiedVersion: string): boolean {
    // 将版本号字符串按"."拆分成数组
    const targetParts = targetVersion.split('.').map(Number);
    const specifiedParts = specifiedVersion.split('.').map(Number);

    // 比较两个版本号的每一部分
    const length = Math.max(targetParts.length, specifiedParts.length);

    for (let i = 0; i < length; i++) {
      // 如果 targetParts 或 specifiedParts 的长度不一致，补充为0
      const targetPart = targetParts[i] || 0;
      const specifiedPart = specifiedParts[i] || 0;

      // 如果目标版本大于指定版本，则返回 true
      if (targetPart > specifiedPart) {
        return true;
      }

      // 如果目标版本小于指定版本，则返回 false
      if (targetPart < specifiedPart) {
        return false;
      }
    }

    // 如果两者版本号相同，返回 true
    return true;
  }
  get_MACAddress() {
    this.gateway_config_MAC_msg = 'waiting...';
    this.GatewayService.get_MACAddress(this.editing_gateway_id).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.toggle_mac_modal();
        this.gateway_config_MAC_list = [];

        data.msg.split(';').forEach((str: string) => {
          const parts = str.split(':');
          if (parts.length >= 2) {
            this.gateway_config_MAC_list.push({
              interface: parts[0],
              address: parts.slice(1).join(':')
            });
          }
        });
        this.gateway_config_MAC_msg = '';
      },
      (error) => {
        this.router.navigate(['/404']);
        console.error(error);
      }
    );
  }
  get_remote_driver_pin_data(){
    this.GatewayService.get_pin_data(this.editing_gateway_id, this.gateway_config_testing_driver_pin_portName, this.gateway_config_testing_driver_pin_slaveAddr, this.gateway_config_testing_driver_pin_pinAddr, this.gateway_config_testing_driver_pin_pinName).subscribe(
      (data) => {
        localStorage.setItem('token', data.token),
        this.gateway_config_get_driver_pin_result = data.msg;
        console.log(data);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )
  }
  getAllowedInputPinNames(slaveAddress: number): string[][] {
    const driver = this.gateway_config.Modbus.Driver.find(d => +d.SlaveAddress === +slaveAddress);
    if (!driver) return [];
    const driverName = driver.DriverName;
    return this.allowed_input_pinName.data[driverName] || [];
  }
  getAllowedOutputPinNames(slaveAddress: number): string[][] {
    const driver = this.gateway_config.Modbus.Driver.find(d => +d.SlaveAddress === +slaveAddress);
    if (!driver) return [];
    const driverName = driver.DriverName;
    return this.allowed_output_pinName.data[driverName] || [];
  }
  getBoundary(j: number, index: number): number | null {
    const arr = this.gateway_config.Strategy.Logics[this.gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary[j];
    return Array.isArray(arr) ? arr[index] ?? null : null;
  }

  setBoundary(j: number, index: number, value: any): void {
    const arr = this.gateway_config.Strategy.Logics[this.gateway_config_editing_logic].Arguments.CondCtrlByT2_DvcBoundary[j];
    if (Array.isArray(arr)) {
      arr[index] = Number(value); // 確保轉成數字
    }
  }
  create_SSH(){
    this.GatewayService.create_SSH(this.editing_gateway_id).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.gateway_config_ssh_msg = data.ssh_msg;
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )
  }
  toggle_mac_modal(){
    this.gateway_config_visible = !this.gateway_config_visible;
    this.gateway_config_mac_modal_visible = !this.gateway_config_mac_modal_visible;
  }
  toggle_update_view(){
    this.gateway_config_visible = !this.gateway_config_visible;
    this.gateway_config_update_visible = !this.gateway_config_update_visible;
  }
  toggle_get_driver_pin_modal(index: number){
    this.gateway_config_visible = !this.gateway_config_visible;
    this.gateway_config_get_driver_pin_visible = !this.gateway_config_get_driver_pin_visible;
    this.gateway_config_testing_driver_pin_portName = this.gateway_config.Modbus.Driver[index].PortAddress;
    this.gateway_config_testing_driver_pin_slaveAddr = this.gateway_config.Modbus.Driver[index].SlaveAddress;
    this.gateway_config_get_driver_pin_result = '//';
  }
  toggleEditLock(){
    this.editLock = !this.editLock;
  }
  update_gateway(){
    this.GatewayService.update_gateway(this.editing_gateway_id, this.gateway_config_latest_version).subscribe(
      (data) => {
        this.toggle_modal();
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    )
  }
    add_wifi() {
      this.gateway_config.Communication.WiFi.push({
        ssid: '',
        identity: '',
        psk: '',
        key_mgmt: 'WPA-PSK',
        priority: this.gateway_config.Communication.WiFi.length + 4,
      });
    }

    add_Logic() {
      this.gateway_config.Strategy.Logics.push({
        ExecutionPrd: 60,
        Type: this.gateway_config_selected_logic_type,
        InputList: [],
        OutputList: [],
        Arguments: {
          CondCtrlByT2_DvcBoundary: [0],
          CondCtrlByT2_TimeBoundary: [],
          CondCtrlByT2_Declaration: [],
          CondCtrlByT2_Thresholds: [],
          CondCtrlByT2_RedisDB: 'IMARedisModuleData',
          DataTransByRedis_VarName: '',
          DataTransByRedis_VarDefinition: '',
          DataTransByRedis_ServerIP: '',
          DataTransByRedis_RedisDB: 'IMARedisModuleData',
        }
      });
    }
    add_LogicInput(index: number){
      this.gateway_config.Strategy.Logics[index].InputList.push({
        PortName: "/dev/ttyUSB0",
        SlaveAddress: 1,
        PinAddress: 0,
        PinName: ""
      })
    }

    add_LogicOutput(index: number){
      this.gateway_config.Strategy.Logics[index].OutputList.push({
        PortName: "/dev/ttyUSB0",
        SlaveAddress: 1,
        PinAddress: 0,
        PinName: ""
      })
    }
    add_timeBoundary(index: number){
      this.gateway_config.Strategy.Logics[index].Arguments.CondCtrlByT2_TimeBoundary.push({
        WeekDay: [0,1,2,3,4,5,6],
        Time: [[0,0], [23,59]],
        Interval: 1,
        Boundary: [0]
      })
    }
    add_declaration(index: number){
      this.gateway_config.Strategy.Logics[index].Arguments.CondCtrlByT2_Declaration.push({
        VarName: '',
        Definition: '',
        Rolling_Funct: 'None',
        Rolling_Len: 0
      })
    }
    add_threshold(index: number){
      this.gateway_config.Strategy.Logics[index].Arguments.CondCtrlByT2_Thresholds.push({
        Condition: '',
        Action: '',
        Boundary: [0],
        Interval: 1
      })
    }
    add_threshold_boundary(index: number, condIndex: number){
      this.gateway_config.Strategy.Logics[index].Arguments.CondCtrlByT2_Thresholds[condIndex].Boundary.push(0)
    }
    add_timeBoundary_boundary(index: number, condIndex: number){
      this.gateway_config.Strategy.Logics[index].Arguments.CondCtrlByT2_TimeBoundary[condIndex].Boundary.push(0)
    }
    add_dvcBoundary_boundary(index: number){
      this.gateway_config.Strategy.Logics[index].Arguments.CondCtrlByT2_DvcBoundary.push(0)
    }
    add_Driver(){
      var write_slave_addr = 1;
      var test = true;
      while (test){
        for (let i = 0; i < this.gateway_config.Modbus.Driver.length; i++) {
          if (write_slave_addr == this.gateway_config.Modbus.Driver[i].SlaveAddress) {
            write_slave_addr += 1;
            test = false;
          }
        }
        test = !test;
      }
      this.gateway_config.Modbus.Driver.push({
        DriverName: 'ADTek-CPM10',
        PortAddress: '/dev/ttyUSB0',
        SlaveAddress: write_slave_addr,
        PinConfig: []
      })
    }
    add_pinconfig(driver_index: number){
      this.gateway_config.Modbus.Driver[driver_index].PinConfig.push({
        PinIdx: 0,
        SensorType: 'D',
        Definition: []
      })
    }
    add_pindefinition(driver_index: number, pinconfig_index: number){
      this.gateway_config.Modbus.Driver[driver_index].PinConfig[pinconfig_index].Definition.push({
        Pin: "",
        RenameAs: "",
        Read: "",
        Write: "",
        Register_W: ""
      })
    }
    remove_LogicInput(logic_index: number, pin_index: number){
      this.gateway_config.Strategy.Logics[logic_index].InputList.splice(pin_index, 1);
    }
    remove_LogicOutput(logic_index: number, pin_index: number){
      this.gateway_config.Strategy.Logics[logic_index].OutputList.splice(pin_index, 1);
    }

    remove_logic(index: number){
      this.gateway_config.Strategy.Logics.splice(index, 1);
    }
    remove_timeBoundary(logic_index: number, TimeBoundary_index: number){
      this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary.splice(TimeBoundary_index, 1);
    }
    remove_wifi(index: number) {
      this.gateway_config.Communication.WiFi.splice(index, 1);

    }
    remove_declaration(logic_index: number, declaration_index: number){
      this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_Declaration.splice(declaration_index, 1);
    }
    remove_threshold(logic_index: number, threshold_index: number){
      this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_Thresholds.splice(threshold_index, 1);
    }
    remove_timeBoundary_boundary(logic_index: number, cond_index: number, boundary_index: number){
      this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary[cond_index].Boundary.splice(boundary_index, 1);
    }
    remove_threshold_boundary(logic_index: number, cond_index: number, boundary_index: number){
      this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_Thresholds[cond_index].Boundary.splice(boundary_index, 1);
    }
    remove_dvcBoundary_boundary(logic_index: number, boundary_index: number){
      this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_DvcBoundary.splice(boundary_index, 1);
    }
    remove_driver(driver_index: number){
      this.gateway_config.Modbus.Driver.splice(driver_index, 1);
    }
    remove_pinconfig(driver_index: number, pinconfig_index: number){
      this.gateway_config.Modbus.Driver[driver_index].PinConfig.splice(pinconfig_index, 1);
    }
    remove_pindifinition(driver_index: number, pinconfig_index: number, definition_index: number){
      this.gateway_config.Modbus.Driver[driver_index].PinConfig[pinconfig_index].Definition.splice(definition_index, 1);
    }
    isSelected(dayIndex: number, logic_index: number, boundary_index: number): boolean {
      const weekDay = this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary[boundary_index].WeekDay;
      return weekDay.includes(dayIndex);
    }
    onDayChange(isChecked: boolean, dayIndex: number, logic_index: number, boundary_index: number) {
      const weekDay = this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary[boundary_index].WeekDay;

      if (isChecked) {
        weekDay.push(dayIndex);
      } else {
        const index = weekDay.indexOf(dayIndex);
        if (index !== -1) {
          weekDay.splice(index, 1);
        }
      }
      weekDay.sort((a, b) => a - b);
    }

    isBoundaryArray(boundary: IntOrPair): boolean {
      return Array.isArray(boundary);
    }
    toggleThresholdBoundaryType(logic_index: number, cond_index: number, boundary_index: number): void {
      if (this.isBoundaryArray(this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_Thresholds[cond_index].Boundary[boundary_index])) {
        this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_Thresholds[cond_index].Boundary[boundary_index] = 0;
      } else {
        this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_Thresholds[cond_index].Boundary[boundary_index] = [0, 0];
      }
    }
    toggleTimeBoundaryType(logic_index: number, cond_index: number, boundary_index: number): void {
      if (this.isBoundaryArray(this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary[cond_index].Boundary[boundary_index])) {
        this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary[cond_index].Boundary[boundary_index] = 0;
      } else {
        this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_TimeBoundary[cond_index].Boundary[boundary_index] = [0, 0];
      }
    }
    toggleDvcBoundaryType(logic_index: number, boundary_index: number): void {
      if (this.isBoundaryArray(this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_DvcBoundary[boundary_index])) {
        this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_DvcBoundary[boundary_index] = 0;
      } else {
        this.gateway_config.Strategy.Logics[logic_index].Arguments.CondCtrlByT2_DvcBoundary[boundary_index] = [0, 0];
      }
    }
    toggle_pin_config_modal(driver_index: number){
      this.gateway_config_pin_config_visible = !this.gateway_config_pin_config_visible;
      this.gateway_config_visible = !this.gateway_config_visible;
      this.gateway_config_editing_pin_config = driver_index;

    }
    toggle_edit_logic_modal(logic_index: number){
      this.gateway_config_visible = !this.gateway_config_visible;
      this.gateway_config_edit_logic_visible = !this.gateway_config_edit_logic_visible;
      this.gateway_config_editing_logic = logic_index;
    }
    toggle_add_logic_modal(){
      this.gateway_config_visible = !this.gateway_config_visible;
      this.gateway_config_add_logic_visible = !this.gateway_config_add_logic_visible;
    }

    trackByBoundary(index: number, boundary: any): number {
      return index;
    }
    filterAndFillZero(event: any): void {
      const input = event.target as HTMLInputElement;
      let filteredValue = input.value.replace(/[^0-9]/g, '');
      if (filteredValue.length > 1 && filteredValue.startsWith('0')) {
        filteredValue = filteredValue.substring(1);
      }
      if (filteredValue === '') {
        input.value = '0';
      } else {
        input.value = filteredValue;
      }
    }
    filter_valid_serialNo(){

    }
    padNumber(num: number, padNum: number): string {
      return num.toString().padStart(padNum, '0');
    }

    toggle_edit_pin_modal(editing_pin_config: number, index: number){
      this.gateway_config_pin_config_visible = !this.gateway_config_pin_config_visible;
      this.gateway_config_edit_pin_def_index = index;
      this.gateway_config_edit_pin_def_config_visible = !this.gateway_config_edit_pin_def_config_visible;
    }






    set_pin_definition(editing_pin_config: number, edit_pin_def_index: number){
      const ori_def = structuredClone(default_pin_data.data[this.gateway_config.Modbus.Driver[editing_pin_config].DriverName as keyof typeof default_pin_data.data][edit_pin_def_index].Definition[0].Read);
      var renameAs = ''
      if (this.gateway_config_editing_pin_def_dtype=='T'){
        renameAs = 'temperature';
      } else if (this.gateway_config_editing_pin_def_dtype=='H'){
        renameAs = 'humidity';
      } else if (this.gateway_config_editing_pin_def_dtype=='P'){
        renameAs = 'pressure';
      }
      this.gateway_config.Modbus.Driver[editing_pin_config].PinConfig[edit_pin_def_index].Definition[0].Read = `(${ori_def})*${1/(2**this.gateway_config_editing_pin_def_adc)*(this.gateway_config_editing_pin_def_hrange-this.gateway_config_editing_pin_def_lrange)}+${this.gateway_config_editing_pin_def_lrange}`;
      this.gateway_config.Modbus.Driver[editing_pin_config].PinConfig[edit_pin_def_index].SensorType = this.gateway_config_editing_pin_def_dtype;
      this.gateway_config.Modbus.Driver[editing_pin_config].PinConfig[edit_pin_def_index].Definition[0].RenameAs = renameAs;
      this.toggle_edit_pin_modal(editing_pin_config, edit_pin_def_index);
    }
    reset_pin_definition(editing_pin_config: number, edit_pin_def_index: number){
      const ori = structuredClone(default_pin_data.data[this.gateway_config.Modbus.Driver[editing_pin_config].DriverName as keyof typeof default_pin_data.data][edit_pin_def_index]);
      this.gateway_config.Modbus.Driver[editing_pin_config].PinConfig[edit_pin_def_index] = ori;
    }

    change_driver(showCardIdx: number){
      if (!this.is_ai(this.gateway_config.Modbus.Driver[showCardIdx].DriverName)){
        this.gateway_config.Modbus.Driver[showCardIdx].PinConfig = [];
      } else {
        this.gateway_config.Modbus.Driver[showCardIdx].PinConfig = structuredClone(default_pin_data.data[this.gateway_config.Modbus.Driver[showCardIdx].DriverName as keyof typeof default_pin_data.data]);
      }
    }
    is_ai(driver_name: string){
      const io_list = ['SD400', 'IMA-AI', 'JY-DAM0222','THSXX','BAT-08']
      for (let i = 0; i <= io_list.length; i ++){
        if (driver_name == io_list[i]){
          return true
        }
      }
      return false
    }
    switch_editing_dtype(){
      if (this.gateway_config_editing_pin_def_dtype=='T'){
        this.gateway_config_editing_pin_def_lrange = 0;
        this.gateway_config_editing_pin_def_hrange = 50;
        this.gateway_config_editing_pin_def_adc = 12;
      } else if (this.gateway_config_editing_pin_def_dtype=='H'){
        this.gateway_config_editing_pin_def_lrange = 0;
        this.gateway_config_editing_pin_def_hrange = 1;
        this.gateway_config_editing_pin_def_adc = 12;
      } else if (this.gateway_config_editing_pin_def_dtype=='P'){
        this.gateway_config_editing_pin_def_lrange = 0;
        this.gateway_config_editing_pin_def_hrange = 4;
        this.gateway_config_editing_pin_def_adc = 12;
      }
    }
}
