from flask import request, Blueprint, jsonify, current_app, send_from_directory, abort
from accessories import mongo, sqldb, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required, role_required
from bson.objectid import ObjectId
from sqlalchemy import text
import os

device_dashboard_page = Blueprint('device_dashboard', __name__)

@device_dashboard_page.route('/save_plot', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def save_plot(token):
    data = request.get_json()
    project_code = data.get('project_code')
    projectplot_name = data.get('projectplot_name')
    plot_data = data.get('data')
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    if project_code not in user['project']:
        return jsonify({'message': 'Invalid token!'}), 401
    id = mongo.db.projectPlot.find_one({'project_code': project_code, 'projectplot_name': projectplot_name}).get("_id")
    update_json_in_mongo({
        'data': plot_data
    },'projectPlot', id, False)

    return jsonify({"token": get_refresh_token(token)}), 200


@device_dashboard_page.route('/get_projectplot', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'customer'])
def get_projectplot(token):
    data = request.get_json()
    project_code = data.get('project_code')
    projectplot_name = data.get('projectplot_name')
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    if project_code not in user.get('project') and project_code not in user.get('customer_project').keys():
        return jsonify({'message': 'Invalid token!'}), 401
    
    plot_data = mongo.db.projectPlot.find_one({'project_code': project_code, 'projectplot_name': projectplot_name}).get("data") or {}
    return jsonify({"token": get_refresh_token(token),
                    "data": plot_data}), 200


@device_dashboard_page.route('/get_projectplot_object', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'customer'])
def get_projectplot_object(token):
    data = request.get_json()
    project_code = data.get('project_code')
    
    ##Replace IMABOX to Gateway and IMA-PM to PowerMeter
    object_list = list(mongo.db.projectPlot_object.find({"project_code": project_code})) or []
    change = False
    for i in object_list:
        if i.get('type') == "IMABOX":
            i['type'] = "Gateway"
            i['serialNo'] = i['serialNo'].replace("IMABOX", "Gateway")
            change = True
        if i.get('type') == "IMA-PM":
            i['type'] = "PowerMeter"
            i['serialNo'] = i['serialNo'].replace("IMA-PM", "PowerMeter")
            change = True
        if i.get('show_in_view_mode'):
            i.pop('show_in_view_mode')
        if i.get('tableName'):
            i.pop('tableName')
        if i.get('columnName'):
            i.pop('columnName')
        if change:
            update_json_in_mongo(i, 'projectPlot_object', i.get("_id"))
    ## End section
    
    refreshed_object_list = list(mongo.db.projectPlot_object.find({"project_code": project_code}, {"_id": 0})) or []
    return jsonify({"token": get_refresh_token(token),
                    "object_list": refreshed_object_list}), 200

@device_dashboard_page.route('/get_projectplot_link', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'customer'])
def get_projectplot_link(token):
    data = request.get_json()
    project_code = data.get('project_code')
    user_project_list = get_user_info(token, 'project')
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    if project_code not in user_project_list and project_code not in user.get('customer_project').keys():
        return jsonify({'message': 'Invalid token!'}), 401
    
    link_list = list(mongo.db.projectPlot_link.find({"project_code": project_code}, {"_id": 0})) or []
    # 檢查 source/target 是否存在於 projectPlot_cell
    cleaned_link_list = []
    for link in link_list:
        source_id = link.get('source')
        target_id = link.get('target')
        serialNo = link.get('serialNo')
        # 查找對應元件是否存在
        source_exists = mongo.db.projectPlot_object.find_one({
            "project_code": project_code,
            "serialNo": source_id
        })
        target_exists = mongo.db.projectPlot_object.find_one({
            "project_code": project_code,
            "serialNo": target_id
        })

        # 若兩者都存在，保留；否則從資料庫刪除
        if source_exists and target_exists:
            cleaned_link_list.append(link)
        else:
            mongo.db.projectPlot_link.delete_one({
                "project_code": project_code,
                "serialNo": serialNo
            })
    return jsonify({"token": get_refresh_token(token),
                    "link_list": cleaned_link_list}), 200

@device_dashboard_page.route('/get_projectplot_object_config', methods=['OPTIONS', 'POST'])
@role_required(['customer', 'project leader'])
def get_projectplot_object_config(token):
    data = request.get_json()
    project_code = data.get('project_code')
    serialNo = data.get('serialNo')
    user_project_list = get_user_info(token, 'project')
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    if project_code not in user_project_list and project_code not in user.get('customer_project').keys():
        return jsonify({'message': 'Invalid token!'}), 401
    
    object_info = mongo.db.projectPlot_object.find_one({"project_code": project_code, "serialNo": serialNo})
    ## 如果object_info存在特殊key則移除並儲存至mongoDB
    for info in list(object_info.keys()):
        if info not in ['project_code', 'name', 'type', 'place_id', 'serialNo', 'create_time', 'last_updated', 'gateway_id', '_id']:
            del object_info[info]
    update_json_in_mongo(object_info, 'projectPlot_object', object_info.get('_id'))
    object_info.pop('_id', None)  
    ## section end
    return jsonify({"token": get_refresh_token(token),
                    "object_info": object_info }), 200


@device_dashboard_page.route('/get_projectplot_link_config', methods=['OPTIONS', 'POST'])
@role_required(['customer', 'project leader'])
def get_projectplot_link_config(token):
    if request.method == 'OPTIONS':
        return '', 204
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401

    data = request.get_json()
    project_code = data.get('project_code')
    serialNo = data.get('serialNo')
    user_project_list = get_user_info(token, 'project')
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    if project_code not in user_project_list and project_code not in user.get('customer_project').keys():
        return jsonify({'message': 'Invalid token!'}), 401
    link_info = mongo.db.projectPlot_link.find_one({"project_code": project_code, "serialNo": serialNo}, {"_id": 0})
    return jsonify({"token": get_refresh_token(token),
                    "link_info": link_info }), 200



@device_dashboard_page.route('/create_projectplot_object', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_projectplot_object(token):
    data = request.get_json()
    project_code = data.get('project_code')
    name = data.get('name')
    type = data.get('type')
    place_id = data.get('place_id')
    serialNo = data.get('serialNo')
    if mongo.db.projectPlot_object.find_one({"project_code": project_code, "serialNo": serialNo}) or not name:
        return jsonify({'message': 'object repeated'}), 401
    object_dict = {
        "project_code": project_code,
        "name": name,
        "type": type,
        "place_id": place_id,
        "serialNo": serialNo
    }
    
    save_json_to_mongo(object_dict, 'projectPlot_object', ObjectId())

    return jsonify({"token": get_refresh_token(token)}), 200


@device_dashboard_page.route('/create_projectplot_link', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_projectplot_link(token):
    data = request.get_json()
    project_code = data.get('project_code')
    name = data.get('name')
    serialNo = data.get('serialNo')
    [link_type, source, target] = serialNo.split(":")
    switch_serialNo = f"{link_type}:{target}:{source}"
    if mongo.db.projectPlot_link.find_one({"project_code": project_code, "serialNo": serialNo}) or mongo.db.projectPlot_link.find_one({"project_code": project_code, "serialNo": switch_serialNo}):
        return jsonify({'message': 'object repeated'}), 401
    
    link_dict = {
        "project_code": project_code,
        "name": name,
        "serialNo": serialNo,
        "type": link_type,
        "source": source,
        "target": target
    }
    
    save_json_to_mongo(link_dict, 'projectPlot_link', ObjectId())

    return jsonify({"token": get_refresh_token(token) , "object_dict": link_dict}), 200




@device_dashboard_page.route('/remove_projectplot_object', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_projectplot_object(token):
    data = request.get_json()
    project_code = data.get('project_code')
    serialNo = data.get('serialNo')
    target = mongo.db.projectPlot_object.find_one({"project_code": project_code, "serialNo": serialNo})
    if not target:
        return jsonify({'message': 'object not found'}), 401

    remove_json_in_mongo('projectPlot_object', target.get('_id'))

    ##同步移除有連接到該物件的連線

    return jsonify({"token": get_refresh_token(token)}), 200

@device_dashboard_page.route('/remove_projectplot_link', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_projectplot_link(token):
    data = request.get_json()
    project_code = data.get('project_code')
    serialNo = data.get('serialNo')
    target = mongo.db.projectPlot_link.find_one({"project_code": project_code, "serialNo": serialNo})
    if not target:
        return jsonify({'message': 'link not found'}), 401

    remove_json_in_mongo('projectPlot_link', target.get('_id'))

    return jsonify({"token": get_refresh_token(token)}), 200



@device_dashboard_page.route('/update_projectplot_object', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def update_projectplot_object(token):
    data = request.get_json()
    project_code = data.get('project_code')
    serialNo = data.get('config').get('serialNo')
    target = mongo.db.projectPlot_object.find_one({"project_code": project_code, "serialNo": serialNo})
    update_json_in_mongo(data.get('config'), 'projectPlot_object', target.get('_id'))
    return jsonify({"token": get_refresh_token(token)}), 200

@device_dashboard_page.route('/update_projectplot_link', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def update_projectplot_link(token):
    data = request.get_json()
    project_code = data.get('project_code')
    serialNo = data.get('config').get('serialNo')
    target = mongo.db.projectPlot_link.find_one({"project_code": project_code, "serialNo": serialNo})
    update_json_in_mongo(data.get('config'), 'projectPlot_link', target.get('_id'))
    return jsonify({"token": get_refresh_token(token)}), 200


@device_dashboard_page.route('/get_column_list_by_tablename', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_column_list_by_tablename(token):
    data = request.get_json()
    tablename = data.get('tablename')
    if not tablename:
        return jsonify({'message': 'Table name is missing!'}), 400
    engine = sqldb.get_engine(current_app, bind='197')
    ret = []
    with engine.connect() as connection:
        check_table_query = text("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = 'ima_thing' AND TABLE_NAME = :tablename;
        """)
        table_exists = connection.execute(check_table_query, {'tablename': tablename}).scalar()
        if not table_exists:
            return jsonify({'message': f"Table '{tablename}' does not exist in database 'ima_thing'!"}), 404
        query_string = text("""
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'ima_thing' AND TABLE_NAME = :tablename;
        """)
        result = connection.execute(query_string, {'tablename': tablename})
        ret = [row[0] for row in result.fetchall()]
    return jsonify({"token": get_refresh_token(token), "data": ret}), 200



@device_dashboard_page.route('/upload_pjplot_background', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def upload_pjplot_background(token):
    file = request.files.get('image')
    project_code = request.form.get('project_code')
    plot_name = request.form.get('plot_name')
    if not project_code or not plot_name:
        return jsonify({'message': 'Data missing!'}), 400
    if project_code not in mongo.db.user.find_one({"email": get_user_info(token, 'email')}).get('project'):
        return jsonify({'message': 'Authentication failed!'}), 400
    
    # create folder
    os.makedirs(current_app.config['PJPLOTBG_UPLOAD_FOLDER'], exist_ok=True)

    filename = ''
    # save image to local storage
    if file:
        filename = f"bg_{project_code}_{plot_name}{file.filename[file.filename.rfind('.'):].lower()}"
        file.save(os.path.join(current_app.config['PJPLOTBG_UPLOAD_FOLDER'], filename))
    # remove other ext img
    for fname in os.listdir(current_app.config['PJPLOTBG_UPLOAD_FOLDER']):
        name, ext = os.path.splitext(fname)
        if file:
            if name == f"bg_{project_code}_{plot_name}" and ext!= file.filename[file.filename.rfind("."):].lower():
                os.remove(os.path.join(current_app.config['PJPLOTBG_UPLOAD_FOLDER'], fname))
        else:
            if name == f"bg_{project_code}_{plot_name}":
                os.remove(os.path.join(current_app.config['PJPLOTBG_UPLOAD_FOLDER'], fname))


    # return file name
    return jsonify({"token": get_refresh_token(token),
                    "filename": filename}), 200


@device_dashboard_page.route('/pjplot_image', methods=['OPTIONS', 'POST'])
@role_required(['customer', 'project leader'])
def pjplot_image(token):
    data = request.get_json()
    project_code = data.get('project_code')
    plot_name = data.get('plot_name')

    ## 補充檢查該帳號是否可訪問


    ####

    
    filename_prefix = f"bg_{project_code}_{plot_name}"
    folder = current_app.config['PJPLOTBG_UPLOAD_FOLDER']
    matched_file = None
    for fname in os.listdir(folder):
        name, ext = os.path.splitext(fname)
        if name == filename_prefix:
            matched_file = fname
            break
    if not matched_file:
        return abort(404, description='Image not found')
    return send_from_directory(folder, matched_file)
