import { Injectable, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ProjectService } from '../service/project.service';

@Injectable()
export abstract class BaseProjectComponent implements OnInit {
  // 共同變數
  project_code: string = '';
  showCard: string = '';
  project: any = {};

  constructor(
    protected router: Router,
    protected route: ActivatedRoute,
    protected projectService: ProjectService
  ) {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
  }

  ngOnInit(): void {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }

  // 共同的卡片切換邏輯
  setShowCard(cardName: string): void {
    this.showCard = cardName;
    console.log('切換到卡片:', cardName);
  }

  // 共同的專案資訊載入邏輯
  load_project_info(): void {
    this.projectService.get_project_info(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = this.processProjectData(data.data);
        this.onProjectLoaded();
      },
      (error) => {
        console.error('載入專案資訊失敗:', error);
        this.handleError(error);
      }
    );
  }

  // 共同的錯誤處理邏輯
  protected handleError(error: any): void {
    console.error('發生錯誤:', error);
    this.router.navigate(['/login']);
  }

  // 共同的 token 更新邏輯
  protected updateToken(token: string): void {
    localStorage.setItem('token', token);
  }

  // 抽象方法，由子類別實作
  abstract processProjectData(data: any): any;
  abstract onProjectLoaded(): void;
} 