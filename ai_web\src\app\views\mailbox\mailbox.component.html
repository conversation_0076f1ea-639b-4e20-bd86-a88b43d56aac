<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>收件夾</strong>
      </c-card-header>
      <c-card-body>
        <c-row class="mb-3">
          <c-col>
            <table class="table table-striped table-hover position-relative">
              <thead>
                <tr>
                  <th></th>
                  <th>標題</th>
                  <th>寄件人</th>
                  <th>時間</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let mail of filteredMails">
                  <tr (click)="viewMailDetails(mail)" [class.selected]="selectedMail?.id === mail.id" class="mail-row">
                    <td class="position-relative">
                      <span *ngIf="mail.status === 'send'" class="status-dot"></span>
                    </td>
                    <td>
                      <div class="mail-preview">
                        <span class="subject" [ngClass]="{'font-weight-bold': mail.status === 'send'}">{{ mail.subject }}</span>
                        <small class="content-preview ms-2" [innerHTML]="getContentPreview(mail.content)"></small>
                      </div>
                    </td>
                    <td>{{ mail.sender_name }}</td>
                    <td>{{ mail.time | date:'yyyy-MM-dd HH:mm' }}</td>
                  </tr>
                  <tr *ngIf="selectedMail?.id === mail.id">
                    <td colspan="4">
                      <c-card class="mail-detail p-3">
                        <c-card-header>
                          <h4>{{ mail.subject }}</h4>
                        </c-card-header>
                        <c-card-body>
                          <p class="text-muted">
                            <small>
                              <strong>寄件人：</strong>{{ mail.sender_name }}
                              <strong>時間：</strong>{{ mail.time | date:'yyyy-MM-dd HH:mm:ss' }}
                            </small>
                          </p>
                          <div class="mail-content">
                            <strong>內文：</strong>
                            <div [innerHTML]="convertContent(mail.content)"></div>
                          </div>
                          <div *ngIf="mail.mail_type == 1 && mail.status === 'send'" class="mail-actions mt-3">
                            <button class="btn btn-outline-success me-2" (click)="openModal(mail, 'accept')">同意</button>
                            <button class="btn btn-outline-danger me-2" (click)="openModal(mail, 'reject')">拒絕</button>
                          </div>
                        </c-card-body>
                      </c-card>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </c-col>
        </c-row>
        <div class="px-3 py-2 border-top d-flex justify-content-center align-items-center">
          <c-pagination aria-label="頁碼導航">
            <li cPageItem [disabled]="currentPage === 1">
              <a cPageLink (click)="onPageChange(currentPage - 1)">上一頁</a>
            </li>
            
            <!-- 第一頁 -->
            <li cPageItem [active]="currentPage === 1">
              <a cPageLink (click)="onPageChange(1)">1</a>
            </li>
            
            <!-- 前省略號 -->
            <li cPageItem *ngIf="currentPage > 3">
              <a cPageLink>...</a>
            </li>
            
            <!-- 中間頁 -->
            <ng-container *ngFor="let page of getPaginationPages()">
              <li cPageItem *ngIf="page !== 1 && page !== getTotalPages()" [active]="page === currentPage">
                <a cPageLink (click)="onPageChange(page)">{{ page }}</a>
              </li>
            </ng-container>
            
            <!-- 後省略號 -->
            <li cPageItem *ngIf="currentPage < getTotalPages() - 2">
              <a cPageLink>...</a>
            </li>
            
            <!-- 最後一頁 -->
            <li cPageItem *ngIf="totalPages > 1" [active]="currentPage === totalPages">
              <a cPageLink (click)="onPageChange(totalPages)">{{ totalPages }}</a>
            </li>
            
            <li cPageItem [disabled]="currentPage === totalPages">
              <a cPageLink (click)="onPageChange(currentPage + 1)">下一頁</a>
            </li>
          </c-pagination>
        </div>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
<c-modal #verticallyCenteredModal alignment="top" #scrollableLongContentModal id="liveDemoModal" [visible]="visible" backdrop="static" size="sm">
  <c-modal-header>
    <h5 cModalTitle>訊息通知</h5>
    <button (click)="visible = false" cButtonClose></button>
  </c-modal-header>
    <c-modal-body>
    <c-row>
      <label cCol cLabel="col" class="mx-4" *ngIf = "actionType === 'accept'">
        <strong>確認同意?</strong>
      </label>
      <label cCol cLabel="col" class="mx-4" *ngIf = "actionType === 'reject'">
        <strong>確認拒絕?</strong>
      </label>
    </c-row>
      <button cButton color="primary" class="btn btn-primary mx-2" (click)="confirmAction()"  style="float: right;" variant="outline">確認</button>
    </c-modal-body>
</c-modal>

