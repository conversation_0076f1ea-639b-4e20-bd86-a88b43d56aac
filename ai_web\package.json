{"name": "ima_ai_web", "version": "5.2.16", "copyright": "Copyright 2024 creativeL<PERSON><PERSON><PERSON>", "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people) and contributors", "homepage": "https://coreui.io/angular", "config": {"theme": "default", "coreui_library_short_version": "5.2", "coreui_library_docs_url": "https://coreui.io/angular/docs/"}, "scripts": {"ng": "ng", "start": "ng serve -o", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.1", "@angular/cdk": "^18.2.1", "@angular/common": "^18.2.1", "@angular/compiler": "^18.2.1", "@angular/core": "^18.2.1", "@angular/forms": "^18.2.1", "@angular/language-service": "^18.2.1", "@angular/platform-browser": "^18.2.1", "@angular/platform-browser-dynamic": "^18.2.1", "@angular/router": "^18.2.1", "@coreui/angular": "~5.2.18", "@coreui/angular-chartjs": "~5.2.16", "@coreui/chartjs": "~4.0.0", "@coreui/coreui": "^5.1.2", "@coreui/icons": "^3.0.1", "@coreui/icons-angular": "~5.2.18", "@coreui/utils": "^2.0.2", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@joint/plus": "file:joint-plus.tgz", "@joint/shapes-vsm": "file:joint-vsm-shapes.tgz", "@popperjs/core": "~2.11.8", "backbone": "^1.6.1", "bootstrap": "^5.3.3", "chart.js": "^4.4.4", "joint-plus": "^4.0.1", "jointjs": "^3.7.7", "jquery": "^3.7.1", "lodash-es": "^4.17.21", "ngx-scrollbar": "^13.0.3", "rxjs": "~7.8.1", "tslib": "^2.7.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.1", "@angular/cli": "^18.2.1", "@angular/compiler-cli": "^18.2.1", "@angular/localize": "^18.2.1", "@types/backbone": "^1.4.23", "@types/jasmine": "^5.1.4", "@types/jquery": "^3.5.32", "@types/lodash-es": "^4.17.12", "@types/node": "^20.16.1", "jasmine-core": "^5.2.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "~5.5.4"}, "engines": {"node": "^18.19.0 || ^20.9.0", "npm": ">= 9"}}