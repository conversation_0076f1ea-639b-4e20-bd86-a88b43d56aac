from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail
from flask_mail import Message
from flask_redis import FlaskRedis
from itsdangerous import URLSafeTimedSerializer
from flask import current_app, request, url_for
from flask_login import LoginManager, UserMixin
from queue import Queue
from flask_pymongo import PyMongo
from datetime import datetime, timezone, timedelta
from bson.objectid import ObjectId
import uuid
from sqlalchemy import text
import jwt
from sqlalchemy.exc import OperationalError
import time

sqldb = SQLAlchemy()
mail = Mail()
redis_client = FlaskRedis()
token_store = FlaskRedis()
crawler_redis_client = FlaskRedis(config_prefix='CRAWLER_REDIS_')
mongo = PyMongo()
login_manager = LoginManager()
mqtt_queue = Queue()
login_manager.login_view = "login"
mqtt_client = None

@login_manager.user_loader
def load_user(user_id):
    user_data = mongo.db.user.find_one({'_id': ObjectId(user_id)})
    return user_data

class User(UserMixin):
    def __init__(self, user_data):
        self.id = str(user_data['_id'])
        self.email = user_data['email']
        self.password = user_data['password']
        self.role = user_data['role']

    @property
    def is_active(self):
        return True if self.password else False

    @property
    def is_authenticated(self):
        return True

    @property
    def is_anonymous(self):
        return False

    def get_id(self):
        return self.id



def get_user_info(token, key):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    user = mongo.db.user.find_one({"email": decoded_token['user']})
    return user[key]

def update_json_in_mongo(data, collection_name, doc_name, save_history=True):
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    collection = mongo.db[collection_name]
    history_collection = mongo.db[f"{collection_name}_history"]

    existing_doc = collection.find_one({"_id": doc_name})

    if existing_doc:
        if save_history:
            history_doc = existing_doc.copy()
            history_doc["_id"] = f"{doc_name}_{current_time}"
            history_collection.insert_one(history_doc)

        for key, value in data.items():
            existing_doc[key] = value

        existing_doc['last_updated'] = current_time

        collection.replace_one({"_id": doc_name}, existing_doc)

    else:
        data["_id"] = doc_name
        data['last_updated'] = current_time
        collection.insert_one(data)



def save_json_to_mongo(data_dict, collection_name, document_name, save_history=True):
    collection = mongo.db[collection_name]
    history_collection = mongo.db[f"{collection_name}_history"]
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    data_dict["create_time"] = current_time
    existing_document = collection.find_one({"_id": document_name})
    if existing_document:
        existing_document["archived_time"] = current_time
        if save_history:
            history_collection.insert_one({
                **existing_document,
                "_id": f"{document_name}_{current_time}"
            })
        collection.delete_one({"_id": document_name})

    collection.insert_one({
        "_id": document_name,
        **data_dict
    })

def remove_json_in_mongo(collection_name, doc_name, save_history=True):
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    collection = mongo.db[collection_name]
    history_collection = mongo.db[f"{collection_name}_history"]

    existing_doc = collection.find_one({"_id": doc_name})

    if existing_doc:
        if save_history:
            history_doc = existing_doc.copy()
            history_doc["_id"] = f"{doc_name}_{current_time}"
            history_collection.insert_one(history_doc)
        collection.delete_one({"_id": doc_name})


def get_serializer():
    return URLSafeTimedSerializer(current_app.config['SECRET_KEY'])



def verify_token(token, expiration=3600):
    serializer = get_serializer()
    try:
        user_id = serializer.loads(
            token,
            salt=current_app.config['SECURITY_PASSWORD_SALT'],
            max_age=expiration
        )
    except Exception as e:
        return None
    return user_id

def type_name_trans(name, rev=0):
    mapping = {
        '閘道': 'Gateway',
        '馬達': 'Motor',
        '冰水主機': 'Chiller',
        '空壓機': 'AirCompressor',
        '配電盤': 'Panel',
        '無線基地台': 'Wi-Fi_AP',
        '路由器': 'Router',
        '儲存槽': 'Tank',
        '電表': 'PowerMeter',
        '變頻器': 'Inverter',
        '輸入輸出模組': 'IO',
        '流量計': 'FlowMeter',
        '溫度計': 'TempSensor',
        '乾濕球': 'Psychrometer',
        '壓差計': 'DiffPresGauge',
        '濕度計': 'Hygrometer',
        '自定義': 'Self-defined'
    }

    if not rev:
        return mapping.get(name, 0)
    else:
        reverse_mapping = {v: k for k, v in mapping.items()}
        return reverse_mapping.get(name, 0)

def send_mail(sender, receiver, subject,content, argument="NoArgs",mail_type=0, respond_routes=None,  due_time=168):
    if not sender or not receiver:
        raise ValueError("Sender email or receiver is missing!")
    mail_id = None
    if isinstance(respond_routes, (str, type(None))):  
        respond_routes = {}  
    accept_route = respond_routes.get("accept")
    reject_route = respond_routes.get("reject")
    taipei_tz = timezone(timedelta(hours=8))  
    created_at = datetime.now(taipei_tz)  
    mail_token = f"mail:{sender}:{receiver}:{subject}:{argument}"
    if redis_client.exists(mail_token):
        return {"message": f"Duplicate mail already sent to {receiver} from {sender} with subject '{subject}' and argument '{argument}'."}, 400
    content = content.replace("\n", "<br>")
    subject = subject.replace("\n", " ")
    create_mail_info= text("""
        CREATE TABLE IF NOT EXISTS mail_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender VARCHAR(255) NOT NULL,
            receiver VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            argument TEXT NULL,
            content TEXT NOT NULL,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            due_time INT NOT NULL,
            mail_type INT NOT NULL
        );
    """)
    create_mail_event = text("""
        CREATE TABLE IF NOT EXISTS mail_event (
            id INT AUTO_INCREMENT PRIMARY KEY,
            mail_id INT NOT NULL,
            event VARCHAR(255) NOT NULL,
            event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (mail_id) REFERENCES mail_info(id) ON DELETE CASCADE
        );
    """)
    insert_mail = text("""
        INSERT INTO mail_info (sender, receiver, subject, argument, content, time, due_time, mail_type)
        VALUES (:sender, :receiver, :subject, :argument, :content, :time, :due_time, :mail_type)
    """)
    insert_mail_event = text("""
        INSERT INTO mail_event (mail_id, event, event_time)
        VALUES (:mail_id, 'send', NOW())
    """)
    delay = 1
    max_retries = 3
    for attempt in range(max_retries):
        try:
            sqldb.session.execute(create_mail_info)
            sqldb.session.execute(create_mail_event)
            result = sqldb.session.execute(insert_mail, {
                "sender": sender,
                "receiver": receiver,
                "subject": subject,
                "content": content,
                "argument": argument,
                "time": created_at.strftime('%Y-%m-%d %H:%M:%S'),  
                "due_time": due_time,
                "mail_type": mail_type
            })
            mail_id = result.lastrowid
            sqldb.session.execute(insert_mail_event, {"mail_id": mail_id})
            sqldb.session.commit()
            break  
        except OperationalError:
            if attempt < max_retries - 1:
                time.sleep(delay)
                delay *= 2  
            else:
                raise Exception(f"Database operation failed after {max_retries} attempts")
    if mail_type == 1:
        redis_client.hset(mail_token, mapping={
            'mail_id': mail_id,
            'sender': sender,
            'receiver': receiver,
            'subject': subject,
            'argument': argument,
            'content': content,
            'created_at': str(created_at),
            'due_time': due_time,
            'mail_type': mail_type
        })
        redis_client.expire(mail_token, due_time * 3600)
    receiver_data = mongo.db.user.find_one({"email": receiver})
    notification_method = receiver_data.get("notification_method", {}) if receiver_data else {}
    mail_notification = notification_method.get("mail", True)
    if mail_notification:
        body = f"""
        <strong>{subject}</strong>
        {content}
        """
        if mail_type == 1:  
            accept_route = url_for(accept_route, mail_token=mail_token, _external=True)
            reject_route = url_for(reject_route, mail_token=mail_token, _external=True)
            body += f"""
            請點擊以下按鈕回覆：
            <br>
            <a href="{accept_route}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-align: center; text-decoration: none; border-radius: 5px; display: inline-block;">
            同意
            </a>
            <br>
            <a href="{reject_route}" style="background-color: #f44336; color: white; padding: 10px 20px; text-align: center; text-decoration: none; border-radius: 5px; display: inline-block;">
            拒絕
            </a>
            <br>
            """
        else:
            body += "<br><br>此通知無需回覆。"
        msg = Message(
            subject=f"Notification - {subject}",
            recipients=[receiver],
            html=body,
            sender="<EMAIL>"
        )
        mail.send(msg)
    return {"mail_id": mail_id, "token": mail_token}
