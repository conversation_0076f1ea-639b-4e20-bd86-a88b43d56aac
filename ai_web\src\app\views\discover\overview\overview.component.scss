.app-marketplace {
  padding: 24px;
}

.marketplace-menu {
  flex-wrap: wrap;
}

.menu-button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  background-color: white;
  color: #212529;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
    cursor: pointer;
  }

  &.active {
    background-color: #1E3F20;
    color: white;
    border-color: #1E3F20;
  }
}

.app-list {
  .app-item {
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 16px;
    background-color: white;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
      cursor: pointer;
    }

    .app-icon {
      width: 36px;
      height: 36px;
      color: #1E3F20;
      flex-shrink: 0;
    }

    .app-title {
      font-weight: 600;
      font-size: 18px;
    }

    .app-subtitle {
      color: #6c757d;
      font-size: 14px;
    }
  }
}

.category-section {
  .category-card {
    border: 1px solid #dee2e6;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: white;
    transition: all 0.2s;

    &:hover {
      background-color: #1E3F20;
      color: white;
      cursor: pointer;
    }
  }
}
.search-box {
  input {
    width: 200px;
    border-radius: 20px;
    padding: 4px 12px;
    border: 1px solid #dee2e6;
    transition: border-color 0.2s;

    &:focus {
      outline: none;
      border-color: #1E3F20;
    }
  }
}
.app-action-btn {
  font-size: 0.85rem;
  padding: 0.3rem 0.8rem;
  border-radius: 0.4rem;
  border: none;
  transition: all 0.2s ease;
}

.app-action-btn.free-btn {
  background-color: #e7f9ed;
  color: #2e7d32;
}
.app-action-btn.free-btn:hover {
  background-color: #d0f0db;
  color: #1b5e20;
}

.app-action-btn.paid-btn {
  background-color: #fff7e6;
  color: #c47f00;
}
.app-action-btn.paid-btn:hover {
  background-color: #ffecc0;
  color: #8a5b00;
}

.app-action-btn.licensed-btn {
  background-color: #e6f0ff;
  color: #0052cc;
}
.app-action-btn.licensed-btn:hover {
  background-color: #ccdfff;
  color: #003d99;
}

.app-action-btn.disabled-btn {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.app-action-btn:hover {
  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.15);
}
