import { GatewayService } from './../../../service/gateway.service';
import { ProjectService } from './../../../service/project.service';
import { DashboardService } from './../../../service/dashboard.service';
import { environment } from '../../../../environments/environment';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import '@joint/plus/joint-plus.css';
import {
  ButtonCloseDirective,
  ButtonDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  TableModule,
  GridModule,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  ThemeDirective,
  NavModule,
  ListGroupDirective,
  ListGroupItemDirective,
  InputGroupComponent,
  InputGroupTextDirective,
} from '@coreui/angular';
import {
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormSelectDirective,
} from '@coreui/angular';
import { type_name_trans } from '../object_type';
import {
  object_type_list,
  link_type_list,
  serial_no_list,
  link_connectable_object,
} from '../object_type';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { IconDirective } from '@coreui/icons-angular';
import { cilMoodBad, cilSearch, cilOptions } from '@coreui/icons';

interface plot_object {
  type: string;
  name: string;
  place_id: string;
  serialNo: string;
}
interface Project {
  project_name: string;
  project_code: string;
  place_id: string[];
  gateway: string[];
}

interface link {
  type: string;
  name: string;
  source: string;
  target: string;
  source_serialNo: string;
  target_serialNo: string;
  serialNo: string;
}

interface TeamMember {
  name: string;
  role: string;
}

interface customer {
  name: string;
  email: string;
  auth: auth;
}
interface auth {
  view: boolean;
  accounting: boolean;
  controller: boolean;
  view_plot: string[];
  accounting_place_id: string[];
  controller_parameters: string[];
}

@Component({
  selector: 'app-list',
  standalone: true,
  imports: [
    ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    FormsModule,
    FormControlDirective,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    RowComponent,
    TextColorDirective,
    FormSelectDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    ThemeDirective,
    RouterModule,
    NavModule,
    ListGroupDirective,
    ListGroupItemDirective,
    InputGroupComponent,
    InputGroupTextDirective,
    IconDirective,
  ],
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss'],
})
export class ListComponent implements OnInit {
  icons = { cilMoodBad, cilSearch, cilOptions };
  searchPlotText = '';
  searchObjectText = '';
  searchPlaceIdText = '';
  searchLinkText = '';
  searchGatewayText = '';
  searchEventText = '';
  searchInvoiceText = '';
  project_code = '';
  project: Project;
  event_list: any[] = [];
  invoice_list: any[] = [];
  errmsg = '';
  titles: string[] = [];
  place_id_list: string[] = [];
  remove_serialNo = '0';
  remove_place_id = '';
  remove_object_type = '';
  remove_object_name = '';
  remove_link_serialNo = '';
  remove_config_index = 0;
  new_object_type = '';
  new_link_type = '';
  new_link_serialNo = '';
  new_serialNo = '0';
  new_place_id = '';
  new_project_place_id = '';
  new_object_name = '';
  new_link_source = '';
  new_link_target = '';
  new_link_name = '';
  showCard = '';
  projectplot_list: string[] = [];
  projectobject_list: plot_object[] = [];
  projectlink_list: link[] = [];
  connectableSourceList: plot_object[];
  connectableLinkTypeList: string[];
  connectableTargetList: plot_object[];
  object_type_list = object_type_list;
  link_type_list = link_type_list;
  visible = false;
  create_visible = false;
  create_link_visible = false;
  remove_visible = false;
  edit_visible = false;
  remove_projectplot_visible = false;
  remove_link_visible = false;
  remove_place_id_visible = false;
  remove_config_visible = false;
  create_place_id_visible = false;
  production = environment.production;
  input_name = '';
  remove_projectplot_index = 0;
  available_source_list: string[] = [];
  available_target_list: string[] = [];

  constructor(
    private ProjectService: ProjectService,
    private GatewayService: GatewayService,
    private DashboardService: DashboardService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.project = {
      project_code: '',
      project_name: '',
      place_id: [],
      gateway: [],
    };
    this.showCard = 'place_id';
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
  }

  ngOnInit(): void {
    this.get_project_info();

  }

  get_project_info(): void {
    this.ProjectService.get_project_info(this.project_code).subscribe(
      (response: any) => {
        const data = response.data;
        this.project = {
          project_name: data.project_name,
          project_code: data.project_code,
          place_id: data.place_id || [],
          gateway: data.gateway || [],
        };
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
    this.load_project_info();
    this.get_events_list();
    this.get_invoices_list();
  }

  load_project_info() {
    this.projectplot_list = [];
    this.ProjectService.get_projectplotlist(this.project_code).subscribe(
      (data) => {
        for (let i = 0; i < data.projectplot_list.length; i++) {
          this.projectplot_list[i] = data.projectplot_list[i].projectplot_name;
        }
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
    this.projectobject_list = [];
    this.DashboardService.get_projectplot_object(this.project_code).subscribe(
      (data) => {
        for (let i = 0; i < data.object_list.length; i++) {
          this.projectobject_list.push({
            type: type_name_trans(data.object_list[i].type, true) || '',
            name: data.object_list[i].name,
            place_id: data.object_list[i].place_id,
            serialNo: data.object_list[i].serialNo,
          });
        }
        localStorage.setItem('token', data.token);
        this.projectlink_list = [];
        this.DashboardService.get_projectplot_link(this.project_code).subscribe(
          (data) => {
            for (let i = 0; i < data.link_list.length; i++) {
              const matchedSource = this.projectobject_list.find(
                (obj) => obj.serialNo === data.link_list[i].source
              );
              const source_name = matchedSource ? matchedSource.name : '';
              const matchedTarget = this.projectobject_list.find(
                (obj) => obj.serialNo === data.link_list[i].target
              );
              const target_name = matchedTarget ? matchedTarget.name : '';
              this.projectlink_list.push({
                type: type_name_trans(data.link_list[i].type, true) || '',
                name: data.link_list[i].name,
                source: source_name,
                source_serialNo: data.link_list[i].source,
                target: target_name,
                target_serialNo: data.link_list[i].target,
                serialNo: data.link_list[i].serialNo,
              });
            }
            localStorage.setItem('token', data.token);
          },
          (error) => {
            this.router.navigate(['/404']);
          }
        );
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  remove_projectplot(idx: number) {
    this.ProjectService.remove_projectplot(
      this.project_code,
      this.projectplot_list[idx]
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.toggle_remove_projectplot_view(0);
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  toggle_create_place_id_view() {
    this.input_name = '';
    this.create_place_id_visible = !this.create_place_id_visible;
  }

  toggle_remove_link_view(index: number) {
    if (index >= 0) {
      this.remove_link_serialNo = this.projectlink_list[index].serialNo;
      this.remove_link_visible = true;
    } else {
      this.remove_link_visible = false;
    }
  }

  toggle_remove_config_view(index: number) {
    if (index >= 0) {
      this.remove_config_index = index;
      this.remove_config_visible = true;
    } else {
      this.remove_config_visible = false;
    }
  }

  toggle_remove_object_view(index: number) {
    if (index >= 0) {
      this.remove_object_type = this.projectobject_list[index].type;
      this.remove_object_name = this.projectobject_list[index].name;
      this.remove_place_id = this.projectobject_list[index].place_id;
      this.remove_serialNo = this.projectobject_list[index].serialNo;
      this.remove_visible = true;
    } else {
      this.remove_visible = false;
    }
  }

  toggle_remove_projectplot_view(index: number) {
    this.remove_projectplot_index = index;
    this.remove_projectplot_visible = !this.remove_projectplot_visible;
  }
  toggle_remove_place_id_view(index: number) {
    this.remove_place_id = this.project.place_id[index];
    this.remove_place_id_visible = !this.remove_place_id_visible;
  }

  create_place_id() {
    if (this.input_name.length == 11) {
      this.ProjectService.create_place_id(
        this.project.project_code,
        this.input_name
      ).subscribe(
        (data) => {
          this.load_project_info();
          this.toggle_create_place_id_view();
          localStorage.setItem('token', data.token);
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
    }
  }

  remove_object(remove_serialNo: string) {
    this.DashboardService.remove_projectplot_object(
      this.project_code,
      remove_serialNo
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.toggle_remove_object_view(-1);
        localStorage.setItem('token', data.token);
        for (let link of this.projectlink_list) {
          if (
            link.source_serialNo === this.remove_serialNo ||
            link.target_serialNo === this.remove_serialNo
          ) {
            this.remove_link(link.serialNo);
          }
        }
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  remove_link(serialNo: string) {
    this.DashboardService.remove_projectplot_link(
      this.project_code,
      serialNo
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.toggle_remove_link_view(-1);
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  remove_place_id_f(place_id: string) {
    this.ProjectService.remove_place_id(this.project_code, place_id).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.toggle_remove_place_id_view(-1);
        for (let object of this.projectobject_list) {
          if (object.place_id === place_id) {
            this.DashboardService.remove_projectplot_object(
              this.project_code,
              object.serialNo
            ).subscribe(
              (data) => {
                localStorage.setItem('token', data.token);
                for (let link of this.projectlink_list) {
                  if (
                    link.source_serialNo === object.serialNo ||
                    link.target_serialNo === object.serialNo
                  ) {
                    this.remove_link(link.serialNo);
                  }
                }
              },
              (error) => {
                this.router.navigate(['/404']);
              }
            );
          }
        }
        this.load_project_info();
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  remove_gateway_config(index: number) {
    this.GatewayService.remove_gateway(
      this.project_code,
      this.project.gateway[index]
    ).subscribe(
      (data) => {
        this.get_project_info();
        this.toggle_remove_config_view(-1);
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  filter_serialNo() {
    this.new_serialNo = serial_no_list[0];
    for (let i = 0; i < serial_no_list.length; i++) {
      for (let j = 0; j < this.projectobject_list.length; j++) {
        const po = this.projectobject_list[j];
        const serial = po.serialNo;
        const lastPart = serial.substring(serial.lastIndexOf('-') + 1); // 取最後一段尾碼

        if (
          po.place_id == this.new_place_id &&
          po.type == this.new_object_type &&
          lastPart == this.new_serialNo
        ) {
          this.new_serialNo = serial_no_list[i + 1];
        }
      }
    }

    this.new_serialNo =
      type_name_trans(this.new_object_type) +
      '-' +
      this.new_place_id +
      '-' +
      this.new_serialNo;
  }

  refresh_connectable_link(): void {
    this.new_link_type = '';
    this.new_link_target = '';
    this.new_link_name = '';

    const allLinkTypes: string[] = Object.keys(link_connectable_object);

    const source = this.projectobject_list.find(
      (obj: plot_object) => obj.serialNo === this.new_link_source
    );

    if (!source) {
      this.connectableLinkTypeList = [];
      return;
    }

    this.connectableLinkTypeList = allLinkTypes.filter((linkType: string) => {
      const validTypes: string[] = link_connectable_object[linkType];
      return validTypes.includes(type_name_trans(source.type) || '');
    });
  }
  refresh_connectable_target(): void {
    this.new_link_target = '';
    this.new_link_name = '';

    if (!this.new_link_source || !this.new_link_type) {
      this.connectableTargetList = [];
      return;
    }

    const validTypes: string[] =
      link_connectable_object[this.new_link_type] || [];

    this.connectableTargetList = this.projectobject_list.filter(
      (obj: plot_object) =>
        validTypes.includes(type_name_trans(obj.type) || '') &&
        obj.serialNo !== this.new_link_source
    );
  }

  clear_link() {
    this.connectableSourceList = this.projectobject_list;
    this.connectableTargetList = this.projectobject_list;
    this.connectableLinkTypeList = this.link_type_list
      .map((linkType) => type_name_trans(linkType)) // 將每個元素傳入 type_name_trans 函數
      .filter((result) => result !== null) as string[];
    this.new_link_source = '';
    this.new_link_type = '';
    this.new_link_target = '';
    this.new_link_name = '';
  }

  type_name_trans(name: string, rev: boolean = false): string | null {
    return type_name_trans(name, rev);
  }

  setShowCard(input: string, card_index: number = 0): void {
    this.showCard = input;
  }

  filteredPlots() {
    return this.projectplot_list.filter((p) =>
      p.toLowerCase().includes(this.searchPlotText.toLowerCase())
    );
  }

  filteredObjects() {
    return this.projectobject_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchObjectText.toLowerCase())
    );
  }

  filteredPlaceId() {
    return this.project.place_id.filter((p) =>
      p.toLowerCase().includes(this.searchPlaceIdText.toLowerCase())
    );
  }

  filteredLinks() {
    return this.projectlink_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchLinkText.toLowerCase())
    );
  }

  filteredGateways(): string[] {
    if (!this.searchGatewayText) {
      return this.project.gateway;
    }
    return this.project.gateway.filter((gateway) =>
      gateway.toLowerCase().includes(this.searchGatewayText.toLowerCase())
    );
  }

  filteredEvents() {
    if (!this.searchEventText) {
      return this.event_list;
    }
    return this.event_list.filter((event) =>
      event.name?.toLowerCase().includes(this.searchEventText.toLowerCase())
    );
  }

  filteredInvoices() {
    if (!this.searchInvoiceText) {
      return this.invoice_list;
    }
    return this.invoice_list.filter((invoice) =>
      invoice.invoiceNumber?.toLowerCase().includes(this.searchInvoiceText.toLowerCase())
    );
  }
  get_events_list(): void {
    this.ProjectService.get_events_list(this.project_code).subscribe(
      (data: any) => {
        console.log(data);
        this.event_list = data.events_data || [];
        localStorage.setItem('token', data.token);
      },
      (error: any) => {
        this.router.navigate(['/404']);
      }
    );

  }

  get_invoices_list(): void {
    this.ProjectService.get_invoices_list().subscribe(
      (data: any) => {
        console.log(data);
        // 處理 invoice_numbers 陣列
        if (data.invoice_numbers && Array.isArray(data.invoice_numbers)) {
          this.invoice_list = data.invoice_numbers.map((invoiceNumber: string) => ({
            invoiceNumber: invoiceNumber,
            id: invoiceNumber // 使用發票號碼作為ID
          }));
        } else if (data.invoices_data) {
          this.invoice_list = data.invoices_data || [];
        } else {
          this.invoice_list = [];
        }
        localStorage.setItem('token', data.token);
      },
      (error: any) => {
        //this.router.navigate(['/404']);
        console.log("invoice error");
      }
    );
  }

  // 查看發票詳情
  viewInvoiceDetail(invoice: any): void {
    // 可以導航到詳細頁面或開啟modal
    console.log('查看發票詳情:', invoice.invoiceNumber);
    // 例如：this.router.navigate(['/invoice-detail', invoice.invoiceNumber]);
  }

  // 下載發票證明（已取消功能）
   downloadInvoiceProof(invoice: any): void {
    console.log(invoice.invoiceNumber);
  //   console.log(invoice.invoiceNumber);
  //   this.ProjectService.get_invoices_pdf(invoice.invoiceNumber).subscribe((blob) => {
  //     if (!blob) return;
  // 
  //     const url = window.URL.createObjectURL(blob);
  //     const a = document.createElement('a');
  //     a.href = url;
  //     a.download = `${invoice.invoiceNumber}.pdf`;
  //     a.click();
  //     window.URL.revokeObjectURL(url);
  //   }, error => {
  //     console.error('❌ 發票下載失敗:', error);
  //   });
   }


}
