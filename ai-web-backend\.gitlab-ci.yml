image: python:3.9

before_script:
  - apt-get update
  - apt-get install -y build-essential libffi-dev python3-dev python3-pip

stages:
  - build and deploy
  
build and deploy:
    stage: build and deploy
    script:
        - mkdir -p ~/.ssh
        - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
        - chmod 600 ~/.ssh/id_rsa
        - ls -lah ~/.ssh/id_rsa
        - ssh -o StrictHostKeyChecking=no ima@************* "sudo rm -r /home/<USER>/flask-backend"
        - ssh -o StrictHostKeyChecking=no ima@************* "mkdir -p /home/<USER>/flask-backend"
        - ls -al
        - scp -r . ima@*************:/home/<USER>/flask-backend
        - |
          ssh ima@************* << 'EOF'
          cd /home/<USER>/flask-backend
          python3 -m venv venv
          . venv/bin/activate
          pip install -r requirements.txt
          echo $(openssl rand -base64 32 | tr -dc 'A-Za-z0-9' | head -c 32) > /home/<USER>/flask-backend/security_key
          sudo systemctl restart ai-web-backend.service
          EOF
