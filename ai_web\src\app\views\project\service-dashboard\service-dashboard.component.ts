import { ProjectService } from './../../../service/project.service';
import { ServiceService } from './../../../service/service.service';
import { UserService } from '../../../service/user.service';
import { Component,ViewChild } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ChartjsComponent } from '@coreui/angular-chartjs';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
  ButtonGroupComponent,
  CardFooterComponent,
  ProgressComponent,
  ProgressBarComponent,
  FormCheckComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  ModalFooterComponent,
  ThemeDirective,
  NavModule,
  ListGroupDirective,
  ListGroupItemDirective,
  InputGroupTextDirective,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilHttps, cilMoodBad,
  cilBarChart,
  cilMap,
  cilBolt,
  cilChartLine,
  cilGroup,
  cilListRich,
  cilSettings,
  cilEqualizer } from '@coreui/icons';
interface project {
  project_code: string;
  project_name: string;
  place_id_list: string[];
}
interface member {
  name: string;
  email: string;
}
interface service{
  service_name : string;
  service_type : string;
  service_id : string;
}
interface TpcBillInfo {
  place_id: string;
  ebpps_url: string;
  ebpps_name: string;
  ebpps_customer_address: string;
  ebpps_bill_address: string;
  electricity_type: string;
  feeder_number: string;
  group_powerout: string;
  company_id: string;
  bill_year_month: string;
  bill_date_next: string | null;
  bill_date: string | null;
  date_readmeter: string | null;
  date_next_readmeter: string | null;
  date_bill_start: string | null;
  date_bill_end: string | null;
  day_used: number | null;
  min_kwh: number | null;
  fee_total: number | null;
  fee_kw: number | null;
  fee_kwfine: number | null;
  fee_kwh: number | null;
  fee_pf: number | null;
  fee_public: number | null;
  fee_tax: number | null;
  kwh_total: number | null;
  kwh_peak: number | null;
  kwh_semipeak: number | null;
  kwh_offpeak: number | null;
  kwh_saturday: number | null;
  contract_peak: number | null;
  contract_semipeak: number | null;
  contract_offpeak: number | null;
  contract_saturday: number | null;
  kw_peak: number | null;
  kw_semipeak: number | null;
  kw_offpeak: number | null;
  kw_saturday: number | null;
  subcontracted_kwh_peak: number | null;
  subcontracted_kwh_semipeak: number | null;
  subcontracted_kwh_offpeak: number | null;
  subcontracted_kwh_saturday: number | null;
  pf: number | null;
  adjustment_factor: number | null;
  kwh_per_day: number | null;
  created_at: string | null;
  status: string;
  tpc_ach: string;
  payment_deadline: string | null;
  reduce_kwh: number | null;
  time_type: string;
  day_deducted: string | null;
  day_next_deducted: string | null;
  customer_service_hotline: string;
  tpc_comapny_id: string;
  service_department: string;
  service_department_address: string;
  electronic_bill_discount_reduction: number | null;
  fee_before_tax: number | null;
  kwh_per_fuel_cost: number | null;
  current_period_carbon_emissions: number | null;
  kwh_per_recyling_fund: number | null;
  group: string;
  group_name: string;
  meter_number: string;
  multiplier: number;
  energy_saving_reward: number | null;
  kwh_per_average_price: number | null;
  price_adjustment_discount: number | null;
  late_payment_fee: number | null;
  power_outage_deduction: number | null;
}


@Component({
  selector: 'app-service-dashboard',
  standalone: true,
  imports: [ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    IconDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,
    ButtonGroupComponent,
    ChartjsComponent,
    CardFooterComponent,
    ProgressComponent,
    ProgressBarComponent,
    FormCheckInputDirective,
    FormCheckComponent,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    ModalFooterComponent,
    ThemeDirective,
    NavModule,
    ListGroupDirective,
    ListGroupItemDirective,
    InputGroupTextDirective,
  ],
  templateUrl: './service-dashboard.component.html',
  styleUrl: './service-dashboard.component.scss'
})

export class ServiceDashboardComponent {
  @ViewChild('myModal') myModal!: ModalComponent;

  validServices = ['electricity-bill-service-dashboard', 'customer-service', 'performance', 'engineering'];
    project: project;
    place_id:string;
    service_info : service = {
      service_name: '',
      service_type: '',
      service_id: ''
    }; ;
    tpc_bill_info: TpcBillInfo = {
      place_id: '',
      ebpps_url: '',
      ebpps_name: '',
      ebpps_customer_address: '',
      ebpps_bill_address: '',
      electricity_type: '',
      feeder_number: '',
      group_powerout: '',
      company_id: '',
      bill_year_month: '',
      bill_date_next: null,
      bill_date: null,
      date_readmeter: null,
      date_next_readmeter: null,
      date_bill_start: null,
      date_bill_end: null,
      day_used: null,
      min_kwh: null,
      fee_total: null,
      fee_kw: null,
      fee_kwfine: null,
      fee_kwh: null,
      fee_pf: null,
      fee_public: null,
      fee_tax: null,
      kwh_total: null,
      kwh_peak: null,
      kwh_semipeak: null,
      kwh_offpeak: null,
      kwh_saturday: null,
      contract_peak: null,
      contract_semipeak: null,
      contract_offpeak: null,
      contract_saturday: null,
      kw_peak: null,
      kw_semipeak: null,
      kw_offpeak: null,
      kw_saturday: null,
      subcontracted_kwh_peak: null,
      subcontracted_kwh_semipeak: null,
      subcontracted_kwh_offpeak: null,
      subcontracted_kwh_saturday: null,
      pf: null,
      adjustment_factor: null,
      kwh_per_day: null,
      created_at: null,
      status: '',
      tpc_ach: '',
      payment_deadline: null,
      reduce_kwh: null,
      time_type: '',
      day_deducted: null,
      day_next_deducted: null,
      customer_service_hotline: '',
      tpc_comapny_id: '',
      service_department: '',
      service_department_address: '',
      electronic_bill_discount_reduction: null,
      fee_before_tax: null,
      kwh_per_fuel_cost: null,
      current_period_carbon_emissions: null,
      kwh_per_recyling_fund: null,
      group: '',
      group_name: '',
      meter_number: '',
      multiplier: 0,
      energy_saving_reward: null,
      kwh_per_average_price: null,
      price_adjustment_discount: null,
      late_payment_fee: null,
      power_outage_deduction: null
    };
        servicedashboard: string = '';
    successMessage :string
    errorMessage :string
    project_code: string = '';
    showCard = '';
    showFeature='';
    member_list: member[] = [];
    customer_list : member[]=[];
    DetailofElectricityPlanChangeType:boolean = false;
    service_name: string = '';
    userFirstName : string;
    userLastName :string;
    eventTypeMap: { [key: string]: string } = {
      'electricity_plan_change': '用電變更',
      'meter_error': '電表異常',
    };

    public visible = false;

    features :string[] = [];

    // 事件相關變數
    searchEventText = '';
    event_list: any[] = [];

    // 圖示
    icons = {
    cilHttps,
    cilMoodBad,
    cilBarChart,
    cilMap,
    cilBolt,
    cilChartLine,
    cilGroup,
    cilListRich,
    cilSettings,
    cilEqualizer };
    electricity_plan_change_form = {
      user_name: '',
      service_id : '',
      project_code : '',
      project_name : '',
      place_id_ebpps_customer_address: '',
      company_id_name: '',
      current_usage: '',
      bill_address: '',

      electricity_type: '',
      time_type: '',
      contract_peak: 0 ,
      contract_semipeak: 0,
      contract_saturday: 0,
      contract_offpeak : 0,

      new_power_type: '',
      new_time_tariff: '',
      new_peak_capacity: null,
      new_halfpeak_capacity: null,
      new_saturday_capacity: null,
      new_contract_offpeak : null ,
      contract_note: ''
    };
    hovering = [false, false, false, false, false, false, false];


    electricity_pricing_service_feature_list = [{'feature_name':'用電變更','feature_type':'electricity_plan_change','icon':this.icons.cilEqualizer}];
    constructor(
      private route: ActivatedRoute,
      private router: Router,
      private projectService: ProjectService,
      private serviceService: ServiceService,
      private userService : UserService
    )
      {
    }
    ngOnInit(): void {
      this.route.paramMap.subscribe((params) => {
        this.project_code = params.get('project_code') || '';
        this.place_id = params.get('place_id') || '';
        this.service_info.service_id = params.get('service_id') || '';
        this.electricity_plan_change_form.service_id =  params.get('service_id') || '';
        this.get_service_info(params.get('service_id')) ;
        this.load_project_info();
        this.showFeatureCard(this.service_info.service_type);
        this.get_event_info_list(params.get('service_id'));
        this.get_tpc_bill_info();
        this.getUserName();
        this.get_all_member();
          });}
    load_project_info() {
      this.projectService.get_project_info(this.project_code).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          this.project = {
            project_code: data.data.project_code || '',
            project_name: data.data.project_name || '',
            place_id_list: data.data.place_id || [],
          };
          this.electricity_plan_change_form.project_code = data.data.project_code
          this.electricity_plan_change_form.project_name = data.data.project_name
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
    }


    get_project_member( callback: () => void) {
      this.projectService.get_project_member(this.project_code).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          for (let i = 0; i < data.member_list.length; i++) {
            this.member_list[i] = {
              name: data.member_list[i].name,
              email: data.member_list[i].email,
            };
          }
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
    }
    get_customer_member(project_code: string, callback: () => void) {
      this.projectService.get_customer_member(project_code).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          this.customer_list = [];
          for (let i = 0; i < data.member_list.length; i++) {
            this.customer_list[i] = {
              name: data.member_list[i].name,
              email: data.member_list[i].email,
            };
          }
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
    }
    get_all_member() {
      this.get_project_member(() => {
        this.get_customer_member(this.project_code,() => {
          this.member_list = [...this.member_list, ...this.customer_list];
        });
      });
    }

    get_service_info(service_id :any) {
      console.log(service_id)
      this.serviceService.get_service_info(service_id).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          this.service_info ={
            service_id :data.data.service_id || '',
            service_name : data.data.service_name || '',
            service_type : data.data.service_type || '',
          }
          console.log('info:',this.service_info)
        },
        (error) => {
        }
      );
    }

    setShowCard(input: string, ): void {
      this.showCard = input;
      this.showFeature = '';
    }
    showFeatureCard(service_type: string): void {
      this.service_name= service_type;
    }
    setShowFeature(input: string): void {
      this.showFeature = input;

    }

    // 事件相關方法
    filteredEvents() {
      const keyword = this.searchEventText?.toLowerCase().trim();
      if (!keyword) return this.event_list;

      return this.event_list.filter((event) => {
        const event_type_original = (event.event_type || '').toLowerCase();
        const event_type_translated = this.translateEventType(event_type_original).toLowerCase();
        const project_leader = (event.role.project_leader || '');
        // 尋找匹配的會員
        const matched_member = this.member_list.find(
          member => (member.name || '').toLowerCase() === keyword
        );
        const member_email = matched_member?.email?.toLowerCase() || '';
        console.log('projectleader', matched_member);
            const isMemberMatch = matched_member && project_leader.includes(member_email);
        return event_type_original.includes(keyword) ||
               event_type_translated.includes(keyword) ||
               isMemberMatch; // 只在匹配的情況下才檢查 email
      });
    }


    get_event_info_list(service_id :any): void {
      this.serviceService.get_event_info_list(service_id).subscribe(
        (response: any) => {
          this.event_list = response.data || [];
          console.log('Events response:', this.event_list);
        }
      );
    }

    // 在新分頁中開啟事件詳情頁面
    viewEventDetails(event: any): void {
      console.log('在新分頁開啟事件詳情:', event);

      // 構建完整 URL - 使用新的路由格式
      const baseUrl = window.location.origin;
      const eventId = event.event_id || event.Application_Item || '';

      const fullUrl = `${baseUrl}/project/event/${eventId}/info`;

      // 在新分頁中開啟
      window.open(fullUrl, '_blank');
    }
    get_tpc_bill_info() {this.serviceService.get_tpc_bill_info(this.place_id).subscribe(
      (data) => {
        console.log('data',data)
        this.tpc_bill_info = data.data ;
        this.electricity_plan_change_form.place_id_ebpps_customer_address = this.place_id+this.tpc_bill_info?.ebpps_customer_address|| '';
        this.electricity_plan_change_form.company_id_name = this.tpc_bill_info?.company_id+ this.tpc_bill_info?.ebpps_name|| '';
        this.electricity_plan_change_form.bill_address = this.tpc_bill_info?.ebpps_bill_address || '';
        this.electricity_plan_change_form.electricity_type = this.tpc_bill_info.electricity_type || '';
        this.electricity_plan_change_form.time_type = this.tpc_bill_info.time_type || '';
        this.electricity_plan_change_form.contract_peak = this.tpc_bill_info.contract_peak || 0;
        this.electricity_plan_change_form.contract_semipeak = this.tpc_bill_info.contract_semipeak || 0;
        this.electricity_plan_change_form.contract_saturday = this.tpc_bill_info.contract_saturday || 0;
        this.electricity_plan_change_form.contract_offpeak = this.tpc_bill_info.contract_offpeak || 0;
        localStorage.setItem('token', data.token);},
      (error) => {
      }
    );
  }
  getUserName() {
      this.userService.get_self_info().subscribe(
        (data) => {
          this.userFirstName = data.user.firstname;
          this.userLastName = data.user.lastname;
          this.userService.userFirstName = data.user.firstname;
          this.userService.userLastName = data.user.lastname;
          localStorage.setItem('token', data.token);
          this.electricity_plan_change_form.user_name = this.userFirstName + this.userLastName;
        },
        (error) => {
          this.userFirstName = '';
          this.userLastName = '';
        }
      );
  }

  toggleLiveDemo() {
    this.visible = !this.visible;
  }

  handleLiveDemoChange(event: any) {
    this.visible = event;
  }
send_request_for_electricity_plan_change(electricity_plan_change_form :any){
    this.serviceService.electricity_plan_change_request_submit(electricity_plan_change_form).subscribe(
      (response) => {
        this.successMessage = '請求提交成功';
        window.location.reload();
      },
      (error) => {
        this.errorMessage = '有欄位尚未填寫或勾選欄尚未勾選';
      }
    );
  }
  translateEventType(event_type: string): string {
    for (let key in this.eventTypeMap) {
      if (event_type.includes(key)) return this.eventTypeMap[key];
    }
    return '其他';
  }

}
