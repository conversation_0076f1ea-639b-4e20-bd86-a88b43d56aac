class Config:
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
            'pool_recycle': 3600,
            'pool_size': 5,
            'max_overflow': 10
        }
    MONGO_URI = "mongodb://localhost:27017/web"
    SECRET_KEY = open('./security_key','r').read()
    MAIL_SERVER = 'smtp.office365.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = '<EMAIL>'
    MAIL_PASSWORD = 'pls@42838254'
    REDIS_URL = 'redis://localhost:6379/0'
    MAIL_DEFAULT_SENDER = '<EMAIL>'
    SECURITY_PASSWORD_SALT = open('./security_key','r').read()
    
    
    SERIAL_LIST = [
        '0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z',
        'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z']
    ROUTE_ROLE_MAPPING = { ## This is frontend route
        '/home': ['user'],
        '/groups': ['user'],
        '/terms': ['user'],
        '/discover': ['user'],
        '/contact-us': ['user'],
        '/accessories': ['user'],
        '/project': ['project leader', 'TPC_Officer'],
        '/project/device-dashboard-edit': ['project leader'],
        '/project/device-dashboard-view': ['project leader', 'customer', 'eng'],
        '/project/object': ['project leader'],
        '/project/link': ['project leader'],
        '/project/event-detail':['project leader','TPC_Officer'],
        '/admin': ['admin'],
        '/gateway/overview': [''],
        '/gateway/setting': [''],
        '/device/view': ['admin', 'project leader', 'eng', 'warehouse'],
        '/device/history': ['admin', 'project leader', 'eng', 'warehouse'],
        '/construction': [''],
        '/customer_service': ['customer', 'admin'],
        '/engineering_tracking': [''],
        '/inventory/lan-installer': ['warehouse'],
        '/document-management': [''],
        '/data-analysis': [''],
        '/proposal': [''],
        '/mailbox': ['user'],
        '/settings': ['user'],
        '/bill-analysis':['project leader'],
        '/work-dispatch': ['project leader','admin','vendor'],
        '/admin': ['admin'],
        '/enterprise': ['user'],
        '/team': ['user'],
        '/scheduler':['user'],
        '/documentDemands': ['TPC_Officer']
    }
    ROUTE_ENTERPRISE_ROLE_MAPPING = { 
        '/team': ['member'],
        '/settings': ['admin'],
        '/dashboard': ['member'],
    }
    ROUTE_TEAM_ROLE_MAPPING = { 
        '/discover': ['member'],
        '/team': ['member'],
        '/settings': ['admin'],
        '/dashboard': ['member']
    }

    APPLICATION_INFO = [
        {
            "id": 1,
            "type": "free",
        },
        {
            "id": 2,
            "type": "licensed",
        },
        {
            "id": 3,
            "type": "paid",
        },
    ]
 
class DevelopmentConfig(Config):
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://team_pi:Pi42838254!@127.0.0.1:3306/web_test'
    SQLALCHEMY_BINDS = {
        #'196': 'mysql+pymysql://team_pi:pi42838254@*************:3306/ima_thing',
        #'197': 'mysql+pymysql://team_pi:Pi42838254!@*************:3306/ima_thing',
        #'195': 'mysql+pymysql://team_pi:pi42838254@*************:3306/ima_thing'
    }
    API_BASE_URL = 'http://localhost:5000'
    DOMAIN_NAME = 'http://localhost:4200'
    CRAWLER_REDIS_URL = 'redis://localhost:6379/0'
    DEBUG = True
    #將頭像儲存絕對位置放這，調用config，看圖像前後端儲存方法
    AVATAR_UPLOAD_FOLDER = './images/avatars'
    PJPLOTBG_UPLOAD_FOLDER = './images/pjplotbg'
    INVOICE_UPLOAD_FOLDER = './images/invoice'
    HT1_ADDRESS = '../HT1/'

class ProductionConfig(Config):
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://team_pi:Pi42838254!@*************:3306/web'
    SQLALCHEMY_BINDS = {
        '196': 'mysql+pymysql://team_pi:pi42838254@*************:3306/ima_thing',
        '197': 'mysql+pymysql://team_pi:Pi42838254!@*************:3306/ima_thing',
        '195': 'mysql+pymysql://team_pi:pi42838254@*************:3306/ima_thing',
    }
    DOMAIN_NAME = 'https://ai.ima-ems.com'
    API_BASE_URL = 'https://ai-api.ima-ems.com'
    CRAWLER_REDIS_URL = 'redis://*************:6379/1'
    AVATAR_UPLOAD_FOLDER = '/home/<USER>/ai_web/account_icon/'
    PJPLOTBG_UPLOAD_FOLDER = '/home/<USER>/ai_web/project_plot_background'
    HT1_ADDRESS = '/home/<USER>/HT1/'

class TestConfig(Config):
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://team_pi:Pi42838254!@127.0.0.1:3306/web_test'
    SQLALCHEMY_BINDS = {
        '196': 'mysql+pymysql://team_pi:pi42838254@*************:3306/ima_thing',
        '197': 'mysql+pymysql://team_pi:Pi42838254!@*************:3306/ima_thing',
        '195': 'mysql+pymysql://team_pi:pi42838254@*************:3306/ima_thing'
    }
    API_BASE_URL = 'http://*************:5000'
    CRAWLER_REDIS_URL = 'redis://*************:6379/0'
    DOMAIN_NAME = 'http://*************'
    DEBUG = True
    #將頭像儲存絕對位置放這，調用config，看圖像前後端儲存方法
    AVATAR_UPLOAD_FOLDER = './images/avatars'
    PJPLOTBG_UPLOAD_FOLDER = './images/pjplotbg'
    HT1_ADDRESS = '../HT1/'


class mqttConfig:
   broker = 'imabox-server3.ima-ems.com'
   port = 8883