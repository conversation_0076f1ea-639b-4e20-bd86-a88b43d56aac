<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div>
      <h2
        class="mb-0"
        style="cursor: pointer"
      >
        <strong>群組</strong>
      </h2>
    </div>
  </c-col>
</c-row>
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'enterprise'"
            (click)="setShowCard('enterprise')"
            class="custom-link m"
            >我的法人</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'enterprise'"
            class="custom-link active"
            >我的法人</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'team'"
            (click)="setShowCard('team')"
            class="custom-link m"
            >我的團隊</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'team'"
            class="custom-link active"
            >我的團隊</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>

<!-- 法人列表 -->
<div>
  <c-card
    class="mb-4"
    *ngIf="showCard == 'enterprise' && enterprise_list.length > 0"
  >
    <c-card-header
      class="d-flex justify-content-between align-items-center flex-wrap"
    >
    <strong>企業法人列表</strong>

      <!-- 右側：搜尋欄 -->
      <div class="input-group " style="max-width: 300px">
        <button
          class="btn bg-white mx-2 shadow-sm"
          style="
            border: none;
            width: 2.5rem;
            height: 2.5rem;
            padding: 0;
            font-size: 1.5rem;
            line-height: 1;
          "
          title="建立法人"
          (click)="create_enterprise()"
        >
          +
        </button>
        <span class="input-group-text bg-white border-end-0 shadow-sm">
          <svg cIcon name="cilSearch" size="lg" title="搜尋企業法人"></svg>
        </span>
        <input
          cInput
          type="text"
          class="form-control border-start-0 shadow-sm"
          placeholder="搜尋企業法人"
          [(ngModel)]="searchETText"
        />
      </div>
    </c-card-header>

    <!-- 法人列表 -->
    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex justify-content-between align-items-center"
        *ngFor="let enterprise of filteredEnterprise(); let i = index"
      >
        <!-- 左側：企業資訊 -->
        <div
          class="flex-grow-1"
          style="cursor: pointer"
          [routerLink]="['/enterprise', enterprise.taxId, 'dashboard']"
        >
          <div class="fw-bold">{{ enterprise.name }}</div>
          <div class="text-muted small">
            {{ enterprise.taxId }} | {{ enterprise.address }}
          </div>
        </div>


      </div>
    </div>
  </c-card>
</div>



<!-- 企業團隊列表 -->
<div>
  <c-card
    class="mb-4"
    *ngIf="showCard == 'team' && team_list.length > 0"
  >
    <c-card-header
      class="d-flex justify-content-between align-items-center flex-wrap gap-2"
    >
      <strong>企業團隊列表</strong>
      <div class="d-flex align-items-center gap-2" style="max-width: 100%">
        <!-- 搜尋欄 -->
        <button
        class="btn shadow-sm bg-white"
        style="
          border: none;
          width: 2.5rem;
          height: 2.5rem;
          padding: 0;
          font-size: 1.5rem;
          line-height: 1;
        "
        title="申請加入團隊"
        (click)="toggle_apply_team()"
      >
        +
      </button>
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg cIcon name="cilSearch" size="xl" title="搜尋企業團隊"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋企業團隊..."
            [(ngModel)]="searchTeamText"
          />
        </div>
      </div>
    </c-card-header>
    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex justify-content-between align-items-center"
        *ngFor="let team of team_list; let i = index"
      >
        <div
        class="flex-grow-1"
        style="cursor: pointer"
        [routerLink]="['/team', team.team_code, 'dashboard']"
      >
        <div class="fw-bold">{{ team.team_name }}</div>
        <div class="text-muted small">
           {{ team.team_code }} | {{ team_enterprise_name_list[i] }}
        </div>
      </div>
        <c-dropdown>
          <button
            cDropdownToggle
            [caret]="false"
            class="btn bg-white d-flex justify-content-center align-items-center"
            style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="black"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="5" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="12" cy="19" r="2" />
            </svg>
          </button>
          <ul cDropdownMenu>
            <a
              cDropdownItem
              style="cursor: pointer"

            >
              團隊選項1
            </a>
            <a
              cDropdownItem
              style="cursor: pointer"

            >
              團隊選項2
            </a>
            <a
              cDropdownItem
              style="cursor: pointer"
              class="text-danger"
            >
              離開
            </a>
          </ul>
        </c-dropdown>
      </div>
    </div>
  </c-card>
</div>

<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="showCard == 'enterprise' && enterprise_list.length == 0"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未加入企業法人</h4>
        <p class="text-body-secondary">
          按<span
            class="fw-bold text-primary"
            style="cursor: pointer"
            (click)="create_enterprise()"
            >此</span
          >創建或加入
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>


<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="showCard == 'team' && team_list.length == 0"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未加入企業團隊</h4>
        <p class="text-body-secondary">
          請進入企業法人新增企業團隊
        </p>
      </c-col>
    </c-row>
  </c-container>
</div>


<c-modal [visible]="apply_team_visible">
  <c-modal-header>
    <h5 cModalTitle>申請加入企業團隊</h5>
    <button cButtonClose (click)="toggle_apply_team()"></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mb-3">
      <label for="teamIdInput" class="form-label">團隊 ID</label>
      <input
        id="teamIdInput"
        type="text"
        class="form-control"
        placeholder="請輸入團隊 ID"
        [(ngModel)]="inputTeamId"
      />
    </div>
  </c-modal-body>

  <c-modal-footer>
    <button cButton variant="ghost" (click)="toggle_apply_team()">取消</button>
    <button
      cButton
      color="primary"
      class="text-white mx-2"
      [disabled]="inputTeamId.length != 8"
      (click)="apply_entering_team()"
    >
      確認
    </button>
  </c-modal-footer>
</c-modal>

