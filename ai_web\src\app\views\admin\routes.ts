import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'Service Management'
    },
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'user_manager',
        loadComponent: () => import('./user-manager/user-manager.component').then(m => m.UserManagerComponent),
        data: {
          title: 'User_manager'
        }
      }
    ]
  }
];


