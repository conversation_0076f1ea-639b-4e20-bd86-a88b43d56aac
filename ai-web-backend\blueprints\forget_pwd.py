from flask import request, Blueprint, jsonify, current_app
from flask_mail import Message
import json
from accessories import mail, token_store, mongo, get_serializer

forgetpwd_page = Blueprint('forget-pwd', __name__)

@forgetpwd_page.route('/forgot-password', methods=['POST'])
def forgot_password():
    def send_reset_email(to_email, reset_link):
        msg = Message("Reset Your Password", recipients=[to_email])
        msg.body = f'Click the link to reset your password: {reset_link}\nThis link will expire in 5 minutes.'
        mail.send(msg)

    data = request.json
    email = data.get('email')
    user = mongo.db.user.find_one({"email": email})
    if user:
        serializer = get_serializer()
        token = serializer.dumps(email, salt='forget-password-salt')
        token_store.set(token, json.dumps({
            'email': email,
            'used': False
        }), ex=300)
        reset_link = f"{current_app.config['DOMAIN_NAME']}/reset-pwd/{token}"

        send_reset_email(email, reset_link)
        return jsonify({'message': 'Password reset email sent.'}), 200
    else:
        return jsonify({'error': 'Email not registered.'}), 404


