import { Component, OnInit } from '@angular/core';
import { SharedModule, enterprise, user, team } from '../../../shared/shared.module';
import { SharedService } from '../../../service/shared.service';
import { EnterpriseService } from '../../../service/enterprise.service';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';

@Component({
  selector: 'app-create-team',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-team.component.html',
  styleUrl: './create-team.component.scss'
})
export class CreateTeamComponent {
  team_name: string;
  taxId: string;
  constructor(private router: Router,
    private enterpriseService: EnterpriseService,
    private sharedService: SharedService
  ){
    this.taxId = this.sharedService.getRouteId();
  }
  create_team(){
    this.enterpriseService.create_team(this.taxId, this.team_name).subscribe(
      (data) => {
        localStorage.setItem('token', data.token)
        this.router.navigate([`/enterprise/${this.taxId}/team/overview`]);
      },
      (error) => {

      }
    )
  }
}
