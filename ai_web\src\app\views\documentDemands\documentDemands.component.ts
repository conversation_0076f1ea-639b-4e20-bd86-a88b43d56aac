import { Component, OnInit } from '@angular/core';
import { CommonModule }      from '@angular/common';
import { FormsModule }       from '@angular/forms';
import { ButtonDirective }   from '@coreui/angular';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { AdminService } from '../../service/admin.service';
import { UserService } from '../../service/user.service';
import{ProjectService}from'../../service/project.service'
import { ModalModule } from '@coreui/angular';
import {
CardBodyComponent, CardComponent, CardHeaderComponent, ColComponent,
  TableModule, GridModule, InputGroupComponent, ModalBodyComponent, ModalComponent,
  ModalFooterComponent, ModalHeaderComponent, ModalTitleDirective, RowComponent,
  TextColorDirective, FormControlDirective, FormLabelDirective, FormSelectDirective,
  ButtonCloseDirective,
} from '@coreui/angular';


//import { UserManagerComponent }     from '../../views/admin/user-manager/user-manager.component'
import { DocumentDemandsService, DocumentDemandsForm } from '../../service/documentdemands.service';
import { ProgressService, ProgressData, Project } from '../../service/progress.service';
interface EventData {
  event_id: string;
  event_type: string;
  project_code: string;
  project_name: string;
  service_id: string;
  role: {
    project_leader: string;
  };
  original_type: {
    type: string;
    time: string;
    contract_peak: string;
    contract_semipeak: string;
    contract_offpeak: string;
    contract_saturday: string;
  };
  changed_type: {
    type: string;
    time: string;
    contract_peak: string;
    contract_semipeak: string;
    contract_offpeak: string;
    contract_saturday: string;
  };
  place_id_info: {
    "company_id_name": string;
    "current_usage": string;
    "place_id_ebpps_customer_address": string;
    "bill_address":string;
  };
  create_time: string;
  last_updated: string;
  place_id: string;
  stage: string;
  status: string;
}


@Component({
  selector: 'app-documentDemands',
  standalone: true,
  imports: [
    ButtonDirective, CardBodyComponent, CardComponent, CardHeaderComponent, ColComponent,
    CommonModule, FormsModule, TableModule, GridModule, ModalModule,
    InputGroupComponent, ModalBodyComponent, ModalComponent, ModalFooterComponent,
    ModalHeaderComponent, ModalTitleDirective, RowComponent, TextColorDirective,
    FormControlDirective, FormLabelDirective, FormSelectDirective,ButtonCloseDirective
  ],
  templateUrl: './documentDemands.component.html',
  styleUrls: ['./documentDemands.component.scss']
})
export class DocumentDemandsComponent implements OnInit {
  place_id = '';
  appNumber  = '';
  user_name: string;
  target_event_id : string = '';
  member: string;
  show_event_info_type :boolean = false;
  event_list: any[] = [];
  target_event:EventData = {
    event_id: '',
    event_type: '',
    project_code: '',
    project_name: '',
    service_id: '',
    role: {
      project_leader: ''
    },
    original_type: {
      type: '',
      time: '',
      contract_peak: '',
      contract_semipeak: '',
      contract_offpeak: '',
      contract_saturday: ''
    },
    changed_type: {
      type: '',
      time: '',
      contract_peak: '',
      contract_semipeak: '',
      contract_offpeak: '',
      contract_saturday: ''
    },
    place_id_info: {
      company_id_name: '',
      current_usage: '',
      place_id_ebpps_customer_address: '',
      bill_address: ''
    },
    create_time: '',
    last_updated: '',
    place_id: '',
    stage: '',
    status: ''
  };
  
  // 從後端拿到的所有階段順序
  statuses: string[] = [];
  // 取得後，直接把陣列排序好
  projects: Project[] = [];
  eventTypeMap: { [key: string]: string } = {
    'electricity_plan_change': '用電變更',
    'meter_error': '電表異常',
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService,
    private documentdemandsService: DocumentDemandsService,
    private progressSvc: ProgressService,
    private userService: UserService
    //private usermanger: UserManagerComponent
  ) {}

  ngOnInit() {
    this.get_not_acceptance_number_event_info_list()
  }
  translateEventType(event_type: string): string {
    for (let key in this.eventTypeMap) {
      if (event_type.includes(key)) return this.eventTypeMap[key];
    }
    return '其他';
  }
  get_not_acceptance_number_event_info_list(): void {
    this.documentdemandsService.get_not_acceptance_number_event_info_list().subscribe(
      (response: any) => {
        this.event_list = response.data || [];
      },
      (error) => {
        console.log('error:',error)
      }
    );
  }
  show_event_info(event_id :any){
    this.target_event_id = event_id
    this.show_event_info_type =true
    this.target_event = this.event_list.find(e => e.event_id === event_id);
    this.get_requester_info(this.target_event.role.project_leader)
  }
  close_event_info(){
    this.target_event_id = ''
    this.show_event_info_type =false
    this.target_event = {
      event_id: '',
      event_type: '',
      project_code: '',
      project_name: '',
      service_id: '',
      role: {
        project_leader: ''
      },
      original_type: {
        type: '',
        time: '',
        contract_peak: '',
        contract_semipeak: '',
        contract_offpeak: '',
        contract_saturday: ''
      },
      changed_type: {
        type: '',
        time: '',
        contract_peak: '',
        contract_semipeak: '',
        contract_offpeak: '',
        contract_saturday: ''
      },
      place_id_info: {
        company_id_name: '',
        current_usage: '',
        place_id_ebpps_customer_address: '',
        bill_address: ''
      },
      create_time: '',
      last_updated: '',
      place_id: '',
      stage: '',
      status: ''
    };
  }
  get_requester_info(email:any):void{
    this.documentdemandsService.get_requester_info(email).subscribe(      
      (response: any) => {
      this.member = response.data || '';
      console.log('member:',this.member)
    },
    (error) => {
      console.log('error:',error)
    });
  }
  take_case(event_id:any):void{
    this.documentdemandsService.take_case(event_id).subscribe(      
      (response: any) => {
        window.location.reload();
    },
    (error) => {
    });
  }

 
}