import { ProjectService } from './../../service/project.service';
import { UserService } from './../../service/user.service';
import { Component, inject } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { NgScrollbar } from 'ngx-scrollbar';
import { CommonModule } from '@angular/common';
import { ProgressComponent } from '@coreui/angular';
import {
  ContainerComponent,
  ColorModeService,
  ShadowOnScrollDirective,
  SidebarBrandComponent,
  SidebarComponent,
  SidebarHeaderComponent,
  SidebarNavComponent
} from '@coreui/angular';
import { DefaultFooterComponent, DefaultHeaderComponent } from './';
import { navItems } from './_nav';

@Component({
  selector: 'app-dashboard',
  templateUrl: './default-layout.component.html',
  styleUrls: ['./default-layout.component.scss'],
  standalone: true,
  imports: [
    SidebarComponent,
    SidebarHeaderComponent,
    SidebarBrandComponent,
    RouterLink,
    NgScrollbar,
    SidebarNavComponent,
    DefaultHeaderComponent,
    ShadowOnScrollDirective,
    ContainerComponent,
    RouterOutlet,
    DefaultFooterComponent,
    CommonModule,
    ProgressComponent
  ]
})
export class DefaultLayoutComponent {
  public navItems : any;
  public userRoles: string[] = [];
  readonly #colorModeService = inject(ColorModeService);
  readonly colorMode = this.#colorModeService.colorMode;
  constructor(private userService: UserService, private ProjectService: ProjectService) {
    this.getUserRoles();
  }
  getUserRoles() {
    this.userService.getUserRoles().subscribe(
      (data) => {
        this.userRoles = data.roles;
        localStorage.setItem('token', data.token);
        this.filterNavItems();
      },
      (error) => {
        this.userRoles = [];
      }
    );
  }

  filterNavItems() {
    this.navItems = navItems
      .filter(item => this.isRoleAllowed(item))  // 先過濾主項目
      .map(item => {
        if (item.children) {
          // 過濾每個 item 的 children
          item.children = item.children.filter(child => this.isRoleAllowed(child));
        }
        return item;
      });
  }

  isRoleAllowed(item: any): boolean {
    if (!item.roles || !Array.isArray(item.roles)) {
      return false;
    }
    // 判斷主項目或子項目的角色是否包含當前用戶的角色
    const allowed = item.roles.some((role: string) => this.userRoles.includes(role));
    return allowed;
  }


  onScrollbarUpdate($event: any) {
    // 可以根據需要處理滾動條事件
  }
}

