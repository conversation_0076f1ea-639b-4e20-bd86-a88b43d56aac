import { Component } from '@angular/core';
import { SharedModule } from '../../../shared/shared.module';
import { EnterpriseService } from './../../../service/enterprise.service';
import { ProjectService } from '../../../service/project.service';
import { SharedService } from '../../../service/shared.service';
import { UserService } from '../../../service/user.service';
import { RouterLink, RouterOutlet, ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss'
})
export class OverviewComponent {
  taxId: string = '';
  constructor(private userService: UserService, private ProjectService: ProjectService, private route: ActivatedRoute, private router: Router, private EnterpriseService: EnterpriseService,
              private sharedService: SharedService
  ) {
    this.taxId = this.sharedService.getRouteId();

  }

}
