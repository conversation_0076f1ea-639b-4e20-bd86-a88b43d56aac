import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(private http: HttpClient) { }

  save_plot(project_code: string, projectplot_name: string, data: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/save_plot`, {project_code: project_code, projectplot_name: projectplot_name, data: data}, { headers });
  }

  get_plot(project_code: string, projectplot_name: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/get_projectplot`, {project_code: project_code, projectplot_name: projectplot_name}, { headers });
  }

  get_projectplot_object(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/get_projectplot_object`, {project_code: project_code}, { headers });
  }
  get_projectplot_link(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/get_projectplot_link`, {project_code: project_code}, { headers });
  }

  get_projectplot_object_config(project_code: string, serialNo: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/get_projectplot_object_config`, {project_code: project_code, serialNo: serialNo}, { headers });
  }

  get_projectplot_link_config(project_code: string, serialNo: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/get_projectplot_link_config`, {project_code: project_code, serialNo: serialNo}, { headers });
  }


  create_projectplot_object(project_code: string, type: string, name: string, place_id: string, serialNo: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/create_projectplot_object`, {project_code: project_code,
      type: type, name: name,
      place_id: place_id, serialNo: serialNo}, { headers });
  }
  create_projectplot_link(project_code: string, serialNo: string, name: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/create_projectplot_link`, {project_code: project_code, serialNo: serialNo, name: name}, { headers });
  }
  remove_projectplot_object(project_code: string, serialNo: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/remove_projectplot_object`, {project_code: project_code, serialNo: serialNo}, { headers });
  }
  remove_projectplot_link(project_code: string, serialNo: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/remove_projectplot_link`, {project_code: project_code,
      serialNo: serialNo}, { headers });
  }
  update_projectplot_object(project_code: string, config: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/update_projectplot_object`, {project_code, config}, { headers });
  }
  update_projectplot_link(project_code: string, config: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/update_projectplot_link`, {project_code, config}, { headers });
  }
  get_column_list_by_tablename(tablename: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/get_column_list_by_tablename`, {tablename: tablename}, { headers });
  }
  upload_pjplot_background(formData: FormData): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/upload_pjplot_background`, formData, { headers });
  }
  get_pjplot_image(project_code: string, plot_name: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device_dashboard/pjplot_image`, {project_code: project_code, plot_name: plot_name}, { headers, responseType: 'blob' });
  }
}
