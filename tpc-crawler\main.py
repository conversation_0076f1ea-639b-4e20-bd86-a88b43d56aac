import time, random, sys,threading
from redis import Redis 
import config 
from tpc_crawler import tpc_crawler_threads
from legal_entity_crawler import legal_entity_crawler_thread
from tpc_case_and_invoice_crawler import tpc_case_and_invoice_crawler_thread
from tw_bank_crawler import tw_bank_crawler_service
def main():
    if len(sys.argv) > 8:
        if sys.argv[-1] == 'production':
            conf = config.prd_config()
        if sys.argv[-1] == 'test_env':
            conf = config.test_config()
    else:
        conf =config.dev_config()
    # Max threads = 10
    time.sleep(random.uniform(2, 5))
    redis_client = Redis(host=conf.redis_host, port=conf.redis_port, db=conf.redis_db,decode_responses=True)
    #透過後端訪問redis是否有需求 get redis key list
    tpc_crawler_thread = threading.Thread(target= tpc_crawler_threads,args=(redis_client,conf),name='tpc_crawler_thread',daemon=False)
    tpc_crawler_thread.start()
    legal_entity_crawler_threads = threading.Thread(target= legal_entity_crawler_thread,args=(redis_client,conf),name='legal_entity_crawler_thread',daemon=False)
    legal_entity_crawler_threads.start()

    ## thread產生應該是設置一個worker上限(10個)，然後當redis有資料的時候，根據key開啟對應的爬蟲
    ## 一個是檢查整支爬蟲程式有沒有跳異常（systemctl service），如果異常則重啟整個程式
    ## 另一個是開啟一個監聽爬蟲狀態的thread(is_alive)，確保爬蟲執行緒正常運作，理論上爬蟲跳錯不應該佔用worker，而是回報錯誤（API）

    #tpc_case_and_invoice_crawler_threads = threading.Thread(target= tpc_case_and_invoice_crawler_thread,name='tpc_case_and_invoice_crawler_thread',daemon=False)
    #tpc_case_and_invoice_crawler_threads.start()
    ## 在抓key的時候，應該是放在main裡面，同理法人、電子發票、匯款等爬蟲需求，會有一個總的loop再檢查是否有待執行的需求，如果有，則開啟一個worker執行，結束後關閉，如果有多個需求，則依序安排worker配置
    ## 在main裡面對redis未執行的queue裡面迭代，需定義讀到什麼redis的key會執行什麼function，然後把這些function定義在其他檔案(tpc_crawler.py)裡面（法人等爬蟲也在這）

    #tw_bank_crawler
    #tw_bank_crawler_service()
    #time.sleep(random.uniform(2, 5))

if __name__ == '__main__':
    main()
