<!---以下為圖-->
<c-card  class="card my-4">
  <c-card-body  class="card-body">
    <c-row  class="row">
      <c-col  sm="5"  class="col-sm-5">
        <h4  id="traffic" class="card-title mb-0">Traffic</h4>
        <div  class="small text-body-secondary">January - December 2023</div>
      </c-col>
      <c-col  sm="7" class="d-none d-md-block col-sm-7" >
        <button  cbutton="" color="primary" aria-label="Download" class="btn float-end btn-primary" type="button">
        </button>
        <form  novalidate="" class="ng-untouched ng-pristine ng-valid">
          <c-button-group  role="group" class="float-end me-3 btn-group">
            <input  formcontrolname="trafficRadio" type="radio" value="Day" id="dayRadio" class="btn-check ng-untouched ng-pristine ng-valid">
            <label  cbutton="" cformchecklabel="" color="secondary" variant="outline" for="dayRadio" class="btn form-check-label btn-outline-secondary" type="button"> Day </label>
            <input  formcontrolname="trafficRadio" type="radio" value="Month" id="radioMonth" class="btn-check ng-untouched ng-pristine ng-valid">
            <label  cbutton="" cformchecklabel="" color="secondary" variant="outline" for="radioMonth" class="btn form-check-label btn-outline-secondary" type="button"> Month </label>
            <input  formcontrolname="trafficRadio" type="radio" value="Year" id="radioYear" class="btn-check ng-untouched ng-pristine ng-valid">
            <label  cbutton="" cformchecklabel="" color="secondary" variant="outline" for="radioYear" class="btn form-check-label btn-outline-secondary" type="button"> Year </label>
          </c-button-group>
        </form>
      </c-col>
    </c-row>
    <c-chart   class="chart-wrapper" style="margin-top: 40px; height: 300px;">
      <canvas  role="img" style="display: block; box-sizing: border-box; height: 300px; width: 1238px;" height="300" id="c-chartjs-24" width="1238"> Main chart </canvas>
      <div class="chartjs-tooltip" style="opacity: 0; left: 50.015999px; top: 290.8608px;">
        <table style="margin: 0px;">
          <thead class="chartjs-tooltip-header">
            <tr class="chartjs-tooltip-header-item" style="border-width: 0px;">
              <th style="border-width: 0px;">January</th>
            </tr>
          </thead>
          <tbody class="chartjs-tooltip-body">
            <tr class="chartjs-tooltip-body-item">
              <td style="border-width: 0px;">
                <span style="background: rgb(51, 153, 255); border-width: 2px; margin-right: 10px; height: 10px; width: 10px; display: inline-block;"></span></td></tr><tr class="chartjs-tooltip-body-item">
              <td style="border-width: 0px;">
                <span style="background: rgb(27, 158, 62); border-width: 2px; margin-right: 10px; height: 10px; width: 10px; display: inline-block;"></span>
              </td>
            </tr>
            <tr class="chartjs-tooltip-body-item">
              <td style="border-width: 0px;">
                <span style="background: rgb(229, 83, 83); border-width: 2px; margin-right: 10px; height: 10px; width: 10px; display: inline-block;"></span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </c-chart>
  </c-card-body>
  <c-card-footer  class="card-footer">
    <c-row  class="row text-center mb-2 row-cols-1 row-cols-lg-4 row-cols-sm-2 row-cols-xl-5 g-4">
      <c-col   class="col">
        <div  class="text-body-secondary">Visits</div>
        <strong >29.703 Users (40%)</strong>
        <c-progress  thin="" color="success" value="40" aria-label="User visits" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" role="progressbar">
          <c-progress-bar  class="progress-bar bg-success ng-star-inserted" style="width: 40%;">
          </c-progress-bar>
        </c-progress>
      </c-col>
      <c-col   class="col">
        <div  class="text-body-secondary">Unique</div>
        <div  class="fw-semibold text-truncate">24.093 Users (20%)</div>
        <c-progress  thin="" color="info" value="20" aria-label="Unique users" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" role="progressbar">
          <c-progress-bar  class="progress-bar bg-info ng-star-inserted" style="width: 20%;">
          </c-progress-bar>
        </c-progress>
      </c-col>
      <c-col   class="col">
        <div  class="text-body-secondary">Page views</div>
        <div  class="fw-semibold text-truncate">78.706 Views (60%)</div>
        <c-progress  thin="" color="warning" value="60" aria-label="Page views" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" role="progressbar">
          <c-progress-bar  class="progress-bar bg-warning ng-star-inserted" style="width: 60%;">
          </c-progress-bar>
        </c-progress>
      </c-col>
      <c-col   class="col">
        <div  class="text-body-secondary">New Users</div>
        <div  class="fw-semibold text-truncate">22.123 Users (80%)</div>
        <c-progress  thin="" color="danger" value="80" aria-label="New users" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" role="progressbar">
          <c-progress-bar  class="progress-bar bg-danger ng-star-inserted" style="width: 80%;">
          </c-progress-bar>
        </c-progress>
      </c-col>
      <c-col  class="d-none d-xl-block col" >
        <div  class="text-body-secondary">Bounce Rate</div>
        <div  class="fw-semibold text-truncate">Average Rate (40.15%)</div>
        <c-progress  thin="" value="40" aria-label="Bounce rate" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" role="progressbar">
          <c-progress-bar  class="progress-bar ng-star-inserted" style="width: 40%;">
          </c-progress-bar>
        </c-progress>
      </c-col>
    </c-row>
  </c-card-footer>
</c-card>
<!---以下為表格-->
<c-col  xs="" _nghost-ng-c1814645994="" class="col">
  <c-card  class="card mb-4">
    <c-card-header  class="card-header">Traffic &amp; Sales</c-card-header>
    <c-card-body  class="card-body">
      <c-row  class="row">
        <c-col  md="6" xl="6" xs="12" _nghost-ng-c1814645994="" class="col-12 col-md-6 col-xl-6">
          <c-row  class="row">
            <c-col  xs="6" _nghost-ng-c1814645994="" class="col-6">
              <div  class="border-start border-start-4 border-start-info py-1 px-3 mb-3">
                <div  class="text-body-secondary text-truncate small">New Clients</div>
                <div  class="fs-5 fw-semibold">9,123</div>
              </div>
            </c-col>
            <c-col  xs="6" _nghost-ng-c1814645994="" class="col-6">
              <div  class="border-start border-start-4 border-start-danger py-1 px-3 mb-3">
                <div  class="text-body-secondary text-truncate small"> Recurring Clients </div>
                <div  class="fs-5 fw-semibold">22,643</div>
              </div>
            </c-col>
          </c-row>
          <hr  class="mt-0">
          <table cTable small>
            <tbody>
              <ng-container *ngFor="let place_id of project.place_id_list">
                <tr (click)="viewPlaceIdFeatures(place_id)" [class.selected]="selectedplaceid === place_id" class="place_id-row">
                  <td>
                    <div class="place_id_feature_preview"  style="text-align: center;">
                      <span class="hover-text"style="text-align: center; font-size: 1.3rem; font-weight: 500;" >{{place_id }}</span>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="selectedplaceid  === place_id">
                  <td colspan="4">
                    <ng-container *ngFor=  "let item of show_service_list ">
                      <div
                        class="flex-grow-1"
                        style="cursor: pointer"
                        [routerLink]="['/project',project.project_code ,selectedplaceid,item.service_id, 'servicedashboard']"
                      >
                        <div class="fw-bold">{{ item.service_name }}</div>
                        <div class="text-muted small">
                          {{ item.service_type }}
                        </div>
                      </div>
                      <hr  class="mt-0">
                    </ng-container>  
                    <div style="text-align: center; font-size: 1.3rem; font-weight: 500;" class="hover-text" (click)="toggleLiveDemo()"  >
                    <svg [cIcon]="icons.cilColorBorder"></svg>
                        新增服務
                      </div>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>          
    <small >
      <sup >
        <span  class="badge badge-pill badge-sm bg-info"></span>
      </sup>
      <span >New clients</span>
      <sup >
        <span  class="badge badge-pill badge-sm bg-danger"></span>
      </sup>
      <span >Recurring clients</span>
    </small>
</c-col>
<c-col  md="6" xl="6" xs="12" _nghost-ng-c1814645994="" class="col-12 col-md-6 col-xl-6">
  <c-row  class="row">
    <c-col  xs="6" _nghost-ng-c1814645994="" class="col-6">
      <div  class="border-start border-start-4 border-start-warning py-1 px-3 mb-3">
        <div  class="text-body-secondary text-truncate small">Page views</div>
        <div  class="fs-5 fw-semibold">78,623</div>
      </div>
    </c-col>
    <c-col  xs="6" _nghost-ng-c1814645994="" class="col-6">
      <div  class="border-start border-start-4 border-start-success py-1 px-3 mb-3">
        <div  class="text-body-secondary text-truncate small">Organic</div>
        <div  class="fs-5 fw-semibold">49,123</div>
      </div>
    </c-col>
  </c-row>
  <hr  class="mt-0">
  <div  class="progress-group mb-4">
    <div  class="progress-group-header">
      <svg  cIcon="" name="cilUser" class="icon icon-lg me-2"viewBox="0 0 512 512" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"pointer-events="none" role="img"></svg>
      <span >Male</span>
      <span  class="ms-auto font-semibold">43%</span>
      <div  class="progress-group-bars">
      <c-progress  thin="" color="warning" value="43"
            aria-label="Male users" class="progress progress-thin"
            _nghost-ng-c3774253327="" aria-valuenow="43" aria-valuemin="0"
            aria-valuemax="100" role="progressbar">
        <c-progress-bar _ngcontent-ng-c3774253327=""
                  class="progress-bar bg-warning ng-star-inserted"
                  style="width: 43%;">
        </c-progress-bar>
      </c-progress>
    </div>
    <div  class="divider d-flex justify-content-center">
      <button  cbutton="" color="transparent" size="sm"
        type="button" aria-label="Options"
        class="btn text-muted btn-link btn-sm btn-transparent">
      </button>
    </div>
  </div>
  <c-modal id="liveDemoModal" [visible]="visible" (visibleChange)="handleLiveDemoChange($event)">
    <c-modal-header>
      <h5 cModalTitle>新增服務</h5>
    </c-modal-header>
    <c-modal-body>
    <form [formGroup]="serviceForm"  style="padding: 1rem;">
    <div class="mb-3">
      <label for="serviceName" class="form-label">服務名稱</label>
      <input 
        type="text" 
        id="serviceName" 
        class="form-control" 
        formControlName="servicename" 
        placeholder="請輸入服務名稱"
      />
    </div>
    <div class="mb-3">
      <label for="serviceType" class="form-label">服務類型</label>
      <select 
        id="serviceType" 
        class="form-select" 
        formControlName="servicetype"
      >
        <option *ngFor="let item of service_type_dict | keyvalue" [value]=" item.value ">{{ item.key }}</option>
      </select>
    </div>
    </form>
  </c-modal-body>
  <c-modal-footer>
    <button (click)="toggleLiveDemo()" cButton color="secondary">
      取消此次變更
    </button>
    <button cButton color="primary"(click) = "createService()">送出變更</button>
  </c-modal-footer>
</c-modal>
