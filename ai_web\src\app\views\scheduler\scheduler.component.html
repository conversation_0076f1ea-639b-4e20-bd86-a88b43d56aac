<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'overview'"
            (click)="setShowCard('overview')"
            class="custom-link"
            >總覽</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'overview'"
            class="custom-link active"
            >總覽</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'gantt'"
            (click)="setShowCard('gantt')"
            class="custom-link"
            >甘特圖</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'gantt'"
            class="custom-link active"
            >甘特圖</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'calendar'"
            (click)="setShowCard('calendar')"
            class="custom-link"
            >行事曆</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'calendar'"
            class="custom-link active"
            >行事曆</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>


<c-card class="shadow-sm" *ngIf="showCard == 'overview'">
  <c-card-header>
      <div class="d-flex flex-wrap justify-content-between align-items-center gap-2 mb-2">
        <div class="d-flex flex-wrap align-items-center gap-2">
          <div class="input-group shadow-sm" style="max-width: 400px;">
            <span class="input-group-text bg-white border-end-0">
              <svg [cIcon]="icons.cilSearch" size="sm" title="搜尋"></svg>
            </span>
            <input
              cInput
              type="text"
              class="form-control border-start-0"
              placeholder="搜尋專案、事件名稱"
              [(ngModel)]="searchText"
              (input)="applyFilters()"
            />
          </div>
        </div>
      </div>
    </c-card-header>
  <c-card-body class="p-0">
    <div class="table-responsive">
      <table cTable hover class="mb-0 border-top-0"> 	
        <thead class="table-light">
          <tr>
            <th>
              <div class="d-flex align-items-center">
                <span class="fw-bold">專案名稱</span>
                <svg *ngIf="sortField === 'project_name'" [cIcon]="sortDirection === 'asc' ? icons.cilArrowTop : icons.cilArrowBottom" size="sm" class="ms-1"></svg>
                <span class="cursor-pointer" (click)="toggleSort('project_name')"></span>
              </div>
            </th>
            <th>
              <div class="d-flex align-items-center">
                <c-dropdown class="d-inline-flex align-items-center">
                  <a cDropdownToggle class="text-decoration-none text-dark cursor-pointer me-1">
                    <span class="fw-bold">事件名稱</span>
                    <small *ngIf="eventNameFilter !== 'all'" class="ms-1 badge bg-light text-primary">
                      {{ eventNameFilter }}
                    </small>
                  </a>
                  <ul cDropdownMenu>
                    <li><a cDropdownItem (click)="setEventNameFilter('all')">全部</a></li>
                    <li *ngFor="let eventName of eventNameList">
                      <a cDropdownItem (click)="setEventNameFilter(eventName)">{{ eventName }}</a>
                    </li>
                  </ul>
                </c-dropdown>
                <svg *ngIf="sortField === 'event_name'" [cIcon]="sortDirection === 'asc' ? icons.cilArrowTop : icons.cilArrowBottom" size="sm" class="ms-1"></svg>
                <span class="cursor-pointer" (click)="toggleSort('event_name')"></span>
              </div>
            </th>
            <th>
              <div class="d-flex align-items-center">
                <c-dropdown class="d-inline-flex align-items-center">
                  <a cDropdownToggle class="text-decoration-none text-dark cursor-pointer me-1">
                    <span class="fw-bold">專案狀態</span>
                    <small *ngIf="statusFilter !== 'all'" class="ms-1 badge bg-light text-primary">
                      {{ statusFilter }}
                    </small>
                  </a>
                  <ul cDropdownMenu>
                    <li><a cDropdownItem (click)="setStatusFilter('all')">全部</a></li>
                    <li *ngFor="let status of statusList">
                      <a cDropdownItem (click)="setStatusFilter(status)">
                        {{ status }}
                      </a>
                    </li>
                  </ul>
                </c-dropdown>
                <svg *ngIf="sortField === 'status'" [cIcon]="sortDirection === 'asc' ? icons.cilArrowTop : icons.cilArrowBottom" size="sm" class="ms-1"></svg>
                <span class="cursor-pointer" (click)="toggleSort('status')"></span>
              </div>
            </th>
            <th>
              <div class="d-flex align-items-center">
                <span class="fw-bold cursor-pointer" (click)="toggleSort('last_update')">最近更新時間</span>
                <svg *ngIf="sortField === 'last_update'" [cIcon]="sortDirection === 'asc' ? icons.cilArrowTop : icons.cilArrowBottom" size="sm" class="ms-1"></svg>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="loading">
            <td colspan="4" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">載入中...</span>
              </div>
              <p class="mt-2 mb-0">載入資料中...</p>
            </td>
          </tr>
          <tr *ngIf="!loading && filteredTasks.length === 0">
            <td colspan="4" class="text-center py-4">
              <p class="mb-0">沒有符合條件的事件</p>
            </td>
          </tr>
          <tr *ngFor="let task of getCurrentPageTasks(); let i = index" 
             class="cursor-pointer" 
             (click)="onGanttClick(task, null, '總覽列表')">
            <td>
              <div class="fw-bold">{{ task.project_name }}</div>
            </td>
            <td>
              <div>{{ getEventTypeDisplay(task) }}</div>
            </td>
            <td>
              <c-badge [class]="'badge ' + getStatusClass(task)">
                {{ getStatusDisplay(task).text }}
              </c-badge>
            </td>
            <td>
              {{ formatDate(task.last_update) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </c-card-body>
  
  <!-- 分頁控制 -->
  <c-card-footer class="d-flex justify-content-center align-items-center">
    <c-pagination aria-label="頁碼導航">
      <li cPageItem [disabled]="currentPage === 1">
        <a cPageLink (click)="pageChanged(currentPage - 1)">上一頁</a>
      </li>
      
      <!-- 第一頁 -->
      <li cPageItem [active]="currentPage === 1">
        <a cPageLink (click)="pageChanged(1)">1</a>
      </li>
      
      <!-- 前省略號 -->
      <li cPageItem *ngIf="currentPage > 3">
        <a cPageLink>...</a>
      </li>
      
      <!-- 中間頁 -->
      <ng-container *ngFor="let page of getPaginationPages()">
        <li cPageItem *ngIf="page !== 1 && page !== getTotalPages()" [active]="page === currentPage">
          <a cPageLink (click)="pageChanged(page)">{{ page }}</a>
        </li>
      </ng-container>
      
      <!-- 後省略號 -->
      <li cPageItem *ngIf="currentPage < getTotalPages() - 2">
        <a cPageLink>...</a>
      </li>
      
      <!-- 最後一頁 -->
      <li cPageItem *ngIf="getTotalPages() > 1" [active]="currentPage === getTotalPages()">
        <a cPageLink (click)="pageChanged(getTotalPages())">{{ getTotalPages() }}</a>
      </li>
      
      <li cPageItem [disabled]="currentPage === getTotalPages() || totalItems === 0">
        <a cPageLink (click)="pageChanged(currentPage + 1)">下一頁</a>
      </li>
    </c-pagination>
  </c-card-footer>
</c-card>

<c-card class="shadow-sm" *ngIf="showCard == 'calendar'">
  <c-card-header>
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h5 class="mb-0">專案行事曆</h5>
        <p class="text-muted small mb-0">
          以行事曆形式顯示所有專案排程
        </p>
      </div>
    </div>
  </c-card-header>
  <c-card-body>
    <div class="calendar-business-container">
      <full-calendar [options]="calendarOptions"></full-calendar>
    </div>
  </c-card-body>
</c-card>

<!-- 甘特圖頁面 -->
<c-card class="shadow-sm" *ngIf="showCard == 'gantt'">
  <c-card-header>
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div>
        <h5 class="mb-0">專案甘特圖</h5>
        <p class="text-muted small mb-0">專案事件進度時間軸視圖</p>
      </div>
      
      <!-- 時間範圍滑桿 -->
      <div class="d-flex align-items-center gap-4" style="min-width: 800px; width: 100%; max-width: 1200px;">
        <div class="d-flex align-items-center gap-2">
          <svg [cIcon]="icons.cilCalendar" size="sm" class="text-muted"></svg>
          <small class="text-muted fw-semibold">時間範圍</small>
        </div>
        
        <!-- 滑桿容器 -->
        <div class="flex-grow-1 position-relative">
          <div class="d-flex align-items-center gap-2">
            <!-- 開始日期標籤 -->
            <small class="text-muted" style="min-width: 80px;">
              {{ sliderCalc.formatDate(sliderCalc.startDate) }}
            </small>
            
            <!-- 滑桿 -->
            <div class="flex-grow-1 position-relative">
              <input 
                type="range" 
                class="form-range gantt-time-slider"
                [min]="0" 
                [max]="dayOptions.length - 1" 
                [value]="selectedDayIndex"
                (input)="onSliderChange($event)"
                style="width: 100%;">
              
              <!-- 滑桿刻度標籤 -->
              <div class="slider-ticks d-flex justify-content-between position-absolute w-100" style="top: 25px; font-size: 10px;">
                <small *ngFor="let day of dayOptions; let i = index" 
                       class="text-muted tick-label"
                       [class.text-primary]="i === selectedDayIndex"
                       [class.fw-bold]="i === selectedDayIndex">
                  <span *ngIf="day < 0">過去{{Math.abs(day)}}天</span>
                  <span *ngIf="day >= 0">{{day}}天</span>
                </small>
              </div>
            </div>
            
            <!-- 結束日期標籤 -->
            <small class="text-muted" style="min-width: 80px;">
              {{ sliderCalc.formatDate(sliderCalc.endDate) }}
            </small>
          </div>
        </div>
        
        <!-- 日期範圍顯示 - 移到滑桿區域外 -->
        <div class="text-center mt-3">
          <small class="text-success fw-semibold px-3 py-1 rounded" style="background-color: rgba(25, 135, 84, 0.1);">
            {{ sliderCalc.selectedText }}
          </small>
        </div>
      </div>
    </div>
  </c-card-header>
  
  <c-card-body class="p-0">
    <!-- 甘特圖容器 -->
    <div class="gantt-container" [style.height.px]="ganttHeight">
      <!-- 時間軸頭部 -->
      <div class="gantt-header">
        <div class="gantt-task-column">
          <div class="gantt-task-header">事件 </div>
        </div>
        <div class="gantt-timeline-header" 
             [style.width.px]="timelineWidth"
             (scroll)="onHeaderScroll($event)"
             #timelineHeader>
          <div class="gantt-time-scale" [style.width.px]="timelineWidth">
            <div *ngFor="let period of timePeriods; let i = index"
                 class="gantt-time-period"
                 [style.width.px]="periodWidth"
                 [ngClass]="period.cssClass">
              {{ period.label }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 甘特圖主體 -->
      <div class="gantt-body" 
           [style.height.px]="totalRowsHeight">
        
        <!-- 事件列表區域 -->
        <div class="gantt-task-list">
          <div *ngFor="let event of ganttEvents; let eventIndex = index"
               class="gantt-event-group">
            
            <!-- 事件標題 -->
            <div class="gantt-event-row" 
                 [style.height.px]="rowHeight"
                 (click)="onGanttClick(event, null, '事件標題')">
              <div class="gantt-event-title">
                <strong>{{ event.name }}</strong>
                <span class="text-muted ms-2">({{ event.steps.length }} 步驟)</span>
              </div>
            </div>
            
            <!-- 步驟列表 -->
            <div class="gantt-steps">
              <div *ngFor="let step of event.steps; let stepIndex = index"
                   class="gantt-step-row"
                   [style.height.px]="rowHeight"
                   (click)="onGanttClick(event, step, '步驟行')">
                <div class="gantt-step-title">
                  <span class="ms-4">{{ step.name }}</span>
                  <small class="text-muted ms-2">{{ step.status }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 時間軸滾動容器 -->
        <div class="gantt-timeline-container" 
             (scroll)="onTimelineScroll($event)"
             [style.height.px]="totalRowsHeight"
             #timelineContainer>
          <!-- 時間軸區域 -->
          <div class="gantt-timeline" 
               [style.width.px]="timelineWidth"
               [style.height.px]="totalRowsHeight">
          
            <!-- 網格線 -->
            <div class="gantt-grid">
              <div *ngFor="let period of timePeriods; let i = index"
                   class="gantt-grid-line"
                   [style.left.px]="i * periodWidth"
                   [style.height.px]="totalRowsHeight">
              </div>
            </div>
            
            <!-- 步驟橫條 -->
            <div class="gantt-bars">
              <div *ngFor="let event of ganttEvents; let eventIndex = index"
                   class="gantt-event-bars">
                
                <!-- 步驟橫條 -->
                <div class="gantt-step-bars">
                  <div *ngFor="let step of event.steps; let stepIndex = index"
                       class="gantt-step-bar-container"
                       [style.top.px]="ganttCalc.stepTop(eventIndex, stepIndex)">
                    
                    <!-- 步驟橫條 -->
                    <div class="gantt-progress-bar"
                         [style.left.px]="ganttCalc.timePos(step.start_time)"
                         [style.width.px]="ganttCalc.timeWidth(step.start_time, step.end_time)"
                         [ngClass]="'status-' + step.status"
                         (click)="onGanttClick(event, step, '橫條')">
                      
                      <!-- 步驟名稱顯示 -->
                      <div class="gantt-step-name">
                        <span class="step-name-text">{{ step.name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </c-card-body>
  
  <!-- 甘特圖控制面板 -->
  <c-card-footer class="bg-light">
    <div class="d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center gap-3">
        <small class="text-muted">
          顯示 {{ ganttEvents.length }} 個事件，
          共 {{ totalStepsCount }} 個節點
        </small>
      </div>
      
      <div class="d-flex align-items-center gap-2">
        <small class="text-muted">縮放: {{ Math.round(zoomLevel * 100) }}%</small>
        <button class="btn btn-outline-secondary btn-sm" (click)="resetView()">
          重置視圖
        </button>
      </div>
    </div>
  </c-card-footer>
</c-card>

