<div class="container py-5 px-3 max-w-screen-md mx-auto">
  <div class="card mb-4 shadow rounded-3">
    <!-- Carousel Images 在上方 -->
    <div class="w-100" >
      <c-carousel [dark]="true" *ngIf="app?.images?.length">
  <c-carousel-indicators></c-carousel-indicators>
  <c-carousel-inner>
    <c-carousel-item *ngFor="let img of app?.images">
      <div class="ratio ratio-16x9 rounded-top-3">
  <img
    [src]="img"
    [alt]="app?.title"
    class="d-block w-100 h-100"
    style="object-fit:cover;"
  />
</div>
    </c-carousel-item>
  </c-carousel-inner>
  <c-carousel-control caption="Previous" direction="prev"></c-carousel-control>
  <c-carousel-control caption="Next" direction="next"></c-carousel-control>
</c-carousel>

    </div>

    <!-- 文字內容全部在下方 -->
    <div class="px-4 py-3">
      <h2 class="fw-bold mb-1 text-center">{{ app?.title }}</h2>
      <div class="text-muted mb-1 text-center">{{ app?.subtitle }}</div>
      <div class="text-secondary small mb-2 text-center">{{ app?.category?.join(', ') }}</div>
      <p class="mb-3 text-center" style="font-size: 1.05rem; line-height: 1.7;">
        {{ app?.description }}
      </p>
      <!-- Rating -->
      <div class="mb-2">
        <div class="d-flex align-items-center justify-content-center gap-2 mb-2 flex-wrap">
          <ng-container *ngFor="let star of [1,2,3,4,5]">
            <svg
              width="24"
              height="24"
              [attr.fill]="star <= getRoundedRating() ? '#FFD700' : '#eee'"
              style="margin-right:2px;"
            >
              <polygon points="13,2 16,10 25,10 18,15 20,23 13,18 6,23 8,15 1,10 10,10"/>
            </svg>
          </ng-container>
          <span class="ms-2 small text-secondary">
            ({{ app?.rating_count || 0 }} 則評價, 平均 {{ app?.rating_avg || 0 }}/5)
          </span>
        </div>
        <!-- Rating 分佈 -->
        <div class="mt-2">
          <div *ngFor="let dist of app?.rating_dist || []; let i = index" class="d-flex align-items-center justify-content-center small">
            <span class="me-1" style="width: 16px;">{{ 5 - i }}</span>
            <div class="progress flex-grow-1 me-2" style="height:6px; max-width: 120px;">
              <div
                class="progress-bar"
                [style.width]="dist + '%'"
                [ngClass]="{
                  'bg-warning': 5-i >= 4,
                  'bg-info': 5-i == 3,
                  'bg-secondary': 5-i <= 2
                }"
              ></div>
            </div>
            <span>{{ dist }}%</span>
          </div>
        </div>
      </div>
      <!-- 取得應用按鈕（分佈下方，置中、寬度較小） -->
      <div class="d-flex justify-content-center mt-3">
        <button cButton variant="outline"
          [ngClass]="
            app?.type === 'free' ? 'free-btn' :
            app?.type === 'paid' && hasBoundPaymentAccount ? 'paid-btn' :
            app?.type === 'paid' && !hasBoundPaymentAccount ? 'disabled-btn' :
            app?.type === 'licensed' && !hasBoundPayoutAccount ? 'disabled-btn' :
            'licensed-btn'
          "
          [disabled]="
            (app?.type === 'paid' && !hasBoundPaymentAccount) ||
            (app?.type === 'licensed' && !hasBoundPayoutAccount)
          "
          (click)="getApp()"
        >
          {{
            app?.type === 'free' ? '取得' :
            app?.type === 'paid'
              ? hasBoundPaymentAccount ? '取得' : '設定企業付款帳戶後取得' :
            app?.type === 'licensed'
              ? hasBoundPayoutAccount ? '取得' : '設定企業收款帳戶後取得' :
            ''
          }}
        </button>
      </div>
    </div>
    <!-- 留言與評論區一樣放下方，保持置中與留白 -->
    <div class="mx-auto mb-4" style="max-width:700px;">
      <form (ngSubmit)="submitComment()" [formGroup]="commentForm">
        <label class="form-label fw-semibold text-center w-100">留下評論與評分</label>
        <div class="mb-2 text-center">
          <ng-container *ngFor="let star of [1,2,3,4,5]">
            <svg
              width="26"
              height="26"
              [attr.fill]="star <= (hoverRating || commentRating) ? '#FFD700' : '#eee'"
              style="cursor:pointer"
              (click)="commentRating = star"
              (mouseenter)="hoverRating = star"
              (mouseleave)="hoverRating = 0"
            >
              <polygon points="13,2 16,10 25,10 18,15 20,23 13,18 6,23 8,15 1,10 10,10"/>
            </svg>
          </ng-container>
          <span *ngIf="commentRating" class="ms-2 text-warning">{{ commentRating }} 星</span>
        </div>
        <div class="d-flex justify-content-center">
          <textarea
            formControlName="comment"
            rows="2"
            class="form-control mb-2"
            style="max-width:700px; min-width:300px; width:80%;"
            placeholder="請分享你的想法"
          ></textarea>
        </div>
        <div class="d-flex justify-content-center">
          <button cButton variant="outline" type="submit" class="primary"
            [disabled]="commentForm.invalid || !commentRating">
            送出
          </button>
        </div>
      </form>
    </div>

    <!-- 使用者評論區（標題、評論卡片、無評論訊息都一起包在 card 內） -->
    <div class="px-4 pb-4">
      <h6 class="fw-semibold mb-3 text-center">使用者評論</h6>
      <c-row class="g-4 px-3" *ngIf="app?.comments?.length; else noComment">
        <c-col
          *ngFor="let c of app?.comments || []"
          [ngClass]="app?.comments?.length === 1 ? 'col-12' : 'col-12 col-md-6 col-lg-4'"
        >
          <c-card class="h-100 shadow-sm border-0">
            <c-card-body class="d-flex flex-column align-items-center justify-content-center">
              <div class="d-flex align-items-center mb-2 flex-wrap justify-content-center">
                <span class="fw-semibold me-2">{{ c.user }}</span>
                <ng-container *ngIf="c.rating">
                  <span class="d-inline-flex align-items-center ms-1" style="gap:2px;">
                    <ng-container *ngFor="let star of [1,2,3,4,5]">
                      <svg
                        width="20"
                        height="20"
                        [attr.fill]="star <= c.rating ? '#FFD700' : '#eee'"
                        style="vertical-align: middle;"
                      >
                        <polygon points="13,2 16,10 25,10 18,15 20,23 13,18 6,23 8,15 1,10 10,10"/>
                      </svg>
                    </ng-container>
                  </span>
                </ng-container>
              </div>
              <div class="text-secondary small mb-2">{{ c.date }}</div>
              <div style="white-space: pre-line; text-align:center;">{{ c.text }}</div>
            </c-card-body>
          </c-card>
        </c-col>
      </c-row>
      <ng-template #noComment>
        <div class="text-secondary small py-5 text-center">
          目前尚無評論，歡迎成為第一位留言者！
        </div>
      </ng-template>
    </div>

  </div>
</div>
<c-modal [(visible)]="showTermsModal" backdrop="static" keyboard="false">
  <c-modal-header>
    <h5 class="modal-title">使用條款確認</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mb-3" style="max-height:250px; overflow:auto;">
      <h6 class="fw-bold">應用使用條款</h6>
      <div class="text-secondary small" [innerText]="app?.terms || '未提供使用條款'"></div>
    </div>
    <div class="form-check mb-2">
      <input
        class="form-check-input"
        type="checkbox"
        id="acceptTerms"
        [(ngModel)]="termsAccepted"
      />
      <label class="form-check-label" for="acceptTerms">
        我已閱讀並同意使用條款
      </label>
    </div>
  </c-modal-body>

  <c-modal-footer>
    <button cButton color="secondary" variant="ghost" (click)="closeModal()">取消</button>
    <button cButton color="primary" class="text-white"
      [disabled]="!termsAccepted"
      (click)="confirmAppActivation()"
    >
      確認開通
    </button>
  </c-modal-footer>
</c-modal>
