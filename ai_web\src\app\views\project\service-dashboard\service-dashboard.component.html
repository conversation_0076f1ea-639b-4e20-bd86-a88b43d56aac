<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div *ngIf="project">
      <h2
        class="mb-0"
        [routerLink]="['/project', project_code]"
        style="cursor: pointer"
      >
        <strong>服務儀表板 - {{service_info.service_name}}</strong>
        <strong></strong>
      </h2>
    </div>
  </c-col>
</c-row>
<c-modal  id="liveDemoModal" [visible]="DetailofElectricityPlanChangeType" (close)="DetailofElectricityPlanChangeType = false"
>
  <c-modal-header>
    <h5 cModalTitle>電費變更申請內容</h5>
    <button  cButtonClose (click) = "DetailofElectricityPlanChangeType = false"></button>
  </c-modal-header>
  <c-modal-body><div class="card mb-4">
    <div class="card-header">電費變更申請內容</div>
    <div class="card-body">
      <!-- 基本資料 -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">需求人</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.user_name || '—' }}
          </div>
        </div>
        <div class="col-md-6">
          <label class="form-label">電費用電地址</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.place_id_ebpps_customer_address  || '—' }}
          </div>
        </div>
      </div>
  
      <!-- 統編與戶名 -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">統編戶名</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.company_id_name || '—' }}
          </div>
        </div>
        <div class="col-md-6">
          <label class="form-label">通訊地址</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.bill_address || '—' }}
          </div>
        </div>
      </div>
  
      <!-- 原本用電資料 -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">原用電種類</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.electricity_type || '—' }}
          </div>
        </div>
        <div class="col-md-6">
          <label class="form-label">原時間電價</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.time_type || '—' }}
          </div>
        </div>
      </div>
  
      <!-- 原契約容量 -->
      <div class="row mb-3">
        <div class="col-md-4">
          <label class="form-label">原尖峰契約容量</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.contract_peak || '—' }}
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label">原半尖峰契約容量</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.contract_semipeak || '—' }}
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label">原週六契約容量</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.contract_saturday || '—' }}
          </div>
        </div>
      </div>
  
      <!-- 欲變更內容 -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">欲變更用電種類</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.new_power_type || '—' }}
          </div>
        </div>
        <div class="col-md-6">
          <label class="form-label">欲變更時間電價</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.new_time_tariff || '—' }}
          </div>
        </div>
      </div>
  
      <div class="row mb-3">
        <div class="col-md-4">
          <label class="form-label">欲變更尖峰契約容量</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.new_peak_capacity || '—' }}
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label">欲變更半尖峰契約容量</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.new_halfpeak_capacity || '—' }}
          </div>
        </div>
        <div class="col-md-4">
          <label class="form-label">欲變更週六契約容量</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.new_saturday_capacity || '—' }}
          </div>
        </div>
      </div>
  
      <!-- 備註 -->
      <div class="row mb-3">
        <div class="col-md-12">
          <label class="form-label">契約移入移出說明</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ electricity_plan_change_form.contract_note || '—' }}
          </div>
        </div>
      </div>
    </div>
  </div>
  </c-modal-body>
  <c-modal-footer>
    <button  cButton color="secondary">
      Close
    </button>
    <button cButton color="primary" (click) = 'send_request_for_electricity_plan_change(electricity_plan_change_form)'>送出請求</button>
  </c-modal-footer>
</c-modal>
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'statistics'"
            (click)="setShowCard('statistics')"
            class="custom-link m"
            >統計</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'statistics'"
            class="custom-link active"
            >統計</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'managements'"
            (click)="setShowCard('managements')"
            class="custom-link m"
            >管理</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'managements'"
            (click)="setShowCard('managements')"
            class="custom-link active"
            >管理</a
          >
        </c-nav-item>
        <span class="nav-divider">|</span>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'event'"
            (click)="setShowCard('event')"
            class="custom-link"
            >事件</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'event'"
            class="custom-link active"
            >事件</a
          >
        </c-nav-item>
        <c-nav-item>
            <a
              cNavLink
              *ngIf="showCard != 'settings'"
              (click)="setShowCard('settings')"
              class="custom-link"
              >設定</a
            >
            <a
              [active]="true"
              cNavLink
              *ngIf="showCard == 'settings'"
              class="custom-link active"
              >設定</a
            >
          </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>

<!---統計頁面-->
<c-card  class="card my-4" *ngIf="showCard == 'statistics' ">
    <c-card-body  class="card-body">
        <c-row  class="row">
        <c-col  sm="5"  class="col-sm-5">
            <h4  id="traffic" class="card-title mb-0">Traffic</h4>
            <div  class="small text-body-secondary">January - December 2023</div>
        </c-col>
        <c-col  sm="7" class="d-none d-md-block col-sm-7" >
            <button  cbutton="" color="primary" aria-label="Download" class="btn float-end btn-primary" type="button">
            </button>
            <form  novalidate="" class="ng-untouched ng-pristine ng-valid">
            <c-button-group  role="group" class="float-end me-3 btn-group">
                <input  formcontrolname="trafficRadio" type="radio" value="Day" id="dayRadio" class="btn-check ng-untouched ng-pristine ng-valid">
                <label  cbutton="" cformchecklabel="" color="secondary" variant="outline" for="dayRadio" class="btn form-check-label btn-outline-secondary" type="button"> Day </label>
                <input  formcontrolname="trafficRadio" type="radio" value="Month" id="radioMonth" class="btn-check ng-untouched ng-pristine ng-valid">
                <label  cbutton="" cformchecklabel="" color="secondary" variant="outline" for="radioMonth" class="btn form-check-label btn-outline-secondary" type="button"> Month </label>
                <input  formcontrolname="trafficRadio" type="radio" value="Year" id="radioYear" class="btn-check ng-untouched ng-pristine ng-valid">
                <label  cbutton="" cformchecklabel="" color="secondary" variant="outline" for="radioYear" class="btn form-check-label btn-outline-secondary" type="button"> Year </label>
            </c-button-group>
            </form>
        </c-col>
        </c-row>
        <c-chart   class="chart-wrapper" style="margin-top: 40px; height: 300px;">
        <canvas  role="img" style="display: block; box-sizing: border-box; height: 300px; width: 1238px;" height="300" id="c-chartjs-24" width="1238"> Main chart </canvas>
        <div class="chartjs-tooltip" style="opacity: 0; left: 50.015999px; top: 290.8608px;">
            <table style="margin: 0px;">
            <thead class="chartjs-tooltip-header">
                <tr class="chartjs-tooltip-header-item" style="border-width: 0px;">
                <th style="border-width: 0px;">January</th>
                </tr>
            </thead>
            <tbody class="chartjs-tooltip-body">
                <tr class="chartjs-tooltip-body-item">
                <td style="border-width: 0px;">
                    <span style="background: rgb(51, 153, 255); border-width: 2px; margin-right: 10px; height: 10px; width: 10px; display: inline-block;"></span></td></tr><tr class="chartjs-tooltip-body-item">
                <td style="border-width: 0px;">
                    <span style="background: rgb(27, 158, 62); border-width: 2px; margin-right: 10px; height: 10px; width: 10px; display: inline-block;"></span>
                </td>
                </tr>
                <tr class="chartjs-tooltip-body-item">
                <td style="border-width: 0px;">
                    <span style="background: rgb(229, 83, 83); border-width: 2px; margin-right: 10px; height: 10px; width: 10px; display: inline-block;"></span>
                </td>
                </tr>
            </tbody>
            </table>
        </div>
        </c-chart>
    </c-card-body>
    <c-card-footer  class="card-footer">
        <c-row  class="row text-center mb-2 row-cols-1 row-cols-lg-4 row-cols-sm-2 row-cols-xl-5 g-4">
        <c-col   class="col">
            <div  class="text-body-secondary">Visits</div>
            <strong >29.703 Users (40%)</strong>
            <c-progress  thin="" color="success" value="40" aria-label="User visits" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" role="progressbar">
            <c-progress-bar  class="progress-bar bg-success ng-star-inserted" style="width: 40%;">
            </c-progress-bar>
            </c-progress>
        </c-col>
        <c-col   class="col">
            <div  class="text-body-secondary">Unique</div>
            <div  class="fw-semibold text-truncate">24.093 Users (20%)</div>
            <c-progress  thin="" color="info" value="20" aria-label="Unique users" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" role="progressbar">
            <c-progress-bar  class="progress-bar bg-info ng-star-inserted" style="width: 20%;">
            </c-progress-bar>
            </c-progress>
        </c-col>
        <c-col   class="col">
            <div  class="text-body-secondary">Page views</div>
            <div  class="fw-semibold text-truncate">78.706 Views (60%)</div>
            <c-progress  thin="" color="warning" value="60" aria-label="Page views" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" role="progressbar">
            <c-progress-bar  class="progress-bar bg-warning ng-star-inserted" style="width: 60%;">
            </c-progress-bar>
            </c-progress>
        </c-col>
        <c-col   class="col">
            <div  class="text-body-secondary">New Users</div>
            <div  class="fw-semibold text-truncate">22.123 Users (80%)</div>
            <c-progress  thin="" color="danger" value="80" aria-label="New users" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" role="progressbar">
            <c-progress-bar  class="progress-bar bg-danger ng-star-inserted" style="width: 80%;">
            </c-progress-bar>
            </c-progress>
        </c-col>
        <c-col  class="d-none d-xl-block col" >
            <div  class="text-body-secondary">Bounce Rate</div>
            <div  class="fw-semibold text-truncate">Average Rate (40.15%)</div>
            <c-progress  thin="" value="40" aria-label="Bounce rate" class="progress mt-2 progress-thin" _nghost-ng-c3774253327="" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" role="progressbar">
            <c-progress-bar  class="progress-bar ng-star-inserted" style="width: 40%;">
            </c-progress-bar>
            </c-progress>
        </c-col>
        </c-row>
    </c-card-footer>
</c-card>
<!---管理頁面-->
<ng-container *ngIf="showCard === 'managements' && showFeature === ''">
    <c-row class="g-4 px-3">
      <ng-container *ngFor="let feature of electricity_pricing_service_feature_list">
        <c-row class="g-4 px-3">
          <c-col xs="12" md="6" lg="4">
            <c-card
              class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
              style="cursor: pointer; min-height: 140px; border-color: #ca4a00; color: #ca4a00; transition: transform 0.2s ease, box-shadow 0.2s ease;"
              (click)="setShowFeature(feature.feature_type)"
              (mouseenter)="hovering[0] = true"
              (mouseleave)="hovering[0] = false"
              [ngStyle]="hovering[0] ? {
                transform: 'scale(1.03)',
                boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
              } : {}"
            >
              <div>
                <svg [cIcon]= "feature.icon" style="width: 2.5rem; height: 2.5rem;"></svg>
                <p class="mt-2 mb-0 fw-bold small">{{feature.feature_name}}</p>
              </div>
            </c-card>
          </c-col>
        </c-row>
      </ng-container>
  </c-row>
</ng-container>

<!-- 設定頁面 -->
<c-card class="mb-4" *ngIf="showCard == 'settings'">
  <c-card-header>
    <h6 class="mb-0 fw-bold small">服務設定</h6>
  </c-card-header>
  <c-card-body>
    <div class="text-muted text-center py-5">
      <svg
        [cIcon]="icons.cilHttps"
        width="3rem"
        height="3rem"
        class="mb-3 text-primary"
      ></svg>
      <h5 class="mb-2">設定功能開發中</h5>
      <p class="text-body-secondary">服務設定功能將在未來版本中推出</p>
    </div>
  </c-card-body>
</c-card>

<ng-container *ngIf="showFeature == 'electricity_plan_change'">
<!-- 🔹 基本資料區塊 -->
<form  >

  <!-- 🔹 基本資料 -->
  <div class="card mb-4">
    <div class="card-header">基本資料</div>
    <div class="card-body">
      <div class="row mb-3">
        <div class="col-md-12 text-center">
          <label class="form-label">需求人</label>
          <div class="form-control" style="background-color: #f9f9f9;">
            {{ userFirstName || '—' }}{{ userLastName || '—' }}
          </div>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">電費用電地址 <span class="text-danger">*</span></label>
          <input type="text" class="form-control" name="place_id_ebpps_customer_address" [(ngModel)]="electricity_plan_change_form.place_id_ebpps_customer_address"[placeholder]=  "(place_id || '') + (tpc_bill_info.ebpps_customer_address || '') || '—'">
        </div>
        <div class="col-md-6">
          <label class="form-label">統編戶名</label>

          <input type="text" class="form-control" name="company_id_name" [(ngModel)]="electricity_plan_change_form.company_id_name"[placeholder]=  "(tpc_bill_info.company_id || '') + (tpc_bill_info.ebpps_name || '') || '—'">
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">現場用途</label>
          <input type="text" class="form-control" name="current_usage" [(ngModel)]="electricity_plan_change_form.current_usage">
        </div>
        <div class="col-md-6">
          <label class="form-label">通訊地址</label>
          <input type="text" class="form-control" name="bill_address" [(ngModel)]="electricity_plan_change_form.bill_address"[placeholder]=  "tpc_bill_info.ebpps_bill_address || '—' " >
        </div>
      </div>
    </div>
  </div>

  <!-- 🔹 原本內容 -->
  <div class="card mb-4">
    <div class="card-header">原本內容</div>
    <div class="card-body">
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">原用電種類</label>
          <input type="text" class="form-control" name="electricity_type" [(ngModel)]="electricity_plan_change_form.electricity_type"[placeholder]=  "tpc_bill_info.electricity_type || '—' " >
        </div>
        <div class="col-md-6">
          <label class="form-label">原時間電價</label>
          <input type="text" class="form-control" name="time_type" [(ngModel)]="electricity_plan_change_form.time_type"[placeholder]=  "tpc_bill_info.time_type || '—' " >
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">原尖峰契約容量</label>
          <input type="text" class="form-control" name="contract_peak" [(ngModel)]="electricity_plan_change_form.contract_peak"[placeholder]=  "tpc_bill_info.contract_peak || '—' " >
        </div>
        <div class="col-md-6">
          <label class="form-label">原半尖峰契約容量</label>
          <input type="text" class="form-control" name="contract_semipeak" [(ngModel)]="electricity_plan_change_form.contract_semipeak"[placeholder]=  "tpc_bill_info.contract_semipeak || '—' " >
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">原週六契約容量</label>
          <input type="text" class="form-control" name="contract_saturday" [(ngModel)]="electricity_plan_change_form.contract_saturday"[placeholder]=  "tpc_bill_info.contract_saturday || '—' " >
        </div>
        <div class="col-md-6">
          <label class="form-label">原離峰契約容量</label>
          <input type="text" class="form-control" name="contract_offpeak" [(ngModel)]="electricity_plan_change_form.contract_offpeak" [placeholder]="tpc_bill_info.contract_offpeak || '—'" />
        </div>
      </div>
    </div>
  </div>

  <!-- 🔹 調整內容 -->
  <div class="card mb-4">
    <div class="card-header">調整內容</div>
    <div class="card-body">
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">欲變更用電種類</label>
          <select class="form-select" name="new_power_type" [(ngModel)]="electricity_plan_change_form.new_power_type">
            <option value="" disabled selected>請選擇</option>
            <option value="表燈營業">表燈營業</option>
            <option value="表燈非營業">表燈非營業</option>
            <option value="表燈時間">表燈時間</option>
            <option value="簡易住宿">簡易住宿</option>
            <option value="低壓電">低壓電</option>
            <option value="高壓">高壓</option>
            <option value="特高壓">特高壓</option>
          </select>
        </div>
        <div class="col-md-6">
          <label class="form-label">欲變更時間電價</label>
          <select class="form-select" name="new_time_tariff" [(ngModel)]="electricity_plan_change_form.new_time_tariff">
            <option value="" disabled selected>請選擇</option>
            <option value="批次生產">批次生產</option>
            <option value="三段式">三段式</option>
            <option value="二段式">二段式</option>
            <option value="非時間">非時間</option>
          </select>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">欲變更尖峰契約容量</label>
          <input type="number" class="form-control" name="new_peak_capacity" [(ngModel)]="electricity_plan_change_form.new_peak_capacity">
        </div>
        <div class="col-md-6">
          <label class="form-label">欲變更半尖峰契約容量</label>
          <input type="number" class="form-control" name="new_halfpeak_capacity" [(ngModel)]="electricity_plan_change_form.new_halfpeak_capacity">
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">欲變更週六契約容量</label>
          <input type="number" class="form-control" name="new_saturday_capacity" [(ngModel)]="electricity_plan_change_form.new_saturday_capacity">
        </div>
        <div class="col-md-6">
          <label class="form-label">欲變更離峰契約容量</label>
          <input type="number" class="form-control" name="new_contract_offpeak" [(ngModel)]="electricity_plan_change_form.new_contract_offpeak">
        </div>
      </div>
    </div>
    <div class="col-md-12 text-center mb-4">
      <label class="form-label">契約移入移出說明</label>
      <input type="text" class="form-control" name="contract_note" [(ngModel)]="electricity_plan_change_form.contract_note">
    </div>
  </div>
</form>
<div class="text-end">
  <button  class="btn btn-primary btn-lg" (click) = "DetailofElectricityPlanChangeType = true">提出變更</button>
</div>
</ng-container>

<!-- 事件列表 -->
<div>
  <c-card
    class="mb-4"
    *ngIf="showCard == 'event'"
  >
    <c-card-header
      class="d-flex justify-content-between align-items-center flex-wrap gap-2"
    >
      <strong>事件列表</strong>
      <div class="d-flex align-items-center gap-2" style="max-width: 100%">
        <!-- 搜尋欄 -->
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg cIcon name="cilSearch" size="xl" title="搜尋事件"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋事件..."
            [(ngModel)]="searchEventText"
          />
        </div>
      </div>
    </c-card-header>
    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex justify-content-between align-items-center"
        *ngFor="let event of filteredEvents(); let i = index"
      >
        <div
        class="flex-grow-1"
        style="cursor: pointer"
        (click)="viewEventDetails(event)"
      >
        <div class="fw-bold">{{ project.project_name }}</div>
        <div class="text-muted small">
           {{ place_id  }} | {{ translateEventType(event.event_type) }}
        </div>
      </div>
        <c-dropdown>
          <button
            cDropdownToggle
            [caret]="false"
            class="btn bg-white d-flex justify-content-center align-items-center"
            style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="black"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="5" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="12" cy="19" r="2" />
            </svg>
          </button>
          <ul cDropdownMenu>
            <a cDropdownItem style="cursor: pointer" (click)="viewEventDetails(event)">查看詳情</a>
          </ul>
        </c-dropdown>
      </div>
    </div>
  </c-card>
</div>

<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
>
  <c-container>
    <c-row class="justify-content-center"
    *ngIf = "showCard == 'event' && filteredEvents().length === 0"
    >
      <c-col md="8" class="text-center">
        <svg
          cIcon name="cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未創立事件</h4>
      </c-col>
    </c-row>
  </c-container>
</div>


<!---
<c-card
  class="mb-4"
  *ngIf="showCard == 'event'"
>
        <c-card-header
    class="d-flex justify-content-between align-items-center flex-wrap gap-2"
  >
    <h6 class="mb-0 fw-bold small">事件列表</h6>
  </c-card-header>
  <c-card-body>
    <div class="list-group list-group-flush mb-3" *ngIf="filteredEvents().length > 0">
      <div
        class="list-group-item d-flex justify-content-between align-items-center py-2"
        *ngFor="let event of filteredEvents(); let i = index"
      >
        <div class="d-flex align-items-center flex-grow-1">
          <span class="fw-bold fs-3 flex-fill">{{ project.project_name || '(未命名事件)' }}</span>
          <span class="fw-bold flex-fill">電號：{{ place_id || '未知' }}</span>
          <span class="fw-bold flex-fill">創建時間：{{ event.create_time || '未知' }}</span>
          <span class="fw-bold badge bg-secondary">{{ translateEventType(event.event_type) || '未知' }}</span>
        </div>
        <c-dropdown>
          <button
            cDropdownToggle
            [caret]="false"
            class="btn bg-white d-flex justify-content-center align-items-center"
            style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="black"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="5" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="12" cy="19" r="2" />
            </svg>
          </button>
          <ul cDropdownMenu>
            <a cDropdownItem style="cursor: pointer" (click)="viewEventDetails(event)">查看詳情</a>
          </ul>
        </c-dropdown>
      </div>
    </div>
    <div
      *ngIf="filteredEvents().length === 0"
      class="text-muted text-center py-5"
    >
      <svg
        [cIcon]="icons.cilMoodBad"
        width="3rem"
        height="3rem"
        class="mb-3 text-primary"
      ></svg>
      <h5 class="mb-2">尚無事件資料</h5>
      <p class="text-body-secondary">目前沒有任何事件記錄</p>
    </div>
  </c-card-body>
</c-card>
-->
