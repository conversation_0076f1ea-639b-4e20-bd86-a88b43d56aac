<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Admin角色管理</strong>
      </c-card-header>
      <c-card-body>
        <table class="table table-striped table-bordered mt-3" *ngIf="users.length > 0">
          <thead class="table">
            <tr>
              <th>用戶</th>
              <th>Mail</th>
              <th>角色</th>
            </tr>
          </thead>
          <tbody> 
            <tr *ngFor="let user of users; let i = index">
              <td>{{ user.firstname }}, {{ user.lastname }}</td>
              <td>{{ user.email }}</td>
              <td>
                <div class="d-flex flex-wrap">
                  <div *ngFor="let role of availableRoles" class="form-check me-3">
                    <input type="checkbox"
                           class="form-check-input"
                           [id]="role + user.email"
                           [checked]="user.role?.includes(role)"  
                           (change)="toggleRole(user, role, $event)" />
                    <label class="form-check-label" [for]="role + user.email">{{ role }}</label>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      <div class="d-flex justify-content-center">
        <button cButton variant="outline" color="primary" (click)="openConfirmationModal()">更新角色</button>
      </div>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

      
<c-modal [visible]="showConfirmationModal" (close)="showConfirmationModal = false">
  <c-modal-header>
    <strong>確認修改</strong>
  </c-modal-header>
  <c-modal-body>
      <li *ngFor="let user of usersToUpdate">
        <strong>{{ user.email}}</strong> 角色變更為：
        <span *ngFor="let role of user.roles"> {{ role }} </span>
      </li>
  </c-modal-body>
  <c-modal-footer>
    <button class="btn btn-success" (click)="confirmUpdate()">確定</button>
    <button class="btn btn-danger" (click)="showConfirmationModal = false">取消</button>
  </c-modal-footer>
</c-modal>
