from flask import request, Blueprint, jsonify
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required
from bson.objectid import ObjectId
from datetime import datetime, timedelta
from sqlalchemy import text
from flask import current_app
from accessories import sqldb
import json,os



baseline_page = Blueprint('baseline_module', __name__)

'''
@baseline_page.route('/Calculate_Baseline', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def Calculate_Baseline():
    if request.method == 'OPTIONS':
        return '', 204
        
    # 驗證 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401  ##-----------------------------
    import_electricity_price()
    # 獲取請求數據
    data = request.get_json()
    place_id = data.get('place_id')
    startDate = data.get('startDate')
    endDate = data.get('endDate')
    taipowerRate = data.get('taipowerRate')
    summer_costs, summer_kwhs, non_summer_costs, non_summer_kwhs, summer_avg_cost, non_summer_avg_cost, min_monthly_kwh, monthly_records = Year_baseline_cost(startDate, endDate, place_id, taipowerRate)
    print(f"place_id: {place_id}, startDate: {startDate}, endDate: {endDate}, taipowerRate: {taipowerRate}")
    print(f"夏月平均每度電費: {summer_avg_cost:.2f} 元/度")
    print(f"非夏月平均每度電費: {non_summer_avg_cost:.2f} 元/度")
    print(f"最低使用度數: {min_monthly_kwh:.2f} 度")
    print("\n月份記錄:", monthly_records)
    #--------------------------------
    return jsonify({
            "token": get_refresh_token(token),
            "summer_avg_cost": summer_avg_cost,
            "non_summer_avg_cost": non_summer_avg_cost, 
            "min_monthly_kwh": min_monthly_kwh,
            "monthly_records": monthly_records
          
        }), 200
        
'''

   