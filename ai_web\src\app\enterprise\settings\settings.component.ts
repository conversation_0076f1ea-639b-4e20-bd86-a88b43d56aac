import { Component, OnInit } from '@angular/core';
import { SharedModule, enterprise, user, bank, credit_card } from '../../shared/shared.module';
import { SharedService } from '../../service/shared.service';
import { EnterpriseService } from '../../service/enterprise.service';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { data } from 'jquery';

// 定義 Member 介面，確保成員物件有 role 屬性
interface Member extends user {
  selected?: boolean; // 新增選中狀態屬性
}

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent implements OnInit {
  paymentModalVisible = false;
  payoutModalVisible = false;

  paymentMethod: 'credit' | 'bank' = 'credit';

  creditCard: credit_card = {
    number: '',
    name: '',
    expMonth: '',
    expYear: '',
    cvv: ''
  };

  bankAccount: bank = {
    name: '',
    bankCode: '',
    branchCode: '',
    accountNumber: ''
  };
  confirmDataCorrect: boolean = false;



  enterprise: enterprise = {
    taxId: '',
    name: '',
    address: '',
    telephone: '',
    applications: [],
  };

  // 成員相關屬性
  adminMembers: Member[] = [];
  regularMembers: Member[] = [];
  accountingMembers: Member[] = [];
  loadingMembers = false;

  // 模態框相關屬性
  editMode = false;
  save_info_done = false;

  availableRoles = [
    { value: 'admin', label: '管理員' },
  ];


  showCard: string = 'info';

  constructor(
    private enterpriseService: EnterpriseService,
    private sharedService: SharedService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // 獲取企業資訊
    this.loadEnterpriseInfo();
  }

  // 載入企業資訊
  loadEnterpriseInfo(): void {
    this.enterpriseService.get_enterprise_info(this.sharedService.getRouteId()).subscribe(
      (data) => {
        if (data && data.data) {
          this.enterprise = data.data;
        }
      },
      (error) => {
        console.error('獲取企業資訊失敗:', error);
      }
    );
  }

  setShowCard(card: string) {
    this.showCard = card;
  }

  saveEnterprise() {
    this.enterpriseService.update_enterprise_info(this.enterprise.taxId, this.enterprise).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.loadEnterpriseInfo();
        this.save_info_done = true;
      },
      (error) => {

      }
    )
  }

  confirmRemove(member: any) {
    if (confirm(`確定要移除 ${member.email} 嗎？`)) {
      this.removeMember(member);
    }
  }

  removeMember(member: any) {
    this.enterpriseService.remove_member(this.enterprise.taxId, member).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.loadEnterpriseInfo();
      },
      (error) => {

      }
    )
    this.enterprise.accounts = this.enterprise.accounts.filter(
      (m: any) => m.email !== member.email
    );
  }

  reply_application(applicant: any, reply: boolean) {
    this.enterpriseService.reply_entering_application(this.enterprise.taxId, applicant, reply).subscribe(
    (data) => {
      localStorage.setItem('token', data.token);
      this.loadEnterpriseInfo();
    },
    (error) => {
      this.router.navigate(['/404']);
    }
    )

  }
  isFormInvalid(form: 'payment' | 'payout' = 'payment'): boolean {
    if (this.paymentMethod === 'credit' && form !== 'payout') {
      // 簡單信用卡驗證
      const { number, name, expMonth, expYear, cvv } = this.creditCard;
      const ccNumberValid = /^\d{16}$/.test(number.trim());
      const ccNameValid = name.trim().length > 0;
      const expMonthValid = /^\d{1,2}$/.test(expMonth.trim()) && +expMonth >= 1 && +expMonth <= 12;
      const expYearValid = /^\d{4}$/.test(expYear.trim());
      const cvvValid = /^\d{3,4}$/.test(cvv.trim());
      return !(ccNumberValid && ccNameValid && expMonthValid && expYearValid && cvvValid);
    }

    if (this.paymentMethod === 'bank' || form === 'payout') {
      const { name, bankCode, branchCode, accountNumber } = this.bankAccount;
      const accNameValid = name.trim().length > 0;
      const bankCodeValid = /^\d{3}$/.test(bankCode.trim());
      const branchCodeValid = /^\d{1,4}$/.test(branchCode.trim());
      const accountValid = /^\d{5,20}$/.test(accountNumber.trim());
      return !(accNameValid && bankCodeValid && branchCodeValid && accountValid);
    }

    return true; // fallback: invalid
  }


  onRoleChange(member: any, event: any) {
    const role = event.target.value;
     // 確保 'member' 這個基礎角色存在
     if (!member.roles.includes('member')) {
      member.roles.push('member');
    }
    if (event.target.checked) {
      // 勾選：加入角色
      if (!member.roles.includes(role)) {
        member.roles.push(role);
      }
    } else {
      // 取消勾選：移除角色
      member.roles = member.roles.filter((r: string) => r !== role);
    }


    this.enterpriseService.edit_member_auth(this.enterprise.taxId, member.email, member.roles).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);

      },
      (error) => {
        if (error.error.roles){
          member.roles = error.error.roles
        }
      }
    )
  }

  toggleEditMode() {
    this.editMode = !this.editMode;
  }

  togglePaymentModal() {
    this.creditCard = {
      number: '',
      name: '',
      expMonth: '',
      expYear: '',
      cvv: ''
    };

    this.bankAccount = {
      name: '',
      bankCode: '',
      branchCode: '',
      accountNumber: ''
    };
    this.paymentModalVisible = !this.paymentModalVisible;
    this.confirmDataCorrect = false;
  }

  togglePayoutModal(){
    this.bankAccount = {
      name: '',
      bankCode: '',
      branchCode: '',
      accountNumber: ''
    };
    this.payoutModalVisible = !this.payoutModalVisible;
    this.confirmDataCorrect = false;
  }

  savePayment() {
    this.enterpriseService.update_enterprise_payment(this.enterprise.taxId, this.paymentMethod, this.creditCard, this.bankAccount).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.loadEnterpriseInfo();
        this.togglePaymentModal();
      },
      (error) => {

      }
    )
  }

  savePayout() {
    this.enterpriseService.update_enterprise_payout(this.enterprise.taxId, this.bankAccount).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.loadEnterpriseInfo();
        this.togglePayoutModal();
      },
      (error) => {

      }
    )

  }

}
