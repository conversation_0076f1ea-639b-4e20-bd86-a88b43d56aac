import { DeviceService } from '../../../service/device.service';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  InputGroupComponent
} from '@coreui/angular';
import { RowComponent, TextColorDirective, FormControlDirective, FormLabelDirective,FormSelectDirective } from '@coreui/angular';
import { ButtonDirective } from '@coreui/angular';

interface ShowTableEntry {
  device_id: string;
  data: { [key: string]: any }[];
}

@Component({
  selector: 'app-view',
  standalone: true,
  imports: [
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    InputGroupComponent,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    FormSelectDirective
  ],
  templateUrl: './view.component.html',
  styleUrls: ['./view.component.scss']
})
export class ViewComponent {
  public database: string = '';
  public gateway_id: string = '';
  public successMsg: string = '';
  public pendingMsg: string = '';
  public errMsg: string = '';
  public show_table: ShowTableEntry[] = [];

  constructor(private deviceService: DeviceService) {}

  search() {
    this.pendingMsg = 'Searching...';
    this.deviceService.search(this.database, this.gateway_id).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.successMsg = 'Success!';
        this.pendingMsg = '';
        this.errMsg = '';
        this.show_table = [];

        this.show_table = data.data
          .map((entry: { device_id: string; data: object[] }) => ({
            device_id: entry.device_id,
            data: entry.data
          }))
          .sort((a: { device_id: string }, b: { device_id: string }) =>
            a.device_id.localeCompare(b.device_id)
          );

        console.log(this.show_table);
      },
      (error) => {
        this.successMsg = '';
        this.pendingMsg = '';
        this.errMsg = 'Data not found, connection error or authorization error!';
      }
    );
}


  getColumnNames(data: { [key: string]: any }): string[] {
    return Object.keys(data);
  }

  getColumnValues(data: { [key: string]: any }): any[] {
    return Object.values(data);
  }
}
