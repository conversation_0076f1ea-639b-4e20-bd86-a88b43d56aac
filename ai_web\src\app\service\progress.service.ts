import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Project {
  name: string;
  status: string;
}

export interface ProgressData {
  statuses: string[];
  projects: Project[];
}

@Injectable({
  providedIn: 'root'
})

export class ProgressService {
  private apiUrl_get = `${environment.apiBaseUrl}/progress/Create_Progress`;
  private apiUrl_update = `${environment.apiBaseUrl}/progress/Update_Progress`;

  constructor(private http: HttpClient) {}

  /** 從後端讀取進度矩陣資料 */
  getProgressData(): Observable<ProgressData> {
    const token = localStorage.getItem('token');
    const url = `${this.apiUrl_get}/${token}`;
    return this.http.get<ProgressData>(url);
  }

  updateProgressData(projectName: string, newStatus: string): Observable<any> {
    const token = localStorage.getItem('token');
    const url = `${this.apiUrl_update}/${token}`;
    return this.http.post<any>(
      `${url}`,
      { place_id: projectName, status: newStatus }
    );
  }
}