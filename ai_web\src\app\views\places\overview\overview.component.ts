import { ProjectService } from './../../../service/project.service';
import { Component } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilHttps } from '@coreui/icons';

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    IconDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss'
})
export class OverviewComponent {
  icons = { cilHttps };

}
