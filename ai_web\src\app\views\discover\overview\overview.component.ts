import { Component } from '@angular/core';
import { SharedModule, enterprise, appItem } from '../../../shared/shared.module';
import { DiscoverService } from '../../../service/discover.service';
import { OnInit } from '@angular/core';

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss'
})
export class OverviewComponent implements OnInit {
  constructor(
    private sharedModule: SharedModule,
    private discoverService: DiscoverService
  ){
  }
  menuItems = [
    { label: '最新', value: 'latest' },
    { label: '免費應用', value: 'free' },
    { label: '付費應用', value: 'paid' },
    { label: '授權應用', value: 'licensed' },
    { label: '能源使用戶', value: 'user' },
    { label: '能源服務商', value: 'service' },
    { label: '硬體服務應用', value: 'hardware' },
    { label: '軟體服務應用', value: 'software' }
  ];

  allApps: appItem[] = [
    // 免費個人服務（type: 免費）
    {
      title: '個人戶電費單分析',
      subtitle: '分析個人用電狀況與費用趨勢',
      icon: 'cilChartPie',
      type: 'free',
      user: 'person',
      category: ['個人使用戶', '軟體服務應用'],
      id: 1
    },
    {
      title: '個人戶能源顧問診斷',
      subtitle: '提供個人用戶專業的能源診斷建議',
      icon: 'cilMagnifyingGlass',
      type: 'free',
      user: 'person',
      category: ['個人使用戶', '軟體服務應用'],
      id: 2
    },
    {
      title: '能源管理服務',
      subtitle: '提供保證節省的能源管理服務',
      icon: 'cilChartLine',
      type: 'paid',
      user: 'person',
      category: ['能源服務商', '軟體服務應用'],
      id: 3
    },
  ]
  selectedMenu = 'latest';
  hasBoundAccount: boolean = true; // 範例，可根據實際邏輯判斷
searchKeyword = '';
filteredApps = [...this.allApps];

ngOnInit() {

}

openApp(id: number){
  console.log(id)
}

onMenuChange(menu: string) {
  this.selectedMenu = menu;
}

get filtered_apps() {
  const keyword = this.searchKeyword.toLowerCase();

  return this.allApps.filter((app) => {
    const matchesKeyword =
      app.title.toLowerCase().includes(keyword) ||
      app.subtitle.toLowerCase().includes(keyword);

    // 判斷分類
    let matchesCategory = false;

    if (['popular_user', 'popular_service', 'latest'].includes(this.selectedMenu)) {
      matchesCategory = true; // 這三種不過濾分類
    } else if (['free', 'paid', 'licensed'].includes(this.selectedMenu)) {
      matchesCategory = app.type === this.selectedMenu;
    } else {
      // selectedMenu 是 category key，判斷 app.category 陣列是否包含該分類名稱
      const categoryName = this.getCategoryName(this.selectedMenu);
      matchesCategory = app.category.includes(categoryName);
    }

    return matchesKeyword && matchesCategory;
  });
}


getCategoryName(menu: string): string {
  const map = {
    user: '能源使用戶',
    service: '能源服務商',
    hardware: '硬體服務應用',
    software: '軟體服務應用'
  } as const;

  return map[menu as keyof typeof map] || '';
}



}
