<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div>
      <h2 class="mb-0" style="cursor: pointer">
        <strong>企業團隊設定</strong>
      </h2>
    </div>
  </c-col>
</c-row>

<!-- 分頁標籤 -->
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'info'"
            (click)="setShowCard('info')"
            class="custom-link"
            >資訊</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'info'"
            class="custom-link active"
            >資訊</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'member'"
            (click)="setShowCard('member')"
            class="custom-link"
            >成員</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'member'"
            class="custom-link active"
            >成員</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard !== 'app'"
            (click)="setShowCard('app')"
            class="custom-link"
            >應用</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard === 'app'"
            class="custom-link active"
            >應用</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>

<!-- 法人資訊 -->
<c-card *ngIf="showCard === 'info'" class="mb-4">
  <c-card-header>
    <strong>資訊</strong>
  </c-card-header>
  <c-card-body>
    <div class="mb-3">
      <label class="form-label">團隊ID</label>
      <input cInput class="form-control" [(ngModel)]="team.team_code" disabled />
    </div>
    <div class="mb-3">
      <label class="form-label">名稱</label>
      <input cInput class="form-control" [(ngModel)]="team.team_name"/>
    </div>
    <c-alert color="success" *ngIf="save_info_done">完成!</c-alert>
    <button cButton class="btn btn-primary" variant="outline" (click)="saveTeam()">儲存</button>
  </c-card-body>
</c-card>

<div class="d-flex flex-wrap gap-3">
  <!-- 成員管理 -->
  <c-card *ngIf="showCard === 'member'" class="flex-grow-1 flex-basis-0 mb-4" style="min-width: 300px;">
    <c-card-header class="d-flex justify-content-between align-items-center">
      <strong>成員管理</strong>
      <button
        class="btn btn-outline-danger btn-sm"
        (click)="toggleEditMode()"
      >
        <svg cIcon name="cilUserUnfollow" class="me-1"></svg>移除
      </button>
    </c-card-header>

    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex align-items-center justify-content-between"
        *ngFor="let member of team.accounts"
      >
        <div class="d-flex align-items-center gap-2">
          <!-- 刪除按鈕 -->
          <button
            *ngIf="editMode"
            class="btn p-0 d-flex align-items-center justify-content-center"
            style="width: 24px; height: 24px; background-color: transparent; border: none;"
            (click)="confirmRemove(member)"
          >
            <svg
              cIcon
              name="cilMinus"
              style="color: red; width: 20px; height: 20px;"
            ></svg>
          </button>

          <!-- Email -->
          <div class="fw-bold">{{ member.email }}</div>
        </div>

        <!-- 角色勾選 -->
        <div class="d-flex gap-2">
          <div class="form-check" *ngFor="let role of availableRoles">
            <input
              class="form-check-input"
              type="checkbox"
              [id]="'role-' + member.email + '-' + role.value"
              [value]="role.value"
              [checked]="member.roles.includes(role.value)"
              (change)="onRoleChange(member, $event)"
            />
            <label class="form-check-label" [for]="'role-' + member.email + '-' + role.value">
              {{ role.label }}
            </label>
          </div>
        </div>
      </div>

      <!-- 新增成員按鈕 -->
       <!--
      <div class="list-group-item text-center">
        <button
          class="btn p-0 border-0 bg-transparent d-flex align-items-center justify-content-center w-100"
          (click)="addMember()"
        >
          <svg
            cIcon
            name="cilPlus"
            style="color: green; width: 24px; height: 24px;"
          ></svg>
        </button>
      </div>
-->
    </div>
  </c-card>


  <!-- 申請列表 -->
  <c-card *ngIf="showCard === 'member'" class="flex-grow-1 flex-basis-0 mb-4" style="min-width: 300px;">
    <c-card-header>
      <strong>申請加入列表</strong>
    </c-card-header>

    <div *ngIf="team.applications ? team.applications.length > 0 : false; else noApplication">
      <div
        class="list-group list-group-flush"
        *ngFor="let applicant of team.applications"
      >
        <div class="list-group-item d-flex align-items-center justify-content-between">
          <!-- Email -->
          <div class="flex-grow-1">
            <div class="fw-bold">{{ applicant.email }}</div>
          </div>

          <!-- 按鈕 -->
          <div class="d-flex gap-2">
            <!-- 接受 -->
            <button
              class="btn p-0 d-flex align-items-center justify-content-center"
              style="width: 36px; height: 36px; background-color: transparent; border: none;"
              (click)="reply_application(applicant, true)"
            >
              <svg cIcon name="cilCheckCircle" style="color: green; width: 32px; height: 32px;"></svg>
            </button>

            <!-- 拒絕 -->
            <button
              class="btn p-0 d-flex align-items-center justify-content-center"
              style="width: 36px; height: 36px; background-color: transparent; border: none;"
              (click)="reply_application(applicant, false)"
            >
              <svg cIcon name="cilXCircle" style="color: red; width: 32px; height: 32px;"></svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <ng-template #noApplication>
      <div class="list-group-item text-center text-muted">
        目前沒有申請
      </div>
    </ng-template>
  </c-card>
</div>
