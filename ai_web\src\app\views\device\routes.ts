import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'Devi<PERSON>'
    },
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'view',
        loadComponent: () => import('./view/view.component').then(m => m.ViewComponent),
        data: {
          title: 'View'
        }
      },
      {
        path: 'history',
        loadComponent: () => import('./history/history.component').then(m => m.HistoryComponent),
        data: {
          title: 'history'
        }
      }
    ]
  }
];


