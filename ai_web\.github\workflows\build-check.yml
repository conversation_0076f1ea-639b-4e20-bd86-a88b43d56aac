name: Build Check

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:
    branches:
      - main
      - v5.*
  schedule:
    - # build runs every weekday at 4:15AM UTC
    - cron: '15 4 * * *'

env:
  FORCE_COLOR: 2
  NODE: 20

jobs:
  build:
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macOS-latest]
        node-version: [20.x]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.node-version }}

      - name: Install npm dependencies
        run: npm install

      - name: Run build
        run: npm run build
