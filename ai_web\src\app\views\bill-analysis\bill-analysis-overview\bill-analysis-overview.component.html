<!-- 專案標題與建立按鈕 -->
<c-row class="align-items-center mb-4 px-3">
    <c-col>
      <h2 class="mb-0"><strong></strong></h2>
    </c-col>
    <c-col class="text-end">
      <button
        cButton
        color="primary"
        class="text-white mx-2"
        [routerLink]="['/bill-analysis/submit-crawler-request']"
      >
        新增電費分析需求
      </button>
    </c-col>
  </c-row>
  
  <!-- 搜尋欄 -->
  <c-row class="mb-4 px-3">
    <c-col xs="12" md="6">
      <div class="input-group shadow-sm">
        <span class="input-group-text bg-white border-end-0">
          <svg [cIcon]="icons.cilSearch" size="xl" title="List Icon"></svg>
        </span>
        <input
          cInput
          type="text"
          class="form-control border-start-0"
          placeholder="搜尋電號名稱..."
          [(ngModel)]="searchText"
        />
      </div>
    </c-col>
  </c-row>
  
  <!-- 專案卡片列表 -->
  <c-row class="g-4 px-3">
    <c-col xs="12" md="6" lg="4" *ngFor="let project of filteredPlaceId()">
      <c-card class="h-100 shadow-sm border-0">
        <c-card-body>
          <c-col class="d-flex justify-content-between align-items-center">
            <h5 class="fw-bold mb-0" style="font-size: 1.5rem;">
              {{ project.place_id }}
            </h5>
            <button
              cButton
              color="danger"
              class="text-white btn-xs"
              size="sm"
              (click)="remove_place_id_analysis(project.place_id)"
              style="font-size: 0.8rem; padding: 0.2rem 0.5rem;"
            >
              移除
            </button>
          </c-col>
          <p class="text-muted mb-2"style="margin-top: 2rem; font-size: 1.3rem;">此電號分析可讀取之範圍：{{ project.earliest_month }}~{{ project.latest_month }}</p>
          <div class="d-flex justify-content-end gap-2">
            <button
              cButton
              color="primary"
              class="text-white"
              size="sm"
              [routerLink]="['/bill-analysis', project.place_id]"
              style="margin-top: 2rem; font-size: 1.3rem;"
            >
              進入
            </button>
          </div>
        </c-card-body>
      </c-card>
    </c-col>
  
    <c-col xs="12" md="6" lg="4">
      <c-card
        class="h-100 border border-primary border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center"
        style="cursor: pointer; min-height: 200px"
        [routerLink]="['/bill-analysis/submit-crawler-request']"
      >
        <div>
          <h1 class="mt-2 mb-0 text-primary fw-bold">+</h1>
        </div>
      </c-card>
    </c-col>
  </c-row>