from flask import request, Blueprint, jsonify, current_app, send_file
from accessories import mongo, sqldb, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required
from sqlalchemy import text
from bson.objectid import ObjectId
import requests
from io import BytesIO
group_page = Blueprint('group', __name__)

@group_page.route('/searchEnterpriseId', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def searchEnterpriseId(token):
    data = request.get_json()
    enterpriseId = data.get('enterpriseId')
    enterprise = mongo.db.enterprise.find_one({'taxId': enterpriseId}, {"_id": 0})
    enterprise_name = ''
    if enterprise:
        enterprise_name = enterprise.get("name", '')
    registered = bool(enterprise)
    url = 'https://dataset.einvoice.nat.gov.tw/ods/portal/api/v1/InvoiceBusinessList'
    params = {'busiBan': enterpriseId}
    headers = {'accept': 'application/json'}

    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()

        data = response.json()
        return jsonify({"token": get_refresh_token(token), 
                        "registered": registered,
                        "enterprise_name": enterprise_name,
                        "data": data}), 200

    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return jsonify({'error': 'Failed to fetch data'}), 500

@group_page.route('/searchAddressPostalCode', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def searchAddressPostalCode(token):
    data = request.get_json()
    address = data.get('address')
    
    return jsonify({"token": get_refresh_token(token), 
                        "postal_code": data}), 200

@group_page.route('/create_enterprise', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def create_enterprise(token):
    data = request.get_json()
    enterpriseTaxId = data.get('enterprise').get('taxId')
    enterpriseName = data.get('enterprise').get('name')
    enterpriseAddress = data.get('enterprise').get('address')
    enterpriseTelephone = data.get('enterprise').get('telephone')
    if not enterpriseTaxId or not enterpriseName or not enterpriseAddress or not enterpriseTelephone:
        return jsonify({'message': 'Missing required fields!'}), 400
    # Check if the enterprise already exists
    existing_enterprise = mongo.db.enterprise.find_one({'enterpriseTaxId': enterpriseTaxId})
    if existing_enterprise:
        return jsonify({'message': 'Enterprise already exists!'}), 400
    
    # update user json in mongo
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({'email': user_email})
    if not user:
        return jsonify({'message': 'User not found!'}), 404
    if 'enterprise' not in user:
        user['enterprise'] = []
    if enterpriseTaxId not in user['enterprise']:
        user['enterprise'].append(enterpriseTaxId)
    save_json_to_mongo({
        'taxId': enterpriseTaxId,
        'name': enterpriseName,
        'address': enterpriseAddress,
        'telephone': enterpriseTelephone,
        'created_by': get_user_info(token, 'email'),
        'accounts': [{
            "email": get_user_info(token, 'email'),
            "roles": ['admin', 'member']
        }],
    }, 'enterprise', ObjectId())
    update_json_in_mongo({"enterprise": user['enterprise']}, 'user', get_user_info(token, '_id'))
    return jsonify({"token": get_refresh_token(token)}), 200

@group_page.route('/apply_entering_enterprise', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def apply_entering_enterprise(token):
    data = request.get_json()
    taxId = data.get("taxId")
    enterprise = mongo.db.enterprise.find_one({"taxId": taxId}, {"_id": 0})
    enterprise_dbId = mongo.db.enterprise.find_one({"taxId": taxId}).get("_id")
    if not enterprise:
        return jsonify({'message': 'Enterprise not found!'}), 404
    application_list = enterprise.get("applications") or []
    user_email = get_user_info(token, 'email')
    for application in application_list:
        if application.get("email") == user_email:
            return jsonify({'message': 'Enterprise applied!'}), 404
    application_list.append({
        "email": user_email
    })
    enterprise['applications'] = application_list
    update_json_in_mongo(enterprise, 'enterprise', enterprise_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200

@group_page.route('/apply_entering_team', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def apply_entering_team(token):
    data = request.get_json()
    team_code = data.get("team_code")
    team_info = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    team_dbId = mongo.db.team.find_one({"team_code": team_code}).get("_id")
    if not team_info:
        return jsonify({'message': 'Enterprise not found!'}), 404
    application_list = team_info.get("applications") or []
    user_email = get_user_info(token, 'email')
    for application in application_list:
        if application.get("email") == user_email:
            return jsonify({'message': 'Enterprise applied!'}), 404
    application_list.append({
        "email": user_email
    })
    team_info['applications'] = application_list
    update_json_in_mongo(team_info, 'team', team_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200

@group_page.route('/get_user_enterprise', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_user_enterprise(token):
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({'email': user_email})
    if not user:
        return jsonify({'message': 'User not found!'}), 404
    enterprises = []
    if 'enterprise' in user:
        for enterprise_id in user['enterprise']:
            enterprise = mongo.db.enterprise.find_one({'taxId': enterprise_id})
            if enterprise:
                enterprises.append({
                    'taxId': enterprise.get('taxId'),
                    'name': enterprise.get('name'),
                    'address': enterprise.get('address'),
                    'telephone': enterprise.get('telephone'),
                    'project_code_rule': enterprise.get('project_code_rule')
                })
    return jsonify({"token": get_refresh_token(token),
                        "enterprises": enterprises
                        }), 200

@group_page.route('/get_user_team', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_user_team(token):
    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({'email': user_email})
    if not user:
        return jsonify({'message': 'User not found!'}), 404
    teams = []
    if 'team' in user:
        for team_code in user['team']:
            team = mongo.db.team.find_one({'team_code': team_code})
            if team:
                teams.append({
                    'team_code': team.get('team_code'),
                    'team_name': team.get('team_name'),
                    'taxId': team.get('taxId')
                })

    return jsonify({"token": get_refresh_token(token),
                        "teams": teams
                        }), 200
