<!--<c-header class="mb-4 d-print-none" position="sticky">-->
<ng-container>
  <c-container [fluid]="true" class="border-bottom px-4">
    <button
      [cSidebarToggle]="sidebarId()"
      cHeaderToggler
      class="btn"
      toggle="visible"
      style="margin-inline-start: -14px;"
      aria-label="Toggle sidebar navigation"
    >
      <svg cIcon name="cilMenu" size="lg"></svg>
    </button>


    <c-header-nav class=" d-md-flex ms-auto ">

    </c-header-nav>
    <c-header-nav class="ms-auto ms-md-0">
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-white"></div>
      </div>
    </c-header-nav>

    <c-header-nav class="ms-auto ms-md-0">
      <a>嗨, {{user.firstname}}, {{user.lastname}}</a>
    </c-header-nav>

    <c-header-nav class="mx-0">
      <ng-container *ngTemplateOutlet="userDropdown" />
    </c-header-nav>

  </c-container>
</ng-container>
<!--</c-header>-->

<ng-template #userDropdown>
  <c-dropdown [popperOptions]="{ placement: 'bottom-start' }" variant="nav-item">
    <button [caret]="false" cDropdownToggle class="py-0" aria-label="Open user menu">
      <img
      [src]="previewImage || profile_picture || 'assets/images/avatars/default-user.png'"
      class="rounded-circle border"
      width="45"
      height="45"
      loading="lazy"
      alt="User avatar"/>
      <span *ngIf="hasUnreadMail" class="mail-badge"></span>
    </button>
    <ul cDropdownMenu class="pt-0 w-auto">
       <!--這裡新增設定可以:上傳頭圖，修改聯絡資訊，-->
       <li>
        <a cDropdownItem (click)="OpenMailbox()">
          <svg cIcon class="me-2" name="cilEnvelopeClosed"></svg>
          信箱
          <span *ngIf="hasUnreadMail" class="mail-badge"></span>
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="" (click)="onlogout()">
          <svg cIcon class="me-2" name="cilAccountLogout"></svg>
          登出
        </a>
      </li>
    </ul>
  </c-dropdown>
</ng-template>
