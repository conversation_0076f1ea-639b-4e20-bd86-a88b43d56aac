import { ProjectService } from './../../../service/project.service';
import { UserService } from './../../../service/user.service';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { cilSearch } from '@coreui/icons';
import { IconDirective } from '@coreui/icons-angular';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
} from '@coreui/angular';
import {
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
} from '@coreui/angular';

interface project {
  code: string;
  name: string;
  leader: string;
  create_time: string;
}

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [
    ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    InputGroupComponent,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    RouterModule,
    IconDirective,
  ],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss',
})
export class OverviewComponent {
  icons = { cilSearch };
  modalVisible: boolean = false;
  new_projectcode: string;
  new_projectname: string;
  userFirstName: string = '';
  userLastName: string = '';
  errmsg: string;
  project_list: project[] = [];
  searchText: string = '';
  leave_visible: boolean;
  leave_index: number;
  hovering: boolean[];
  constructor(
    private ProjectService: ProjectService,
    private router: Router,
    private userService: UserService
  ) {
    this.new_projectcode = '';
    this.new_projectname = '';
    this.errmsg = '';
    this.leave_visible = false;
    this.leave_index = 0;
    this.get_project_list();
    this.hovering = this.filteredProjects().map(() => false);
  }

  get_project_list() {
    this.ProjectService.get_project_list().subscribe(
      (data) => {
        for (let i = 0; i < data.Project_List.length; i++) {
          this.project_list[i] = {
            code: data.Project_List[i].project_code,
            name: data.Project_List[i].project_name,
            leader: data.Project_List[i].project_leader,
            create_time: data.Project_List[i].create_time,
          };
        }
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
        this.errmsg = 'Project find error!';
      }
    );
  }

  add_project() {
    if (this.new_projectcode.length == 9) {
      this.ProjectService.create_project(
        this.new_projectcode,
        this.new_projectname
      ).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          this.project_list = [];
          this.get_project_list();
          this.new_projectcode = '';
          this.new_projectname = '';
          this.errmsg = '';
        },
        (error) => {
          this.router.navigate(['/404']);
          this.errmsg = 'Project create error!';
        }
      );
    }
  }
  openModal() {
    this.modalVisible = true;
  }

  closeModal() {
    this.modalVisible = false;
    this.new_projectcode = '';
    this.new_projectname = '';
  }
  leave_project(index: number) {
    this.ProjectService.leave_project(this.project_list[index].code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project_list.splice(index, 1);
      },
      (error) => {
        this.errmsg = 'Project create error!';
        this.router.navigate(['/404']);
      }
    );
  }

  view_project(index: number): void {
    const selectedProject = this.project_list[index];

    this.router.navigate([`/project/${selectedProject.code}`]);
  }

  toggle_leave_view(index: number) {
    this.leave_visible = !this.leave_visible;
    this.leave_index = index;
  }
  filteredProjects() {
    return this.project_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }
}
