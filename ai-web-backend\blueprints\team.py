from flask import request, Blueprint, jsonify, current_app
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo, type_name_trans
from blueprints.api import get_user_info, get_refresh_token, verify_token, team_role_required, generate_secure_random_string
from bson.objectid import ObjectId

team_page = Blueprint('team', __name__)

@team_page.route('/get_team_info', methods=['OPTIONS', 'POST', 'GET'])
@team_role_required(['member'])
def get_team_info(team_code, token):
    team = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})

    return jsonify({"token": get_refresh_token(token),
                   "data": team}), 200


@team_page.route('/get_user_team_roles', methods=['OPTIONS', 'POST', 'GET'])
@team_role_required(['member'])
def get_user_team_roles(team_code, token):
    team_info = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    user_email = get_user_info(token, "email")
    roles = []
    for account in team_info.get("accounts"):
        if account.get("email") == user_email:
            roles = account.get("roles")
    return jsonify({"token": get_refresh_token(token),
                    "roles": roles}), 200

@team_page.route('/update_team_info', methods=['OPTIONS', 'POST', 'GET'])
@team_role_required(['admin'])
def update_team_info(team_code, token):
    data = request.get_json()
    team_name = data.get("team").get("team_name")
    team_info = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    team_dbId = mongo.db.team.find_one({"team_code": team_code}).get("_id")
    team_info["team_name"] = team_name
    update_json_in_mongo(team_info, 'team', team_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200



@team_page.route('/reply_entering_application', methods=['OPTIONS', 'POST'])
@team_role_required(['admin'])
def reply_entering_application(team_code, token):
    data = request.get_json()
    applicant_email = data.get("applicant").get("email")
    reply = data.get("reply")
    team = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    team_dbId = mongo.db.team.find_one({"team_code": team_code}).get("_id")
    for i in team.get("accounts"):
        if i.get("email") == applicant_email:
            return jsonify({"msg": "application repeated!"}), 400
    account_list = team.get("accounts")
    if reply:
        account_list.append({
            "email": applicant_email,
            "roles": ['member']
        })
        team["accounts"] = account_list
        applicant = mongo.db.user.find_one({"email": applicant_email}, {"_id": 0})
        applicant_dbId = mongo.db.user.find_one({"email": applicant_email}).get("_id")
        applicant_team_list = applicant.get("team") or []
        if team_code not in applicant_team_list:
            applicant_team_list.append(team_code)
        applicant['team'] = applicant_team_list
        update_json_in_mongo(applicant, 'user', applicant_dbId)
    
    for i in team.get("applications"):
        if i.get("email") == applicant_email:
            team['applications'].remove(i)
    update_json_in_mongo(team, 'team', team_dbId)
    
    return jsonify({"token": get_refresh_token(token),}), 200


@team_page.route('/remove_member', methods=['OPTIONS', 'POST'])
@team_role_required(['admin'])
def remove_member(team_code, token):
    data = request.get_json()
    member = data.get("member").get("email")
    if member == get_user_info(token, 'email'):
        return jsonify({"msg": 'Cannot remove account!'}), 400
    team = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    team_dbId = mongo.db.team.find_one({"team_code": team_code}).get("_id")
    account_list = team.get("accounts")
    for i in account_list:
        if member == i.get("email"):
            account_list.remove(i)
            break
    team["accounts"] = account_list
    user = mongo.db.user.find_one({"email": member}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": member}).get("_id")
    user_team_list = user.get("team")
    if team_code in user_team_list:
        user_team_list.remove(team_code)
    user["team"] = user_team_list
    update_json_in_mongo(user, 'user', user_dbId)
    update_json_in_mongo(team, 'team', team_dbId)
    return jsonify({"token": get_refresh_token(token),}), 200


@team_page.route('/edit_member_auth', methods=['OPTIONS', 'POST'])
@team_role_required(['admin'])
def edit_member_auth(team_code, token):
    data = request.get_json()
    applicant = data.get("applicant")
    roles = data.get('roles')
    team = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    team_dbId = mongo.db.team.find_one({"team_code": team_code}).get("_id")
    original_roles = []
    admin_count = 0
    for idx, i in enumerate(team.get("accounts")):
        if i.get("email") == applicant:
            original_roles = team.get("accounts")[idx]["roles"]
            team.get("accounts")[idx]["roles"] = roles
        if 'admin' in team.get("accounts")[idx]["roles"]:
            admin_count += 1
    
    if applicant == get_user_info(token, "email") and 'admin' not in roles and admin_count == 0:
        return jsonify({"msg": "cannot remove last admin",
                        "roles": original_roles}), 400
    update_json_in_mongo(team, 'team', team_dbId)
    return jsonify({"token": get_refresh_token(token),}), 200

@team_page.route('/check_enterprise_payment', methods=['OPTIONS', 'POST'])
@team_role_required(['member'])
def check_enterprise_payment(team_code, token):
    enterprise_taxId = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0}).get("taxId")
    enterprise_payment_method = mongo.db.enterprise.find_one({"taxId": enterprise_taxId}, {"_id": 0}).get("payment_method", '')

    return jsonify({"token": get_refresh_token(token),
                    "data": bool(enterprise_payment_method)}), 200


@team_page.route('/check_enterprise_payout', methods=['OPTIONS', 'POST'])
@team_role_required(['member'])
def check_enterprise_payout(team_code, token):
    enterprise_taxId = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0}).get("taxId")
    enterprise_payout_method = mongo.db.enterprise.find_one({"taxId": enterprise_taxId}, {"_id": 0}).get("payout_bank", '')

    return jsonify({"token": get_refresh_token(token),
                    "data": bool(enterprise_payout_method)}), 200





@team_page.route('/activate_application', methods=['OPTIONS', 'POST'])
@team_role_required(['member'])
def activate_application(team_code, token):
    data = request.get_json()
    appId = data.get("appId")
    team_info = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0})
    team_dbId = mongo.db.team.find_one({"team_code": team_code}).get("_id")
    app_type = ''
    ## 檢查type、不存在activated_applications
    for app in current_app.config['APPLICATION_INFO']:
        if app.get("id") == appId:
            app_type = app.get("type")
    if not app_type:
        return jsonify({"msg": 'App not found!'}), 400
    team_activated_applications = team_info.get("activated_applications", [])
    for activated_application in team_activated_applications:
        if activated_application.get("appId") == appId:
            return jsonify({"msg": 'App has been activated!'}), 400
    if app_type == 'free':
        team_activated_applications.append({
            "appId": appId
        })
        team_info["activated_applications"] = team_activated_applications
        update_json_in_mongo(team_info, 'team', team_dbId)
        return jsonify({"token": get_refresh_token(token),
                        "msg": "Application has been activated successfully!"}), 200
    elif app_type == 'paid':
        enterprise_taxId = mongo.db.team.find_one({"team_code": team_code}, {"_id": 0}).get("taxId")
        enterprise_payment_method = mongo.db.enterprise.find_one({"taxId": enterprise_taxId}, {"_id": 0}).get("payment_method", '')
        if not enterprise_payment_method:
            return jsonify({"msg": 'Enterprise payment method not verified!'}), 400
        team_activated_applications.append({
            "appId": appId
        })
        team_info["activated_applications"] = team_activated_applications
        update_json_in_mongo(team_info, 'team', team_dbId)
        return jsonify({"token": get_refresh_token(token),
                        "msg": "Application has been activated successfully!"}), 200
    elif app_type == 'licensed':
        team_pending_applications = team_info.get("pending_applications", [])
        for pending_application in team_pending_applications:
            if pending_application.get("appId") == appId:
                return jsonify({"msg": 'App has been applied! Please wait for official response!'}), 400
        team_pending_applications.append({
            "appId": appId
        })
        team_info["pending_applications"] = team_pending_applications
        update_json_in_mongo(team_info, 'team', team_dbId)
        return jsonify({"token": get_refresh_token(token),
                        "msg": "Application has been applied successfully! Please wait for official response! It may take 1 to 4 weeks to get the approval."}), 200
