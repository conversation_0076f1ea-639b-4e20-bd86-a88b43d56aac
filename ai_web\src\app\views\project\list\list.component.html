<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div *ngIf="project">
      <h2
        class="mb-0"
        [routerLink]="['/project', project_code]"
        style="cursor: pointer"
      >
        <strong>{{ project.project_name }} - 資訊清單</strong>
      </h2>
    </div>
  </c-col>
</c-row>
<c-card class="mb-2">
  <c-card-header>
    <c-col xs="12">
      <c-nav variant="underline" class="custom-nav">
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'place_id'"
            (click)="setShowCard('place_id')"
            class="custom-link m"
            >電號</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'place_id'"
            class="custom-link active"
            >電號</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'contract'"
            (click)="setShowCard('contract')"
            class="custom-link m"
            >合約</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'contract'"
            class="custom-link active"
            >合約</a
          >
        </c-nav-item>
        <c-nav-item class="ms-2">
          <a
            cNavLink
            *ngIf="showCard != 'service'"
            (click)="setShowCard('service')"
            class="custom-link m"
            >服務</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'service'"
            class="custom-link active"
            >服務</a
          >
        </c-nav-item>
        <span class="nav-divider">|</span>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'plot'"
            (click)="setShowCard('plot')"
            class="custom-link"
            >規劃圖</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'plot'"
            class="custom-link active"
            >規劃圖</a
          >
        </c-nav-item>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'object'"
            (click)="setShowCard('object')"
            class="custom-link"
            >元件</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'object'"
            class="custom-link active"
            >元件</a
          >
        </c-nav-item>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'link'"
            (click)="setShowCard('link')"
            class="custom-link"
            >連結</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'link'"
            class="custom-link active"
            >連結</a
          >
        </c-nav-item>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'config'"
            (click)="setShowCard('config')"
            class="custom-link"
            >閘道設定</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'config'"
            class="custom-link active"
            >閘道設定</a
          >
        </c-nav-item>
        <span class="nav-divider">|</span>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'event'"
            (click)="setShowCard('event')"
            class="custom-link"
            >事件</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'event'"
            class="custom-link active"
            >事件</a
          >
        </c-nav-item>
        <c-nav-item>
          <a
            cNavLink
            *ngIf="showCard != 'invoice'"
            (click)="setShowCard('invoice')"
            class="custom-link"
            >發票內容</a
          >
          <a
            [active]="true"
            cNavLink
            *ngIf="showCard == 'invoice'"
            class="custom-link active"
            >發票內容</a
          >
        </c-nav-item>
      </c-nav>
    </c-col>
  </c-card-header>
</c-card>

<!-- 專案電號列表 -->
<div *ngIf="project">
  <c-card
    class="mb-4"
    *ngIf="showCard == 'place_id' && project.place_id.length > 0"
  >
    <c-card-header
      class="d-flex justify-content-between align-items-center flex-wrap gap-2"
    >
      <div class="d-flex align-items-center gap-2" style="max-width: 100%">
        <!-- 新增電號按鈕 -->
        <button
          class="btn shadow-sm bg-white"
          style="
            border: none;
            width: 2.25rem;
            height: 2.25rem;
            padding: 0;
            font-size: 1.25rem;
            line-height: 1;
          "
          (click)="toggle_create_place_id_view()"
          title="新增電號"
        >
          +
        </button>

        <!-- 搜尋欄 -->
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg [cIcon]="icons.cilSearch" size="xl" title="搜尋電號"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋電號..."
            [(ngModel)]="searchPlaceIdText"
          />
        </div>
      </div>
    </c-card-header>
    <div class="list-group list-group-flush">
      <div
        class="list-group-item d-flex justify-content-between align-items-center"
        *ngFor="let place_id of filteredPlaceId(); let i = index"
      >
        <div class="fw-bold">{{ place_id }}</div>
        <c-dropdown>
          <button
            cDropdownToggle
            [caret]="false"
            class="btn bg-white d-flex justify-content-center align-items-center"
            style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="black"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="5" r="2" />
              <circle cx="12" cy="12" r="2" />
              <circle cx="12" cy="19" r="2" />
            </svg>
          </button>
          <ul cDropdownMenu>
            <a
              cDropdownItem
              style="cursor: pointer"
              [routerLink]="['/project/', project_code, 'tpc-bills', place_id]"
            >
              電費單
            </a>
            <a
              cDropdownItem
              style="cursor: pointer"
              [routerLink]="[
                '/project/',
                project_code,
                'place-id-info',
                place_id
              ]"
            >
              內容
            </a>
            <a
              cDropdownItem
              style="cursor: pointer"
              class="text-danger"
              (click)="toggle_remove_place_id_view(i)"
            >
              刪除
            </a>
          </ul>
        </c-dropdown>
      </div>
    </div>
  </c-card>

  <div
    class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
    *ngIf="showCard == 'place_id' && project.place_id.length == 0"
  >
    <c-container>
      <c-row class="justify-content-center">
        <c-col md="8" class="text-center">
          <svg
            [cIcon]="icons.cilMoodBad"
            style="width: 3rem; height: 3rem"
            class="mb-3 text-primary"
          ></svg>
          <h4 class="mb-2">尚未創建電號</h4>
          <p class="text-body-secondary">
            按<span
              class="fw-bold text-primary"
              (click)="toggle_create_place_id_view()"
              style="cursor: pointer"
              >此</span
            >新增
          </p>
        </c-col>
      </c-row>
    </c-container>
  </div>
</div>

<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="showCard == 'contract'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          [cIcon]="icons.cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未創建合約</h4>
      </c-col>
    </c-row>
  </c-container>
</div>

<div
  class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
  *ngIf="showCard == 'service'"
>
  <c-container>
    <c-row class="justify-content-center">
      <c-col md="8" class="text-center">
        <svg
          [cIcon]="icons.cilMoodBad"
          style="width: 3rem; height: 3rem"
          class="mb-3 text-primary"
        ></svg>
        <h4 class="mb-2">尚未創建服務</h4>
      </c-col>
    </c-row>
  </c-container>
</div>

<c-row>
  <c-col xs="12">
    <c-card
      class="mb-4"
      *ngIf="showCard == 'plot' && projectplot_list.length > 0"
    >
      <c-card-header
        class="d-flex justify-content-between align-items-center flex-wrap gap-2"
      >
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg [cIcon]="icons.cilSearch" size="xl" title="List Icon"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋規劃圖名稱..."
            [(ngModel)]="searchPlotText"
          />
        </div>
      </c-card-header>

      <div class="list-group list-group-flush">
        <div
          class="list-group-item d-flex justify-content-between align-items-center"
          *ngFor="let plot of filteredPlots(); let i = index"
        >
          <div class="fw-bold">{{ plot }}</div>
          <c-dropdown>
            <button
              cDropdownToggle
              [caret]="false"
              class="btn bg-white d-flex justify-content-center align-items-center"
              style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="black"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="5" r="2" />
                <circle cx="12" cy="12" r="2" />
                <circle cx="12" cy="19" r="2" />
              </svg>
            </button>
            <ul cDropdownMenu>
              <a
                cDropdownItem
                style="cursor: pointer"
                class="text-danger"
                (click)="toggle_remove_projectplot_view(i)"
              >
                刪除
              </a>
            </ul>
          </c-dropdown>
        </div>
      </div>
    </c-card>

    <div
      class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
      *ngIf="showCard == 'plot' && projectplot_list.length == 0"
    >
      <c-container>
        <c-row class="justify-content-center">
          <c-col md="8" class="text-center">
            <svg
              [cIcon]="icons.cilMoodBad"
              style="width: 3rem; height: 3rem"
              class="mb-3 text-primary"
            ></svg>
            <h4 class="mb-2">尚未創建規劃圖</h4>
            <p class="text-body-secondary">
              請至<span
                class="fw-bold text-primary"
                [routerLink]="['/project/', project_code, 'plot']"
                style="cursor: pointer"
                >專案規劃頁面</span
              >開始編輯
            </p>
          </c-col>
        </c-row>
      </c-container>
    </div>

    <!-- 元件設定 -->
    <c-card
      class="mb-4"
      *ngIf="showCard == 'object' && projectobject_list.length > 0"
    >
      <c-card-header
        class="d-flex justify-content-between align-items-center flex-wrap gap-2"
      >
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg [cIcon]="icons.cilSearch" size="xl" title="搜尋元件"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋元件名稱..."
            [(ngModel)]="searchObjectText"
          />
        </div>
      </c-card-header>

      <c-card-body>
        <div class="list-group list-group-flush mb-3">
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
            *ngFor="let object of filteredObjects(); let i = index"
          >
            <div class="flex-grow-1">
              <div class="fw-bold text-dark mb-1">{{ object.name }}</div>
              <div class="text-muted small">類型：{{ object.type }}</div>
              <div class="text-muted small">序列號：{{ object.serialNo }}</div>
            </div>

            <c-dropdown>
              <button
                cDropdownToggle
                [caret]="false"
                class="btn bg-white d-flex justify-content-center align-items-center"
                style="
                  border: none;
                  width: 2.25rem;
                  height: 2.25rem;
                  padding: 0;
                "
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="black"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="5" r="2" />
                  <circle cx="12" cy="12" r="2" />
                  <circle cx="12" cy="19" r="2" />
                </svg>
              </button>
              <ul cDropdownMenu>
                <a
                  cDropdownItem
                  style="cursor: pointer"
                  class="text-danger"
                  (click)="toggle_remove_object_view(i)"
                  >移除</a
                >
              </ul>
            </c-dropdown>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <div
      class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
      *ngIf="showCard == 'object' && projectobject_list.length == 0"
    >
      <c-container>
        <c-row class="justify-content-center">
          <c-col md="8" class="text-center">
            <svg
              [cIcon]="icons.cilMoodBad"
              style="width: 3rem; height: 3rem"
              class="mb-3 text-primary"
            ></svg>
            <h4 class="mb-2">尚未創建元件</h4>
            <p class="text-body-secondary">
              請至<span
                class="fw-bold text-primary"
                [routerLink]="['/project/', project_code, 'plot']"
                style="cursor: pointer"
                >專案規劃頁面</span
              >開始編輯
            </p>
          </c-col>
        </c-row>
      </c-container>
    </div>

    <c-card
      class="mb-4"
      *ngIf="showCard == 'link' && projectlink_list.length > 0"
    >
      <c-card-header class="d-flex justify-content-between align-items-center">
        <div class="input-group shadow-sm" style="max-width: 300px">
          <span class="input-group-text bg-white border-end-0">
            <svg [cIcon]="icons.cilSearch" size="xl" title="搜尋元件"></svg>
          </span>
          <input
            cInput
            type="text"
            class="form-control border-start-0"
            placeholder="搜尋連結名稱..."
            [(ngModel)]="searchLinkText"
          />
        </div>
      </c-card-header>

      <c-card-body>
        <div
          class="list-group list-group-flush mb-3"
          *ngIf="filteredLinks().length > 0"
        >
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
            *ngFor="let link of filteredLinks(); let i = index"
          >
            <div class="flex-grow-1">
              <div class="fw-bold text-dark mb-1">
                {{ link.name ? link.name : "(未命名)" }}
              </div>
              <div class="text-muted small">類型：{{ link.type }}</div>
              <div class="text-muted small">起始元件：{{ link.source }}</div>
              <div class="text-muted small">目標元件：{{ link.target }}</div>
            </div>

            <c-dropdown>
              <button
                cDropdownToggle
                [caret]="false"
                class="btn bg-white d-flex justify-content-center align-items-center"
                style="
                  border: none;
                  width: 2.25rem;
                  height: 2.25rem;
                  padding: 0;
                "
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="black"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="5" r="2" />
                  <circle cx="12" cy="12" r="2" />
                  <circle cx="12" cy="19" r="2" />
                </svg>
              </button>
              <ul cDropdownMenu>
                <a
                  cDropdownItem
                  style="cursor: pointer"
                  class="text-danger"
                  (click)="toggle_remove_link_view(i)"
                  >移除</a
                >
              </ul>
            </c-dropdown>
          </div>
        </div>
        <div
          *ngIf="filteredLinks().length === 0"
          class="text-muted text-center py-3"
        >
          查無符合的連結
        </div>
      </c-card-body>
    </c-card>
    <!-- 閘道設定 -->
    <c-card
      class="mb-4"
      *ngIf="showCard == 'config' && project.gateway.length > 0"
    >
      <c-card-body>
        <div class="list-group list-group-flush mb-3">
          <div
            class="list-group-item d-flex justify-content-between align-items-center"
            *ngFor="let config of project.gateway; let i = index"
          >
            <div class="flex-grow-1">
              <div class="fw-bold text-dark mb-1">{{ config }}</div>
            </div>

            <c-dropdown>
              <button
                cDropdownToggle
                [caret]="false"
                class="btn bg-white d-flex justify-content-center align-items-center"
                style="
                  border: none;
                  width: 2.25rem;
                  height: 2.25rem;
                  padding: 0;
                "
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="black"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="5" r="2" />
                  <circle cx="12" cy="12" r="2" />
                  <circle cx="12" cy="19" r="2" />
                </svg>
              </button>
              <ul cDropdownMenu>
                <a
                  cDropdownItem
                  style="cursor: pointer"
                  class="text-danger"
                  (click)="toggle_remove_config_view(i)"
                  >移除</a
                >
              </ul>
            </c-dropdown>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <div
      class="bg-light dark:bg-transparent py-5 d-flex justify-content-center align-items-center"
      *ngIf="showCard == 'link' && projectlink_list.length == 0"
    >
      <c-container>
        <c-row class="justify-content-center">
          <c-col md="8" class="text-center">
            <svg
              [cIcon]="icons.cilMoodBad"
              style="width: 3rem; height: 3rem"
              class="mb-3 text-primary"
            ></svg>
            <h4 class="mb-2">尚未創建連結</h4>
            <p class="text-body-secondary">
              請至<span
                class="fw-bold text-primary"
                [routerLink]="['/project/', project_code, 'plot']"
                style="cursor: pointer"
                >專案規劃頁面</span
              >開始編輯
            </p>
          </c-col>
        </c-row>
      </c-container>
    </div>

        <!-- 事件列表 -->
    <c-card
      class="mb-4"
      *ngIf="showCard == 'event'"
    >
            <c-card-header
        class="d-flex justify-content-between align-items-center flex-wrap gap-2"
      >
        <h6 class="mb-0 fw-bold small">事件列表</h6>
      </c-card-header>
      <c-card-body>
        <div class="list-group list-group-flush mb-3" *ngIf="filteredEvents().length > 0">
          <div
            class="list-group-item d-flex justify-content-between align-items-center py-2"
            *ngFor="let event of filteredEvents(); let i = index"
          >
            <div class="d-flex align-items-center flex-grow-1">
              <span class="fw-bold me-3">{{ event.Application_Item || '(未命名事件)' }}</span>
              <span class="text-muted small me-3">{{ event.Application_Date || '未知' }}</span>
              <span class="badge bg-secondary">{{ event.STATUS || '未知' }}</span>
            </div>
            <c-dropdown>
              <button
                cDropdownToggle
                [caret]="false"
                class="btn bg-white d-flex justify-content-center align-items-center"
                style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="black"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="5" r="2" />
                  <circle cx="12" cy="12" r="2" />
                  <circle cx="12" cy="19" r="2" />
                </svg>
              </button>
              <ul cDropdownMenu>
                <a cDropdownItem style="cursor: pointer">查看詳情</a>

              </ul>
            </c-dropdown>
          </div>
        </div>
        <div
          *ngIf="filteredEvents().length === 0"
          class="text-muted text-center py-5"
        >
          <svg
            [cIcon]="icons.cilMoodBad"
            style="width: 3rem; height: 3rem"
            class="mb-3 text-primary"
          ></svg>
          <h5 class="mb-2">尚無事件資料</h5>
          <p class="text-body-secondary">目前沒有任何事件記錄</p>
        </div>
      </c-card-body>
    </c-card>

        <!-- 發票內容列表 -->
    <c-card
      class="mb-4"
      *ngIf="showCard == 'invoice'"
    >
      <c-card-header
        class="d-flex justify-content-between align-items-center flex-wrap gap-2"
      >
        <h6 class="mb-0 fw-bold small">發票內容列表</h6>
      </c-card-header>
      <c-card-body>
        <div class="list-group list-group-flush mb-3" *ngIf="filteredInvoices().length > 0">
          <div
            class="list-group-item d-flex justify-content-between align-items-center py-2"
            *ngFor="let invoice of filteredInvoices(); let i = index"
          >
            <div class="flex-grow-1">
              <span class="fw-bold">{{ invoice.invoiceNumber || '(未知發票號碼)' }}</span>
            </div>
            <c-dropdown>
              <button
                cDropdownToggle
                [caret]="false"
                class="btn bg-white d-flex justify-content-center align-items-center"
                style="border: none; width: 2.25rem; height: 2.25rem; padding: 0"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="black"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="5" r="2" />
                  <circle cx="12" cy="12" r="2" />
                  <circle cx="12" cy="19" r="2" />
                </svg>
              </button>
              <ul cDropdownMenu>
                <a cDropdownItem style="cursor: pointer" (click)="viewInvoiceDetail(invoice)">查看詳情</a>
                <a cDropdownItem style="cursor: pointer" (click)="downloadInvoiceProof(invoice)">下載證明</a>
              </ul>
            </c-dropdown>
          </div>
        </div>
        <div
          *ngIf="filteredInvoices().length === 0"
          class="text-muted text-center py-5"
        >
          <svg
            [cIcon]="icons.cilMoodBad"
            style="width: 3rem; height: 3rem"
            class="mb-3 text-primary"
          ></svg>
          <h5 class="mb-2">尚無發票資料</h5>
          <p class="text-body-secondary">目前沒有任何發票記錄</p>
        </div>
      </c-card-body>
    </c-card>

    <c-modal id="liveDemoModal" [visible]="remove_visible" scrollable>
      <c-modal-header>
        <h5 cModalTitle>移除元件</h5>
        <button (click)="toggle_remove_object_view(0)" cButtonClose></button>
      </c-modal-header>
      <c-modal-body>
        <strong>確認移除元件、設定檔及相關連結？</strong><br />
        <table>
          <tr>
            <th>類型</th>
            <th>
              {{ remove_object_type }}
            </th>
          </tr>
          <tr>
            <th>所屬電號</th>
            <th>
              {{ remove_place_id }}
            </th>
          </tr>
          <tr>
            <th>元件ID</th>
            <th>
              {{ remove_serialNo }}
            </th>
          </tr>
          <tr>
            <th>名稱</th>
            <th>
              {{ remove_object_name }}
            </th>
          </tr>
        </table>
      </c-modal-body>
      <c-modal-footer>
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="remove_object(remove_serialNo)"
        >
          確認移除
        </button>
      </c-modal-footer>
    </c-modal>

    <c-modal
      id="liveDemoModal"
      [visible]="remove_projectplot_visible"
      scrollable
    >
      <c-modal-header>
        <h5 cModalTitle>移除專案規劃圖</h5>
        <button
          (click)="toggle_remove_projectplot_view(0)"
          cButtonClose
        ></button>
      </c-modal-header>
      <c-modal-body> <strong>確認移除規劃圖？</strong><br /> </c-modal-body>
      <c-modal-footer>
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="remove_projectplot(remove_projectplot_index)"
        >
          確認移除
        </button>
      </c-modal-footer>
    </c-modal>

    <c-modal id="liveDemoModal" [visible]="remove_link_visible" scrollable>
      <c-modal-header>
        <h5 cModalTitle>移除連結</h5>
        <button (click)="toggle_remove_link_view(0)" cButtonClose></button>
      </c-modal-header>
      <c-modal-body> <strong>確認移除連結？</strong><br /> </c-modal-body>
      <c-modal-footer>
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="remove_link(remove_link_serialNo)"
        >
          確認移除
        </button>
      </c-modal-footer>
    </c-modal>

    <c-modal id="liveDemoModal" [visible]="remove_config_visible" scrollable>
      <c-modal-header>
        <h5 cModalTitle>移除閘道設定</h5>
        <button (click)="toggle_remove_config_view(0)" cButtonClose></button>
      </c-modal-header>
      <c-modal-body> <strong>確認移除閘道設定？</strong><br /> </c-modal-body>
      <c-modal-footer>
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="remove_gateway_config(remove_config_index)"
        >
          確認移除
        </button>
      </c-modal-footer>
    </c-modal>

    <c-modal id="liveDemoModal" [visible]="remove_place_id_visible" scrollable>
      <c-modal-header>
        <h5 cModalTitle>移除電號</h5>
        <button (click)="toggle_remove_place_id_view(0)" cButtonClose></button>
      </c-modal-header>
      <c-modal-body>
        <strong>確認移除電號{{ remove_place_id }}及相關元件?</strong><br />
      </c-modal-body>
      <c-modal-footer>
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="remove_place_id_f(remove_place_id)"
        >
          確認移除
        </button>
      </c-modal-footer>
    </c-modal>

    <c-modal id="liveDemoModal" [visible]="create_place_id_visible">
      <c-modal-header>
        <h5 cModalTitle>新增電號</h5>
        <button (click)="toggle_create_place_id_view()" cButtonClose></button>
      </c-modal-header>
      <c-modal-body>
        <c-row class="mb-3">
          <c-col>
            <label cCol cLabel="col" for="project" class="mx-2"> 電號 </label>
          </c-col>
          <c-col>
            <input
              cCol
              cLabel="col"
              cFormControl
              id="name"
              type="text"
              value="{{ input_name }}"
              [(ngModel)]="input_name"
            />
          </c-col>
        </c-row>
      </c-modal-body>
      <c-modal-footer>
        <button
          cButton
          variant="outline"
          color="primary"
          (click)="create_place_id()"
        >
          新增
        </button>
      </c-modal-footer>
    </c-modal>
  </c-col></c-row
>
