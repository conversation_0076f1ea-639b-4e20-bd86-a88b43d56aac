import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AdminService } from '../../../service/admin.service';
import { ModalModule } from '@coreui/angular';
import {
  ButtonDirective, CardBodyComponent, CardComponent, CardHeaderComponent, ColComponent,
  TableModule, GridModule, InputGroupComponent, ModalBodyComponent, ModalComponent,
  ModalFooterComponent, ModalHeaderComponent, ModalTitleDirective, RowComponent,
  TextColorDirective, FormControlDirective, FormLabelDirective, FormSelectDirective
} from '@coreui/angular';

@Component({
  selector: 'app-user-manager',
  standalone: true,
  imports: [
    ButtonDirective, CardBodyComponent, CardComponent, CardHeaderComponent, ColComponent,
    CommonModule, FormsModule, ReactiveFormsModule, TableModule, GridModule, ModalModule,
    InputGroupComponent, ModalBodyComponent, ModalComponent, ModalFooterComponent,
    ModalHeaderComponent, ModalTitleDirective, RowComponent, TextColorDirective,
    FormControlDirective, FormLabelDirective, FormSelectDirective
  ],
  templateUrl: './user-manager.component.html',
  styleUrl: './user-manager.component.scss'
})
export class UserManagerComponent {
  availableRoles = [];
  users: any[] = [];
  allUsers: any[] = [];
  modifiedUsers: Record<string, any> = {}; // 紀錄被修改的用戶
  usersToUpdate: any[] = []; // 用來存放被修改的用戶列表
  showConfirmationModal = false; // 是否顯示確認對話框
  constructor(private router: Router, private adminService: AdminService) {
    this.getUsersRoles();
  }

  // 取得使用者與角色
  getUsersRoles() {
    this.adminService.get_users_roles().subscribe(
      (data) => {
        if (!data || !Array.isArray(data.users)) {
          console.error("後端返回的 users 不是陣列:", data);
          return;
        }
        this.allUsers = data.users;
        this.users = [...this.allUsers];
        this.availableRoles = data.roles;
      },
      (error) => {
        console.error("取得使用者列表失敗:", error);
        this.router.navigate(['/404']);
      }
    );
  }

  // 處理角色變更
  toggleRole(user: any, role: string, event: any) {
    if (!user.role) {
      user.role = [];
    }
    if (event.target.checked) {
      user.role.push(role);
    } else {
      user.role = user.role.filter((r: string) => r !== role);
    }
    this.modifiedUsers[user.email] = { email: user.email, roles: [...user.role] };
  }

  // 開啟確認對話框
  openConfirmationModal() {
    this.usersToUpdate = Object.values(this.modifiedUsers); // 轉成陣列，避免 ngFor 錯誤
    if (this.usersToUpdate.length === 0) {
      alert("沒有變更的資料");
      return;
    }
    this.showConfirmationModal = true;
  }

  // 確認送出
  confirmUpdate() {
    this.adminService.update_users_roles(this.usersToUpdate).subscribe(
      () => {
        this.getUsersRoles(); // 重新加載用戶列表
        this.modifiedUsers = {}; // 清空變更記錄
        this.showConfirmationModal = false;
      },
      (error) => {
        console.error("更新用戶角色失敗:", error);
        this.router.navigate(['/404']);
      }
    );
  }
}
