import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DiscoverService {

  constructor(private http: HttpClient) { }
  test(): Observable<any> {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null);
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post(`${environment.apiBaseUrl}/discover/test`, {}, { headers });
    }
}
