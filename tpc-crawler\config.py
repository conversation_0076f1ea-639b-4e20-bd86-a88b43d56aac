class config:
    
    tpc_bill_conversion_dict = {
        'place_id': ['電號'],
        'ebpps_customer_address': ['用電地址'],
        'ebpps_bill_address': ['通訊地址'],
        'bill_year_month': ['帳單月份', '收費月份'],
        'bill_date': ['本次收費日'],
        'bill_date_next': ['下次收費日'],
        'kwh_total': ['用電度數（度）', '用電度數(度)', '度數'],
        'fee_total': ['應繳總金額'],
        'status': ['繳費狀況'],
        'payment_deadline': ['繳費期限'],
        'electricity_type': ['電價種類'],
        'feeder_number': ['饋線代號'],
        'group_powerout': ['輪流停電組別'],
        'date_bill_start': ['計費起始日期'],
        'date_bill_end': ['計費截止日期'],
        'date_readmeter': ['抄表日期', '本次抄表日'],
        'date_next_readmeter': ['下次抄表日'],
        'min_kwh': ['底度'],
        'day_used': ['本期用電日數', '用電日數'],
        'kwh_per_day': ['平均用電度數', '日平均度數'],
        'contract_peak': ['經常契約', '經常(尖峰)契約', '經常（尖峰）契約'],
        'kwh_peak': ['計費度數/經常度數', '計費度數/尖峰度數', '計費度數/經常(尖峰)度數', '計費度數/經常（尖峰）度數'],
        'kw_peak': ['經常需量', '經常(尖峰)需量', '經常（尖峰）需量'],
        'kwh_offpeak': ['計費度數/離峰度數'],
        'contract_offpeak': ['離峰契約'],
        'kw_offpeak': ['離峰需量'],
        'contract_semipeak': ['半尖峰(非夏月)契約',  '半尖峰（非夏月）契約'],
        'kw_semipeak': [ '半尖峰(非夏月)需量', '半尖峰（非夏月）需量'],
        'kwh_semipeak': ['計費度數/半尖峰度數'],
        'kwh_saturday': ['計費度數/週六半尖峰度數'],
        'contract_saturday': ['週六半尖峰契約', '週六半尖峰契容'],
        'kw_saturday': ['週六半尖峰需量'],
        'pf': ['功率因數(%)', '功率因數（％）','功率係數（％）', '功率係數(%)'],
        'adjustment_factor': ['調整係數', '調整係數(%)', '調整係數（％）'],
        'fee_kw': ['基本電費', '基本電費(約定)', '基本電費(合約)', '基本電費（約定）', '基本電費（合約）'],
        'fee_kwh': ['流動電費'],
        'fee_kwfine': ['非約定基本電費', '基本電費(非約定)', '超約附加費', '基本電費（非約定）'],
        'fee_pf': ['功率因數調整費'],
        'fee_tax': ['營業稅'],
        'fee_public': ['分攤公共電費','公共設施電費'],
        'tpc_ach': ['代繳帳號'],
        'time_type': ['時間種類'],
        'company_id': ['用戶營利事業統一編號'],
        'day_deducted': ['本次扣款日期'],
        'day_next_deducted': ['下次扣款日期'],
        'customer_service_hotline': ['客服專線'],
        'tpc_comapny_id': ['本公司營利事業統一編號'],
        'service_department': ['服務單位'],
        'service_department_address': ['服務地址'],
        'energy_saving_reward': ['節電獎勵'],
        'electronic_bill_discount_reduction': ['電子帳單優惠減收金額'],
        'fee_before_tax': ['稅前應繳總金額'],
        'kwh_per_fuel_cost': ['每度燃料成本'],
        'current_period_carbon_emissions': ['本期碳排量'],
        'kwh_per_recyling_fund': ['每度繳交再生基金'],
        'kwh_per_average_price': ['當期每度平均電價'],
        'reduce_kwh': ['減少用電量'],
        'subcontracted_kwh_peak': ['轉供度數/尖峰用電度數'],
        'subcontracted_kwh_semipeak': ['轉供度數/半尖峰用電度數'],
        'subcontracted_kwh_offpeak': ['轉供度數/離峰用電度數'],
        'subcontracted_kwh_saturday': ['轉供度數/週六半尖峰用電度數'],
        'price_adjustment_discount': ['電價減調金額','電價調整金額','加〈減〉收金額','加<減>收金額','加(減)收金額','加（減）收金額','優惠金額'],
        'late_payment_fee': ['遲付費用'],
        'power_outage_deduction': ['停電扣減金額'],

    }
    legal_entity_conversion_dict = {
    'company_id': ['統一編號','分公司統一編號','商業統一編號','工廠登記編號'],
    'company_name': ['公司名稱', '分公司名稱','商業名稱','工廠名稱','有限合夥名稱'],
    'registration_status': ['登記現況'],
    'foreign_company_name': ['章程所訂外文公司名稱'],
    'registered_capital': ['資本總額(元)','資本額(元)','工廠資本額'],
    'paid_in_capital': ['實收資本額(元)','實收出資額(元)'],
    'par_value_per_share': ['每股金額(元)'],
    'total_issued_shares': ['已發行股份總數(股)'],
    'representative_name': ['代表人姓名','負責人姓名','工廠負責人姓名'],
    'company_address': ['公司所在地','分公司所在地','地址','工廠地址','所在地'],
    'registration_authority': ['登記機關'],
    'date_of_approval': ['核准設立日期'],
    'date_of_last_change': ['最後核准變更日期','最近異動日期'],
    'special_shares_multiple_votes': ['複數表決權特別股'],
    'special_shares_veto_rights': ['對於特定事項具否決權特別股'],
    'business_scope': ['所營事業資料','營業項目','所營事業項目'],
    'branch_company_manager_name': ['分公司經理姓名'],
    'head_company_id': ['總(本)公司統一編號','公司(營利事業)統一編號'],
    'head_company_name': ['總(本)公司名稱'],
    'organization Type':['組織類型','工廠組織型態'],
    'factory_registration_approval_date': ['工廠登記核准日期'],
    'factory_establishment_permit_number': ['工廠設立許可案號'],
    'factory_establishment_approval_date': ['工廠設立核准日期'],
    'permit_revocation_date': ['工廠設立許可廢止核准日期'],
    'factory_close_date': ['工廠登記歇業核准日期'],
    'factory_revocation_date': ['工廠登記廢止核准日期'],
    'factory_announced_revocation_date': ['工廠登記公告廢止核准日期'],
    'last_inspection_year': ['最近一次校正年度'],
    'last_inspection_result': ['最近一次校正結果'],
    'industry_category_v11': ['產業類別(第11版)'],
    'main_product_v11': ['主要產品(第11版)'],
    'industry_category_v10': ['產業類別(第10版)'],
    'main_product_v10': ['主要產品(第10版)'],
    'duration_period': ['存續期間'],
    'last_registration_status': ['最近一次登記狀況'],
    'last_change_date': ['核准日期及文號'],
    'general_partner_name': ['普通合夥人姓名'],
    'limited_partner_name': ['有限合夥人'],
    'manager_name': ['經理人姓名'],
    'dissolution_conditions': ['約定解散事由']
}
   
    redis_host = 'localhost'
    redis_port = 6379
    TPC_crawler_URL = 'https://service.taipower.com.tw/ebpps2/'
    legal_entity_crawler_URL = 'https://findbiz.nat.gov.tw/fts/query/QueryBar/queryInit.do'
    account = "********"
    password = "********"
    pic_path = './vPic'
    yearmonth_crawler_key = 'tpc_crawler_request'
    bill_crawler_key = 'bill_crawler_request'
    delete_place_id_crawler_key ='delete_place_id_request'
    legal_entity_crawler_key = 'legal_entity_crawler_request'
    RPA_fill_in_key = 'fill_in_bizform'
    RPA_update_key = 'update_bizform'

class dev_config(config):
    api_base = 'http://localhost:5000'
    production = False
    redis_db = 0
    enable_exponential_backoff = False

class prd_config(config):
    api_base = 'https://ai-api.ima-ems.com'
    production = True
    redis_db = 1
    enable_exponential_backoff = True
    
class test_config(config):
    api_base = 'http://*************:5000'
    production = True
    redis_db = 0
    enable_exponential_backoff = False