import { CustomerService } from './../../../service/customer.service';
import { DashboardService } from '../../../service/dashboard.service';
import { environment } from '../../../../environments/environment'
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import '@joint/plus/joint-plus.css';
import {
  ButtonDirective,
  CardBodyComponent, CardComponent,
  ColComponent, TableModule,
  GridModule
} from '@coreui/angular';
import { Router, RouterLink } from '@angular/router';
import { RowComponent, TextColorDirective } from '@coreui/angular';

interface project_plot{
  project_code: string;
  project_name: string;
  plot_name: string;
}

@Component({
  selector: 'app-device-dashboard',
  standalone: true,
  imports: [
    ButtonDirective,
    CardBodyComponent, CardComponent,
    ColComponent, CommonModule, FormsModule, ReactiveFormsModule, TableModule,
    GridModule, RowComponent, TextColorDirective
  ],
  templateUrl: './device-dashboard.component.html',
  styleUrls: ['./device-dashboard.component.scss']
})
export class DeviceDashboardComponent {
  errmsg = '';
  titles: string[] = [];
  projectplot_list: project_plot[] = [];
  visible = false;
  create_visible = false;
  remove_visible = false;
  edit_visible = false;
  production = environment.production;
  input_name = "";

  constructor(private CustomerService: CustomerService, private DashboardService: DashboardService, private router: Router) {
    this.get_projectplot_list();
  }

  get_projectplot_list(){
    this.CustomerService.get_projectplot_list().subscribe(
      (data) => {
        for (let i = 0; i < data.plot_list.length; i++) {
          this.projectplot_list[i] = {
            project_code: data.plot_list[i].project_code,
            project_name: data.plot_list[i].project_name,
            plot_name: data.plot_list[i].plot_name
          }
        }
        console.log(this.projectplot_list);
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.errmsg = 'Project find error!';
        this.router.navigate(['/404']);
      }
    );
  }
  view_projectplot(idx: number){
    window.open(`/project/device-dashboard-view/${this.projectplot_list[idx].project_code}/${this.projectplot_list[idx].plot_name}`, '_blank');
  }

}
