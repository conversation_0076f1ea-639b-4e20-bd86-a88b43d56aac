<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="26.0.13">
  <diagram name="Page-1" id="9f46799a-70d6-7492-0946-bef42562c5a5">
    <mxGraphModel dx="1969" dy="1318" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="gBLg9mQjdeM4AzGfakd_-79" value="" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-100" y="-30" width="1480" height="450" as="geometry" />
        </mxCell>
        <mxCell id="78961159f06e98e8-17" value="send_role" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=#dae8fc;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="75" y="70" width="230" height="254" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="78961159f06e98e8-25" value="input" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontStyle=1;fontSize=20;gradientColor=none;rounded=0;" parent="78961159f06e98e8-17" vertex="1">
          <mxGeometry y="26" width="230" height="30" as="geometry" />
        </mxCell>
        <mxCell id="78961159f06e98e8-21" value="receiver" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" parent="78961159f06e98e8-17" vertex="1">
          <mxGeometry y="56" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-1" value="role" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="78961159f06e98e8-17">
          <mxGeometry y="78" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="78961159f06e98e8-19" value="" style="line;html=1;strokeWidth=1;fillColor=#dae8fc;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" parent="78961159f06e98e8-17" vertex="1">
          <mxGeometry y="100" width="230" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-21" value="&lt;font style=&quot;font-size: 17px;&quot;&gt;檢查寄件人角色身分pl、收件人角色&lt;/font&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=14;fontStyle=0" vertex="1" parent="78961159f06e98e8-17">
          <mxGeometry y="108" width="230" height="42" as="geometry" />
        </mxCell>
        <mxCell id="78961159f06e98e8-20" value="output" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" parent="78961159f06e98e8-17" vertex="1">
          <mxGeometry y="150" width="230" height="30" as="geometry" />
        </mxCell>
        <mxCell id="78961159f06e98e8-27" value="token" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" parent="78961159f06e98e8-17" vertex="1">
          <mxGeometry y="180" width="230" height="70" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-10" value="send_project_invitation" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=#dae8fc;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="660" y="70" width="230" height="246" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-11" value="input" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontStyle=1;fontSize=20;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="26" width="230" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-12" value="receiver" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="56" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-13" value="project_code" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="78" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-14" value="" style="line;html=1;strokeWidth=1;fillColor=#dae8fc;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="100" width="230" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-20" value="&lt;font&gt;檢查寄件人、收件人身分pl、是否擁有此專案&lt;/font&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=17;fontStyle=0" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="108" width="230" height="52" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-15" value="output" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="160" width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-18" value="token" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-10">
          <mxGeometry y="200" width="230" height="44" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-22" value="send_system_notification" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=#dae8fc;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="940" y="70" width="230" height="246" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-23" value="input" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontStyle=1;fontSize=20;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="26" width="230" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-24" value="receiver" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="56" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-25" value="content" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="78" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-26" value="" style="line;html=1;strokeWidth=1;fillColor=#dae8fc;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="100" width="230" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-27" value="&lt;font&gt;檢查寄件人身分user、&lt;/font&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=17;fontStyle=0" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="108" width="230" height="52" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-28" value="output" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="160" width="230" height="50" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-29" value="token" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-22">
          <mxGeometry y="210" width="230" height="34" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-30" value="" style="group;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" connectable="0" parent="1">
          <mxGeometry x="380" y="70" width="230" height="267" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-2" value="send_project_invitation" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=#dae8fc;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-30">
          <mxGeometry y="1" width="230" height="266" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-3" value="input" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontStyle=1;fontSize=20;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="26" width="230" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-4" value="receiver" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="56" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-5" value="project_code" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="78" width="230" height="22" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-6" value="" style="line;html=1;strokeWidth=1;fillColor=#dae8fc;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;strokeColor=#6c8ebf;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="100" width="230" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-19" value="&lt;font&gt;檢查寄件人身分pl、是否擁有此專案，收件人是否擁有此專案&lt;/font&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=17;fontStyle=0" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="108" width="230" height="72" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-7" value="output" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="180" width="230" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-17" value="token" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry y="220" width="230" height="44" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-9" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;labelBackgroundColor=none;startFill=0;startSize=8;endArrow=diamondThin;endFill=0;endSize=16;fontFamily=Verdana;fontSize=12;fillColor=#dae8fc;strokeColor=#6c8ebf;" edge="1" parent="gBLg9mQjdeM4AzGfakd_-30" target="gBLg9mQjdeM4AzGfakd_-2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
            <mxPoint x="105" as="sourcePoint" />
            <mxPoint x="305" y="218" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-71" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-45" target="gBLg9mQjdeM4AzGfakd_-55">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-45" target="gBLg9mQjdeM4AzGfakd_-47">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-45" target="gBLg9mQjdeM4AzGfakd_-63">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-45" value="用戶回覆" style="whiteSpace=wrap;html=1;fontFamily=Verdana;verticalAlign=top;fillColor=none;fontStyle=1;startSize=26;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;resizable=1;" vertex="1" parent="1">
          <mxGeometry x="585" y="890" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-36" target="gBLg9mQjdeM4AzGfakd_-45">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-47" value="accept" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=none;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="1013" width="330" height="250" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-51" value="" style="line;html=1;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-47">
          <mxGeometry y="26" width="330" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-52" value="&lt;font style=&quot;font-size: 17px; background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;&lt;font style=&quot;&quot;&gt;根據不同主題連接到不同router並利用mail_token找尋redis資料，對用戶mongoDB作相對應事情，並用&lt;span style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; white-space: pre;&quot;&gt;save_mail_event_to_SQL儲存到SQL&lt;/span&gt;&lt;br&gt;&lt;/font&gt;調用send_mail()通知原寄件人，mail沒有token&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=14;fontStyle=0" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-47">
          <mxGeometry y="34" width="330" height="146" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-53" value="output" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-47">
          <mxGeometry y="180" width="330" height="44" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-54" value="token" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-47">
          <mxGeometry y="224" width="330" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-55" value="reject" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=none;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="1013" width="330" height="256" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-59" value="" style="line;html=1;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-55">
          <mxGeometry y="26" width="330" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-60" value="&lt;div&gt;&lt;span style=&quot;font-size: 17px;&quot;&gt;根據不同主題連接到不同router並利用mail_token找尋redis資料，並用save_mail_event_to_SQL儲存到SQL&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;font-size: 17px;&quot;&gt;調用send_mail()通知原寄件人&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=14;fontStyle=0" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-55">
          <mxGeometry y="34" width="330" height="136" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-61" value="output" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-55">
          <mxGeometry y="170" width="330" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-62" value="token" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-55">
          <mxGeometry y="196" width="330" height="60" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-63" value="view" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fillColor=none;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="1">
          <mxGeometry x="930" y="1011" width="340" height="254" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-67" value="" style="line;html=1;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-63">
          <mxGeometry y="26" width="340" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-68" value="&lt;span style=&quot;font-size: 17px;&quot;&gt;如果是不用回覆的儲存到sql狀態是viewed&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=14;fontStyle=0" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-63">
          <mxGeometry y="34" width="340" height="42" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-69" value="output" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=20;fontStyle=1" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-63">
          <mxGeometry y="76" width="340" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-70" value="token" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-63">
          <mxGeometry y="102" width="340" height="108" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.007;entryY=0.272;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="78961159f06e98e8-17" target="gBLg9mQjdeM4AzGfakd_-38">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-2" target="gBLg9mQjdeM4AzGfakd_-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.664;entryY=0.017;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-10" target="gBLg9mQjdeM4AzGfakd_-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.013;entryY=0.39;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="gBLg9mQjdeM4AzGfakd_-22" target="gBLg9mQjdeM4AzGfakd_-38">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-80" value="router" style="triangle;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="-160" y="138" width="60" height="110" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-81" value="api" style="triangle;whiteSpace=wrap;html=1;fontSize=20;container=0;" vertex="1" parent="1">
          <mxGeometry x="330" y="620" width="60" height="110" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-82" value="router" style="triangle;whiteSpace=wrap;html=1;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="90" y="1080" width="60" height="110" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-36" value="send_mail()" style="swimlane;html=1;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;swimlaneFillColor=#ffffff;rounded=0;shadow=0;comic=0;labelBackgroundColor=none;strokeWidth=1;fontFamily=Verdana;fontSize=12;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;container=0;" vertex="1" parent="1">
          <mxGeometry x="390" y="460" width="510" height="398" as="geometry">
            <mxRectangle x="80" y="110" width="200" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-37" value="input" style="text;html=1;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontStyle=1;fontSize=20;container=0;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-36">
          <mxGeometry y="26" width="510" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-38" value="&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;sender, 寄件人&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;&lt;span style=&quot;&quot;&gt;receiver&lt;/span&gt;, 收件人 檢查&lt;b&gt;&lt;font style=&quot;color: rgb(255, 78, 2);&quot;&gt;有無收件人，output 0 :success,123...error&lt;/font&gt;&lt;/b&gt;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: rgb(255, 78, 2);&quot;&gt;&lt;b&gt;，1:type error...全部跑完return0&lt;/b&gt;&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;span style=&quot;color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;subject&lt;/span&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;, 主題(專案檢視邀請、)專案協作邀請、系統通知訊息、角色指派邀請&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;&lt;span style=&quot;&quot;&gt;content&lt;/span&gt;,內文 &lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;&lt;span style=&quot;&quot;&gt;argument&lt;/span&gt;, 專案碼(project_code)、角色(role)&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;&lt;span style=&quot;&quot;&gt;mail_type&lt;/span&gt;, 是否需要回覆 0:不需要(notification) 1:需要&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;due_time=24 失效時間(預設24小時)如果不需要回覆可以不給值，&lt;/font&gt;&lt;/div&gt;&lt;div style=&quot;font-family: Consolas, &amp;quot;Courier New&amp;quot;, monospace; font-size: 14px; line-height: 19px; white-space: pre;&quot;&gt;&lt;font style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: rgb(255, 78, 2);&quot;&gt;&lt;b&gt;改成一個禮拜，一個月&lt;/b&gt;&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=15;container=0;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-36">
          <mxGeometry y="56" width="510" height="204" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-41" value="&lt;font&gt;創建mail_token、當前時間，如果資料表不存在自動創建，將資料放進資料表，如果mail_type == 1(需要回覆)丟進redis儲存，查看receiver通知方式，如果有mail就發送mail，，，，，&lt;b&gt;&lt;font style=&quot;color: rgb(255, 78, 2);&quot;&gt;role_interaction.py放在project那邊&lt;/font&gt;&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;whiteSpace=wrap;overflow=hidden;rotatable=1;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;fontSize=17;fontStyle=0;container=0;" vertex="1" parent="gBLg9mQjdeM4AzGfakd_-36">
          <mxGeometry y="260" width="510" height="90" as="geometry" />
        </mxCell>
        <mxCell id="gBLg9mQjdeM4AzGfakd_-40" value="" style="line;html=1;strokeWidth=1;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=1;labelPosition=right;points=[];portConstraint=eastwest;movable=1;resizable=1;deletable=1;editable=1;locked=0;connectable=1;container=0;" vertex="1" parent="1">
          <mxGeometry x="390" y="670" width="510" height="8" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
