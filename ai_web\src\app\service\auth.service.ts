import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../environments/environment'
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private http: HttpClient) {}

  login(credentials: any): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.http.post(`${environment.apiBaseUrl}/login/login`, credentials, { headers });
  }

  isAuthenticated(): Observable<boolean> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    if (token) {
      return this.http.post<{ isAuthenticated: boolean , token?: string}>(`${environment.apiBaseUrl}/api/isAuthenticated`, {}, { headers })
        .pipe(
          map(response => {
            if (response.token && response.isAuthenticated){
              localStorage.setItem('token', response.token);
            }
            return response.isAuthenticated;
          }),
          catchError(() => {
            return of(false);
          })
        );
    } else {
      return of(false);
    }
  }

  logout() {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    this.http.post(`${environment.apiBaseUrl}/login/logout`, {}, { headers })
      .subscribe({
        next: (response) => {
          console.log('Logout successful');
          localStorage.removeItem('token');
        },
        error: (error) => {
          console.error('Logout failed', error);
        }
      });
  }

  verifyAccess(route: string): Observable<boolean> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<boolean>(`${environment.apiBaseUrl}/api/verify-access`, { route }, { headers });
  }

  verifyEnterpriseAccess(taxId: string, route: string): Observable<boolean> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<boolean>(`${environment.apiBaseUrl}/api/verify-enterprise-access`, { taxId: taxId, route: route }, { headers });
  }

  verifyTeamAccess(team_code: string, route: string): Observable<boolean> {
    const token = localStorage.getItem('token');
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<boolean>(`${environment.apiBaseUrl}/api/verify-team-access`, { team_code: team_code, route: route }, { headers });
  }

  forgotPassword(email: string): Observable<any> {
    return this.http.post(`${environment.apiBaseUrl}/forget-pwd/forgot-password`,  email);
  }

  resetPassword(token: string, password: string): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
    });

    const body = JSON.stringify({
      token: token,
      password: password,
    });

    return this.http.post<any>(`${environment.apiBaseUrl}/reset-pwd/reset-password`, body, { headers: headers });
  }
}
