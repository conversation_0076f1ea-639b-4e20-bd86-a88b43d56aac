<div class="bg-light dark:bg-transparent min-vh-100 d-flex flex-column">
  <!-- Header Section -->
  <header class="navbar navbar-light shadow-sm custom-2">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center" href="home">
        <!-- Company Icon -->
        <img
          src="assets/imalogo_white.ico"
          alt="Your Logo"
          class="sidebar-brand-full mx-1"
          height="32"
        />
        <!-- Company Name -->
        <span class="fs-4" style="color: white">IMA-One</span>
      </a>
    </div>
  </header>

  <div class="flex-grow-1 d-flex flex-row align-items-center">
    <c-container breakpoint="md">
      <c-row class="justify-content-center">
        <c-col lg="10" xl="8">
          <c-card-group class="text-center">
            <c-card class="p-4">
              <c-card-body class="text-center">
                <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
                  <h1>登入</h1>
                  <p class="text-body-secondary">登入您的帳號</p>
                  <div *ngIf="errorMessage" class="alert alert-danger">
                    {{ errorMessage }}
                  </div>
                  <c-input-group class="mb-3 justify-content-center">
                    <span cInputGroupText>&#64;</span>
                    <input
                      autoComplete="email"
                      formControlName="email"
                      placeholder="Email"
                    />
                  </c-input-group>

                  <c-input-group class="mb-3 justify-content-center">
                    <span cInputGroupText>
                      <svg cIcon name="cilLockLocked"></svg>
                    </span>
                    <input
                      autoComplete="password"
                      formControlName="password"
                      placeholder="Password"
                      type="password"
                    />
                  </c-input-group>
                  <c-row>
                    <c-col xs="6">
                      <button cButton class="btn custom-2" type="submit">
                        登入
                      </button>
                    </c-col>
                    <c-col class="text-right" xs="6">
                      <button cButton color="link" routerLink="/forget-pwd">
                        忘記密碼？
                      </button>
                    </c-col>
                  </c-row>
                </form>
              </c-card-body>
            </c-card>
            <c-card
              [ngStyle]="{ 'minWidth.%': 44 }"
              class="text-white py-5 custom-2"
            >
              <c-card-body class="text-center">
                <div>
                  <h2>註冊</h2>
                  <p>沒有帳戶？</p>
                  <button
                    [active]="true"
                    cButton
                    class="btn custom-3"
                    routerLink="/register"
                    color="#ffffff"
                  >
                    現在註冊！
                  </button>
                </div>
              </c-card-body>
            </c-card>
          </c-card-group>
        </c-col>
      </c-row>
    </c-container>
  </div>
</div>
