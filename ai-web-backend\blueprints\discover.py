from werkzeug.security import generate_password_hash
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required,token_required
from flask import jsonify, request, redirect, url_for, Blueprint, current_app

discover_page = Blueprint('discover', __name__)
@discover_page.route('/test', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def test(token):
    return jsonify({
        "token": get_refresh_token(token),
        "msg": "test"}), 200
