from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail
from flask_redis import FlaskRedis
from itsdangerous import URLSafeTimedSerializer
from flask import current_app
from flask_login import LoginManager, UserMixin
from queue import Queue
from flask_pymongo import PyMongo
from datetime import datetime
from bson.objectid import ObjectId
import paho.mqtt.client as mqtt
from accessories import mongo, sqldb
from datetime import datetime
from sqlalchemy import text
import json,os

# 歷史電價日期列表（按照時間從新到舊排序）*******************************************以下還有許多要修的地方 ex.重複引用的資料.重複的if迴圈.超約附加費與契約費的簡化
HISTORY_DATES = [
    "20241026", "20240401", "20231001", "20230401",
    "20220701", "20180401", "20160401", "20151001", "20150401"
]
electricity_codes = {
    "A000": ["低壓表燈非營業用","表燈非營業用"],
    "A001": ["A001"],
    "A010": ["表燈營業用", "低壓表燈營業用"],
    "A011": ["A011"],
    "A200": ["表燈標準營業用","表燈標準非營業用"],
    "A201": ["表燈標準非營業用J"],
    "A300": ["A300"],
    "A301": ["A301"],
    "B200": ["表燈標準營業用","住商時間營業用","表燈簡易營業用"],
    "B201": ["B201"],
    "B300": ["表燈簡易非營業用","住商時間非營業用","住商時間非營業用"],
    "B301": ["B301"],
    "C000": ["電力需量營業用", "電力裝置","需量綜合營業用","需量電力","需量綜合營業用","需量綜合非營業用","裝置電力","低壓需量電力非營業用","需量電力非營業用"],
    "C001": ["C001"],
    "C200": ["電力需量非營業用","電力需量","表燈時間營業用","表燈時間非營業用","低壓需量綜合營業用","低壓表燈時間電價營業用","低壓表燈時間電價營業用","低壓需量綜合非營業用"],
    "C201": ["C201"],
    "C300": ["C300"],
    "C301": ["C301"],
    "D200": ["高壓電力綜合營業用", "高壓需量非營業用", "高壓需量非營業用","高壓綜合非營業用私立高中(職業)用戶","高壓需量綜合非營業用","高壓需量綜合營業用","高壓"],
    "D201": ["需量綜合非營業用私立國小用戶","高壓綜合非營業用私立國中用戶"],
    "D202": ["D202"],
    "D300": ["高壓電力非營業用","高壓電力綜合非營業用","高壓綜合營業用","高壓綜合非營業用","高壓綜合非營業用私立大專院校用戶","高壓綜合非營業用","高壓需量電力非營業用"],
    "D301": ["高壓電力綜合非營業用私立國中用戶"],
    "D302": ["特高壓電力綜合非營業用公立大專院校用戶"],
    "D310": ["D301"],
    "D312": ["D312"],
    "D311": ["D311"],
    "E200": ["E200"],
    "E201": ["E201"],
    "E202": ["E202"],
    "E300": ["特高壓電力非營業用","特高壓電力綜合營業用","特高壓電力綜合營業用","特高需量非營業用","特高綜合營業用"],
    "E301": ["E301"],
    "E302": ["高壓電力綜合非營業用私立大專院校用戶"],
    "E310": ["E310"],
    "E311": ["E311"],
    "E312": ["E312"],
}

'''
def get_price_dates_for_range(start_date, end_date): #根據起始與結束日 找到對應的電價表和天數 
    """
    找出日期範圍內需要使用的電價日期和對應的使用區間
    :param start_date: 起始日期，格式 YYYY-MM-DD
    :param end_date: 結束日期，格式 YYYY-MM-DD
    :return: 電價使用區間的詳細信息
    """
    # 轉換日期為 datetime 對象以便計算
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # 將 YYYY-MM-DD 轉換為 YYYYMMDD 格式
    start_compare = start_dt.strftime("%Y%m%d")
    end_compare = end_dt.strftime("%Y%m%d")

    # 找出起始和結束日期對應的歷史價格日期
    start_price_date = None
    end_price_date = None
    for date in HISTORY_DATES:
        if date < start_compare and start_price_date is None:
            start_price_date = date
        if date < end_compare and end_price_date is None:
            end_price_date = date
        if start_price_date and end_price_date:
            break
    
    # 如果沒找到，使用最後一個日期
    start_price_date = start_price_date or HISTORY_DATES[-1]
    end_price_date = end_price_date or HISTORY_DATES[-1]

    # 找出價格變更的分界點
    for date in HISTORY_DATES:
        if date < end_compare:
            # 將 YYYYMMDD 轉換回 YYYY-MM-DD 格式
            split_date = f"{date[:4]}-{date[4:6]}-{date[6:]}"
            split_dt = datetime.strptime(split_date, "%Y-%m-%d")
            
            if start_dt <= split_dt <= end_dt:
                # 計算天數
                first_period_days = (split_dt - start_dt).days + 1
                second_period_days = (end_dt - split_dt).days + 1

                # 檢查 MongoDB 中是否存在對應的電價資料
                try:
                    first_found = mongo.db.electricity_price.find_one(
                        {"Taipower_Pricing_Effective_Date": start_price_date}
                    ) is not None
                    second_found = mongo.db.electricity_price.find_one(
                        {"Taipower_Pricing_Effective_Date": end_price_date}
                    ) is not None
                except Exception as e:
                    print(f"查詢電價資料時發生錯誤: {str(e)}")
                    first_found = False
                    second_found = False

                return {
                    "periods": [
                        {
                            "price_date": start_price_date,
                            "start_date": start_date,
                            "end_date": split_date,
                            "days": first_period_days,
                            "found": first_found
                        },
                        {
                            "price_date": end_price_date,
                            "start_date": split_date,
                            "end_date": end_date,
                            "days": second_period_days,
                            "found": second_found
                        }
                    ],
                    "total_days": first_period_days + second_period_days
                }

    # 如果沒有分界點，表示整個期間使用同一個價格
    total_days = (end_dt - start_dt).days + 1
    
    # 檢查 MongoDB 中是否存在對應的電價資料
    try:
        found = mongo.db.electricity_price.find_one(
            {"Taipower_Pricing_Effective_Date": start_price_date}
        ) is not None
    except Exception as e:
        print(f"查詢電價資料時發生錯誤: {str(e)}")
        found = False

    return {
        "periods": [
            {
                "price_date": start_price_date,
                "start_date": start_date,
                "end_date": end_date,
                "days": total_days,
                "found": found
            }
        ],
        "total_days": total_days
    }

def get_bill_by_place_and_date(place_id, year_month): #根據電號和年月查詢電費帳單資料
    """
    根據電號和年月查詢電費帳單資料
    :param place_id: 場所編號（電號）
    :param year_month: 年月，格式為 'YYYYMM'
    :return: 該電號在指定年月的帳單資料
    """
    try:
        # 構建日期範圍
        start_date = f"{year_month}01"
        if year_month.endswith('12'):
            next_year = str(int(year_month[:4]) + 1)
            end_date = f"{next_year}0101"
        else:
            next_month = str(int(year_month[-2:]) + 1).zfill(2)
            end_date = f"{year_month[:4]}{next_month}01"

        # SQL 查詢
        query = text("""
            SELECT *
            FROM temp.tpc_bills
            WHERE place_id = :place_id
            AND date_bill_start >= :start_date
            AND date_bill_start < :end_date
            AND fee_total IS NOT NULL 
            AND fee_total != 0
        """)

        # 使用 sqldb.get_engine
        engine = sqldb.get_engine(current_app, bind='195')
        with engine.connect() as connection:
            result = connection.execute(
                query,
                {"place_id": place_id, "start_date": start_date, "end_date": end_date}
            ).first()

            if result:
                # 將結果轉換為字典
                bill_data = result._asdict()
                
                # 格式化返回的資料
                formatted_result = {
                    '場所編號': bill_data.get('place_id', '無資料'),
                    '用戶名稱': bill_data.get('ebpps_name', '無資料'),
                    '用電類型': bill_data.get('electricity_type', '無資料'),
                    '目標': bill_data.get('aim', '無資料'),
                    '帳單年月': bill_data.get('bill_year_month', '無資料'),
                    '抄表日期': bill_data.get('date_readmeter', '無資料'),
                    '下次抄表日期': bill_data.get('date_next_readmeter', '無資料'),
                    '計費起始日': bill_data.get('date_bill_start', '無資料'),
                    '計費結束日': bill_data.get('date_bill_end', '無資料'),
                    '用電天數': bill_data.get('day_used', '無資料'),
                    '最小用電量': bill_data.get('min_kwh', '無資料'),
                    '總金額': bill_data.get('fee_total', '無資料'),
                    '需量電費': bill_data.get('fee_kw', '無資料'),
                    '超約附加費': bill_data.get('fee_kwfine', '無資料'),
                    '用電電費': bill_data.get('fee_kwh', '無資料'),
                    '功率因數費': bill_data.get('fee_pf', '無資料'),
                    '公共用電費': bill_data.get('fee_public', '無資料'),
                    '其他費用': bill_data.get('fee_other', '無資料'),
                    '營業稅': bill_data.get('fee_tax', '無資料'),
                    '能源費': bill_data.get('fee_oe', '無資料'),
                    '總用電量': bill_data.get('kwh_total', '無資料'),
                    '尖峰用電量': bill_data.get('kwh_peak', '無資料'),
                    '半尖峰用電量': bill_data.get('kwh_semipeak', '無資料'),
                    '離峰用電量': bill_data.get('kwh_offpeak', '無資料'),
                    '週六用電量': bill_data.get('kwh_saturday', '無資料'),
                    '契約尖峰': bill_data.get('contract_peak', '無資料'),
                    '契約半尖峰': bill_data.get('contract_semipeak', '無資料'),
                    '契約離峰': bill_data.get('contract_offpeak', '無資料'),
                    '契約週六': bill_data.get('contract_saturday', '無資料'),
                    '尖峰需量': bill_data.get('kw_peak', '無資料'),
                    '半尖峰需量': bill_data.get('kw_semipeak', '無資料'),
                    '離峰需量': bill_data.get('kw_offpeak', '無資料'),
                    '週六需量': bill_data.get('kw_saturday', '無資料'),
                    '功率因數': bill_data.get('pf', '無資料'),
                    '每度電費': bill_data.get('fee_per_degree', '無資料'),
                    '每日用電量': bill_data.get('kwh_per_day', '無資料'),
                    '建立時間': bill_data.get('created_at', '無資料'),
                    '繳費狀態': bill_data.get('status', '無資料'),
                    '台電銷帳編號': bill_data.get('tpc_ach', '無資料'),
                    '繳費期限': bill_data.get('payment_deadline', '無資料')
                }
                return formatted_result
            return None

    except Exception as e:
        print(f"查詢帳單資料時發生錯誤: {str(e)}")
        return None

   # 計算超約附加費的方法
def calculate_penalty(demand, contract, rate):
    """
    計算超約附加費
    """
    if not demand or not contract:
        return 0
        
    # 將輸入值統一轉換為float類型
    demand = float(demand)
    contract = float(contract)
    rate = float(rate)
    
    # 計算契約容量的1.1倍並根據小數點進行進位
    contract_110_exact = contract * 1.1
    decimal_part = contract_110_exact - int(contract_110_exact)
    if decimal_part >= 0.5:
        contract_110 = int(contract_110_exact) + 1
    else:
        contract_110 = int(contract_110_exact)
    
    # 計算超約量
    over_contract = demand - contract
    
    if over_contract > 0:
        # 判斷是否在契約容量1.1倍以內
        if demand <= contract_110:
            penalty = over_contract * rate * 2
            print(f"超約附加費計算(契約1.1倍以內): ({demand} - {contract}) × {rate} × 2 = {penalty:.2f} 元")
        else:
            # 分段計算超約附加費
            within_110 = contract_110 - contract  # 契約~1.1契約的用量
            beyond_110 = demand - contract_110    # 超過1.1契約的用量
            
            penalty = (within_110 * rate * 2) + (beyond_110 * rate * 3)
            print(f"超約附加費計算:")
            print(f"契約1.1倍以內: ({contract_110} - {contract}) × {rate} × 2 = {within_110 * rate * 2:.2f} 元")
            print(f"契約1.1倍以外: ({demand} - {contract_110}) × {rate} × 3 = {beyond_110 * rate * 3:.2f} 元")
            print(f"總計: {penalty:.2f} 元")
            
        return penalty
    return 0

def electricity_usage_cel(bill_data, price_date=None, days=None):
    try:
        # 基本資訊
        #print("\n=== 電費帳單分析 ===")
        #if price_date and days:
        #    print(f"分析區間電價生效日: {price_date}")
        #    print(f"區間天數: {days} 天")
            
        # 在函數開始時統一初始化所有變數
        total_fee = 0
        usage_fee = 0
        total_contract_fee = 0
        total_penalty = 0
        pf_fee = 0
        
        electricity_type = bill_data['用電類型']
        type_code = find_usage_type(electricity_type, electricity_codes)
        formula_steps = []
        
        contract_price=0
        over_price=0
        # 查詢對應電價資料
        if price_date:
            try:
                price_data = mongo.db.electricity_price.find_one(
                    {"Taipower_Pricing_Effective_Date": price_date}
                )
                
                
                
                #print("\n【契約容量資訊】")
                contract_peak = float(bill_data['契約尖峰'] or 0)
                contract_semipeak = float(bill_data['契約半尖峰'] or 0)
                contract_offpeak = float(bill_data['契約離峰'] or 0)
                contract_saturday = float(bill_data['契約週六'] or 0)
                
                contract = [
                    float(contract_peak or 0),
                    float(contract_semipeak or 0), 
                    float(contract_offpeak or 0),
                    float(contract_saturday or 0)
                ]
                
                if contract[0] > 0 and all(x == 0 for x in contract[1:]):
                    contract = [contract[0]] * 4
                else:
                    # 否則累加計算,例如18,2,2,2變成18,20,22,24
                    for i in range(1, len(contract)):
                        contract[i] += contract[i-1]
                
                # 判斷是否為夏季月份(6-9月)
                bill_start_date = datetime.strptime(bill_data['計費起始日'].strftime("%Y-%m-%d"), "%Y-%m-%d")
                start_month = bill_start_date.month
                is_summer = 6 <= start_month <= 9
                rate_index = 0 if is_summer else 1  # 夏季用0，非夏季用1
                
                # 1. 表燈用電類型
                if type_code in ['A000', 'A001']:  # 表燈非營業用
                    total_kwh = float(bill_data['總用電量'])
                    total_fee = 0  # 初始化總費用
                    #print("\n【表燈非營業用電分析】")
                    #print(f"計費起始月份: {start_month}月 ({'夏月' if is_summer else '非夏月'})")
                    
                    # 六段式計費
                    # 第一級距 (0-240度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, 120 * 2)
                        fee = kwh_used * price_data[type_code]['120'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 240度以下, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['120'][rate_index]:.4f}")
                        
                    # 第二級距 (241-660度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (330 - 120) * 2)
                        fee = kwh_used * price_data[type_code]['330'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 241-660度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['330'][rate_index]:.4f}")
                    
                    # 第三級距 (661-1000度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (500 - 330) * 2)
                        fee = kwh_used * price_data[type_code]['500'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 661-1000度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['500'][rate_index]:.4f}")
                    # 第四級距 (1001-1400度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (700 - 500) * 2)
                        fee = kwh_used * price_data[type_code]['700'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 1001-1400度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['700'][rate_index]:.4f}")
                        
                    # 第五級距 (1401-2000度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (1000 - 700) * 2)
                        fee = kwh_used * price_data[type_code]['1000'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 1401-2000度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['1000'][rate_index]:.4f}")
                    # 第六級距 (2001度以上)
                    if total_kwh > 0:
                        fee = total_kwh * price_data[type_code]['1001'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 2001度以上, 用電量: {total_kwh:.2f}, 費用: {fee:.2f}")
                        formula_steps.append(f"{total_kwh:.2f} * {price_data[type_code]['1001'][rate_index]:.4f}")
                    #print(f"總電費: {total_fee:.2f}")
                    
                    # 更新各項費用
                    usage_fee = total_fee
                    contract_fee = 0
                    penalty_fee = 0
                    pf_fee = 0
                
                elif type_code == 'A010':  # 表燈營業用
                    total_kwh = float(bill_data['總用電量'])
                    total_fee = 0  # 初始化總費用
         
         
                    # 第一級距 (0-660度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, 330 * 2)
                        fee = kwh_used * price_data[type_code]['330'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 660度以下, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['330'][rate_index]:.4f}")
                    # 第二級距 (661-1400度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (700 - 330) * 2)
                        fee = kwh_used * price_data[type_code]['700'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 661-1400度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['700'][rate_index]:.4f}")
                    # 第三級距 (1401-3000度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (1500 - 700) * 2)
                        fee = kwh_used * price_data[type_code]['1500'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 1401-3000度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['1500'][rate_index]:.4f}")
                    # 第四級距 (3001-6000度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (3000 - 1500) * 2)
                        fee = kwh_used * price_data[type_code]['3000'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 3001-6000度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['3000'][rate_index]:.4f}")
                    # 第五級距 (6001度以上)
                    if total_kwh > 0:
                        fee = total_kwh * price_data[type_code]['3001'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 6001度以上, 用電量: {total_kwh:.2f}, 費用: {fee:.2f}")
                        formula_steps.append(f"{total_kwh:.2f} * {price_data[type_code]['3001'][rate_index]:.4f}")
                    #print(f"總電費: {total_fee:.2f}")
                    
                    # 更新各項費用
                    usage_fee = total_fee
                    contract_fee = 0
                    penalty_fee = 0
                    pf_fee = 0
                
                elif type_code == 'A011':  # 凍漲表燈營業用
                    total_kwh = float(bill_data['總用電量'])
                    total_fee = 0  # 初始化總費用
                    #print("\n【凍漲表燈營業用電分析】")
                    #print(f"計費起始月份: {start_month}月 ({'夏月' if is_summer else '非夏月'})")
                    
                    # 四段式計費
                    # 第一級距 (0-660度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, 330 * 2)
                        fee = kwh_used * price_data[type_code]['330'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 660度以下, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['330'][rate_index]:.4f}")
                    # 第二級距 (661-1400度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (700 - 330) * 2)
                        fee = kwh_used * price_data[type_code]['700'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 661-1400度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['700'][rate_index]:.4f}")
                    # 第三級距 (1401-3000度)
                    if total_kwh > 0:
                        kwh_used = min(total_kwh, (1500 - 700) * 2)
                        fee = kwh_used * price_data[type_code]['1500'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 1401-3000度, 用電量: {kwh_used:.2f}, 費用: {fee:.2f}")
                        total_kwh -= kwh_used
                        formula_steps.append(f"{kwh_used:.2f} * {price_data[type_code]['1500'][rate_index]:.4f}")
                    # 第四級距 (3001度以上)
                    if total_kwh > 0:
                        fee = total_kwh * price_data[type_code]['1501'][rate_index]
                        total_fee += fee
                        #print(f"用電級距: 3001度以上, 用電量: {total_kwh:.2f}, 費用: {fee:.2f}")
                        formula_steps.append(f"{total_kwh:.2f} * {price_data[type_code]['1501'][rate_index]:.4f}")
                    #print(f"總電費: {total_fee:.2f}")
                    
                    # 更新各項費用
                    usage_fee = total_fee
                    contract_fee = 0
                    penalty_fee = 0
                    pf_fee = 0
                
                elif type_code in ['B200', 'B201']:  # 住商時間營業用/住商簡易兩段式
                    total_kwh = float(bill_data['總用電量'])
                    total_fee = 0  # 初始化總費用
                    #print("\n【住商時間電價分析】")
                    
                    # 取得各時段用電量，如果是None則設為0
                    peak_kwh = float(bill_data['尖峰用電量'] or 0)
                    off_peak_kwh = float(bill_data['離峰用電量'] or 0)
                    saturday_kwh = float(bill_data['週六用電量'] or 0)

                    # 計算各時段基本費用
                    fee_peak = peak_kwh * price_data[type_code]['peak'][rate_index]
                    fee_off_peak = off_peak_kwh * price_data[type_code]['off_peak'][rate_index]
                    fee_saturday = saturday_kwh * price_data[type_code]['saturday_offpeak'][rate_index]
                    formula_steps.append(f"{peak_kwh:.2f} * {price_data[type_code]['peak'][rate_index]:.4f}")
                    formula_steps.append(f"{off_peak_kwh:.2f} * {price_data[type_code]['off_peak'][rate_index]:.4f}")
                    formula_steps.append(f"{saturday_kwh:.2f} * {price_data[type_code]['saturday_offpeak'][rate_index]:.4f}")
                    
                    # 基本電費加總
                    total_fee = fee_peak + fee_off_peak + fee_saturday
                    
                    
                    contract_price = price_data[type_code]['base']
                    #print(f"契約容量費用: {contract_price}")  
                    formula_steps.append(f"{contract_peak:.2f} * {price_data[type_code]['base']:.4f}")

                    # 判斷是否需要加收超約附加費
                    if total_kwh > 2000:
                        over_kwh = total_kwh - 2000  # 超過的度數
                        over_fee = over_kwh * price_data[type_code]['over_2000'] # 超約附加費
                        total_fee += over_fee
                        formula_steps.append(f"{over_kwh:.2f} * {price_data[type_code]['over_2000']:.4f}")
                        #print(f"用電量超過2000度，超約度數: {over_kwh:.2f}, 超約附加費: {over_fee:.2f}")
                       
                    # 顯示各時段用電資訊
                    #print(f"尖峰用電量: {peak_kwh:.2f}, 費用: {fee_peak:.2f}")
                    #print(f"離峰用電量: {off_peak_kwh:.2f}, 費用: {fee_off_peak:.2f}")
                    if saturday_kwh > 0:
                        #print(f"週六用電量: {saturday_kwh:.2f}, 費用: {fee_saturday:.2f}")
                        pass
                    #print(f"總電費: {total_fee:.2f}")
                    
                    # 更新各項費用
                    usage_fee = total_fee
                    contract_fee = 0
                    penalty_fee = 0
                    pf_fee = 0
                    
                elif type_code in ['B300', 'B301']:  # 住商時間營業用/住商簡易三段式
                    #print("\n【住商時間三段式電價分析】")
                    
                    # 取得各時段用電量，如果是None則設為0
                    peak_kwh = float(bill_data['尖峰用電量'] or 0)
                    semi_peak_kwh = float(bill_data['半尖峰用電量'] or 0) 
                    off_peak_kwh = float(bill_data['離峰用電量'] or 0)
                    saturday_kwh = float(bill_data['週六用電量'] or 0)
                    total_kwh = float(bill_data['總用電量'])
                    total_fee = 0
                    
                    contract_price = price_data[type_code]['base']
                    formula_steps.append(f"{contract_peak:.2f} * {price_data[type_code]['base']:.4f}")


             
                    if total_kwh > 2000:
                        over_kwh = total_kwh - 2000  
                        over_fee = over_kwh * price_data[type_code]['over_2000']  
                        total_fee += over_fee
                        #print(f"用電量超過2000度，超約度數: {over_kwh:.2f}, 超約附加費: {over_fee:.2f}")
                        formula_steps.append(f"{over_kwh:.2f} * {price_data[type_code]['over_2000']:.4f}")
                    # 根據夏月/非夏月計算費用
                    if rate_index == 0:  # 夏月
                        fee_peak = peak_kwh * price_data[type_code]['peak'][rate_index]
                        fee_semi_peak = semi_peak_kwh * price_data[type_code]['semi_peak'][rate_index]
                        fee_off_peak = off_peak_kwh * price_data[type_code]['off_peak'][rate_index]
                        fee_saturday = saturday_kwh * price_data[type_code]['saturday_offpeak'][rate_index]
                        total_fee += fee_peak + fee_semi_peak + fee_off_peak + fee_saturday
                        formula_steps.append(f"{peak_kwh:.2f} * {price_data[type_code]['peak'][rate_index]:.4f}")
                        formula_steps.append(f"{semi_peak_kwh:.2f} * {price_data[type_code]['semi_peak'][rate_index]:.4f}")
                        formula_steps.append(f"{off_peak_kwh:.2f} * {price_data[type_code]['off_peak'][rate_index]:.4f}")
                        formula_steps.append(f"{saturday_kwh:.2f} * {price_data[type_code]['saturday_offpeak'][rate_index]:.4f}")
                    else:  # 非夏月
                        fee_semi_peak = semi_peak_kwh * price_data[type_code]['semi_peak'][rate_index]
                        fee_off_peak = off_peak_kwh * price_data[type_code]['off_peak'][rate_index]
                        fee_saturday = saturday_kwh * price_data[type_code]['saturday_offpeak'][rate_index]
                        total_fee += fee_semi_peak + fee_off_peak + fee_saturday
                        formula_steps.append(f"{semi_peak_kwh:.2f} * {price_data[type_code]['semi_peak'][rate_index]:.4f}")
                        formula_steps.append(f"{off_peak_kwh:.2f} * {price_data[type_code]['off_peak'][rate_index]:.4f}")
                        formula_steps.append(f"{saturday_kwh:.2f} * {price_data[type_code]['saturday_offpeak'][rate_index]:.4f}")

    
                    
                    if rate_index == 0:  # 夏月
                        #print(f"\n尖峰用電量: {peak_kwh:.2f}, 費用: {fee_peak:.2f}")
                        pass
                    #print(f"半尖峰用電量: {semi_peak_kwh:.2f}, 費用: {fee_semi_peak:.2f}")
                    #print(f"離峰用電量: {off_peak_kwh:.2f}, 費用: {fee_off_peak:.2f}")
                    if saturday_kwh > 0:
                        #print(f"週六用電量: {saturday_kwh:.2f}, 費用: {fee_saturday:.2f}")
                        pass
                    #print(f"總電費: {total_fee:.2f}")
                    
                    # 更新各項費用
                    usage_fee = total_fee
                    contract_fee = 0
                    penalty_fee = 0
                    pf_fee = 0
                    
                # 4. 二段式時間電價類型
                elif type_code in ['A200', 'A201', 'C200', 'C201',
                                 'D200', 'D201', 'D202', 'E200', 'E201', 'E202']:
                        #print("\n【二段式時間電價分析】")
      
                        #print("\n【用電成本分析】")
                        
                        # 取得用電量資料並安全轉換為float
                        peak_kwh = float(bill_data['尖峰用電量'] or 0)
                        off_peak_kwh = float(bill_data['離峰用電量'] or 0)
                        saturday_kwh = float(bill_data['週六用電量'] or 0)
                        
                     
                        
                        # 取得對應費率並安全轉換為float
                        peak_rate = float(price_data[type_code]['Peak'][rate_index])
                        off_peak_rate = float(price_data[type_code]['Off_Peak'][rate_index])
                        saturday_half_peak_rate = float(price_data[type_code]['Saturday_Half_Peak'][rate_index])
                        
                        # 計算各時段費用
                        peak_cost = peak_kwh * peak_rate
                        off_peak_cost = off_peak_kwh * off_peak_rate
                        saturday_cost = saturday_kwh * saturday_half_peak_rate
                        formula_steps.append(f"{peak_kwh:.2f} * {peak_rate:.4f}")
                        formula_steps.append(f"{off_peak_kwh:.2f} * {off_peak_rate:.4f}")
                        formula_steps.append(f"{saturday_kwh:.2f} * {saturday_half_peak_rate:.4f}")
                        # 計算總費用
                        total_energy_cost = peak_cost + off_peak_cost + saturday_cost 
                        
                        # 輸出詳細計算結果
                        #print(f"\n計費月份: {start_month}月 ({'夏月' if is_summer else '非夏月'})")
                        #print("\n各時段用電量與費用:")
                        #print(f"尖峰時段: {peak_kwh:.2f} 度 × {peak_rate:.4f} 元 = {peak_cost:.2f} 元")
                        #print(f"離峰時段: {off_peak_kwh:.2f} 度 × {off_peak_rate:.4f} 元 = {off_peak_cost:.2f} 元")
                        
                        if saturday_kwh > 0:
                            #print(f"\n週六用電:")
                            #print(f"週六用電: {saturday_kwh:.2f} 度 × {saturday_half_peak_rate:.4f} 元 = {saturday_cost:.2f} 元")
                            pass
                        
                        #print(f"\n流動電費總計: {total_energy_cost:.2f} 元")
                        # 更新各項費用
                        frequent_fee = 0
                        semi_peak_fee = 0
                        saturday_fee = 0
                        off_peak_fee = 0
                        total_contract_fee = 0
                        
                        if type_code in ['A200', 'A201']:
                            base_unit = price_data[type_code]['Base_Unit']  # 一般基本電費
                            base_three = price_data[type_code]['Base_Three']
                            formula_steps.append(f"{base_three}*1")
                        else:
                            base_unit = 0
                            base_three = 0
                        
                                
                        if  type_code in ['C200', 'C201']:
                              # 初始化所有費用變數
                              frequent_fee = semi_peak_fee = saturday_fee = off_peak_fee = total_contract_fee = Base_Three = 0
                              
                              # 從用電類型判斷是否為裝置
                              electricity_type = bill_data['用電類型']
                              is_device = '裝置' in electricity_type
                              
                              if is_device:
                                  base_device = price_data[type_code]['Base_device']  # 裝置基本電費
                                  frequent_fee = contract_peak * base_device
                                  total_contract_fee = frequent_fee
                                  formula_steps.append(f"{contract_peak:.2f} * {base_device:.4f}")
                              else:
                                  Base_Three = price_data[type_code]['Base_Three']  # 一般基本電費
                                  frequent_contract = price_data[type_code]['Frequent_contract'][rate_index]
                                  semi_peak_contract = price_data[type_code]['Non_Summer_contract']
                                  saturday_contract = price_data[type_code]['Saturday_contract'][rate_index]
                                  off_peak_contract = price_data[type_code]['Off_Peak_contract'][rate_index]

                                  # 計算各時段契約容量電費
                                  if contract_peak > 0:  # 經常契約容量
                                      frequent_fee = contract_peak * frequent_contract
                                      formula_steps.append(f"{contract_peak:.2f} * {frequent_contract:.4f}")
                                  if contract_semipeak > 0:  # 半尖峰契約容量
                                      semi_peak_fee = contract_semipeak * semi_peak_contract
                                      formula_steps.append(f"{contract_semipeak:.2f} * {semi_peak_contract:.4f}")
                                  if contract_saturday > 0:  # 週六契約容量 
                                      saturday_fee = contract_saturday * saturday_contract
                                      formula_steps.append(f"{contract_saturday:.2f} * {saturday_contract:.4f}")
                                  if contract_offpeak > 0:  # 離峰契約容量
                                      off_peak_fee = contract_offpeak * off_peak_contract
                                      formula_steps.append(f"{contract_offpeak:.2f} * {off_peak_contract:.4f}")
                                  
                                  total_contract_fee = frequent_fee + semi_peak_fee + saturday_fee + off_peak_fee 
                                  
                                  # 計算超約附加費
                                  total_penalty = 0
                                  max_over_demand = 0
                                  
                                  # 檢查尖峰時段超約
                                  peak_demand = float(bill_data['尖峰需量']) if bill_data['尖峰需量'] is not None else 0
                                  if contract[0] > 0 and peak_demand > contract[0]:
                                      over_amount = float(peak_demand - contract[0])
                                      max_over_demand = over_amount
                                      penalty = calculate_penalty(peak_demand, float(contract[0]), float(frequent_contract))
                                      total_penalty += penalty
                                      formula_steps.append(f"{peak_demand:.2f} * {contract[0]:.4f}")
                                  
                                  # 檢查半尖峰時段超約
                                  semi_peak_demand = float(bill_data['半尖峰需量']) if bill_data['半尖峰需量'] is not None else 0    
                                  if contract[1] > 0 and semi_peak_demand > contract[1]:
                                      over_amount = float(semi_peak_demand - contract[1])
                                      if over_amount > max_over_demand:
                                          penalty = calculate_penalty(semi_peak_demand - max_over_demand, float(contract[1]), float(semi_peak_contract))
                                          total_penalty += penalty
                                          max_over_demand = over_amount
                                          formula_steps.append(f"{semi_peak_demand:.2f} * {contract[1]:.4f}")
                                  
                                  # 檢查離峰時段超約
                                  off_peak_demand = float(bill_data['離峰需量']) if bill_data['離峰需量'] is not None else 0 
                                  if contract[2] > 0 and off_peak_demand > contract[2]:
                                      over_amount = float(off_peak_demand - contract[2])
                                      if over_amount > max_over_demand:
                                          penalty = calculate_penalty(off_peak_demand - max_over_demand, float(contract[2]), float(off_peak_contract))
                                          total_penalty += penalty
                                          max_over_demand = over_amount
                                          formula_steps.append(f"{off_peak_demand:.2f} * {contract[2]:.4f}")
                                  
                                  # 檢查週六需量超約
                                  saturday_demand = float(bill_data['週六需量']) if bill_data['週六需量'] is not None else 0
                                  if contract[3] > 0 and saturday_demand > contract[3]:
                                      over_amount = float(saturday_demand - contract[3])
                                      if over_amount > max_over_demand:
                                          penalty = calculate_penalty(saturday_demand - max_over_demand, float(contract[3]), float(saturday_contract))
                                          total_penalty += penalty
                                          max_over_demand = over_amount
                                          formula_steps.append(f"{saturday_demand:.2f} * {contract[3]:.4f}")
                                  
                                  # 計算總電費
                                  total_bill = total_contract_fee + total_penalty
                        else:
                                # 取得契約容量費率
                                frequent_contract = price_data[type_code]['Frequent_contract'][rate_index]
                                semi_peak_contract = price_data[type_code]['Non_Summer_contract']
                                saturday_contract = price_data[type_code]['Saturday_contract'][rate_index]
                                off_peak_contract = price_data[type_code]['Off_Peak_contract'][rate_index]

                                # 計算各時段契約容量電費
                                if contract_peak > 0:  # 經常契約容量
                                    frequent_fee = contract_peak * frequent_contract
                                    formula_steps.append(f"{contract_peak:.2f} * {frequent_contract:.4f}")
                                if contract_semipeak > 0:  # 半尖峰契約容量
                                    semi_peak_fee = contract_semipeak * semi_peak_contract
                                    formula_steps.append(f"{contract_semipeak:.2f} * {semi_peak_contract:.4f}")
                                if contract_saturday > 0:  # 週六契約容量 
                                    saturday_fee = contract_saturday * saturday_contract
                                    formula_steps.append(f"{contract_saturday:.2f} * {saturday_contract:.4f}")
                                if contract_offpeak > 0:  # 離峰契約容量
                                    off_peak_fee = contract_offpeak * off_peak_contract

                                total_contract_fee = frequent_fee + semi_peak_fee + saturday_fee + off_peak_fee
                                
                                # 計算超約附加費
                                total_penalty = 0
                                max_over_demand = 0
                                
                                # 檢查尖峰時段超約
                                peak_demand = float(bill_data['尖峰需量']) if bill_data['尖峰需量'] is not None else 0
                                if contract[0] > 0 and peak_demand > contract[0]:
                                    over_amount = float(peak_demand - contract[0])
                                    max_over_demand = over_amount
                                    penalty = calculate_penalty(peak_demand, float(contract[0]), float(frequent_contract))
                                    total_penalty += penalty
                                    formula_steps.append(f"{peak_demand:.2f} * {contract[0]:.4f}")
                                # 檢查半尖峰時段超約
                                semi_peak_demand = float(bill_data['半尖峰需量']) if bill_data['半尖峰需量'] is not None else 0    
                                if contract[1] > 0 and semi_peak_demand > contract[1]:
                                    over_amount = float(semi_peak_demand - contract[1])
                                    if over_amount > max_over_demand:  # 只計算超過前一個最大超約量的部分
                                        penalty = calculate_penalty(semi_peak_demand - max_over_demand, float(contract[1]), float(semi_peak_contract))
                                        total_penalty += penalty
                                        max_over_demand = over_amount

                                # 檢查離峰時段超約
                                off_peak_demand = float(bill_data['離峰需量']) if bill_data['離峰需量'] is not None else 0 
                                if contract[2] > 0 and off_peak_demand > contract[2]:
                                    over_amount = float(off_peak_demand - contract[2])
                                    if over_amount > max_over_demand:  # 只計算超過前一個最大超約量的部分
                                        penalty = calculate_penalty(off_peak_demand - max_over_demand, float(contract[2]), float(off_peak_contract))
                                        total_penalty += penalty
                                        max_over_demand = over_amount
                                        formula_steps.append(f"{off_peak_demand:.2f} * {contract[2]:.4f}")
                                # 檢查週六需量超約
                                saturday_demand = float(bill_data['週六需量']) if bill_data['週六需量'] is not None else 0
                                if contract[3] > 0 and saturday_demand > contract[3]:
                                    over_amount = float(saturday_demand - contract[3])
                                    if over_amount > max_over_demand:  # 只計算超過前一個最大超約量的部分
                                        penalty = calculate_penalty(saturday_demand - max_over_demand, float(contract[3]), float(saturday_contract))
                                        total_penalty += penalty
                                        max_over_demand = over_amount
                                        formula_steps.append(f"{saturday_demand:.2f} * {contract[3]:.4f}")
                                
                        

                        usage_fee = total_energy_cost
                        contract_fee = 0
                        penalty_fee = 0
                        pf_fee = 0
                       
                
                # 5. 三段式時間電價類型
                elif type_code in ['A300', 'A301', 'C300', 'C301',
                                 'D300', 'D301', 'D302', 'E300', 'E301', 'E302',
                                 'D310', 'D311', 'D312', 'E310', 'E311', 'E312']:
    
                    
            
                        #print("\n【用電成本分析】")
                        peak_kwh = float(bill_data['尖峰用電量'] or 0)
                        semi_peak_kwh = float(bill_data['半尖峰用電量'] or 0)
                        off_peak_kwh = float(bill_data['離峰用電量'] or 0)
                        saturday_kwh = float(bill_data['週六用電量'] or 0)
                        total_kwh = float(bill_data['總用電量'])
                        
                        # 根據夏月/非夏月計算費用
                        if rate_index == 0:  # 夏月
                            peak_fee = peak_kwh * price_data[type_code]['peak_summer']  # 夏月尖峰費率
                            semi_peak_fee = semi_peak_kwh * price_data[type_code]['semi_peak_summer']  # 夏月半尖峰費率
                            off_peak_fee = off_peak_kwh * price_data[type_code]['off_peak'][rate_index]
                            saturday_fee = saturday_kwh * price_data[type_code]['Saturday_Half_Peak'][rate_index]
                            total_fee = peak_fee + semi_peak_fee + off_peak_fee + saturday_fee
                            formula_steps.append(f"{peak_kwh:.2f} * {price_data[type_code]['peak_summer']:.4f}")
                            formula_steps.append(f"{semi_peak_kwh:.2f} * {price_data[type_code]['semi_peak_summer']:.4f}")
                            formula_steps.append(f"{off_peak_kwh:.2f} * {price_data[type_code]['off_peak'][rate_index]:.4f}")
                            formula_steps.append(f"{saturday_kwh:.2f} * {price_data[type_code]['Saturday_Half_Peak'][rate_index]:.4f}")
                            #print(f"尖峰用電費用: {peak_fee:.2f}")
                            #print(f"半尖峰用電費用: {semi_peak_fee:.2f}")
                            #print(f"離峰用電費用: {off_peak_fee:.2f}")
                            #print(f"週六用電費用: {saturday_fee:.2f}")
                            #print(f"總電費: {total_fee:.2f}")
                        else:  # 非夏月
                            # 判斷使用尖峰或半尖峰費率
                            if peak_kwh > 0:
                                semi_peak_fee = peak_kwh * price_data[type_code]['semi_peak_non_summer']  # 非夏月尖峰費率
                            else:
                                semi_peak_fee = semi_peak_kwh * price_data[type_code]['semi_peak_non_summer']  # 非夏月半尖峰費率
                            off_peak_fee = off_peak_kwh * price_data[type_code]['off_peak'][rate_index]
                            saturday_fee = saturday_kwh * price_data[type_code]['Saturday_Half_Peak'][rate_index]
                            total_fee = semi_peak_fee + off_peak_fee + saturday_fee
                            formula_steps.append(f"{peak_kwh:.2f} * {price_data[type_code]['semi_peak_non_summer']:.4f}")
                            formula_steps.append(f"{off_peak_kwh:.2f} * {price_data[type_code]['off_peak'][rate_index]:.4f}")
                            formula_steps.append(f"{saturday_kwh:.2f} * {price_data[type_code]['Saturday_Half_Peak'][rate_index]:.4f}") 
                            #print(f"半尖峰用電費用: {semi_peak_fee:.2f}")
                            #print(f"離峰用電費用: {off_peak_fee:.2f}")
                            #print(f"週六用電費用: {saturday_fee:.2f}")
                            #print(f"總電費: {total_fee:.2f}")
                            
                        if type_code in ['C300', 'C301', 'D300', 'D301', 'D302', 'E300', 'E301', 'E302',
                                        'D310', 'D311', 'D312', 'E310', 'E311', 'E312']:
                             #print("\n【三段式時間電價分析】")
                             
                             frequent_fee = 0
                             semi_peak_fee = 0
                             saturday_fee = 0
                             off_peak_fee = 0
                             total_contract_fee = 0
                             
                             # 取得契約容量費率
                             frequent_contract = price_data[type_code]['Frequent_contract'][rate_index]
                             semi_peak_contract = price_data[type_code]['semi_Peak_contract'][rate_index]
                             saturday_contract = price_data[type_code]['Saturday_contract'][rate_index]
                             off_peak_contract = price_data[type_code]['Off_Peak_contract'][rate_index]

                             # 計算各時段契約容量電費
                             if contract_peak > 0:  # 經常契約容量
                                 frequent_fee = contract_peak * frequent_contract
                                 formula_steps.append(f"{contract_peak:.2f} * {frequent_contract:.4f}")
                             if contract_semipeak > 0:  # 半尖峰契約容量
                                 semi_peak_fee = contract_semipeak * semi_peak_contract
                                 formula_steps.append(f"{contract_semipeak:.2f} * {semi_peak_contract:.4f}")
                             if contract_saturday > 0:  # 週六契約容量 
                                 saturday_fee = contract_saturday * saturday_contract
                                 formula_steps.append(f"{contract_saturday:.2f} * {saturday_contract:.4f}")
                             if contract_offpeak > 0:  # 離峰契約容量
                                 off_peak_fee = contract_offpeak * off_peak_contract
                                 formula_steps.append(f"{contract_offpeak:.2f} * {off_peak_contract:.4f}")
                             
                             total_contract_fee = frequent_fee + semi_peak_fee + saturday_fee + off_peak_fee 
                             
                             # 計算超約附加費
                             total_penalty = 0
                             
                             # 檢查尖峰時段超約
                             peak_demand = float(bill_data['尖峰需量']) if bill_data['尖峰需量'] is not None else 0
                             if contract[0] > 0 and peak_demand > contract[0]:
                                 over_amount = float(peak_demand - contract[0])
                                 max_over_demand = over_amount
                                 penalty = calculate_penalty(peak_demand, float(contract[0]), float(frequent_contract))
                                 total_penalty += penalty
                                 formula_steps.append(f"{peak_demand:.2f} * {contract[0]:.4f}")
                             
                             # 檢查半尖峰時段超約
                             semi_peak_demand = float(bill_data['半尖峰需量']) if bill_data['半尖峰需量'] is not None else 0    
                             if contract[1] > 0 and semi_peak_demand > contract[1]:
                                 over_amount = float(semi_peak_demand - contract[1])
                                 if over_amount > max_over_demand:
                                     penalty = calculate_penalty(semi_peak_demand - max_over_demand, float(contract[1]), float(semi_peak_contract))
                                     total_penalty += penalty
                                     max_over_demand = over_amount
                                     formula_steps.append(f"{semi_peak_demand:.2f} * {contract[1]:.4f}")
                             
                             # 檢查離峰時段超約
                             off_peak_demand = float(bill_data['離峰需量']) if bill_data['離峰需量'] is not None else 0 
                             if contract[2] > 0 and off_peak_demand > contract[2]:
                                 over_amount = float(off_peak_demand - contract[2])
                                 if over_amount > max_over_demand:
                                     penalty = calculate_penalty(off_peak_demand - max_over_demand, float(contract[2]), float(off_peak_contract))
                                     total_penalty += penalty
                                     max_over_demand = over_amount
                                     formula_steps.append(f"{off_peak_demand:.2f} * {contract[2]:.4f}")
                             
                             # 檢查週六需量超約
                             saturday_demand = float(bill_data['週六需量']) if bill_data['週六需量'] is not None else 0
                             if contract[3] > 0 and saturday_demand > contract[3]:
                                 over_amount = float(saturday_demand - contract[3])
                                 if over_amount > max_over_demand:
                                     penalty = calculate_penalty(saturday_demand - max_over_demand, float(contract[3]), float(saturday_contract))
                                     total_penalty += penalty
                                     max_over_demand = over_amount
                                     formula_steps.append(f"{saturday_demand:.2f} * {contract[3]:.4f}")
                             
                             # 計算總電費
                             total_bill = total_contract_fee + total_penalty
                             
                        elif type_code in ['A300', 'A301']:
                             #print("\n【三段式時間電價分析】")   
                             
                             frequent_fee = 0
                             semi_peak_fee = 0
                             saturday_fee = 0
                             off_peak_fee = 0
                             total_contract_fee = 0
                             
                             base_unit = price_data[type_code]['Base_Unit']  # 一般基本電費
                             base_three = price_data[type_code]['Base_Three']
                             
                             # 取得契約容量費率
                             frequent_contract = price_data[type_code]['Frequent_contract'][rate_index]
                             semi_peak_contract = price_data[type_code]['Half_Peak_contract'][rate_index]
                             saturday_contract = price_data[type_code]['Saturday_contract'][rate_index]
                             off_peak_contract = price_data[type_code]['Off_Peak_contract'][rate_index]

                             # 計算各時段契約容量電費
                             if contract_peak > 0:  # 經常契約容量
                                 frequent_fee = contract_peak * frequent_contract
                                 formula_steps.append(f"{contract_peak:.2f} * {frequent_contract:.4f}")
                             if contract_semipeak > 0:  # 半尖峰契約容量
                                 semi_peak_fee = contract_semipeak * semi_peak_contract
                                 formula_steps.append(f"{contract_semipeak:.2f} * {semi_peak_contract:.4f}")
                             if contract_saturday > 0:  # 週六契約容量 
                                 saturday_fee = contract_saturday * saturday_contract
                                 formula_steps.append(f"{contract_saturday:.2f} * {saturday_contract:.4f}")
                             if contract_offpeak > 0:  # 離峰契約容量
                                 off_peak_fee = contract_offpeak * off_peak_contract
                                 formula_steps.append(f"{contract_offpeak:.2f} * {off_peak_contract:.4f}")
                             
                             total_contract_fee = frequent_fee + semi_peak_fee + saturday_fee + off_peak_fee + base_unit
                             
                             # 計算超約附加費
                             total_penalty = 0
                             
                             # 檢查尖峰時段超約
                             peak_demand = float(bill_data['尖峰需量']) if bill_data['尖峰需量'] is not None else 0
                             if contract[0] > 0 and peak_demand > contract[0]:
                                 over_amount = float(peak_demand - contract[0])
                                 max_over_demand = over_amount
                                 penalty = calculate_penalty(peak_demand, float(contract[0]), float(frequent_contract))
                                 total_penalty += penalty
                                 formula_steps.append(f"{peak_demand:.2f} * {contract[0]:.4f}")
                             
                             # 檢查半尖峰時段超約
                             semi_peak_demand = float(bill_data['半尖峰需量']) if bill_data['半尖峰需量'] is not None else 0    
                             if contract[1] > 0 and semi_peak_demand > contract[1]:
                                 over_amount = float(semi_peak_demand - contract[1])
                                 if over_amount > max_over_demand:
                                     penalty = calculate_penalty(semi_peak_demand - max_over_demand, float(contract[1]), float(semi_peak_contract))
                                     total_penalty += penalty
                                     max_over_demand = over_amount
                                     formula_steps.append(f"{semi_peak_demand:.2f} * {contract[1]:.4f}")
                            
                            
                
                # 6. 低壓非時間電力用電類型
                elif type_code in ['C000', 'C001']:  # 需量電力非營業用/需量綜合非營業用/低壓非時
                    #print("\n【低壓非時間電力用電分析】")
                    
                    # 取得用電量
                    total_kwh = float(bill_data['總用電量'] or 0)
                    
                    # 取得費率
                    peak_rate = price_data[type_code]['Peak']
                    
                    # 根據夏月/非夏月計算費用
                    if rate_index == 0:  # 夏月
                        total_fee = total_kwh * peak_rate[0]    
                        formula_steps.append(f"{total_kwh:.2f} * {peak_rate[0]:.4f}")
                        #print(f"夏月用電: {total_kwh:.2f} 度 × {peak_rate[0]:.4f} 元 = {total_fee:.2f} 元")
                    else:  # 非夏月
                        total_fee = total_kwh * peak_rate[1] 
                        formula_steps.append(f"{total_kwh:.2f} * {peak_rate[1]:.4f}")
                        #print(f"非夏月用電: {total_kwh:.2f} 度 × {peak_rate[1]:.4f} 元 = {total_fee:.2f} 元")
                    
                    #print(f"\n流動電費總計: {total_fee:.2f} 元")
                    
                    # 更新各項費用
                    usage_fee = total_fee
                    contract_fee = 0
                    penalty_fee = 0
                    pf_fee = 0
                    
                    frequent_fee = 0
                    semi_peak_fee = 0
                    total_contract_fee = 0
                    
                    # 從用電類型判斷是否為裝置
                    electricity_type = bill_data['用電類型']
                    is_device = '裝置' in electricity_type
                    if is_device:
                        #print("【裝置用電】")
                        base_device = price_data[type_code]['Device']  # 裝置基本電費
                        frequent_fee = contract_peak * base_device
                        total_contract_fee = frequent_fee
                        #print(f"裝置用電基本電費計算: {contract_peak} kW × {base_device} 元/kW = {frequent_fee:.2f} 元")
                        #print(f"總契約容量電費: {total_contract_fee:.2f} 元")
                        formula_steps.append(f"{contract_peak:.2f} * {base_device:.4f}")
                    else:
                        frequent_contract = price_data[type_code]['Frequent'][rate_index]
                        semi_peak_contract = price_data[type_code]['Non_Summer_Month']

                        # 計算各時段契約容量電費
                        if contract_peak > 0:  # 經常契約容量
                            frequent_fee = contract_peak * frequent_contract
                            #print(f"經常契約容量計算: {contract_peak} kW × {frequent_contract} 元/kW = {frequent_fee:.2f} 元")
                            formula_steps.append(f"{contract_peak:.2f} * {frequent_contract:.4f}")
                        if contract_semipeak > 0:  # 半尖峰契約容量
                            semi_peak_fee = contract_semipeak * semi_peak_contract
                            #print(f"半尖峰契約容量計算: {contract_semipeak} kW × {semi_peak_contract} 元/kW = {semi_peak_fee:.2f} 元")
                            formula_steps.append(f"{contract_semipeak:.2f} * {semi_peak_contract:.4f}")
                        #print(f"契約容量電費計算: {frequent_fee:.2f} + {semi_peak_fee:.2f} = {total_contract_fee:.2f} 元")
                        total_contract_fee = frequent_fee + semi_peak_fee 
                        #print(f"總契約容量電費: {total_contract_fee:.2f} 元")
                        
                        
                         # 計算超約附加費
                        total_penalty = 0
                        
                        # 檢查尖峰時段超約
                        peak_demand = float(bill_data['尖峰需量']) if bill_data['尖峰需量'] is not None else 0
                        if contract[0] > 0 and peak_demand > contract[0]:
                            over_amount = float(peak_demand - contract[0])
                            max_over_demand = over_amount
                            penalty = calculate_penalty(peak_demand, float(contract[0]), float(frequent_contract))
                            total_penalty += penalty
                            formula_steps.append(f"{peak_demand:.2f} * {contract[0]:.4f}")
                        # 檢查半尖峰時段超約
                        semi_peak_demand = float(bill_data['半尖峰需量']) if bill_data['半尖峰需量'] is not None else 0    
                        if contract[1] > 0 and semi_peak_demand > contract[1]:
                            over_amount = float(semi_peak_demand - contract[1])
                            if over_amount > max_over_demand:  # 只計算超過前一個最大超約量的部分
                                penalty = calculate_penalty(semi_peak_demand - max_over_demand, float(contract[1]), float(semi_peak_contract))
                                total_penalty += penalty
                                max_over_demand = over_amount
                                formula_steps.append(f"{semi_peak_demand:.2f} * {contract[1]:.4f}")
                        # 檢查離峰時段超約
                        off_peak_demand = float(bill_data['離峰需量']) if bill_data['離峰需量'] is not None else 0 
                        if contract[2] > 0 and off_peak_demand > contract[2]:
                            over_amount = float(off_peak_demand - contract[2])
                            if over_amount > max_over_demand:  # 只計算超過前一個最大超約量的部分
                                penalty = calculate_penalty(off_peak_demand - max_over_demand, float(contract[2]), float(off_peak_contract))
                                total_penalty += penalty
                                max_over_demand = over_amount
                                formula_steps.append(f"{off_peak_demand:.2f} * {contract[2]:.4f}")
                            
                        #print(f"\n總超約附加費: {total_penalty:.2f} 元")        
                    
                    
                    
                
               
     

                
                # 功率因數
                power_factor = bill_data.get('pf', '無資料')
                #print(f"\n功率因數: {power_factor}")
            
            
        
                    
                    
                
            except Exception as e:
                print(f"查詢電價資料時發生錯誤: {str(e)}")
                return 0
        
        
        pf_fee = bill_data['功率因數費']
  
        elc_total_fee=0
       # total_fee = float(total_fee)
        total_contract_fee = float(total_contract_fee)
        total_penalty = float(total_penalty)
        pf_fee = float(pf_fee) if pf_fee is not None else 0.0
        
        # 計算總電費
        elc_total_fee = usage_fee+ total_contract_fee + total_penalty + pf_fee
        
        # 顯示費用明細
        #print("\n=== 電費明細 ===")
        #print(f"用電電費: {usage_fee:.2f} 元")
        #print(f"契約電費: {total_contract_fee:.2f} 元")
        #print(f"超約電費: {total_penalty:.2f} 元")
        #print(f"功率因數費: {pf_fee:.2f} 元")
        #print(f"總電費: {elc_total_fee:.2f} 元")
        
        return elc_total_fee, formula_steps
        
    except Exception as e:
        print(f"電費計算過程發生錯誤: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return 0

def find_usage_type(value, electricity_codes):##尋找用電編碼
    """
    根據給定的值 (value)，從 category_mapping 中找到對應的 key。

    :param value: 需要查找的名稱 (如 '低壓表燈非營業')
    :param category_mapping: 以選項代碼為 key，名稱列表為 value 的字典
    :return: 對應的選項代碼 (如 'A000')，如果找不到則返回 None
    """
    for key, values in electricity_codes.items():
        if value in values:
            return key
    return None  # 若找不到則回傳 None


def analyze_electricity_bill(place_id, year_month, use_latest_price=False):
    """
    整合電費帳單分析流程
    :param place_id: 場所編號（電號）
    :param year_month: 年月，格式為 'YYYYMM'
    :param use_latest_price: 是否只使用最新電價，True表示只用最新電價，False表示使用對應時期電價
    """
    try:
        # 1. 獲取帳單資料
        bill = get_bill_by_place_and_date(place_id, year_month)
        if not bill:
            print(f"\n找不到 {place_id} 在 {year_month} 的帳單資料")
            return
            
        
        # 2. 獲取電費期間與天數
        start_date = bill['計費起始日']
        end_date = bill['計費結束日']
        
        formatted_start = start_date.strftime("%Y-%m-%d")
        formatted_end = end_date.strftime("%Y-%m-%d")
        
        if use_latest_price:
            # 使用最新電價進行分析
            latest_price_date = HISTORY_DATES[0]  # 取得最新的電價日期
            total_days = (end_date - start_date).days + 1
            
            # 建立單一區間的價格資訊
            price_info = {
                "periods": [
                    {
                        "price_date": latest_price_date,
                        "start_date": formatted_start,
                        "end_date": formatted_end,
                        "days": total_days,
                        "found": True  # 假設最新電價一定存在
                    }
                ],
                "total_days": total_days
            }
            print("\n=== 使用最新電價進行分析 ===")
        else:
            # 使用對應時期的電價進行分析
            price_info = get_price_dates_for_range(formatted_start, formatted_end)
            print("\n=== 使用歷史電價進行分析 ===")
        
        # 顯示電價期間資訊
        print(f"\n計費期間: {formatted_start} 到 {formatted_end}")
        print(f"總天數: {price_info['total_days']} 天")
        print("\n電價使用區間:")
        
        # 3. 分析每個區間的用電資訊
        for period in price_info["periods"]:
            print(f"\n=== 分析區間: {period['start_date']} 到 {period['end_date']} ===")
            print(f"使用電價生效日: {period['price_date']}")
            print(f"區間天數: {period['days']} 天")
            print(f"電價資料: {'已找到' if period['found'] else '未找到'}")
            
            # 對每個區間進行分析
            electricity_usage_cel(bill, period['price_date'], period['days'])
        
    except Exception as e:
        print(f"分析過程發生錯誤: {str(e)}")
        import traceback
        print(traceback.format_exc())
        
def Pushback_baseline_cost(place_id, year_month, price_date):
    """
    計算指定電號在特定月份的夏月/非夏月電費和總用電度數
    
    :param place_id: 場所編號（電號）
    :param year_month: 年月，格式為 'YYYYMM'
    :param price_date: 台電計價日期
    :return: (夏月電費, 非夏月電費, 總用電度數, 夏月每度電費, 非夏月每度電費, 最低使用度數) 的元組
    """
    try:
        # 獲取帳單資料
        bill = get_bill_by_place_and_date(place_id, year_month)
        print(bill)
        if not bill:
            print(f"\n找不到 {place_id} 在 {year_month} 的帳單資料")
            return None, None, None, None, None, None
            
        # 獲取電價資料
        price_data = mongo.db.electricity_price.find_one(
            {"Taipower_Pricing_Effective_Date": price_date}
        )
        if not price_data:
            print(f"\n找不到 {price_date} 的電價資料")
            return None, None, None, None, None, None
            
        # 獲取總用電度數和計算最低使用度數
        total_kwh = float(bill['總用電量']) if bill['總用電量'] is not None else 0
        min_kwh = round(total_kwh * 0.8, 2)  # 計算最低使用度數（總用電量的80%）
            
        # 計算夏月電費
        print("\n=== 計算夏月電費 ===")
        summer_bill = bill.copy()
        
        summer_bill['計費起始日'] = summer_bill['計費起始日'].replace(month=7)  # 設為夏月(7月)
        summer_cost = electricity_usage_cel(summer_bill, price_date)
        print(summer_cost)
        
        # 計算非夏月電費
        print("\n=== 計算非夏月電費 ===")
        non_summer_bill = bill.copy()
        non_summer_bill['計費起始日'] = non_summer_bill['計費起始日'].replace(month=11)  # 設為非夏月(11月)
        non_summer_cost = electricity_usage_cel(non_summer_bill, price_date)
        
        # 計算每度電費
        summer_per_kwh = round(summer_cost/total_kwh, 2) if total_kwh > 0 else 0
        non_summer_per_kwh = round(non_summer_cost/total_kwh, 2) if total_kwh > 0 else 0
        
        # 顯示結果
        print("\n=== 電費比較 ===")
        print(f"夏月電費: {summer_cost:.2f} 元")
        print(f"非夏月電費: {non_summer_cost:.2f} 元")
        print(f"總用電量: {total_kwh:.2f} 度")
        print(f"最低使用度數: {min_kwh:.2f} 度")
        print(f"夏月每度電費: {summer_per_kwh:.2f} 元/度")
        print(f"非夏月每度電費: {non_summer_per_kwh:.2f} 元/度")
        
        return summer_cost, non_summer_cost, total_kwh, summer_per_kwh, non_summer_per_kwh, min_kwh
        
    except Exception as e:
        print(f"計算電費時發生錯誤: {str(e)}")
        return None, None, None, None, None, None
def Year_baseline_cost(start_ym, end_ym, place_id, price_date):
    """
    分析指定期間的夏月和非夏月用電資訊
    
    :param start_ym: 起始年月，格式為 'YYYYMM'
    :param end_ym: 結束年月，格式為 'YYYYMM'
    :param place_id: 場所編號（電號）
    :param price_date: 採用的電價日期
    :return: (夏月電費列表, 夏月度數列表, 非夏月電費列表, 非夏月度數列表, 夏月每度平均電費, 非夏月每度平均電費, 最低使用度數, 月份記錄)
    """
    try:
        # 初始化統計數據
        summer_total_cost = 0
        summer_total_kwh = 0
        non_summer_total_cost = 0
        non_summer_total_kwh = 0
        min_monthly_kwh = float('inf')
        
        # 初始化月份資料列表
        summer_costs = []
        summer_kwhs = []
        non_summer_costs = []
        non_summer_kwhs = []
        
        # 新增：月份記錄列表
        monthly_records = []
        
        # 將年月字串轉換為日期對象
        start_date = datetime.strptime(start_ym, "%Y%m")
        end_date = datetime.strptime(end_ym, "%Y%m")
        
        print(f"\n分析期間: {start_ym} 到 {end_ym}")
        
        # 遍歷每個月份
        current_date = start_date
        while current_date <= end_date:
            year_month = current_date.strftime("%Y%m")
            
            # 獲取該月份的帳單資料
            bill = get_bill_by_place_and_date(place_id, year_month)
            if bill:
                # 計算該月的電費和用電量
                bill_cost, formula_steps = electricity_usage_cel(bill, price_date)
                bill_kwh = float(bill['總用電量']) if bill['總用電量'] is not None else 0
                
                # 更新最小月用電量
                if bill_kwh > 0:
                    min_monthly_kwh = min(min_monthly_kwh, bill_kwh)
                
                # 判斷是否為夏月(6-9月)
                month = current_date.month
                is_summer = 6 <= month <= 9
                
                # 新增：記錄當月資訊，包含更多帳單資訊
                monthly_record = {
                    'year_month': year_month,
                    'cost': round(bill_cost, 2),
                    'kwh': round(bill_kwh, 2),
                    'formula_steps': formula_steps,
                    'is_summer': is_summer,
                    
                    # Basic Information
                    'place_id': bill['場所編號'],
                    'electricity_type': bill['用電類型'], 
                    'bill_month': bill['帳單年月'],
                    'days_used': bill['用電天數'],
                    
                    # Usage Information
                    'total_kwh': bill['總用電量'],
                    'daily_kwh': bill['每日用電量'],
                    'min_kwh': bill['最小用電量'],
                    'peak_kwh': bill['尖峰用電量'],
                    'semi_peak_kwh': bill['半尖峰用電量'],
                    'off_peak_kwh': bill['離峰用電量'],
                    'saturday_kwh': bill['週六用電量'],
                    
                    # Contract Capacity
                    'contract_peak': bill['契約尖峰'],
                    'contract_semi_peak': bill['契約半尖峰'],
                    'contract_off_peak': bill['契約離峰'],
                    'contract_saturday': bill['契約週六'],
                    
                    # Demand Information
                    'peak_demand': bill['尖峰需量'],
                    'semi_peak_demand': bill['半尖峰需量'],
                    'off_peak_demand': bill['離峰需量'],
                    'saturday_demand': bill['週六需量'],
                    
                    # Fee Details
                    'total_fee': bill['總金額'],
                    'demand_fee': bill['需量電費'],
                    'penalty_fee': bill['超約附加費'],
                    'usage_fee': bill['用電電費'],
                    'power_factor_fee': bill['功率因數費'],
                    'public_fee': bill['公共用電費']
                }
                monthly_records.append(monthly_record)
                
                # 根據季節存入對應列表
                if is_summer:
                    summer_costs.append(bill_cost)
                    summer_kwhs.append(bill_kwh)
                    summer_total_cost += bill_cost
                    summer_total_kwh += bill_kwh
                else:
                    non_summer_costs.append(bill_cost)
                    non_summer_kwhs.append(bill_kwh)
                    non_summer_total_cost += bill_cost
                    non_summer_total_kwh += bill_kwh
                
                print(f"\n{year_month} 用電資訊:")
                print(f"電費: {bill_cost:.2f} 元")
                print(f"用電量: {bill_kwh:.2f} 度")
                print(f"季節: {'夏月' if is_summer else '非夏月'}")
            
            # 移至下一個月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        # 計算夏月和非夏月的平均每度電費
        summer_avg_cost = round(summer_total_cost / summer_total_kwh, 2) if summer_total_kwh > 0 else 0
        non_summer_avg_cost = round(non_summer_total_cost / non_summer_total_kwh, 2) if non_summer_total_kwh > 0 else 0
        
        # 如果沒有找到最小用電量數據，設為0
        if min_monthly_kwh == float('inf'):
            min_monthly_kwh = 0
        
        # 返回結果
        return (
            summer_costs, 
            summer_kwhs, 
            non_summer_costs, 
            non_summer_kwhs, 
            summer_avg_cost, 
            non_summer_avg_cost, 
            min_monthly_kwh,
            monthly_records
        )
            
    except Exception as e:
        print(f"年度基線計算錯誤: {str(e)}")
        return [], [], [], [], 0, 0, 0, []

def import_electricity_price():
    """檢查並匯入電價資料"""
    try:
        price_count = mongo.db.electricity_price.count_documents({})
        print(f"\n=== 電價資料統計 ===")
        print(f"電價資料總筆數: {price_count} 筆")
        
        if price_count == 0:
            price_dates = ["20241026", "20240401", "20231001", "20230401", 
                         "20220701", "20180401", "20160401", "20151001", "20150401"]
            
            for date in price_dates:
                json_path = os.path.join(os.path.dirname(__file__), 'electricity_price', f'{date}.json')
                
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        price_data = json.load(f)
                        # 直接插入 MongoDB
                        mongo.db.electricity_price.insert_one(price_data)
                        print(f"已匯入 {date} 的電價資料")
                        
                except FileNotFoundError:
                    print(f"找不到 {date}.json 檔案")
                    continue
                except json.JSONDecodeError:
                    print(f"{date}.json 檔案格式錯誤")
                    continue
                except Exception as e:
                    print(f"處理 {date}.json 時發生錯誤: {str(e)}")
                    continue
                    
            if mongo.db.electricity_price.count_documents({}) == 0:
                return False, "無法匯入任何電價資料!"
                
        return True, "電價資料檢查完成"
                
    except Exception as e:
        print(f"查詢或更新電價資料時發生錯誤: {str(e)}")
        return False, f"查詢或更新電價資料失敗: {str(e)}"
    
    
'''
                        
