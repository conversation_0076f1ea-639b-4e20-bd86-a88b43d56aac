#paper {
  position: absolute;
  top: 60px; // 確保畫布不被 toolbar 和 fixed-text 遮擋
  left: 60px; // 與 stencil 對齊
  right: 0;
  bottom: 0;
  z-index: 100; // 確保比 offcanvas 低
}

.sidebar-wrapper {
  height: 100vh;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  background-color: #2c3e50;
}

.sidebar-icon-bar {
  width: 60px;
  background-color: #345830;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 1rem;
}

.sidebar-icon {
  width: 100%;
  height: 60px;
  color: white;
  border: none;
  background: none;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  cursor: pointer;
}

.sidebar-icon.active {
  background-color: #1abc9c;
}

.sidebar-dropdown {
  background-color: #406640;
  padding: 1rem;
  top: 60px;
  min-width: 180px;
  color: white;
  height: 100vh;
  z-index: 1150;
}


.custom-header {
  background-color: #345830 !important;
  color: white !important;
  height: 60px !important;
  border: none !important;
  width: calc(100% - 60px); /* 依 sidebar 寬度調整 */
  margin-left: 60px;
}

:host ::ng-deep .joint-inspector.joint-theme-modern {
  color: #ffffff;
}

:host ::ng-deep .joint-stencil.joint-theme-modern .group>.group-label {
  padding: 0 5px 0 34px;
  background: #345830;
}
:host ::ng-deep .joint-stencil.joint-theme-modern .group>.group-label:hover, .joint-stencil.joint-theme-modern .groups-toggle>.group-label:hover {
  color: #c4e7d5;
}
:host ::ng-deep .joint-stencil.joint-theme-modern .group>.group-label, .joint-stencil.joint-theme-modern .groups-toggle>.group-label {
  color: #94ECBE;
}

.fixed-text {
  position: fixed;  /* 固定位置 */
  top: 70px;     /* 距離螢幕底部 10px */
  right: 10px;      /* 距離螢幕右側 10px */
  background: rgba(0, 0, 0, 0.7); /* 半透明背景 */
  color: white;     /* 文字顏色 */
  padding: 5px 10px; /* 內距 */
  border-radius: 5px; /* 圓角 */
  font-size: 20px;  /* 字體大小 */
  z-index: 200;
}

.fixed-text2 {
  position: fixed;  /* 固定位置 */
  top: 70px;     /* 距離螢幕底部 10px */
  left: 70px;      /* 距離螢幕右側 10px */
  background: rgba(0, 0, 0, 0.7); /* 半透明背景 */
  color: white;     /* 文字顏色 */
  padding: 5px 10px; /* 內距 */
  border-radius: 5px; /* 圓角 */
  font-size: 20px;  /* 字體大小 */
  z-index: 200;
}



.list-group-item {
  border: none; // 移除邊框
  background-color: transparent; // 移除背景色
  border-bottom: 1px solid #dee2e6; // 添加底部的橫線
  padding: 0.75rem 1rem; // 調整內邊距
  cursor: pointer; // 鼠標樣式
  transition: background-color 0.2s ease;
}

.list-group-item:last-child {
  border-bottom: none; // 移除最後一個項目的底部橫線
}

.list-group-item:hover {
  background-color: #f8f9fa; // 滑鼠懸停時的背景色
}
