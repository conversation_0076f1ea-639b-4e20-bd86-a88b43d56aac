<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div>
      <h2 class="mb-0" style="cursor: pointer">
        <strong>探索企業能源應用</strong>
      </h2>
    </div>
  </c-col>
</c-row>
<div class="app-marketplace">

  <!-- 搜尋欄 -->
  <div class="search-container mb-3">
    <div class="input-group">
      <span class="input-group-text bg-white border-end-0">
        <svg cIcon name="cilSearch" size="xl" title="List Icon"></svg>
      </span>
      <input
        type="text"
        class="form-control search-input"
        placeholder="搜尋應用或服務..."
        [(ngModel)]="searchKeyword"
      />
    </div>
  </div>


  <!-- 分類選單 -->
  <div class="marketplace-menu d-flex flex-wrap gap-2 mb-4">
    <button
      class="menu-button"
      *ngFor="let item of menuItems"
      [class.active]="selectedMenu === item.value"
      (click)="onMenuChange(item.value)"
    >
      {{ item.label }}
    </button>
  </div>

<!-- 應用清單 -->
<div class="app-list d-flex flex-column gap-3">
  <div
    class="app-item p-3 border rounded shadow-sm hover-shadow-sm transition"
    *ngFor="let app of filtered_apps"
  >
    <div class="d-flex gap-3 align-items-center justify-content-between" (click)="openApp(app.id || 0)">

      <div class="d-flex gap-3 align-items-center">
        <!-- Icon -->
        <svg cIcon [name]="app.icon" class="app-icon" style="width: 32px; height: 32px;"></svg>

        <!-- 資訊 -->
        <div class="flex-grow-1">
          <div class="fw-bold fs-5">{{ app.title }}</div>
          <div class="text-muted small">{{ app.subtitle }}</div>
        </div>
      </div>

      <!-- 改良後的按鈕 -->
      <button
        class="btn btn-sm app-action-btn"
        [ngClass]="
          app.type === 'free' ? 'free-btn' :
          app.type === 'paid' && hasBoundPaymentAccount ? 'paid-btn' :
          app.type === 'paid' && !hasBoundPaymentAccount ? 'disabled-btn' :
          app.type === 'licensed' && !hasBoundPayoutAccount ? 'disabled-btn' :
          'licensed-btn'
        "
        [disabled]="
          (app.type === 'paid' && !hasBoundPaymentAccount) ||
          (app.type === 'licensed' && !hasBoundPayoutAccount)
        "
      >
        {{
          app.type === 'free' ? '取得' :
          app.type === 'paid'
            ? hasBoundPaymentAccount ? '取得' : '設定企業付款帳戶後取得' :
          app.type === 'licensed'
            ? hasBoundPayoutAccount ? '取得' : '設定企業收款帳戶後取得' :
          ''
        }}
      </button>


    </div>
  </div>
</div>
