import { BillAnalysisService } from '../../../service/bill-analysis.service';
import { Component,NgModule,OnInit } from '@angular/core';
import { UserService } from '../../../service/user.service';
import { Router, RouterModule } from '@angular/router';
import { FormGroup, FormsModule, ReactiveFormsModule,FormBuilder,FormControl
 } from '@angular/forms';
import { CommonModule,JsonPipe, NgClass, NgIf  } from '@angular/common';
import{FormCheckComponent,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  FormLabelDirective,
  FormControlDirective,
  RowComponent,
  FormSelectDirective,
  ButtonDirective,
  InputGroupComponent,
  TableModule,
  GridModule,
} from'@coreui/angular'
interface Month {
  value: string;
  label: string;
}
@Component({
  selector: 'app-tpccrawler',
  standalone: true,
  imports: [FormCheckComponent,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    FormLabelDirective,
    FormControlDirective,
    RowComponent,
    FormSelectDirective,
    JsonPipe,
    NgClass,
    NgIf,
    RouterModule,
    ButtonDirective,
    InputGroupComponent,
    TableModule,
    GridModule
  ],
  templateUrl: './tpccrawler.component.html',
  styleUrl: './tpccrawler.component.scss'
})

export class TPCcrawlerComponent {
  customStylesValidated: boolean = false;
  selection1Date : Date = new Date();
  selection2Date : Date;
  selection3Date : Date;
  selection1 :String;
  selection2 :String;
  selection3 :String;
  place_id : string;
  legal_entity_id : string;
  mobile_phone_number : string;
  TPC_online_bills_status :string = '';
  account : string  = '';
  password :string ='';
  telephone_number : string;
  contacter : string;
  authentication : string;
  select_legal_entity_type : string;
  select_authentication_method :string;
  TPCbillsForm : FormGroup;
  errorMessage: String = '';
  successMessage: String = '';
  formErrors :any;
  submitted = false;
  formControls!: string[];
  selectmonths: Month[] = [];
  simpleForm!: FormGroup;
    ngOnInit() : void {
    this.selection2Date = new Date(this.selection1Date);
    this.selection2Date.setMonth(this.selection1Date.getMonth() - 1);
    this.selection3Date = new Date(this.selection1Date);
    this.selection3Date.setMonth(this.selection1Date.getMonth() - 2);
    this.selection1  = `${(this.selection1Date.getFullYear()-1911).toString()}${(this.selection1Date.getMonth()+1).toString().padStart(2,'0')}`;
    this.selection2 = `${(this.selection2Date.getFullYear()-1911).toString()}${(this.selection2Date.getMonth()+1).toString().padStart(2,'0')}`;
    this.selection3 = `${(this.selection3Date.getFullYear()-1911).toString()}${(this.selection3Date.getMonth()+1).toString().padStart(2,'0')}`;

    this.selectmonths = [
      { value: this.selection1, label: this.selection1 },
      { value: this.selection2, label: this.selection2 },
      { value: this.selection3, label: this.selection3 },
    ]as Month[];
  }
  constructor(private fb: FormBuilder, private BillAnalysisService : BillAnalysisService,
   ){

    this.TPCbillsForm = this.fb.group({
      place_id: [''],
      TPC_online_bills_status : [''],
      account : ['********'],
      password :['********'],
      monthselector: [''],
      select_authentication_method:[''],
      authentication: [''],
      select_legal_entity_type : [''],
      legal_entity_id :[''],
      contacter:[''],
      mobile_phone_number:[''],
      telephone_number :[''],
    })
  }

  onSubmit(): void {
      const formData = {
        place_id: this.place_id,
        TPC_online_bills_status: this.TPC_online_bills_status,
        account : this.account,
        password : this.password,
        monthselector : this.selectmonths,
        select_authentication_method : this.select_authentication_method,
        authentication : this.authentication,
        select_legal_entity_type : this.select_legal_entity_type,
        legal_entity_id :this.legal_entity_id,
        contacter : this.contacter,
        mobile_phone_number :this.mobile_phone_number,
        telephone_number : this.telephone_number
      };
      this.BillAnalysisService.submit_request(formData).subscribe(
        (response) => {
          this.successMessage = '請求提交成功';
        },
        (error) => {
          this.errorMessage = '有欄位尚未填寫或勾選欄尚未勾選';
        }
      );
    }
    place_idEvent(event: Event): void {
      const inputElement = event.target as HTMLInputElement;
      const sanitizedValue = inputElement.value.replace(/\D/g, ''); // 清除非數字
      inputElement.value = sanitizedValue.slice(0, 11); // 限制長度
    }

    onlynumberKeyboardEvent(event: KeyboardEvent): void {
      const charCode = event.charCode || event.keyCode;
      // 允許 0-9 的按鍵，其他按鍵阻止
      if (charCode < 48 || charCode > 57) {
        event.preventDefault();
      }
    }

    moneyEvent(event: Event): void {
      const inputElement = event.target as HTMLInputElement;
      let sanitizedValue = inputElement.value.replace(/[^0-9.]/g, ''); // 清除非數字
      let decimalIndex = sanitizedValue.indexOf('.');
      if (decimalIndex !== -1) {
      sanitizedValue = sanitizedValue.slice(0, decimalIndex + 2); // 保留小數點後一位
      }
    }
    moneyKeyboardEvent(event:KeyboardEvent) :void{
      const allowedKeys = [
        'Backspace',
        'Tab',
        'ArrowLeft',
        'ArrowRight',
        'Delete',
        'Enter',
        '.'
      ];

      const key = event.key;
      const inputElement = event.target as HTMLInputElement;

      // 允許功能鍵
      if (allowedKeys.includes(key)) {
        // 如果是小數點，檢查是否已經存在
        if (key === '.' && inputElement.value.includes('.')) {
          event.preventDefault();
        }
        return;
      }

      // 允許數字鍵
      if (/^[0-9]$/.test(key)) {
        return;
      }

      // 阻止其他按鍵
      event.preventDefault();
    }
  }




