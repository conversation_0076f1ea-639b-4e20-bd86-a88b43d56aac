import { ProjectService } from './../../../service/project.service';
import { Component } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilHttps } from '@coreui/icons';

interface project {
  project_code: string;
  project_name: string;
  place_id: string[];
}
@Component({
  selector: 'app-performance',
  standalone: true,
  imports: [
    ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    IconDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,
  ],
  templateUrl: './performance.component.html',
  styleUrl: './performance.component.scss',
})
export class PerformanceComponent {
  icons = { cilHttps };
  project: project;
  project_code: string = '';
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService
  ) {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }

  load_project_info() {
    this.projectService.get_project_info(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = {
          project_code: data.data.project_code || '',
          project_name: data.data.project_name || '',
          place_id: data.data.place_id || [],
        };
      },
      (error) => {}
    );
  }
  ngOnInit(): void {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
  }
}
