from flask import request, Blueprint, jsonify, current_app, send_file
from accessories import mongo, sqldb, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required
from sqlalchemy import text
import csv
import os
from io import BytesIO
device_page = Blueprint('device', __name__)

@device_page.route('/show_table', methods=['OPTIONS', 'POST'])
@role_required(['customer', 'project leader', 'warehouse', 'eng'])
def show_table(token):
    data = request.get_json()
    database = data.get('database')
    gateway_id = data.get('gateway_id')
    if len(gateway_id) < 5 or len(gateway_id) > 17:
        return jsonify({"message": "gateway_id must be between 5 and 17 characters"}), 400
    if database in ['195', '196', '197']:
        engine = sqldb.get_engine(current_app, bind=database)
    else:
        return jsonify({'message': 'unknown database'}), 401

    with engine.connect() as connection:
        tables = connection.execute(text(
            f"SELECT table_name FROM information_schema.tables WHERE table_schema = 'ima_thing' AND table_name LIKE '%{gateway_id}%';"
        )).fetchall()

        if not tables:
            return jsonify({"message": "No matching tables found!"}), 404
        if len(tables) > 200:
            return jsonify({"message": "Too many matches"}), 404
        result_data = []
        for table in tables:
            table_name = table[0]
            query = text(f"SELECT * FROM ima_thing.{table_name} ORDER BY id DESC LIMIT 1")
            result = connection.execute(query)
            column_names = list(result.keys())
            latest_record = result.fetchone()

            if latest_record:
                record_list = []
                for column, value in zip(column_names, latest_record):
                    if isinstance(value, bytes):
                        record_list.append({column: value.decode('utf-8')})
                    else:
                        record_list.append({column: value})
                result_data.append({
                    "device_id": table_name,
                    "data": record_list
                })

        return jsonify({
            "token": get_refresh_token(token),
            "data": result_data
        }), 200



@device_page.route('/search_sql', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'warehouse', 'eng'])
def search_sql(token):
    data = request.get_json()
    database = data.get("database")
    device_id = data.get("device_id")
    start_date = data.get("start_date")
    end_date = data.get("end_date")
    order = data.get("order")
    limit = data.get("limit")
    if len(device_id) != 17:
        return jsonify({"message": "gateway_id must be between 5 and 17 characters"}), 400
    if limit > 10000:
        return jsonify({"message": "out of search limit"}), 404
    if database in ['195', '196', '197']:
        engine = sqldb.get_engine(current_app, bind=database)
    else:
        return jsonify({'message': 'unknown database'}), 401

    with engine.connect() as connection:
        if start_date == '' or end_date == '':
            query = f"""
            SELECT * 
            FROM ima_thing.{device_id} 
            ORDER BY id {order} 
            LIMIT {limit};
        """
        else:
            query = f"""
                SELECT * 
                FROM ima_thing.{device_id} 
                WHERE id BETWEEN '{start_date}' AND '{end_date}' 
                ORDER BY id {order} 
                LIMIT {limit};
            """
        table = connection.execute(text(query))
        if not table.rowcount:
            return jsonify({"message": "No matching tables found!"}), 404

        # 初始化 result
        result = {column: [] for column in table.keys()}

        # 遍歷每一列數據
        for row in table:
            for column, value in zip(table.keys(), row):
                if isinstance(value, bytes):
                    result[column].append(value.decode('utf-8'))
                else:
                    result[column].append(value)

        # 將結果轉換成所需格式
        output = [{column: values} for column, values in result.items()]
    return jsonify({
        "token": get_refresh_token(token),
        "data": output
    }), 200

@device_page.route('/download_sql', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'warehouse', 'eng'])
def download_sql(token):
    data = request.get_json()
    database = data.get("database")
    device_id = data.get("device_id")
    start_date = data.get("start_date")
    end_date = data.get("end_date")
    order = data.get("order")
    limit = data.get("limit")

    if limit > 10000:
        return jsonify({"message": "out of search limit"}), 404
    if database in ['195', '196', '197']:
        engine = sqldb.get_engine(current_app, bind=database)
    else:
        return jsonify({'message': 'unknown database'}), 401

    with engine.connect() as connection:
        if start_date == '' or end_date == '':
            query = f"""
            SELECT * 
            FROM ima_thing.{device_id} 
            ORDER BY id {order} 
            LIMIT {limit};
        """
        else:
            query = f"""
                SELECT * 
                FROM ima_thing.{device_id} 
                WHERE id BETWEEN '{start_date}' AND '{end_date}' 
                ORDER BY id {order} 
                LIMIT {limit};
            """
        table = connection.execute(text(query))
        if not table.rowcount:
            return jsonify({"message": "No matching tables found!"}), 404

        content = ""
        for i in table.keys():
            content += str(i) + ","
        content += "\n"
        
        for row in table:
            for data in row:
                if isinstance(data, bytes):
                    content += str(data.decode('utf-8')) + ","
                else:
                    content += str(data) + ","
            content += "\n"
        with BytesIO() as file:
            return send_file(BytesIO(content.encode()), download_name=f'device_{device_id}_{start_date}_{end_date}.csv', mimetype='text/csv',
            as_attachment=True)

