import json
import time, uuid
from accessories import redis_client, mqtt_client
import paho.mqtt.client as mqtt
from config import mqttConfig

def on_message(client, userdata, message):
    try:
        topic = message.topic.split("/")[0]
        redis_client.hset(topic, mapping={
            'payload': message.payload.decode('utf-8')
        })
        redis_client.expire(topic, 10)
        
    except Exception as e:
        print(f"Failed to process MQTT message: {e}")

def on_connect(client, userdata, flags, rc):
    mqtt_client.subscribe("+/response", qos=2)
    
def send_mqtt_message_with_response(topic, cmd, args, timeout=10):
    global mqtt_client
    mqtt_client = mqtt.Client(protocol=mqtt.MQTTv311, client_id="ima_bnd_server_" + str(uuid.uuid4())[:8],  clean_session=False)
    mqtt_client.on_message = on_message
    mqtt_client.on_connect = on_connect
    mqtt_client.tls_set(ca_certs='.ca/ca.crt')
    mqtt_client.connect(mqttConfig.broker, mqttConfig.port, keepalive=60)
    mqtt_client.loop_start()
    with redis_client.pipeline() as pipe:
        pipe.delete(topic.split("/")[0])
        pipe.execute()
    message = {
        "cmd": cmd,
        "args": args
    }
    payload = json.dumps(message)
    
    if not mqtt_client.is_connected():
        mqtt_client.reconnect()
    result = mqtt_client.publish(topic, payload, qos=2)
    if result.rc != mqtt.MQTT_ERR_SUCCESS:
        return {"error": f"Failed to send message. Error code: {result.rc}"}
    start_time = time.time()
    while time.time() - start_time < timeout:
        response = redis_client.hget(topic.split("/")[0], "payload")
        if response:
            redis_client.delete(topic.split("/")[0])
            return response.decode()
        time.sleep(0.1)
    
    return {"error": "Timeout waiting for response from MQTT"}

def send_mqtt_message_without_response(topic, cmd, args):
    global mqtt_client
    mqtt_client = mqtt.Client(protocol=mqtt.MQTTv311, client_id="ima_bnd_server_" + str(uuid.uuid4())[:8],  clean_session=False)
    mqtt_client.on_message = on_message
    mqtt_client.on_connect = on_connect
    mqtt_client.tls_set(ca_certs='.ca/ca.crt')
    mqtt_client.connect(mqttConfig.broker, mqttConfig.port, keepalive=60)
    mqtt_client.loop_start()
    message = {
        "cmd": cmd,
        "args": args
    }
    payload = json.dumps(message)
    if not mqtt_client.is_connected():
        mqtt_client.reconnect()
    mqtt_client.publish(topic, payload, qos=2)