{"version": 3, "sources": ["../../../../../../node_modules/@fullcalendar/daygrid/internal.js", "../../../../../../node_modules/@fullcalendar/daygrid/index.js"], "sourcesContent": ["import { DateComponent, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, renderScrollShim, createFormatter, BaseComponent, StandardEvent, buildSegTimeText, EventContainer, getSegAnchorAttrs, memoize, MoreLinkContainer, getSegMeta, getUniqueDomId, setRef, DayCellContainer, WeekNumberContainer, buildNavLinkAttrs, hasCustomDayCellContent, addMs, intersectRanges, addDays, SegHierarchy, buildEntryKey, intersectSpans, RefMap, sortEventSegs, isPropsEqual, buildEventRangeKey, BgEvent, renderFill, PositionCache, NowTimer, formatIsoMonthStr, formatDayString, Slicer, DayHeader, DaySeriesModel, DayTableModel, DateProfileGenerator, addWeeks, diffWeeks, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createRef, createElement, Fragment } from '@fullcalendar/core/preact.js';\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\nclass TableView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.headerElRef = createRef();\n  }\n  renderSimpleLayout(headerRowContent, bodyContent) {\n    let {\n      props,\n      context\n    } = this;\n    let sections = [];\n    let stickyHeaderDates = getStickyHeaderDates(context.options);\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunk: {\n        content: bodyContent\n      }\n    });\n    return createElement(ViewContainer, {\n      elClasses: ['fc-daygrid'],\n      viewSpec: context.viewSpec\n    }, createElement(SimpleScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      collapsibleWidth: props.forPrint,\n      cols: [] /* TODO: make optional? */,\n      sections: sections\n    }));\n  }\n  renderHScrollLayout(headerRowContent, bodyContent, colCnt, dayMinWidth) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation');\n    }\n    let {\n      props,\n      context\n    } = this;\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n    let sections = [];\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunks: [{\n          key: 'main',\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }]\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunks: [{\n        key: 'main',\n        content: bodyContent\n      }]\n    });\n    if (stickyFooterScrollbar) {\n      sections.push({\n        type: 'footer',\n        key: 'footer',\n        isSticky: true,\n        chunks: [{\n          key: 'main',\n          content: renderScrollShim\n        }]\n      });\n    }\n    return createElement(ViewContainer, {\n      elClasses: ['fc-daygrid'],\n      viewSpec: context.viewSpec\n    }, createElement(ScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      forPrint: props.forPrint,\n      collapsibleWidth: props.forPrint,\n      colGroups: [{\n        cols: [{\n          span: colCnt,\n          minWidth: dayMinWidth\n        }]\n      }],\n      sections: sections\n    }));\n  }\n}\nfunction splitSegsByRow(segs, rowCnt) {\n  let byRow = [];\n  for (let i = 0; i < rowCnt; i += 1) {\n    byRow[i] = [];\n  }\n  for (let seg of segs) {\n    byRow[seg.row].push(seg);\n  }\n  return byRow;\n}\nfunction splitSegsByFirstCol(segs, colCnt) {\n  let byCol = [];\n  for (let i = 0; i < colCnt; i += 1) {\n    byCol[i] = [];\n  }\n  for (let seg of segs) {\n    byCol[seg.firstCol].push(seg);\n  }\n  return byCol;\n}\nfunction splitInteractionByRow(ui, rowCnt) {\n  let byRow = [];\n  if (!ui) {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = null;\n    }\n  } else {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: []\n      };\n    }\n    for (let seg of ui.segs) {\n      byRow[seg.row].segs.push(seg);\n    }\n  }\n  return byRow;\n}\nconst DEFAULT_TABLE_EVENT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'narrow'\n});\nfunction hasListItemDisplay(seg) {\n  let {\n    display\n  } = seg.eventRange.ui;\n  return display === 'list-item' || display === 'auto' && !seg.eventRange.def.allDay && seg.firstCol === seg.lastCol &&\n  // can't be multi-day\n  seg.isStart &&\n  // \"\n  seg.isEnd // \"\n  ;\n}\nclass TableBlockEvent extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    return createElement(StandardEvent, Object.assign({}, props, {\n      elClasses: ['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event'],\n      defaultTimeFormat: DEFAULT_TABLE_EVENT_TIME_FORMAT,\n      defaultDisplayEventEnd: props.defaultDisplayEventEnd,\n      disableResizing: !props.seg.eventRange.def.allDay\n    }));\n  }\n}\nclass TableListItemEvent extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      seg\n    } = props;\n    let timeFormat = options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT;\n    let timeText = buildSegTimeText(seg, timeFormat, context, true, props.defaultDisplayEventEnd);\n    return createElement(EventContainer, Object.assign({}, props, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-event', 'fc-daygrid-dot-event'],\n      elAttrs: getSegAnchorAttrs(props.seg, context),\n      defaultGenerator: renderInnerContent,\n      timeText: timeText,\n      isResizing: false,\n      isDateSelecting: false\n    }));\n  }\n}\nfunction renderInnerContent(renderProps) {\n  return createElement(Fragment, null, createElement(\"div\", {\n    className: \"fc-daygrid-event-dot\",\n    style: {\n      borderColor: renderProps.borderColor || renderProps.backgroundColor\n    }\n  }), renderProps.timeText && createElement(\"div\", {\n    className: \"fc-event-time\"\n  }, renderProps.timeText), createElement(\"div\", {\n    className: \"fc-event-title\"\n  }, renderProps.event.title || createElement(Fragment, null, \"\\u00A0\")));\n}\nclass TableCellMoreLink extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.compileSegs = memoize(compileSegs);\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      allSegs,\n      invisibleSegs\n    } = this.compileSegs(props.singlePlacements);\n    return createElement(MoreLinkContainer, {\n      elClasses: ['fc-daygrid-more-link'],\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      allDayDate: props.allDayDate,\n      moreCnt: props.moreCnt,\n      allSegs: allSegs,\n      hiddenSegs: invisibleSegs,\n      alignmentElRef: props.alignmentElRef,\n      alignGridTop: props.alignGridTop,\n      extraDateSpan: props.extraDateSpan,\n      popoverContent: () => {\n        let isForcedInvisible = (props.eventDrag ? props.eventDrag.affectedInstances : null) || (props.eventResize ? props.eventResize.affectedInstances : null) || {};\n        return createElement(Fragment, null, allSegs.map(seg => {\n          let instanceId = seg.eventRange.instance.instanceId;\n          return createElement(\"div\", {\n            className: \"fc-daygrid-event-harness\",\n            key: instanceId,\n            style: {\n              visibility: isForcedInvisible[instanceId] ? 'hidden' : ''\n            }\n          }, hasListItemDisplay(seg) ? createElement(TableListItemEvent, Object.assign({\n            seg: seg,\n            isDragging: false,\n            isSelected: instanceId === props.eventSelection,\n            defaultDisplayEventEnd: false\n          }, getSegMeta(seg, props.todayRange))) : createElement(TableBlockEvent, Object.assign({\n            seg: seg,\n            isDragging: false,\n            isResizing: false,\n            isDateSelecting: false,\n            isSelected: instanceId === props.eventSelection,\n            defaultDisplayEventEnd: false\n          }, getSegMeta(seg, props.todayRange))));\n        }));\n      }\n    });\n  }\n}\nfunction compileSegs(singlePlacements) {\n  let allSegs = [];\n  let invisibleSegs = [];\n  for (let placement of singlePlacements) {\n    allSegs.push(placement.seg);\n    if (!placement.isVisible) {\n      invisibleSegs.push(placement.seg);\n    }\n  }\n  return {\n    allSegs,\n    invisibleSegs\n  };\n}\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({\n  week: 'narrow'\n});\nclass TableCell extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.rootElRef = createRef();\n    this.state = {\n      dayNumberId: getUniqueDomId()\n    };\n    this.handleRootEl = el => {\n      setRef(this.rootElRef, el);\n      setRef(this.props.elRef, el);\n    };\n  }\n  render() {\n    let {\n      context,\n      props,\n      state,\n      rootElRef\n    } = this;\n    let {\n      options,\n      dateEnv\n    } = context;\n    let {\n      date,\n      dateProfile\n    } = props;\n    // TODO: memoize this?\n    const isMonthStart = props.showDayNumber && shouldDisplayMonthStart(date, dateProfile.currentRange, dateEnv);\n    return createElement(DayCellContainer, {\n      elTag: \"td\",\n      elRef: this.handleRootEl,\n      elClasses: ['fc-daygrid-day', ...(props.extraClassNames || [])],\n      elAttrs: Object.assign(Object.assign(Object.assign({}, props.extraDataAttrs), props.showDayNumber ? {\n        'aria-labelledby': state.dayNumberId\n      } : {}), {\n        role: 'gridcell'\n      }),\n      defaultGenerator: renderTopInner,\n      date: date,\n      dateProfile: dateProfile,\n      todayRange: props.todayRange,\n      showDayNumber: props.showDayNumber,\n      isMonthStart: isMonthStart,\n      extraRenderProps: props.extraRenderProps\n    }, (InnerContent, renderProps) => createElement(\"div\", {\n      ref: props.innerElRef,\n      className: \"fc-daygrid-day-frame fc-scrollgrid-sync-inner\",\n      style: {\n        minHeight: props.minHeight\n      }\n    }, props.showWeekNumber && createElement(WeekNumberContainer, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-week-number'],\n      elAttrs: buildNavLinkAttrs(context, date, 'week'),\n      date: date,\n      defaultFormat: DEFAULT_WEEK_NUM_FORMAT\n    }), !renderProps.isDisabled && (props.showDayNumber || hasCustomDayCellContent(options) || props.forceDayTop) ? createElement(\"div\", {\n      className: \"fc-daygrid-day-top\"\n    }, createElement(InnerContent, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-day-number', isMonthStart && 'fc-daygrid-month-start'],\n      elAttrs: Object.assign(Object.assign({}, buildNavLinkAttrs(context, date)), {\n        id: state.dayNumberId\n      })\n    })) : props.showDayNumber ?\n    // for creating correct amount of space (see issue #7162)\n    createElement(\"div\", {\n      className: \"fc-daygrid-day-top\",\n      style: {\n        visibility: 'hidden'\n      }\n    }, createElement(\"a\", {\n      className: \"fc-daygrid-day-number\"\n    }, \"\\u00A0\")) : undefined, createElement(\"div\", {\n      className: \"fc-daygrid-day-events\",\n      ref: props.fgContentElRef\n    }, props.fgContent, createElement(\"div\", {\n      className: \"fc-daygrid-day-bottom\",\n      style: {\n        marginTop: props.moreMarginTop\n      }\n    }, createElement(TableCellMoreLink, {\n      allDayDate: date,\n      singlePlacements: props.singlePlacements,\n      moreCnt: props.moreCnt,\n      alignmentElRef: rootElRef,\n      alignGridTop: !props.showDayNumber,\n      extraDateSpan: props.extraDateSpan,\n      dateProfile: props.dateProfile,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      todayRange: props.todayRange\n    }))), createElement(\"div\", {\n      className: \"fc-daygrid-day-bg\"\n    }, props.bgContent)));\n  }\n}\nfunction renderTopInner(props) {\n  return props.dayNumberText || createElement(Fragment, null, \"\\u00A0\");\n}\nfunction shouldDisplayMonthStart(date, currentRange, dateEnv) {\n  const {\n    start: currentStart,\n    end: currentEnd\n  } = currentRange;\n  const currentEndIncl = addMs(currentEnd, -1);\n  const currentFirstYear = dateEnv.getYear(currentStart);\n  const currentFirstMonth = dateEnv.getMonth(currentStart);\n  const currentLastYear = dateEnv.getYear(currentEndIncl);\n  const currentLastMonth = dateEnv.getMonth(currentEndIncl);\n  // spans more than one month?\n  return !(currentFirstYear === currentLastYear && currentFirstMonth === currentLastMonth) && Boolean(\n  // first date in current view?\n  date.valueOf() === currentStart.valueOf() ||\n  // a month-start that's within the current range?\n  dateEnv.getDay(date) === 1 && date.valueOf() < currentEnd.valueOf());\n}\nfunction generateSegKey(seg) {\n  return seg.eventRange.instance.instanceId + ':' + seg.firstCol;\n}\nfunction generateSegUid(seg) {\n  return generateSegKey(seg) + ':' + seg.lastCol;\n}\nfunction computeFgSegPlacement(segs,\n// assumed already sorted\ndayMaxEvents, dayMaxEventRows, strictOrder, segHeights, maxContentHeight, cells) {\n  let hierarchy = new DayGridSegHierarchy(segEntry => {\n    // TODO: more DRY with generateSegUid\n    let segUid = segs[segEntry.index].eventRange.instance.instanceId + ':' + segEntry.span.start + ':' + (segEntry.span.end - 1);\n    // if no thickness known, assume 1 (if 0, so small it always fits)\n    return segHeights[segUid] || 1;\n  });\n  hierarchy.allowReslicing = true;\n  hierarchy.strictOrder = strictOrder;\n  if (dayMaxEvents === true || dayMaxEventRows === true) {\n    hierarchy.maxCoord = maxContentHeight;\n    hierarchy.hiddenConsumes = true;\n  } else if (typeof dayMaxEvents === 'number') {\n    hierarchy.maxStackCnt = dayMaxEvents;\n  } else if (typeof dayMaxEventRows === 'number') {\n    hierarchy.maxStackCnt = dayMaxEventRows;\n    hierarchy.hiddenConsumes = true;\n  }\n  // create segInputs only for segs with known heights\n  let segInputs = [];\n  let unknownHeightSegs = [];\n  for (let i = 0; i < segs.length; i += 1) {\n    let seg = segs[i];\n    let segUid = generateSegUid(seg);\n    let eventHeight = segHeights[segUid];\n    if (eventHeight != null) {\n      segInputs.push({\n        index: i,\n        span: {\n          start: seg.firstCol,\n          end: seg.lastCol + 1\n        }\n      });\n    } else {\n      unknownHeightSegs.push(seg);\n    }\n  }\n  let hiddenEntries = hierarchy.addSegs(segInputs);\n  let segRects = hierarchy.toRects();\n  let {\n    singleColPlacements,\n    multiColPlacements,\n    leftoverMargins\n  } = placeRects(segRects, segs, cells);\n  let moreCnts = [];\n  let moreMarginTops = [];\n  // add segs with unknown heights\n  for (let seg of unknownHeightSegs) {\n    multiColPlacements[seg.firstCol].push({\n      seg,\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0\n    });\n    for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0\n      });\n    }\n  }\n  // add the hidden entries\n  for (let col = 0; col < cells.length; col += 1) {\n    moreCnts.push(0);\n  }\n  for (let hiddenEntry of hiddenEntries) {\n    let seg = segs[hiddenEntry.index];\n    let hiddenSpan = hiddenEntry.span;\n    multiColPlacements[hiddenSpan.start].push({\n      seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0\n    });\n    for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n      moreCnts[col] += 1;\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0\n      });\n    }\n  }\n  // deal with leftover margins\n  for (let col = 0; col < cells.length; col += 1) {\n    moreMarginTops.push(leftoverMargins[col]);\n  }\n  return {\n    singleColPlacements,\n    multiColPlacements,\n    moreCnts,\n    moreMarginTops\n  };\n}\n// rects ordered by top coord, then left\nfunction placeRects(allRects, segs, cells) {\n  let rectsByEachCol = groupRectsByEachCol(allRects, cells.length);\n  let singleColPlacements = [];\n  let multiColPlacements = [];\n  let leftoverMargins = [];\n  for (let col = 0; col < cells.length; col += 1) {\n    let rects = rectsByEachCol[col];\n    // compute all static segs in singlePlacements\n    let singlePlacements = [];\n    let currentHeight = 0;\n    let currentMarginTop = 0;\n    for (let rect of rects) {\n      let seg = segs[rect.index];\n      singlePlacements.push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: true,\n        isAbsolute: false,\n        absoluteTop: rect.levelCoord,\n        marginTop: rect.levelCoord - currentHeight\n      });\n      currentHeight = rect.levelCoord + rect.thickness;\n    }\n    // compute mixed static/absolute segs in multiPlacements\n    let multiPlacements = [];\n    currentHeight = 0;\n    currentMarginTop = 0;\n    for (let rect of rects) {\n      let seg = segs[rect.index];\n      let isAbsolute = rect.span.end - rect.span.start > 1; // multi-column?\n      let isFirstCol = rect.span.start === col;\n      currentMarginTop += rect.levelCoord - currentHeight; // amount of space since bottom of previous seg\n      currentHeight = rect.levelCoord + rect.thickness; // height will now be bottom of current seg\n      if (isAbsolute) {\n        currentMarginTop += rect.thickness;\n        if (isFirstCol) {\n          multiPlacements.push({\n            seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n            isVisible: true,\n            isAbsolute: true,\n            absoluteTop: rect.levelCoord,\n            marginTop: 0\n          });\n        }\n      } else if (isFirstCol) {\n        multiPlacements.push({\n          seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n          isVisible: true,\n          isAbsolute: false,\n          absoluteTop: rect.levelCoord,\n          marginTop: currentMarginTop // claim the margin\n        });\n        currentMarginTop = 0;\n      }\n    }\n    singleColPlacements.push(singlePlacements);\n    multiColPlacements.push(multiPlacements);\n    leftoverMargins.push(currentMarginTop);\n  }\n  return {\n    singleColPlacements,\n    multiColPlacements,\n    leftoverMargins\n  };\n}\nfunction groupRectsByEachCol(rects, colCnt) {\n  let rectsByEachCol = [];\n  for (let col = 0; col < colCnt; col += 1) {\n    rectsByEachCol.push([]);\n  }\n  for (let rect of rects) {\n    for (let col = rect.span.start; col < rect.span.end; col += 1) {\n      rectsByEachCol[col].push(rect);\n    }\n  }\n  return rectsByEachCol;\n}\nfunction resliceSeg(seg, spanStart, spanEnd, cells) {\n  if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n    return seg;\n  }\n  let eventRange = seg.eventRange;\n  let origRange = eventRange.range;\n  let slicedRange = intersectRanges(origRange, {\n    start: cells[spanStart].date,\n    end: addDays(cells[spanEnd - 1].date, 1)\n  });\n  return Object.assign(Object.assign({}, seg), {\n    firstCol: spanStart,\n    lastCol: spanEnd - 1,\n    eventRange: {\n      def: eventRange.def,\n      ui: Object.assign(Object.assign({}, eventRange.ui), {\n        durationEditable: false\n      }),\n      instance: eventRange.instance,\n      range: slicedRange\n    },\n    isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(),\n    isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf()\n  });\n}\nclass DayGridSegHierarchy extends SegHierarchy {\n  constructor() {\n    super(...arguments);\n    // config\n    this.hiddenConsumes = false;\n    // allows us to keep hidden entries in the hierarchy so they take up space\n    this.forceHidden = {};\n  }\n  addSegs(segInputs) {\n    const hiddenSegs = super.addSegs(segInputs);\n    const {\n      entriesByLevel\n    } = this;\n    const excludeHidden = entry => !this.forceHidden[buildEntryKey(entry)];\n    // remove the forced-hidden segs\n    for (let level = 0; level < entriesByLevel.length; level += 1) {\n      entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden);\n    }\n    return hiddenSegs;\n  }\n  handleInvalidInsertion(insertion, entry, hiddenEntries) {\n    const {\n      entriesByLevel,\n      forceHidden\n    } = this;\n    const {\n      touchingEntry,\n      touchingLevel,\n      touchingLateral\n    } = insertion;\n    // the entry that the new insertion is touching must be hidden\n    if (this.hiddenConsumes && touchingEntry) {\n      const touchingEntryId = buildEntryKey(touchingEntry);\n      if (!forceHidden[touchingEntryId]) {\n        if (this.allowReslicing) {\n          // split up the touchingEntry, reinsert it\n          const hiddenEntry = Object.assign(Object.assign({}, touchingEntry), {\n            span: intersectSpans(touchingEntry.span, entry.span)\n          });\n          // reinsert the area that turned into a \"more\" link (so no other entries try to\n          // occupy the space) but mark it forced-hidden\n          const hiddenEntryId = buildEntryKey(hiddenEntry);\n          forceHidden[hiddenEntryId] = true;\n          entriesByLevel[touchingLevel][touchingLateral] = hiddenEntry;\n          hiddenEntries.push(hiddenEntry);\n          this.splitEntry(touchingEntry, entry, hiddenEntries);\n        } else {\n          forceHidden[touchingEntryId] = true;\n          hiddenEntries.push(touchingEntry);\n        }\n      }\n    }\n    // will try to reslice...\n    super.handleInvalidInsertion(insertion, entry, hiddenEntries);\n  }\n}\nclass TableRow extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.cellElRefs = new RefMap(); // the <td>\n    this.frameElRefs = new RefMap(); // the fc-daygrid-day-frame\n    this.fgElRefs = new RefMap(); // the fc-daygrid-day-events\n    this.segHarnessRefs = new RefMap(); // indexed by \"instanceId:firstCol\"\n    this.rootElRef = createRef();\n    this.state = {\n      framePositions: null,\n      maxContentHeight: null,\n      segHeights: {}\n    };\n    this.handleResize = isForced => {\n      if (isForced) {\n        this.updateSizing(true); // isExternal=true\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      state,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let colCnt = props.cells.length;\n    let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt);\n    let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt);\n    let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt);\n    let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt);\n    let {\n      singleColPlacements,\n      multiColPlacements,\n      moreCnts,\n      moreMarginTops\n    } = computeFgSegPlacement(sortEventSegs(props.fgEventSegs, options.eventOrder), props.dayMaxEvents, props.dayMaxEventRows, options.eventOrderStrict, state.segHeights, state.maxContentHeight, props.cells);\n    let isForcedInvisible =\n    // TODO: messy way to compute this\n    props.eventDrag && props.eventDrag.affectedInstances || props.eventResize && props.eventResize.affectedInstances || {};\n    return createElement(\"tr\", {\n      ref: this.rootElRef,\n      role: \"row\"\n    }, props.renderIntro && props.renderIntro(), props.cells.map((cell, col) => {\n      let normalFgNodes = this.renderFgSegs(col, props.forPrint ? singleColPlacements[col] : multiColPlacements[col], props.todayRange, isForcedInvisible);\n      let mirrorFgNodes = this.renderFgSegs(col, buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements), props.todayRange, {}, Boolean(props.eventDrag), Boolean(props.eventResize), false);\n      return createElement(TableCell, {\n        key: cell.key,\n        elRef: this.cellElRefs.createRef(cell.key),\n        innerElRef: this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */,\n        dateProfile: props.dateProfile,\n        date: cell.date,\n        showDayNumber: props.showDayNumbers,\n        showWeekNumber: props.showWeekNumbers && col === 0,\n        forceDayTop: props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */,\n        todayRange: props.todayRange,\n        eventSelection: props.eventSelection,\n        eventDrag: props.eventDrag,\n        eventResize: props.eventResize,\n        extraRenderProps: cell.extraRenderProps,\n        extraDataAttrs: cell.extraDataAttrs,\n        extraClassNames: cell.extraClassNames,\n        extraDateSpan: cell.extraDateSpan,\n        moreCnt: moreCnts[col],\n        moreMarginTop: moreMarginTops[col],\n        singlePlacements: singleColPlacements[col],\n        fgContentElRef: this.fgElRefs.createRef(cell.key),\n        fgContent:\n        // Fragment scopes the keys\n        createElement(Fragment, null, createElement(Fragment, null, normalFgNodes), createElement(Fragment, null, mirrorFgNodes)),\n        bgContent:\n        // Fragment scopes the keys\n        createElement(Fragment, null, this.renderFillSegs(highlightSegsByCol[col], 'highlight'), this.renderFillSegs(businessHoursByCol[col], 'non-business'), this.renderFillSegs(bgEventSegsByCol[col], 'bg-event')),\n        minHeight: props.cellMinHeight\n      });\n    }));\n  }\n  componentDidMount() {\n    this.updateSizing(true);\n    this.context.addResizeHandler(this.handleResize);\n  }\n  componentDidUpdate(prevProps, prevState) {\n    let currentProps = this.props;\n    this.updateSizing(!isPropsEqual(prevProps, currentProps));\n  }\n  componentWillUnmount() {\n    this.context.removeResizeHandler(this.handleResize);\n  }\n  getHighlightSegs() {\n    let {\n      props\n    } = this;\n    if (props.eventDrag && props.eventDrag.segs.length) {\n      // messy check\n      return props.eventDrag.segs;\n    }\n    if (props.eventResize && props.eventResize.segs.length) {\n      // messy check\n      return props.eventResize.segs;\n    }\n    return props.dateSelectionSegs;\n  }\n  getMirrorSegs() {\n    let {\n      props\n    } = this;\n    if (props.eventResize && props.eventResize.segs.length) {\n      // messy check\n      return props.eventResize.segs;\n    }\n    return [];\n  }\n  renderFgSegs(col, segPlacements, todayRange, isForcedInvisible, isDragging, isResizing, isDateSelecting) {\n    let {\n      context\n    } = this;\n    let {\n      eventSelection\n    } = this.props;\n    let {\n      framePositions\n    } = this.state;\n    let defaultDisplayEventEnd = this.props.cells.length === 1; // colCnt === 1\n    let isMirror = isDragging || isResizing || isDateSelecting;\n    let nodes = [];\n    if (framePositions) {\n      for (let placement of segPlacements) {\n        let {\n          seg\n        } = placement;\n        let {\n          instanceId\n        } = seg.eventRange.instance;\n        let isVisible = placement.isVisible && !isForcedInvisible[instanceId];\n        let isAbsolute = placement.isAbsolute;\n        let left = '';\n        let right = '';\n        if (isAbsolute) {\n          if (context.isRtl) {\n            right = 0;\n            left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol];\n          } else {\n            left = 0;\n            right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol];\n          }\n        }\n        /*\n        known bug: events that are force to be list-item but span multiple days still take up space in later columns\n        todo: in print view, for multi-day events, don't display title within non-start/end segs\n        */\n        nodes.push(createElement(\"div\", {\n          className: 'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : ''),\n          key: generateSegKey(seg),\n          ref: isMirror ? null : this.segHarnessRefs.createRef(generateSegUid(seg)),\n          style: {\n            visibility: isVisible ? '' : 'hidden',\n            marginTop: isAbsolute ? '' : placement.marginTop,\n            top: isAbsolute ? placement.absoluteTop : '',\n            left,\n            right\n          }\n        }, hasListItemDisplay(seg) ? createElement(TableListItemEvent, Object.assign({\n          seg: seg,\n          isDragging: isDragging,\n          isSelected: instanceId === eventSelection,\n          defaultDisplayEventEnd: defaultDisplayEventEnd\n        }, getSegMeta(seg, todayRange))) : createElement(TableBlockEvent, Object.assign({\n          seg: seg,\n          isDragging: isDragging,\n          isResizing: isResizing,\n          isDateSelecting: isDateSelecting,\n          isSelected: instanceId === eventSelection,\n          defaultDisplayEventEnd: defaultDisplayEventEnd\n        }, getSegMeta(seg, todayRange)))));\n      }\n    }\n    return nodes;\n  }\n  renderFillSegs(segs, fillType) {\n    let {\n      isRtl\n    } = this.context;\n    let {\n      todayRange\n    } = this.props;\n    let {\n      framePositions\n    } = this.state;\n    let nodes = [];\n    if (framePositions) {\n      for (let seg of segs) {\n        let leftRightCss = isRtl ? {\n          right: 0,\n          left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol]\n        } : {\n          left: 0,\n          right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol]\n        };\n        nodes.push(createElement(\"div\", {\n          key: buildEventRangeKey(seg.eventRange),\n          className: \"fc-daygrid-bg-harness\",\n          style: leftRightCss\n        }, fillType === 'bg-event' ? createElement(BgEvent, Object.assign({\n          seg: seg\n        }, getSegMeta(seg, todayRange))) : renderFill(fillType)));\n      }\n    }\n    return createElement(Fragment, {}, ...nodes);\n  }\n  updateSizing(isExternalSizingChange) {\n    let {\n      props,\n      state,\n      frameElRefs\n    } = this;\n    if (!props.forPrint && props.clientWidth !== null // positioning ready?\n    ) {\n      if (isExternalSizingChange) {\n        let frameEls = props.cells.map(cell => frameElRefs.currentMap[cell.key]);\n        if (frameEls.length) {\n          let originEl = this.rootElRef.current;\n          let newPositionCache = new PositionCache(originEl, frameEls, true,\n          // isHorizontal\n          false);\n          if (!state.framePositions || !state.framePositions.similarTo(newPositionCache)) {\n            this.setState({\n              framePositions: new PositionCache(originEl, frameEls, true,\n              // isHorizontal\n              false)\n            });\n          }\n        }\n      }\n      const oldSegHeights = this.state.segHeights;\n      const newSegHeights = this.querySegHeights();\n      const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true;\n      this.safeSetState({\n        // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n        // Essentially, once you compute an element's height, never null-out.\n        // TODO: always display all events, as visibility:hidden?\n        segHeights: Object.assign(Object.assign({}, oldSegHeights), newSegHeights),\n        maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null\n      });\n    }\n  }\n  querySegHeights() {\n    let segElMap = this.segHarnessRefs.currentMap;\n    let segHeights = {};\n    // get the max height amongst instance segs\n    for (let segUid in segElMap) {\n      let height = Math.round(segElMap[segUid].getBoundingClientRect().height);\n      segHeights[segUid] = Math.max(segHeights[segUid] || 0, height);\n    }\n    return segHeights;\n  }\n  computeMaxContentHeight() {\n    let firstKey = this.props.cells[0].key;\n    let cellEl = this.cellElRefs.currentMap[firstKey];\n    let fcContainerEl = this.fgElRefs.currentMap[firstKey];\n    return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top;\n  }\n  getCellEls() {\n    let elMap = this.cellElRefs.currentMap;\n    return this.props.cells.map(cell => elMap[cell.key]);\n  }\n}\nTableRow.addStateEquality({\n  segHeights: isPropsEqual\n});\nfunction buildMirrorPlacements(mirrorSegs, colPlacements) {\n  if (!mirrorSegs.length) {\n    return [];\n  }\n  let topsByInstanceId = buildAbsoluteTopHash(colPlacements); // TODO: cache this at first render?\n  return mirrorSegs.map(seg => ({\n    seg,\n    isVisible: true,\n    isAbsolute: true,\n    absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n    marginTop: 0\n  }));\n}\nfunction buildAbsoluteTopHash(colPlacements) {\n  let topsByInstanceId = {};\n  for (let placements of colPlacements) {\n    for (let placement of placements) {\n      topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop;\n    }\n  }\n  return topsByInstanceId;\n}\nclass TableRows extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.splitBusinessHourSegs = memoize(splitSegsByRow);\n    this.splitBgEventSegs = memoize(splitSegsByRow);\n    this.splitFgEventSegs = memoize(splitSegsByRow);\n    this.splitDateSelectionSegs = memoize(splitSegsByRow);\n    this.splitEventDrag = memoize(splitInteractionByRow);\n    this.splitEventResize = memoize(splitInteractionByRow);\n    this.rowRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let rowCnt = props.cells.length;\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt);\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt);\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt);\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt);\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt);\n    let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt);\n    // for DayGrid view with many rows, force a min-height on cells so doesn't appear squished\n    // choose 7 because a month view will have max 6 rows\n    let cellMinHeight = rowCnt >= 7 && props.clientWidth ? props.clientWidth / context.options.aspectRatio / 6 : null;\n    return createElement(NowTimer, {\n      unit: \"day\"\n    }, (nowDate, todayRange) => createElement(Fragment, null, props.cells.map((cells, row) => createElement(TableRow, {\n      ref: this.rowRefs.createRef(row),\n      key: cells.length ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */ : row // in case there are no cells (like when resource view is loading)\n      ,\n      showDayNumbers: rowCnt > 1,\n      showWeekNumbers: props.showWeekNumbers,\n      todayRange: todayRange,\n      dateProfile: props.dateProfile,\n      cells: cells,\n      renderIntro: props.renderRowIntro,\n      businessHourSegs: businessHourSegsByRow[row],\n      eventSelection: props.eventSelection,\n      bgEventSegs: bgEventSegsByRow[row].filter(isSegAllDay) /* hack */,\n      fgEventSegs: fgEventSegsByRow[row],\n      dateSelectionSegs: dateSelectionSegsByRow[row],\n      eventDrag: eventDragByRow[row],\n      eventResize: eventResizeByRow[row],\n      dayMaxEvents: props.dayMaxEvents,\n      dayMaxEventRows: props.dayMaxEventRows,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      cellMinHeight: cellMinHeight,\n      forPrint: props.forPrint\n    }))));\n  }\n  componentDidMount() {\n    this.registerInteractiveComponent();\n  }\n  componentDidUpdate() {\n    // for if started with zero cells\n    this.registerInteractiveComponent();\n  }\n  registerInteractiveComponent() {\n    if (!this.rootEl) {\n      // HACK: need a daygrid wrapper parent to do positioning\n      // NOTE: a daygrid resource view w/o resources can have zero cells\n      const firstCellEl = this.rowRefs.currentMap[0].getCellEls()[0];\n      const rootEl = firstCellEl ? firstCellEl.closest('.fc-daygrid-body') : null;\n      if (rootEl) {\n        this.rootEl = rootEl;\n        this.context.registerInteractiveComponent(this, {\n          el: rootEl,\n          isHitComboAllowed: this.props.isHitComboAllowed\n        });\n      }\n    }\n  }\n  componentWillUnmount() {\n    if (this.rootEl) {\n      this.context.unregisterInteractiveComponent(this);\n      this.rootEl = null;\n    }\n  }\n  // Hit System\n  // ----------------------------------------------------------------------------------------------------\n  prepareHits() {\n    this.rowPositions = new PositionCache(this.rootEl, this.rowRefs.collect().map(rowObj => rowObj.getCellEls()[0]),\n    // first cell el in each row. TODO: not optimal\n    false, true);\n    this.colPositions = new PositionCache(this.rootEl, this.rowRefs.currentMap[0].getCellEls(),\n    // cell els in first row\n    true,\n    // horizontal\n    false);\n  }\n  queryHit(positionLeft, positionTop) {\n    let {\n      colPositions,\n      rowPositions\n    } = this;\n    let col = colPositions.leftToIndex(positionLeft);\n    let row = rowPositions.topToIndex(positionTop);\n    if (row != null && col != null) {\n      let cell = this.props.cells[row][col];\n      return {\n        dateProfile: this.props.dateProfile,\n        dateSpan: Object.assign({\n          range: this.getCellRange(row, col),\n          allDay: true\n        }, cell.extraDateSpan),\n        dayEl: this.getCellEl(row, col),\n        rect: {\n          left: colPositions.lefts[col],\n          right: colPositions.rights[col],\n          top: rowPositions.tops[row],\n          bottom: rowPositions.bottoms[row]\n        },\n        layer: 0\n      };\n    }\n    return null;\n  }\n  getCellEl(row, col) {\n    return this.rowRefs.currentMap[row].getCellEls()[col]; // TODO: not optimal\n  }\n  getCellRange(row, col) {\n    let start = this.props.cells[row][col].date;\n    let end = addDays(start, 1);\n    return {\n      start,\n      end\n    };\n  }\n}\nfunction isSegAllDay(seg) {\n  return seg.eventRange.def.allDay;\n}\nclass Table extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.elRef = createRef();\n    this.needsScrollReset = false;\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      dayMaxEventRows,\n      dayMaxEvents,\n      expandRows\n    } = props;\n    let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true;\n    // if rows can't expand to fill fixed height, can't do balanced-height event limit\n    // TODO: best place to normalize these options?\n    if (limitViaBalanced && !expandRows) {\n      limitViaBalanced = false;\n      dayMaxEventRows = null;\n      dayMaxEvents = null;\n    }\n    let classNames = ['fc-daygrid-body', limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced', expandRows ? '' : 'fc-daygrid-body-natural' // will height of one row depend on the others?\n    ];\n    return createElement(\"div\", {\n      ref: this.elRef,\n      className: classNames.join(' '),\n      style: {\n        // these props are important to give this wrapper correct dimensions for interactions\n        // TODO: if we set it here, can we avoid giving to inner tables?\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth\n      }\n    }, createElement(\"table\", {\n      role: \"presentation\",\n      className: \"fc-scrollgrid-sync-table\",\n      style: {\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth,\n        height: expandRows ? props.clientHeight : ''\n      }\n    }, props.colGroupNode, createElement(\"tbody\", {\n      role: \"presentation\"\n    }, createElement(TableRows, {\n      dateProfile: props.dateProfile,\n      cells: props.cells,\n      renderRowIntro: props.renderRowIntro,\n      showWeekNumbers: props.showWeekNumbers,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      businessHourSegs: props.businessHourSegs,\n      bgEventSegs: props.bgEventSegs,\n      fgEventSegs: props.fgEventSegs,\n      dateSelectionSegs: props.dateSelectionSegs,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      dayMaxEvents: dayMaxEvents,\n      dayMaxEventRows: dayMaxEventRows,\n      forPrint: props.forPrint,\n      isHitComboAllowed: props.isHitComboAllowed\n    }))));\n  }\n  componentDidMount() {\n    this.requestScrollReset();\n  }\n  componentDidUpdate(prevProps) {\n    if (prevProps.dateProfile !== this.props.dateProfile) {\n      this.requestScrollReset();\n    } else {\n      this.flushScrollReset();\n    }\n  }\n  requestScrollReset() {\n    this.needsScrollReset = true;\n    this.flushScrollReset();\n  }\n  flushScrollReset() {\n    if (this.needsScrollReset && this.props.clientWidth // sizes computed?\n    ) {\n      const subjectEl = getScrollSubjectEl(this.elRef.current, this.props.dateProfile);\n      if (subjectEl) {\n        const originEl = subjectEl.closest('.fc-daygrid-body');\n        const scrollEl = originEl.closest('.fc-scroller');\n        const scrollTop = subjectEl.getBoundingClientRect().top - originEl.getBoundingClientRect().top;\n        scrollEl.scrollTop = scrollTop ? scrollTop + 1 : 0; // overcome border\n      }\n      this.needsScrollReset = false;\n    }\n  }\n}\nfunction getScrollSubjectEl(containerEl, dateProfile) {\n  let el;\n  if (dateProfile.currentRangeUnit.match(/year|month/)) {\n    el = containerEl.querySelector(`[data-date=\"${formatIsoMonthStr(dateProfile.currentDate)}-01\"]`);\n    // even if view is month-based, first-of-month might be hidden...\n  }\n  if (!el) {\n    el = containerEl.querySelector(`[data-date=\"${formatDayString(dateProfile.currentDate)}\"]`);\n    // could still be hidden if an interior-view hidden day\n  }\n  return el;\n}\nclass DayTableSlicer extends Slicer {\n  constructor() {\n    super(...arguments);\n    this.forceDayIfListItem = true;\n  }\n  sliceRange(dateRange, dayTableModel) {\n    return dayTableModel.sliceRange(dateRange);\n  }\n}\nclass DayTable extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.slicer = new DayTableSlicer();\n    this.tableRef = createRef();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    return createElement(Table, Object.assign({\n      ref: this.tableRef\n    }, this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel), {\n      dateProfile: props.dateProfile,\n      cells: props.dayTableModel.cells,\n      colGroupNode: props.colGroupNode,\n      tableMinWidth: props.tableMinWidth,\n      renderRowIntro: props.renderRowIntro,\n      dayMaxEvents: props.dayMaxEvents,\n      dayMaxEventRows: props.dayMaxEventRows,\n      showWeekNumbers: props.showWeekNumbers,\n      expandRows: props.expandRows,\n      headerAlignElRef: props.headerAlignElRef,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      forPrint: props.forPrint\n    }));\n  }\n}\nclass DayTableView extends TableView {\n  constructor() {\n    super(...arguments);\n    this.buildDayTableModel = memoize(buildDayTableModel);\n    this.headerRef = createRef();\n    this.tableRef = createRef();\n    // can't override any lifecycle methods from parent\n  }\n  render() {\n    let {\n      options,\n      dateProfileGenerator\n    } = this.context;\n    let {\n      props\n    } = this;\n    let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator);\n    let headerContent = options.dayHeaders && createElement(DayHeader, {\n      ref: this.headerRef,\n      dateProfile: props.dateProfile,\n      dates: dayTableModel.headerDates,\n      datesRepDistinctDays: dayTableModel.rowCnt === 1\n    });\n    let bodyContent = contentArg => createElement(DayTable, {\n      ref: this.tableRef,\n      dateProfile: props.dateProfile,\n      dayTableModel: dayTableModel,\n      businessHours: props.businessHours,\n      dateSelection: props.dateSelection,\n      eventStore: props.eventStore,\n      eventUiBases: props.eventUiBases,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      nextDayThreshold: options.nextDayThreshold,\n      colGroupNode: contentArg.tableColGroupNode,\n      tableMinWidth: contentArg.tableMinWidth,\n      dayMaxEvents: options.dayMaxEvents,\n      dayMaxEventRows: options.dayMaxEventRows,\n      showWeekNumbers: options.weekNumbers,\n      expandRows: !props.isHeightAuto,\n      headerAlignElRef: this.headerElRef,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      forPrint: props.forPrint\n    });\n    return options.dayMinWidth ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth) : this.renderSimpleLayout(headerContent, bodyContent);\n  }\n}\nfunction buildDayTableModel(dateProfile, dateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n  return new DayTableModel(daySeries, /year|month|week/.test(dateProfile.currentRangeUnit));\n}\nclass TableDateProfileGenerator extends DateProfileGenerator {\n  // Computes the date range that will be rendered\n  buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n    let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay);\n    let {\n      props\n    } = this;\n    return buildDayTableRenderRange({\n      currentRange: renderRange,\n      snapToWeek: /^(year|month)$/.test(currentRangeUnit),\n      fixedWeekCount: props.fixedWeekCount,\n      dateEnv: props.dateEnv\n    });\n  }\n}\nfunction buildDayTableRenderRange(props) {\n  let {\n    dateEnv,\n    currentRange\n  } = props;\n  let {\n    start,\n    end\n  } = currentRange;\n  let endOfWeek;\n  // year and month views should be aligned with weeks. this is already done for week\n  if (props.snapToWeek) {\n    start = dateEnv.startOfWeek(start);\n    // make end-of-week if not already\n    endOfWeek = dateEnv.startOfWeek(end);\n    if (endOfWeek.valueOf() !== end.valueOf()) {\n      end = addWeeks(endOfWeek, 1);\n    }\n  }\n  // ensure 6 weeks\n  if (props.fixedWeekCount) {\n    // TODO: instead of these date-math gymnastics (for multimonth view),\n    // compute dateprofiles of all months, then use start of first and end of last.\n    let lastMonthRenderStart = dateEnv.startOfWeek(dateEnv.startOfMonth(addDays(currentRange.end, -1)));\n    let rowCnt = Math.ceil(\n    // could be partial weeks due to hiddenDays\n    diffWeeks(lastMonthRenderStart, end));\n    end = addWeeks(end, 6 - rowCnt);\n  }\n  return {\n    start,\n    end\n  };\n}\nvar css_248z = \":root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}\";\ninjectStyles(css_248z);\nexport { DayTableView as DayGridView, DayTable, DayTableSlicer, Table, TableDateProfileGenerator, TableRows, TableView, buildDayTableModel, buildDayTableRenderRange };", "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayGridView as DayTableView, TableDateProfileGenerator } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nvar index = createPlugin({\n  name: '@fullcalendar/daygrid',\n  initialView: 'dayGridMonth',\n  views: {\n    dayGrid: {\n      component: DayTableView,\n      dateProfileGeneratorClass: TableDateProfileGenerator\n    },\n    dayGridDay: {\n      type: 'dayGrid',\n      duration: {\n        days: 1\n      }\n    },\n    dayGridWeek: {\n      type: 'dayGrid',\n      duration: {\n        weeks: 1\n      }\n    },\n    dayGridMonth: {\n      type: 'dayGrid',\n      duration: {\n        months: 1\n      },\n      fixedWeekCount: true\n    },\n    dayGridYear: {\n      type: 'dayGrid',\n      duration: {\n        years: 1\n      }\n    }\n  }\n});\nexport { index as default };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,YAAN,cAAwB,cAAc;AAAA,EACpC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,cAAc,EAAU;AAAA,EAC/B;AAAA,EACA,mBAAmB,kBAAkB,aAAa;AAChD,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,CAAC;AAChB,QAAI,oBAAoB,qBAAqB,QAAQ,OAAO;AAC5D,QAAI,kBAAkB;AACpB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA,UAChB,YAAY;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AACA,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AACD,WAAO,EAAc,eAAe;AAAA,MAClC,WAAW,CAAC,YAAY;AAAA,MACxB,UAAU,QAAQ;AAAA,IACpB,GAAG,EAAc,kBAAkB;AAAA,MACjC,QAAQ,CAAC,MAAM,gBAAgB,CAAC,MAAM;AAAA,MACtC,kBAAkB,MAAM;AAAA,MACxB,MAAM,CAAC;AAAA,MACP;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB,kBAAkB,aAAa,QAAQ,aAAa;AACtE,QAAI,aAAa,KAAK,QAAQ,YAAY;AAC1C,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAChD;AACA,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,oBAAoB,CAAC,MAAM,YAAY,qBAAqB,QAAQ,OAAO;AAC/E,QAAI,wBAAwB,CAAC,MAAM,YAAY,yBAAyB,QAAQ,OAAO;AACvF,QAAI,WAAW,CAAC;AAChB,QAAI,kBAAkB;AACpB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,CAAC;AAAA,UACP,KAAK;AAAA,UACL,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA,UAChB,YAAY;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,aAAS,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACX,CAAC;AAAA,IACH,CAAC;AACD,QAAI,uBAAuB;AACzB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,CAAC;AAAA,UACP,KAAK;AAAA,UACL,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO,EAAc,eAAe;AAAA,MAClC,WAAW,CAAC,YAAY;AAAA,MACxB,UAAU,QAAQ;AAAA,IACpB,GAAG,EAAc,YAAY;AAAA,MAC3B,QAAQ,CAAC,MAAM,gBAAgB,CAAC,MAAM;AAAA,MACtC,UAAU,MAAM;AAAA,MAChB,kBAAkB,MAAM;AAAA,MACxB,WAAW,CAAC;AAAA,QACV,MAAM,CAAC;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,CAAC;AAAA,MACD;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,eAAe,MAAM,QAAQ;AACpC,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,UAAM,CAAC,IAAI,CAAC;AAAA,EACd;AACA,WAAS,OAAO,MAAM;AACpB,UAAM,IAAI,GAAG,EAAE,KAAK,GAAG;AAAA,EACzB;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,MAAM,QAAQ;AACzC,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,UAAM,CAAC,IAAI,CAAC;AAAA,EACd;AACA,WAAS,OAAO,MAAM;AACpB,UAAM,IAAI,QAAQ,EAAE,KAAK,GAAG;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,IAAI,QAAQ;AACzC,MAAI,QAAQ,CAAC;AACb,MAAI,CAAC,IAAI;AACP,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,YAAM,CAAC,IAAI;AAAA,IACb;AAAA,EACF,OAAO;AACL,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,YAAM,CAAC,IAAI;AAAA,QACT,mBAAmB,GAAG;AAAA,QACtB,SAAS,GAAG;AAAA,QACZ,MAAM,CAAC;AAAA,MACT;AAAA,IACF;AACA,aAAS,OAAO,GAAG,MAAM;AACvB,YAAM,IAAI,GAAG,EAAE,KAAK,KAAK,GAAG;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,kCAAkC,gBAAgB;AAAA,EACtD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAU;AACZ,CAAC;AACD,SAAS,mBAAmB,KAAK;AAC/B,MAAI;AAAA,IACF;AAAA,EACF,IAAI,IAAI,WAAW;AACnB,SAAO,YAAY,eAAe,YAAY,UAAU,CAAC,IAAI,WAAW,IAAI,UAAU,IAAI,aAAa,IAAI;AAAA,EAE3G,IAAI;AAAA,EAEJ,IAAI;AAEN;AACA,IAAM,kBAAN,cAA8B,cAAc;AAAA,EAC1C,SAAS;AACP,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,WAAO,EAAc,eAAe,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,MAC3D,WAAW,CAAC,oBAAoB,0BAA0B,YAAY;AAAA,MACtE,mBAAmB;AAAA,MACnB,wBAAwB,MAAM;AAAA,MAC9B,iBAAiB,CAAC,MAAM,IAAI,WAAW,IAAI;AAAA,IAC7C,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC7C,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,QAAQ,mBAAmB;AAC5C,QAAI,WAAW,iBAAiB,KAAK,YAAY,SAAS,MAAM,MAAM,sBAAsB;AAC5F,WAAO,EAAc,gBAAgB,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,MAC5D,OAAO;AAAA,MACP,WAAW,CAAC,oBAAoB,sBAAsB;AAAA,MACtD,SAAS,kBAAkB,MAAM,KAAK,OAAO;AAAA,MAC7C,kBAAkB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,mBAAmB,aAAa;AACvC,SAAO,EAAc,GAAU,MAAM,EAAc,OAAO;AAAA,IACxD,WAAW;AAAA,IACX,OAAO;AAAA,MACL,aAAa,YAAY,eAAe,YAAY;AAAA,IACtD;AAAA,EACF,CAAC,GAAG,YAAY,YAAY,EAAc,OAAO;AAAA,IAC/C,WAAW;AAAA,EACb,GAAG,YAAY,QAAQ,GAAG,EAAc,OAAO;AAAA,IAC7C,WAAW;AAAA,EACb,GAAG,YAAY,MAAM,SAAS,EAAc,GAAU,MAAM,GAAQ,CAAC,CAAC;AACxE;AACA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC5C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,cAAc,QAAQ,WAAW;AAAA,EACxC;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK,YAAY,MAAM,gBAAgB;AAC3C,WAAO,EAAc,mBAAmB;AAAA,MACtC,WAAW,CAAC,sBAAsB;AAAA,MAClC,aAAa,MAAM;AAAA,MACnB,YAAY,MAAM;AAAA,MAClB,YAAY,MAAM;AAAA,MAClB,SAAS,MAAM;AAAA,MACf;AAAA,MACA,YAAY;AAAA,MACZ,gBAAgB,MAAM;AAAA,MACtB,cAAc,MAAM;AAAA,MACpB,eAAe,MAAM;AAAA,MACrB,gBAAgB,MAAM;AACpB,YAAI,qBAAqB,MAAM,YAAY,MAAM,UAAU,oBAAoB,UAAU,MAAM,cAAc,MAAM,YAAY,oBAAoB,SAAS,CAAC;AAC7J,eAAO,EAAc,GAAU,MAAM,QAAQ,IAAI,SAAO;AACtD,cAAI,aAAa,IAAI,WAAW,SAAS;AACzC,iBAAO,EAAc,OAAO;AAAA,YAC1B,WAAW;AAAA,YACX,KAAK;AAAA,YACL,OAAO;AAAA,cACL,YAAY,kBAAkB,UAAU,IAAI,WAAW;AAAA,YACzD;AAAA,UACF,GAAG,mBAAmB,GAAG,IAAI,EAAc,oBAAoB,OAAO,OAAO;AAAA,YAC3E;AAAA,YACA,YAAY;AAAA,YACZ,YAAY,eAAe,MAAM;AAAA,YACjC,wBAAwB;AAAA,UAC1B,GAAG,WAAW,KAAK,MAAM,UAAU,CAAC,CAAC,IAAI,EAAc,iBAAiB,OAAO,OAAO;AAAA,YACpF;AAAA,YACA,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,iBAAiB;AAAA,YACjB,YAAY,eAAe,MAAM;AAAA,YACjC,wBAAwB;AAAA,UAC1B,GAAG,WAAW,KAAK,MAAM,UAAU,CAAC,CAAC,CAAC;AAAA,QACxC,CAAC,CAAC;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,YAAY,kBAAkB;AACrC,MAAI,UAAU,CAAC;AACf,MAAI,gBAAgB,CAAC;AACrB,WAAS,aAAa,kBAAkB;AACtC,YAAQ,KAAK,UAAU,GAAG;AAC1B,QAAI,CAAC,UAAU,WAAW;AACxB,oBAAc,KAAK,UAAU,GAAG;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,0BAA0B,gBAAgB;AAAA,EAC9C,MAAM;AACR,CAAC;AACD,IAAM,YAAN,cAAwB,cAAc;AAAA,EACpC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,YAAY,EAAU;AAC3B,SAAK,QAAQ;AAAA,MACX,aAAa,eAAe;AAAA,IAC9B;AACA,SAAK,eAAe,QAAM;AACxB,aAAO,KAAK,WAAW,EAAE;AACzB,aAAO,KAAK,MAAM,OAAO,EAAE;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM,eAAe,MAAM,iBAAiB,wBAAwB,MAAM,YAAY,cAAc,OAAO;AAC3G,WAAO,EAAc,kBAAkB;AAAA,MACrC,OAAO;AAAA,MACP,OAAO,KAAK;AAAA,MACZ,WAAW,CAAC,kBAAkB,GAAI,MAAM,mBAAmB,CAAC,CAAE;AAAA,MAC9D,SAAS,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,cAAc,GAAG,MAAM,gBAAgB;AAAA,QAClG,mBAAmB,MAAM;AAAA,MAC3B,IAAI,CAAC,CAAC,GAAG;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,MACD,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,MAClB,eAAe,MAAM;AAAA,MACrB;AAAA,MACA,kBAAkB,MAAM;AAAA,IAC1B,GAAG,CAAC,cAAc,gBAAgB,EAAc,OAAO;AAAA,MACrD,KAAK,MAAM;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,QACL,WAAW,MAAM;AAAA,MACnB;AAAA,IACF,GAAG,MAAM,kBAAkB,EAAc,qBAAqB;AAAA,MAC5D,OAAO;AAAA,MACP,WAAW,CAAC,wBAAwB;AAAA,MACpC,SAAS,kBAAkB,SAAS,MAAM,MAAM;AAAA,MAChD;AAAA,MACA,eAAe;AAAA,IACjB,CAAC,GAAG,CAAC,YAAY,eAAe,MAAM,iBAAiB,wBAAwB,OAAO,KAAK,MAAM,eAAe,EAAc,OAAO;AAAA,MACnI,WAAW;AAAA,IACb,GAAG,EAAc,cAAc;AAAA,MAC7B,OAAO;AAAA,MACP,WAAW,CAAC,yBAAyB,gBAAgB,wBAAwB;AAAA,MAC7E,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,SAAS,IAAI,CAAC,GAAG;AAAA,QAC1E,IAAI,MAAM;AAAA,MACZ,CAAC;AAAA,IACH,CAAC,CAAC,IAAI,MAAM;AAAA;AAAA,MAEZ,EAAc,OAAO;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,UACL,YAAY;AAAA,QACd;AAAA,MACF,GAAG,EAAc,KAAK;AAAA,QACpB,WAAW;AAAA,MACb,GAAG,GAAQ,CAAC;AAAA,QAAI,QAAW,EAAc,OAAO;AAAA,MAC9C,WAAW;AAAA,MACX,KAAK,MAAM;AAAA,IACb,GAAG,MAAM,WAAW,EAAc,OAAO;AAAA,MACvC,WAAW;AAAA,MACX,OAAO;AAAA,QACL,WAAW,MAAM;AAAA,MACnB;AAAA,IACF,GAAG,EAAc,mBAAmB;AAAA,MAClC,YAAY;AAAA,MACZ,kBAAkB,MAAM;AAAA,MACxB,SAAS,MAAM;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc,CAAC,MAAM;AAAA,MACrB,eAAe,MAAM;AAAA,MACrB,aAAa,MAAM;AAAA,MACnB,gBAAgB,MAAM;AAAA,MACtB,WAAW,MAAM;AAAA,MACjB,aAAa,MAAM;AAAA,MACnB,YAAY,MAAM;AAAA,IACpB,CAAC,CAAC,CAAC,GAAG,EAAc,OAAO;AAAA,MACzB,WAAW;AAAA,IACb,GAAG,MAAM,SAAS,CAAC,CAAC;AAAA,EACtB;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,MAAM,iBAAiB,EAAc,GAAU,MAAM,GAAQ;AACtE;AACA,SAAS,wBAAwB,MAAM,cAAc,SAAS;AAC5D,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,KAAK;AAAA,EACP,IAAI;AACJ,QAAM,iBAAiB,MAAM,YAAY,EAAE;AAC3C,QAAM,mBAAmB,QAAQ,QAAQ,YAAY;AACrD,QAAM,oBAAoB,QAAQ,SAAS,YAAY;AACvD,QAAM,kBAAkB,QAAQ,QAAQ,cAAc;AACtD,QAAM,mBAAmB,QAAQ,SAAS,cAAc;AAExD,SAAO,EAAE,qBAAqB,mBAAmB,sBAAsB,qBAAqB;AAAA;AAAA,IAE5F,KAAK,QAAQ,MAAM,aAAa,QAAQ;AAAA,IAExC,QAAQ,OAAO,IAAI,MAAM,KAAK,KAAK,QAAQ,IAAI,WAAW,QAAQ;AAAA,EAAC;AACrE;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,IAAI,WAAW,SAAS,aAAa,MAAM,IAAI;AACxD;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,eAAe,GAAG,IAAI,MAAM,IAAI;AACzC;AACA,SAAS,sBAAsB,MAE/B,cAAc,iBAAiB,aAAa,YAAY,kBAAkB,OAAO;AAC/E,MAAI,YAAY,IAAI,oBAAoB,cAAY;AAElD,QAAI,SAAS,KAAK,SAAS,KAAK,EAAE,WAAW,SAAS,aAAa,MAAM,SAAS,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM;AAE1H,WAAO,WAAW,MAAM,KAAK;AAAA,EAC/B,CAAC;AACD,YAAU,iBAAiB;AAC3B,YAAU,cAAc;AACxB,MAAI,iBAAiB,QAAQ,oBAAoB,MAAM;AACrD,cAAU,WAAW;AACrB,cAAU,iBAAiB;AAAA,EAC7B,WAAW,OAAO,iBAAiB,UAAU;AAC3C,cAAU,cAAc;AAAA,EAC1B,WAAW,OAAO,oBAAoB,UAAU;AAC9C,cAAU,cAAc;AACxB,cAAU,iBAAiB;AAAA,EAC7B;AAEA,MAAI,YAAY,CAAC;AACjB,MAAI,oBAAoB,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,SAAS,eAAe,GAAG;AAC/B,QAAI,cAAc,WAAW,MAAM;AACnC,QAAI,eAAe,MAAM;AACvB,gBAAU,KAAK;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,OAAO,IAAI;AAAA,UACX,KAAK,IAAI,UAAU;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,wBAAkB,KAAK,GAAG;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,gBAAgB,UAAU,QAAQ,SAAS;AAC/C,MAAI,WAAW,UAAU,QAAQ;AACjC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,WAAW,UAAU,MAAM,KAAK;AACpC,MAAI,WAAW,CAAC;AAChB,MAAI,iBAAiB,CAAC;AAEtB,WAAS,OAAO,mBAAmB;AACjC,uBAAmB,IAAI,QAAQ,EAAE,KAAK;AAAA,MACpC;AAAA,MACA,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AACD,aAAS,MAAM,IAAI,UAAU,OAAO,IAAI,SAAS,OAAO,GAAG;AACzD,0BAAoB,GAAG,EAAE,KAAK;AAAA,QAC5B,KAAK,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK;AAAA,QACxC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,aAAS,KAAK,CAAC;AAAA,EACjB;AACA,WAAS,eAAe,eAAe;AACrC,QAAI,MAAM,KAAK,YAAY,KAAK;AAChC,QAAI,aAAa,YAAY;AAC7B,uBAAmB,WAAW,KAAK,EAAE,KAAK;AAAA,MACxC,KAAK,WAAW,KAAK,WAAW,OAAO,WAAW,KAAK,KAAK;AAAA,MAC5D,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AACD,aAAS,MAAM,WAAW,OAAO,MAAM,WAAW,KAAK,OAAO,GAAG;AAC/D,eAAS,GAAG,KAAK;AACjB,0BAAoB,GAAG,EAAE,KAAK;AAAA,QAC5B,KAAK,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK;AAAA,QACxC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,mBAAe,KAAK,gBAAgB,GAAG,CAAC;AAAA,EAC1C;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,WAAW,UAAU,MAAM,OAAO;AACzC,MAAI,iBAAiB,oBAAoB,UAAU,MAAM,MAAM;AAC/D,MAAI,sBAAsB,CAAC;AAC3B,MAAI,qBAAqB,CAAC;AAC1B,MAAI,kBAAkB,CAAC;AACvB,WAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO,GAAG;AAC9C,QAAI,QAAQ,eAAe,GAAG;AAE9B,QAAI,mBAAmB,CAAC;AACxB,QAAI,gBAAgB;AACpB,QAAI,mBAAmB;AACvB,aAAS,QAAQ,OAAO;AACtB,UAAI,MAAM,KAAK,KAAK,KAAK;AACzB,uBAAiB,KAAK;AAAA,QACpB,KAAK,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK;AAAA,QACxC,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK,aAAa;AAAA,MAC/B,CAAC;AACD,sBAAgB,KAAK,aAAa,KAAK;AAAA,IACzC;AAEA,QAAI,kBAAkB,CAAC;AACvB,oBAAgB;AAChB,uBAAmB;AACnB,aAAS,QAAQ,OAAO;AACtB,UAAI,MAAM,KAAK,KAAK,KAAK;AACzB,UAAI,aAAa,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ;AACnD,UAAI,aAAa,KAAK,KAAK,UAAU;AACrC,0BAAoB,KAAK,aAAa;AACtC,sBAAgB,KAAK,aAAa,KAAK;AACvC,UAAI,YAAY;AACd,4BAAoB,KAAK;AACzB,YAAI,YAAY;AACd,0BAAgB,KAAK;AAAA,YACnB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK;AAAA,YAC1D,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,aAAa,KAAK;AAAA,YAClB,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,WAAW,YAAY;AACrB,wBAAgB,KAAK;AAAA,UACnB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK;AAAA,UAC1D,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,aAAa,KAAK;AAAA,UAClB,WAAW;AAAA;AAAA,QACb,CAAC;AACD,2BAAmB;AAAA,MACrB;AAAA,IACF;AACA,wBAAoB,KAAK,gBAAgB;AACzC,uBAAmB,KAAK,eAAe;AACvC,oBAAgB,KAAK,gBAAgB;AAAA,EACvC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,OAAO,QAAQ;AAC1C,MAAI,iBAAiB,CAAC;AACtB,WAAS,MAAM,GAAG,MAAM,QAAQ,OAAO,GAAG;AACxC,mBAAe,KAAK,CAAC,CAAC;AAAA,EACxB;AACA,WAAS,QAAQ,OAAO;AACtB,aAAS,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK,OAAO,GAAG;AAC7D,qBAAe,GAAG,EAAE,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK,WAAW,SAAS,OAAO;AAClD,MAAI,IAAI,aAAa,aAAa,IAAI,YAAY,UAAU,GAAG;AAC7D,WAAO;AAAA,EACT;AACA,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,WAAW;AAC3B,MAAI,cAAc,gBAAgB,WAAW;AAAA,IAC3C,OAAO,MAAM,SAAS,EAAE;AAAA,IACxB,KAAK,QAAQ,MAAM,UAAU,CAAC,EAAE,MAAM,CAAC;AAAA,EACzC,CAAC;AACD,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,GAAG,GAAG;AAAA,IAC3C,UAAU;AAAA,IACV,SAAS,UAAU;AAAA,IACnB,YAAY;AAAA,MACV,KAAK,WAAW;AAAA,MAChB,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,EAAE,GAAG;AAAA,QAClD,kBAAkB;AAAA,MACpB,CAAC;AAAA,MACD,UAAU,WAAW;AAAA,MACrB,OAAO;AAAA,IACT;AAAA,IACA,SAAS,IAAI,WAAW,YAAY,MAAM,QAAQ,MAAM,UAAU,MAAM,QAAQ;AAAA,IAChF,OAAO,IAAI,SAAS,YAAY,IAAI,QAAQ,MAAM,UAAU,IAAI,QAAQ;AAAA,EAC1E,CAAC;AACH;AACA,IAAM,sBAAN,cAAkC,aAAa;AAAA,EAC7C,cAAc;AACZ,UAAM,GAAG,SAAS;AAElB,SAAK,iBAAiB;AAEtB,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,QAAQ,WAAW;AACjB,UAAM,aAAa,MAAM,QAAQ,SAAS;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,WAAS,CAAC,KAAK,YAAY,cAAc,KAAK,CAAC;AAErE,aAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS,GAAG;AAC7D,qBAAe,KAAK,IAAI,eAAe,KAAK,EAAE,OAAO,aAAa;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,WAAW,OAAO,eAAe;AACtD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAI,KAAK,kBAAkB,eAAe;AACxC,YAAM,kBAAkB,cAAc,aAAa;AACnD,UAAI,CAAC,YAAY,eAAe,GAAG;AACjC,YAAI,KAAK,gBAAgB;AAEvB,gBAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG;AAAA,YAClE,MAAM,eAAe,cAAc,MAAM,MAAM,IAAI;AAAA,UACrD,CAAC;AAGD,gBAAM,gBAAgB,cAAc,WAAW;AAC/C,sBAAY,aAAa,IAAI;AAC7B,yBAAe,aAAa,EAAE,eAAe,IAAI;AACjD,wBAAc,KAAK,WAAW;AAC9B,eAAK,WAAW,eAAe,OAAO,aAAa;AAAA,QACrD,OAAO;AACL,sBAAY,eAAe,IAAI;AAC/B,wBAAc,KAAK,aAAa;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAEA,UAAM,uBAAuB,WAAW,OAAO,aAAa;AAAA,EAC9D;AACF;AACA,IAAM,WAAN,cAAuB,cAAc;AAAA,EACnC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,aAAa,IAAI,OAAO;AAC7B,SAAK,cAAc,IAAI,OAAO;AAC9B,SAAK,WAAW,IAAI,OAAO;AAC3B,SAAK,iBAAiB,IAAI,OAAO;AACjC,SAAK,YAAY,EAAU;AAC3B,SAAK,QAAQ;AAAA,MACX,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,YAAY,CAAC;AAAA,IACf;AACA,SAAK,eAAe,cAAY;AAC9B,UAAI,UAAU;AACZ,aAAK,aAAa,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,MAAM,MAAM;AACzB,QAAI,qBAAqB,oBAAoB,MAAM,kBAAkB,MAAM;AAC3E,QAAI,mBAAmB,oBAAoB,MAAM,aAAa,MAAM;AACpE,QAAI,qBAAqB,oBAAoB,KAAK,iBAAiB,GAAG,MAAM;AAC5E,QAAI,kBAAkB,oBAAoB,KAAK,cAAc,GAAG,MAAM;AACtE,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,sBAAsB,cAAc,MAAM,aAAa,QAAQ,UAAU,GAAG,MAAM,cAAc,MAAM,iBAAiB,QAAQ,kBAAkB,MAAM,YAAY,MAAM,kBAAkB,MAAM,KAAK;AAC1M,QAAI;AAAA;AAAA,MAEJ,MAAM,aAAa,MAAM,UAAU,qBAAqB,MAAM,eAAe,MAAM,YAAY,qBAAqB,CAAC;AAAA;AACrH,WAAO,EAAc,MAAM;AAAA,MACzB,KAAK,KAAK;AAAA,MACV,MAAM;AAAA,IACR,GAAG,MAAM,eAAe,MAAM,YAAY,GAAG,MAAM,MAAM,IAAI,CAAC,MAAM,QAAQ;AAC1E,UAAI,gBAAgB,KAAK,aAAa,KAAK,MAAM,WAAW,oBAAoB,GAAG,IAAI,mBAAmB,GAAG,GAAG,MAAM,YAAY,iBAAiB;AACnJ,UAAI,gBAAgB,KAAK,aAAa,KAAK,sBAAsB,gBAAgB,GAAG,GAAG,kBAAkB,GAAG,MAAM,YAAY,CAAC,GAAG,QAAQ,MAAM,SAAS,GAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AAC7L,aAAO,EAAc,WAAW;AAAA,QAC9B,KAAK,KAAK;AAAA,QACV,OAAO,KAAK,WAAW,UAAU,KAAK,GAAG;AAAA,QACzC,YAAY,KAAK,YAAY,UAAU,KAAK,GAAG;AAAA,QAC/C,aAAa,MAAM;AAAA,QACnB,MAAM,KAAK;AAAA,QACX,eAAe,MAAM;AAAA,QACrB,gBAAgB,MAAM,mBAAmB,QAAQ;AAAA,QACjD,aAAa,MAAM;AAAA,QACnB,YAAY,MAAM;AAAA,QAClB,gBAAgB,MAAM;AAAA,QACtB,WAAW,MAAM;AAAA,QACjB,aAAa,MAAM;AAAA,QACnB,kBAAkB,KAAK;AAAA,QACvB,gBAAgB,KAAK;AAAA,QACrB,iBAAiB,KAAK;AAAA,QACtB,eAAe,KAAK;AAAA,QACpB,SAAS,SAAS,GAAG;AAAA,QACrB,eAAe,eAAe,GAAG;AAAA,QACjC,kBAAkB,oBAAoB,GAAG;AAAA,QACzC,gBAAgB,KAAK,SAAS,UAAU,KAAK,GAAG;AAAA,QAChD;AAAA;AAAA,UAEA,EAAc,GAAU,MAAM,EAAc,GAAU,MAAM,aAAa,GAAG,EAAc,GAAU,MAAM,aAAa,CAAC;AAAA;AAAA,QACxH;AAAA;AAAA,UAEA,EAAc,GAAU,MAAM,KAAK,eAAe,mBAAmB,GAAG,GAAG,WAAW,GAAG,KAAK,eAAe,mBAAmB,GAAG,GAAG,cAAc,GAAG,KAAK,eAAe,iBAAiB,GAAG,GAAG,UAAU,CAAC;AAAA;AAAA,QAC7M,WAAW,MAAM;AAAA,MACnB,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB;AAClB,SAAK,aAAa,IAAI;AACtB,SAAK,QAAQ,iBAAiB,KAAK,YAAY;AAAA,EACjD;AAAA,EACA,mBAAmB,WAAW,WAAW;AACvC,QAAI,eAAe,KAAK;AACxB,SAAK,aAAa,CAAC,aAAa,WAAW,YAAY,CAAC;AAAA,EAC1D;AAAA,EACA,uBAAuB;AACrB,SAAK,QAAQ,oBAAoB,KAAK,YAAY;AAAA,EACpD;AAAA,EACA,mBAAmB;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,MAAM,aAAa,MAAM,UAAU,KAAK,QAAQ;AAElD,aAAO,MAAM,UAAU;AAAA,IACzB;AACA,QAAI,MAAM,eAAe,MAAM,YAAY,KAAK,QAAQ;AAEtD,aAAO,MAAM,YAAY;AAAA,IAC3B;AACA,WAAO,MAAM;AAAA,EACf;AAAA,EACA,gBAAgB;AACd,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,MAAM,eAAe,MAAM,YAAY,KAAK,QAAQ;AAEtD,aAAO,MAAM,YAAY;AAAA,IAC3B;AACA,WAAO,CAAC;AAAA,EACV;AAAA,EACA,aAAa,KAAK,eAAe,YAAY,mBAAmB,YAAY,YAAY,iBAAiB;AACvG,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,QAAI,yBAAyB,KAAK,MAAM,MAAM,WAAW;AACzD,QAAI,WAAW,cAAc,cAAc;AAC3C,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB;AAClB,eAAS,aAAa,eAAe;AACnC,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,YAAI;AAAA,UACF;AAAA,QACF,IAAI,IAAI,WAAW;AACnB,YAAI,YAAY,UAAU,aAAa,CAAC,kBAAkB,UAAU;AACpE,YAAI,aAAa,UAAU;AAC3B,YAAI,OAAO;AACX,YAAI,QAAQ;AACZ,YAAI,YAAY;AACd,cAAI,QAAQ,OAAO;AACjB,oBAAQ;AACR,mBAAO,eAAe,MAAM,IAAI,OAAO,IAAI,eAAe,MAAM,IAAI,QAAQ;AAAA,UAC9E,OAAO;AACL,mBAAO;AACP,oBAAQ,eAAe,OAAO,IAAI,QAAQ,IAAI,eAAe,OAAO,IAAI,OAAO;AAAA,UACjF;AAAA,QACF;AAKA,cAAM,KAAK,EAAc,OAAO;AAAA,UAC9B,WAAW,8BAA8B,aAAa,kCAAkC;AAAA,UACxF,KAAK,eAAe,GAAG;AAAA,UACvB,KAAK,WAAW,OAAO,KAAK,eAAe,UAAU,eAAe,GAAG,CAAC;AAAA,UACxE,OAAO;AAAA,YACL,YAAY,YAAY,KAAK;AAAA,YAC7B,WAAW,aAAa,KAAK,UAAU;AAAA,YACvC,KAAK,aAAa,UAAU,cAAc;AAAA,YAC1C;AAAA,YACA;AAAA,UACF;AAAA,QACF,GAAG,mBAAmB,GAAG,IAAI,EAAc,oBAAoB,OAAO,OAAO;AAAA,UAC3E;AAAA,UACA;AAAA,UACA,YAAY,eAAe;AAAA,UAC3B;AAAA,QACF,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,IAAI,EAAc,iBAAiB,OAAO,OAAO;AAAA,UAC9E;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,eAAe;AAAA,UAC3B;AAAA,QACF,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,MAAM,UAAU;AAC7B,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,QAAI;AAAA,MACF;AAAA,IACF,IAAI,KAAK;AACT,QAAI,QAAQ,CAAC;AACb,QAAI,gBAAgB;AAClB,eAAS,OAAO,MAAM;AACpB,YAAI,eAAe,QAAQ;AAAA,UACzB,OAAO;AAAA,UACP,MAAM,eAAe,MAAM,IAAI,OAAO,IAAI,eAAe,MAAM,IAAI,QAAQ;AAAA,QAC7E,IAAI;AAAA,UACF,MAAM;AAAA,UACN,OAAO,eAAe,OAAO,IAAI,QAAQ,IAAI,eAAe,OAAO,IAAI,OAAO;AAAA,QAChF;AACA,cAAM,KAAK,EAAc,OAAO;AAAA,UAC9B,KAAK,mBAAmB,IAAI,UAAU;AAAA,UACtC,WAAW;AAAA,UACX,OAAO;AAAA,QACT,GAAG,aAAa,aAAa,EAAc,SAAS,OAAO,OAAO;AAAA,UAChE;AAAA,QACF,GAAG,WAAW,KAAK,UAAU,CAAC,CAAC,IAAI,WAAW,QAAQ,CAAC,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,WAAO,EAAc,GAAU,CAAC,GAAG,GAAG,KAAK;AAAA,EAC7C;AAAA,EACA,aAAa,wBAAwB;AACnC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM,YAAY,MAAM,gBAAgB,MAC3C;AACA,UAAI,wBAAwB;AAC1B,YAAI,WAAW,MAAM,MAAM,IAAI,UAAQ,YAAY,WAAW,KAAK,GAAG,CAAC;AACvE,YAAI,SAAS,QAAQ;AACnB,cAAI,WAAW,KAAK,UAAU;AAC9B,cAAI,mBAAmB,IAAI;AAAA,YAAc;AAAA,YAAU;AAAA,YAAU;AAAA;AAAA,YAE7D;AAAA,UAAK;AACL,cAAI,CAAC,MAAM,kBAAkB,CAAC,MAAM,eAAe,UAAU,gBAAgB,GAAG;AAC9E,iBAAK,SAAS;AAAA,cACZ,gBAAgB,IAAI;AAAA,gBAAc;AAAA,gBAAU;AAAA,gBAAU;AAAA;AAAA,gBAEtD;AAAA,cAAK;AAAA,YACP,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,MAAM;AACjC,YAAM,gBAAgB,KAAK,gBAAgB;AAC3C,YAAM,uBAAuB,MAAM,iBAAiB,QAAQ,MAAM,oBAAoB;AACtF,WAAK,aAAa;AAAA;AAAA;AAAA;AAAA,QAIhB,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,aAAa;AAAA,QACzE,kBAAkB,uBAAuB,KAAK,wBAAwB,IAAI;AAAA,MAC5E,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,WAAW,KAAK,eAAe;AACnC,QAAI,aAAa,CAAC;AAElB,aAAS,UAAU,UAAU;AAC3B,UAAI,SAAS,KAAK,MAAM,SAAS,MAAM,EAAE,sBAAsB,EAAE,MAAM;AACvE,iBAAW,MAAM,IAAI,KAAK,IAAI,WAAW,MAAM,KAAK,GAAG,MAAM;AAAA,IAC/D;AACA,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AACxB,QAAI,WAAW,KAAK,MAAM,MAAM,CAAC,EAAE;AACnC,QAAI,SAAS,KAAK,WAAW,WAAW,QAAQ;AAChD,QAAI,gBAAgB,KAAK,SAAS,WAAW,QAAQ;AACrD,WAAO,OAAO,sBAAsB,EAAE,SAAS,cAAc,sBAAsB,EAAE;AAAA,EACvF;AAAA,EACA,aAAa;AACX,QAAI,QAAQ,KAAK,WAAW;AAC5B,WAAO,KAAK,MAAM,MAAM,IAAI,UAAQ,MAAM,KAAK,GAAG,CAAC;AAAA,EACrD;AACF;AACA,SAAS,iBAAiB;AAAA,EACxB,YAAY;AACd,CAAC;AACD,SAAS,sBAAsB,YAAY,eAAe;AACxD,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,mBAAmB,qBAAqB,aAAa;AACzD,SAAO,WAAW,IAAI,UAAQ;AAAA,IAC5B;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa,iBAAiB,IAAI,WAAW,SAAS,UAAU;AAAA,IAChE,WAAW;AAAA,EACb,EAAE;AACJ;AACA,SAAS,qBAAqB,eAAe;AAC3C,MAAI,mBAAmB,CAAC;AACxB,WAAS,cAAc,eAAe;AACpC,aAAS,aAAa,YAAY;AAChC,uBAAiB,UAAU,IAAI,WAAW,SAAS,UAAU,IAAI,UAAU;AAAA,IAC7E;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,YAAN,cAAwB,cAAc;AAAA,EACpC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,wBAAwB,QAAQ,cAAc;AACnD,SAAK,mBAAmB,QAAQ,cAAc;AAC9C,SAAK,mBAAmB,QAAQ,cAAc;AAC9C,SAAK,yBAAyB,QAAQ,cAAc;AACpD,SAAK,iBAAiB,QAAQ,qBAAqB;AACnD,SAAK,mBAAmB,QAAQ,qBAAqB;AACrD,SAAK,UAAU,IAAI,OAAO;AAAA,EAC5B;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,MAAM,MAAM;AACzB,QAAI,wBAAwB,KAAK,sBAAsB,MAAM,kBAAkB,MAAM;AACrF,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AACtE,QAAI,yBAAyB,KAAK,uBAAuB,MAAM,mBAAmB,MAAM;AACxF,QAAI,iBAAiB,KAAK,eAAe,MAAM,WAAW,MAAM;AAChE,QAAI,mBAAmB,KAAK,iBAAiB,MAAM,aAAa,MAAM;AAGtE,QAAI,gBAAgB,UAAU,KAAK,MAAM,cAAc,MAAM,cAAc,QAAQ,QAAQ,cAAc,IAAI;AAC7G,WAAO,EAAc,UAAU;AAAA,MAC7B,MAAM;AAAA,IACR,GAAG,CAAC,SAAS,eAAe,EAAc,GAAU,MAAM,MAAM,MAAM,IAAI,CAAC,OAAO,QAAQ,EAAc,UAAU;AAAA,MAChH,KAAK,KAAK,QAAQ,UAAU,GAAG;AAAA,MAC/B,KAAK,MAAM,SAAS,MAAM,CAAC,EAAE,KAAK,YAAY,IAA2D;AAAA,MAEzG,gBAAgB,SAAS;AAAA,MACzB,iBAAiB,MAAM;AAAA,MACvB;AAAA,MACA,aAAa,MAAM;AAAA,MACnB;AAAA,MACA,aAAa,MAAM;AAAA,MACnB,kBAAkB,sBAAsB,GAAG;AAAA,MAC3C,gBAAgB,MAAM;AAAA,MACtB,aAAa,iBAAiB,GAAG,EAAE,OAAO,WAAW;AAAA,MACrD,aAAa,iBAAiB,GAAG;AAAA,MACjC,mBAAmB,uBAAuB,GAAG;AAAA,MAC7C,WAAW,eAAe,GAAG;AAAA,MAC7B,aAAa,iBAAiB,GAAG;AAAA,MACjC,cAAc,MAAM;AAAA,MACpB,iBAAiB,MAAM;AAAA,MACvB,aAAa,MAAM;AAAA,MACnB,cAAc,MAAM;AAAA,MACpB;AAAA,MACA,UAAU,MAAM;AAAA,IAClB,CAAC,CAAC,CAAC,CAAC;AAAA,EACN;AAAA,EACA,oBAAoB;AAClB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,qBAAqB;AAEnB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,+BAA+B;AAC7B,QAAI,CAAC,KAAK,QAAQ;AAGhB,YAAM,cAAc,KAAK,QAAQ,WAAW,CAAC,EAAE,WAAW,EAAE,CAAC;AAC7D,YAAM,SAAS,cAAc,YAAY,QAAQ,kBAAkB,IAAI;AACvE,UAAI,QAAQ;AACV,aAAK,SAAS;AACd,aAAK,QAAQ,6BAA6B,MAAM;AAAA,UAC9C,IAAI;AAAA,UACJ,mBAAmB,KAAK,MAAM;AAAA,QAChC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,QAAQ;AACf,WAAK,QAAQ,+BAA+B,IAAI;AAChD,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA,EAGA,cAAc;AACZ,SAAK,eAAe,IAAI;AAAA,MAAc,KAAK;AAAA,MAAQ,KAAK,QAAQ,QAAQ,EAAE,IAAI,YAAU,OAAO,WAAW,EAAE,CAAC,CAAC;AAAA;AAAA,MAE9G;AAAA,MAAO;AAAA,IAAI;AACX,SAAK,eAAe,IAAI;AAAA,MAAc,KAAK;AAAA,MAAQ,KAAK,QAAQ,WAAW,CAAC,EAAE,WAAW;AAAA;AAAA,MAEzF;AAAA;AAAA,MAEA;AAAA,IAAK;AAAA,EACP;AAAA,EACA,SAAS,cAAc,aAAa;AAClC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,MAAM,aAAa,YAAY,YAAY;AAC/C,QAAI,MAAM,aAAa,WAAW,WAAW;AAC7C,QAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,UAAI,OAAO,KAAK,MAAM,MAAM,GAAG,EAAE,GAAG;AACpC,aAAO;AAAA,QACL,aAAa,KAAK,MAAM;AAAA,QACxB,UAAU,OAAO,OAAO;AAAA,UACtB,OAAO,KAAK,aAAa,KAAK,GAAG;AAAA,UACjC,QAAQ;AAAA,QACV,GAAG,KAAK,aAAa;AAAA,QACrB,OAAO,KAAK,UAAU,KAAK,GAAG;AAAA,QAC9B,MAAM;AAAA,UACJ,MAAM,aAAa,MAAM,GAAG;AAAA,UAC5B,OAAO,aAAa,OAAO,GAAG;AAAA,UAC9B,KAAK,aAAa,KAAK,GAAG;AAAA,UAC1B,QAAQ,aAAa,QAAQ,GAAG;AAAA,QAClC;AAAA,QACA,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,KAAK;AAClB,WAAO,KAAK,QAAQ,WAAW,GAAG,EAAE,WAAW,EAAE,GAAG;AAAA,EACtD;AAAA,EACA,aAAa,KAAK,KAAK;AACrB,QAAI,QAAQ,KAAK,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE;AACvC,QAAI,MAAM,QAAQ,OAAO,CAAC;AAC1B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,IAAI,WAAW,IAAI;AAC5B;AACA,IAAM,QAAN,cAAoB,cAAc;AAAA,EAChC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,QAAQ,EAAU;AACvB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB,iBAAiB,QAAQ,oBAAoB;AAGpE,QAAI,oBAAoB,CAAC,YAAY;AACnC,yBAAmB;AACnB,wBAAkB;AAClB,qBAAe;AAAA,IACjB;AACA,QAAI,aAAa;AAAA,MAAC;AAAA,MAAmB,mBAAmB,6BAA6B;AAAA,MAA8B,aAAa,KAAK;AAAA;AAAA,IACrI;AACA,WAAO,EAAc,OAAO;AAAA,MAC1B,KAAK,KAAK;AAAA,MACV,WAAW,WAAW,KAAK,GAAG;AAAA,MAC9B,OAAO;AAAA;AAAA;AAAA,QAGL,OAAO,MAAM;AAAA,QACb,UAAU,MAAM;AAAA,MAClB;AAAA,IACF,GAAG,EAAc,SAAS;AAAA,MACxB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,QACL,OAAO,MAAM;AAAA,QACb,UAAU,MAAM;AAAA,QAChB,QAAQ,aAAa,MAAM,eAAe;AAAA,MAC5C;AAAA,IACF,GAAG,MAAM,cAAc,EAAc,SAAS;AAAA,MAC5C,MAAM;AAAA,IACR,GAAG,EAAc,WAAW;AAAA,MAC1B,aAAa,MAAM;AAAA,MACnB,OAAO,MAAM;AAAA,MACb,gBAAgB,MAAM;AAAA,MACtB,iBAAiB,MAAM;AAAA,MACvB,aAAa,MAAM;AAAA,MACnB,cAAc,MAAM;AAAA,MACpB,kBAAkB,MAAM;AAAA,MACxB,aAAa,MAAM;AAAA,MACnB,aAAa,MAAM;AAAA,MACnB,mBAAmB,MAAM;AAAA,MACzB,gBAAgB,MAAM;AAAA,MACtB,WAAW,MAAM;AAAA,MACjB,aAAa,MAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA,UAAU,MAAM;AAAA,MAChB,mBAAmB,MAAM;AAAA,IAC3B,CAAC,CAAC,CAAC,CAAC;AAAA,EACN;AAAA,EACA,oBAAoB;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,mBAAmB,WAAW;AAC5B,QAAI,UAAU,gBAAgB,KAAK,MAAM,aAAa;AACpD,WAAK,mBAAmB;AAAA,IAC1B,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,oBAAoB,KAAK,MAAM,aACtC;AACA,YAAM,YAAY,mBAAmB,KAAK,MAAM,SAAS,KAAK,MAAM,WAAW;AAC/E,UAAI,WAAW;AACb,cAAM,WAAW,UAAU,QAAQ,kBAAkB;AACrD,cAAM,WAAW,SAAS,QAAQ,cAAc;AAChD,cAAM,YAAY,UAAU,sBAAsB,EAAE,MAAM,SAAS,sBAAsB,EAAE;AAC3F,iBAAS,YAAY,YAAY,YAAY,IAAI;AAAA,MACnD;AACA,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,aAAa,aAAa;AACpD,MAAI;AACJ,MAAI,YAAY,iBAAiB,MAAM,YAAY,GAAG;AACpD,SAAK,YAAY,cAAc,eAAe,kBAAkB,YAAY,WAAW,CAAC,OAAO;AAAA,EAEjG;AACA,MAAI,CAAC,IAAI;AACP,SAAK,YAAY,cAAc,eAAe,gBAAgB,YAAY,WAAW,CAAC,IAAI;AAAA,EAE5F;AACA,SAAO;AACT;AACA,IAAM,iBAAN,cAA6B,OAAO;AAAA,EAClC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,WAAW,WAAW,eAAe;AACnC,WAAO,cAAc,WAAW,SAAS;AAAA,EAC3C;AACF;AACA,IAAM,WAAN,cAAuB,cAAc;AAAA,EACnC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,SAAS,IAAI,eAAe;AACjC,SAAK,WAAW,EAAU;AAAA,EAC5B;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,EAAc,OAAO,OAAO,OAAO;AAAA,MACxC,KAAK,KAAK;AAAA,IACZ,GAAG,KAAK,OAAO,WAAW,OAAO,MAAM,aAAa,MAAM,kBAAkB,SAAS,MAAM,aAAa,GAAG;AAAA,MACzG,aAAa,MAAM;AAAA,MACnB,OAAO,MAAM,cAAc;AAAA,MAC3B,cAAc,MAAM;AAAA,MACpB,eAAe,MAAM;AAAA,MACrB,gBAAgB,MAAM;AAAA,MACtB,cAAc,MAAM;AAAA,MACpB,iBAAiB,MAAM;AAAA,MACvB,iBAAiB,MAAM;AAAA,MACvB,YAAY,MAAM;AAAA,MAClB,kBAAkB,MAAM;AAAA,MACxB,aAAa,MAAM;AAAA,MACnB,cAAc,MAAM;AAAA,MACpB,UAAU,MAAM;AAAA,IAClB,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,eAAN,cAA2B,UAAU;AAAA,EACnC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,qBAAqB,QAAQ,kBAAkB;AACpD,SAAK,YAAY,EAAU;AAC3B,SAAK,WAAW,EAAU;AAAA,EAE5B;AAAA,EACA,SAAS;AACP,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,KAAK,mBAAmB,MAAM,aAAa,oBAAoB;AACnF,QAAI,gBAAgB,QAAQ,cAAc,EAAc,WAAW;AAAA,MACjE,KAAK,KAAK;AAAA,MACV,aAAa,MAAM;AAAA,MACnB,OAAO,cAAc;AAAA,MACrB,sBAAsB,cAAc,WAAW;AAAA,IACjD,CAAC;AACD,QAAI,cAAc,gBAAc,EAAc,UAAU;AAAA,MACtD,KAAK,KAAK;AAAA,MACV,aAAa,MAAM;AAAA,MACnB;AAAA,MACA,eAAe,MAAM;AAAA,MACrB,eAAe,MAAM;AAAA,MACrB,YAAY,MAAM;AAAA,MAClB,cAAc,MAAM;AAAA,MACpB,gBAAgB,MAAM;AAAA,MACtB,WAAW,MAAM;AAAA,MACjB,aAAa,MAAM;AAAA,MACnB,kBAAkB,QAAQ;AAAA,MAC1B,cAAc,WAAW;AAAA,MACzB,eAAe,WAAW;AAAA,MAC1B,cAAc,QAAQ;AAAA,MACtB,iBAAiB,QAAQ;AAAA,MACzB,iBAAiB,QAAQ;AAAA,MACzB,YAAY,CAAC,MAAM;AAAA,MACnB,kBAAkB,KAAK;AAAA,MACvB,aAAa,WAAW;AAAA,MACxB,cAAc,WAAW;AAAA,MACzB,UAAU,MAAM;AAAA,IAClB,CAAC;AACD,WAAO,QAAQ,cAAc,KAAK,oBAAoB,eAAe,aAAa,cAAc,QAAQ,QAAQ,WAAW,IAAI,KAAK,mBAAmB,eAAe,WAAW;AAAA,EACnL;AACF;AACA,SAAS,mBAAmB,aAAa,sBAAsB;AAC7D,MAAI,YAAY,IAAI,eAAe,YAAY,aAAa,oBAAoB;AAChF,SAAO,IAAI,cAAc,WAAW,kBAAkB,KAAK,YAAY,gBAAgB,CAAC;AAC1F;AACA,IAAM,4BAAN,cAAwC,qBAAqB;AAAA;AAAA,EAE3D,iBAAiB,cAAc,kBAAkB,eAAe;AAC9D,QAAI,cAAc,MAAM,iBAAiB,cAAc,kBAAkB,aAAa;AACtF,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,WAAO,yBAAyB;AAAA,MAC9B,cAAc;AAAA,MACd,YAAY,iBAAiB,KAAK,gBAAgB;AAAA,MAClD,gBAAgB,MAAM;AAAA,MACtB,SAAS,MAAM;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AACA,SAAS,yBAAyB,OAAO;AACvC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AAEJ,MAAI,MAAM,YAAY;AACpB,YAAQ,QAAQ,YAAY,KAAK;AAEjC,gBAAY,QAAQ,YAAY,GAAG;AACnC,QAAI,UAAU,QAAQ,MAAM,IAAI,QAAQ,GAAG;AACzC,YAAM,SAAS,WAAW,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,MAAM,gBAAgB;AAGxB,QAAI,uBAAuB,QAAQ,YAAY,QAAQ,aAAa,QAAQ,aAAa,KAAK,EAAE,CAAC,CAAC;AAClG,QAAI,SAAS,KAAK;AAAA;AAAA,MAElB,UAAU,sBAAsB,GAAG;AAAA,IAAC;AACpC,UAAM,SAAS,KAAK,IAAI,MAAM;AAAA,EAChC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,WAAW;AACf,aAAa,QAAQ;;;AC/zCrB,IAAI,QAAQ,aAAa;AAAA,EACvB,MAAM;AAAA,EACN,aAAa;AAAA,EACb,OAAO;AAAA,IACL,SAAS;AAAA,MACP,WAAW;AAAA,MACX,2BAA2B;AAAA,IAC7B;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,QACR,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA,gBAAgB;AAAA,IAClB;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;", "names": []}