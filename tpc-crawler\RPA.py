import redis
import concurrent.futures
import requests
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.options import Options
from pymongo import MongoClient
import time
import re
import json
import sys

r = redis.Redis(host='localhost', port=6379, db=0)
update = False
client = MongoClient(host='localhost', port=27017)
options = Options()
options.add_argument("--start-maximized")
service = Service(ChromeDriverManager().install())
driver = webdriver.Chrome(service=service,options=options)
driver.set_window_size(800, 700) 
wait = WebDriverWait(driver, 30)
shortwait = WebDriverWait(driver, 2)
#db = client["web"]
#col = db["project_case_acceptance"]

#data = sys.argv[1]

mongodb_dict = {
    "需求人": "Requester",
    "原尖峰契約容量": "Original_Peak_Contract_Capacity",
    "繳費金額": "Payment_Amount",
    "部門": "Department",
    "欲變更尖峰契約容量": "Desired_Peak_Contract_Capacity",
    "結案時間": "Case_Closing_Time",
    "電號用電地址": "Electricity_Service_Address",
    "原半尖峰契約容量": "Original_Off-Peak_Contract_Capacity",
    "結案狀態": "Case_Closing_Status",
    "統編戶名": "Unified_Business_No._&_Account_Name",
    "欲變更半尖峰契約容量": "Desired_Off-Peak_Contract_Capacity",
    "現場用途": "Site_Usage_Purpose",
    "原週六契約容量": "Original_Saturday_Contract_Capacity",
    "通訊地址": "Correspondence_Address",
    "欲變更週六契約容量": "Desired_Saturday_Contract_Capacity",
    "原用電種類": "Original_Electricity_Usage_Type",
    "契約移入移出說明": "Contract_Transfer_In/Out_Details",
    "欲變更用電種類": "Desired_Electricity_Usage_Type",
    "接案人": "Case_Handler",
    "原時間電價": "Original_Time-of-Use_Rate",
    "接單日期": "Order_Acceptance_Date",
    "欲變更時間電價": "Desired_Time-of-Use_Rate",
    "受理號碼": "Acceptance_Number",
}

def backend_message(url_str, id):
    url=f'{url_str}/{id}'
    requests.get(url=url,timeout=3)

## 文字 ## 多行文字 ## 數字 ## 關聯性
def fillData_Text(element, inputText):
    element.click()
    action = ActionChains(driver)
    if(update):
        action.key_down(Keys.COMMAND).send_keys('a').key_up(Keys.COMMAND)
        action.send_keys(Keys.DELETE)
    action.send_keys(inputText)
    action.perform()

## 下拉選單
def fillData_dropdown(element, inputText):
    element.click()
    input_box = element.find_element(By.XPATH, './/input')
    if(update):
        input_box.clear()
    input_box.send_keys(inputText)
    try:
        elem = driver.find_element(By.XPATH, f"(//li[contains(@class,'el-select-dropdown__item')]/span[contains(text(), '{inputText}')])[2]")
        idx = 2
    except:
        elem = driver.find_element(By.XPATH, f"(//li[contains(@class,'el-select-dropdown__item')]/span[contains(text(), '{inputText}')])[1]")
        idx = 1
    option = wait.until(EC.element_to_be_clickable((
        By.XPATH,
        f"(//li[contains(@class,'el-select-dropdown__item')]/span[contains(text(), '{inputText}')])[{idx}]"
    )))
    action = ActionChains(driver)
    time.sleep(0.5)
    action.move_to_element(option).click().perform()

## 單選
def fillData_radiogroup(element, inputText):
    labels = element.find_elements(By.XPATH, ".//label[@role='radio']")

    for label in labels:
        radio_input = label.find_element(By.XPATH, ".//input[@class='el-radio__original']")

        if radio_input.get_attribute("value") == inputText:
            label.click()
            break
       

## 多選
def fillData_checkboxgroup(element, inputText):
    if isinstance(inputText, str):
        inputText = eval(inputText)  

    labels = element.find_elements(By.XPATH, ".//label[@class='el-checkbox']")

    for text in inputText:
        for label in labels:
            checkbox_input = label.find_element(By.XPATH, ".//input[@class='el-checkbox__original']")
            if checkbox_input.get_attribute("value") == text:
                label.click()
                break  


## 時間
def fillData_dateandtime(element, inputText):
    element.click()
    element.find_element(By.XPATH, './input').send_keys(inputText)
    action = ActionChains(driver)
    action.move_by_offset(10, 10).click().perform()


## 標籤
def fillData_tag(element, inputText):
    element.click()
    action = ActionChains(driver)
    action.send_keys(inputText)
    action.send_keys(Keys.RETURN)
    action.perform()


## 流程人員
def fillData_people(block, value, idx):
    block.click()
    action = ActionChains(driver)
    action.send_keys(value)
    action.perform()
    dropdown_item = driver.find_elements(By.XPATH, "/html/body/*/div/div/div/ul/li[2]")[idx]
    dropdown_item.click()
    


## 進階表單
def fillData_advancedform(idx,inputList):
    time.sleep(0.5)
    for column_idx , column_item in enumerate(inputList):
        if column_idx > 0:
            element = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[1]/div[%d]/div[%d]/button' %(idx,column_idx+1))))
            element.click()
        element = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[1]/div[%d]/div[%d]/div/div[1]/span[2]/span[2]' %(idx,column_idx+1))))
        element.click()
        num = 1
        column = 1
        while column>0:
            try:
                column += 1
                element = shortwait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[2]/div/div[2]/div[%d]' %column)))
                element_text = element.find_element(By.XPATH, ".").text.strip()  ## 獲取文本
                normalized_text = element_text.split(" ")[0]

                if normalized_text in column_item.keys():
                    num += 1
                    time.sleep(0.5)
                    site =  wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[2]/div/div[2]/div[%d]/div/span[2]/div' %num)))
                    site_type = site.get_attribute("class")
                    if site_type == 'el-input' or site_type == 'el-textarea' or site_type == 'normalField normalField-write': ##文字、網址、多行文字、數字
                        fillData_Text(site, column_item[normalized_text])
                    elif site_type == 'el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--datetime' or site_type == 'el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--date': ##日期與時間
                        fillData_dateandtime(site, column_item[normalized_text])
                    elif site_type == 'el-radio-group': ##單選(資料集)、單選
                        fillData_radiogroup(site, column_item[normalized_text])
                    elif site_type == 'el-checkbox-group': ##多選(資料集)、多選
                        fillData_checkboxgroup(site, column_item[normalized_text])
                    elif site_type == 'el-select': ##下拉(資料集)
                        fillData_dropdown(site, column_item[normalized_text])
                    
            except Exception as e:
                button = driver.find_element(By.XPATH, "//button[@class='editBtn' and contains(text(),'完成')]")
                button.click()
                break
    

def initialization():
    url = "https://member.gsscloud.com/cas/login?service=https%3A%2F%2Fbizform.vitalyun.com%2FBackend%2Fsignin-cas%3Fstate%3DlsQ85OqVxz1o1K20PJGrEsFlxUmIiFY0WROZWrfEUWByJmNwu9TKZk2dslBGW9-Jc0mLqStzLiTT-nglYmUWvdLDj4TCEd_524UohEFd0jCBfbFNkneQW0tNMLjDjuSnKJUZ_0neWtOrhy3bxhQWJHhzpffyPSZ5kB6prJZy-W3mbygDxs0rJiEallX_BxNkK1ey5AqxNiSJ65fOdrZ9r9KyRgvGUNc7KJxFc_4CDVw"
    driver.get(url)
    driver.find_element(By.ID, "username").send_keys('<EMAIL>')
    driver.find_element(By.ID, "password").send_keys('intern@42838254')
    login_button = driver.find_element(By.XPATH, "//button[@type='submit' and text()='登入']")
    login_button.click()

def fillForm(formData):
    try:
        fill_form_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@type='button']")))
        driver.execute_script("arguments[0].scrollIntoView();", fill_form_button)
        fill_form_button.click()
        all_forms_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '全部表單樣板')]")))
        all_forms_button.click()
        search_element = wait.until(EC.element_to_be_clickable((By.XPATH, '//*[@id="searchInput"]')))
        search_element.click()
        action = ActionChains(driver) 
        action.send_keys(formData['Title']) #搜尋解決掃不到資料的錯誤
        action.perform()
        leave_form_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@title='%s' and contains(text(), '%s')]" %(formData["Title"], formData["Title"]))))
        leave_form_button.click()
    except:
        print("沒有正確進入表單")
        pass
    idx = 2
    counter = 0
    while idx>0:
        try:
            idx += 1
            element = shortwait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[1]/div[%d]' %idx)))
            element_text = element.find_element(By.XPATH, ".").text.strip()  ## 獲取文本
            normalized_text = element_text.splitlines()[0].strip()  # 清理多餘 只取第一
            pattern = r"^第\d+欄$"
            '''
            if normalized_text in mongodb_dict and normalized_text in formData.keys():
                tmp = mongodb_dict[normalized_text]
                tmp_text = formData[normalized_text]
                print(tmp_text)
                res1 = col.update_one(
                    {"Electricity_Service_Address": formData['電號用電地址']},    # 篩選條件
                    {"$set": { tmp: tmp_text }}
                )
                print(f"matched: {res1.matched_count}, modified: {res1.modified_count}")'''

            if re.match(pattern, normalized_text):
                fillData_advancedform(idx, formData[f"AdvancedForm{counter}"]) ## 進階表單
                counter += 1
            else:
                if normalized_text in formData.keys():
                    time.sleep(0.7)
                    column =element.find_element(By.CLASS_NAME, 'column-content')
                    block = column.find_element(By.XPATH, './span[1]/div[1]')
                    block_type = block.get_attribute("class")
                    if block_type == "el-input bizf-fields-textalign is-left" or block_type == "el-textarea bizf-fields-textalign is-left" or block_type == "normalField normalField-write bizf-fields-textalign is-left" or block_type == "el-autocomplete bizf-fields-textalign is-left": 
                        fillData_Text(block, formData[normalized_text]) ## 文字、多行文字、數字、關聯性
                    elif block_type == 'el-select bizf-fields-textalign is-left':
                        fillData_dropdown(block, formData[normalized_text]) ## 下拉選單
                    elif block_type == 'el-radio-group bizf-fields-textalign is-left':
                        fillData_radiogroup(block, formData[normalized_text]) ## 單選
                    elif block_type == 'el-checkbox-group bizf-fields-textalign is-left': 
                        fillData_checkboxgroup(block, formData[normalized_text]) ## 多選
                    elif block_type == "el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--date bizf-fields-textalign is-left" or block_type == "el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--datetime bizf-fields-textalign is-left":
                        fillData_dateandtime(block, formData[normalized_text]) ## 日期與時間      
        except Exception as e:
            break

    

    while True:
        try:
            element_1 = shortwait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="wrapper"]/div[2]/div/main/div/div/div[2]/div[1]/span')))
            element_1_text = element_1.find_element(By.XPATH, ".").text.strip()
            normalized_1_text = element_1_text.splitlines()[0].strip()

            if normalized_1_text in formData.keys():
                block_1 = element_1.find_element(By.XPATH, './div[2]/form/div/input[@class="react-autosuggest__input"]')#標籤
                fillData_tag(block_1, formData[normalized_1_text])
            else:
                continue  
        except Exception as e:
            break


    while True:
        try:
            element_2 = shortwait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="wrapper"]/div[2]/div/div/div/div/div[3]')))
            element_2_text = element_2.find_element(By.XPATH, ".").text.strip()
            lines = element_2_text.splitlines() # 將 element_2_text 轉換為多行文本
            filtered_lines = list(filter(lambda x: '第' in x and '關' in x, lines)) # 過濾出包含 "第" 和 "關" 的行
            filtered_lines_no_spaces = [line.replace(" ", "") for line in filtered_lines] # 移除每行中的空格
            matching_lines = [line for line in filtered_lines_no_spaces if line in formData.keys()] # 找到與 formData 鍵匹配的行

            numbers = [line.split("第")[1].split("關")[0] for line in matching_lines if "第" in line and "關" in line]

            for idx, number in enumerate(numbers):
                block_2 = element_2.find_element(By.XPATH, f'./div[{number}]/div[2]/div/div/div/div/div')
                fillData_people(block_2, formData['第%d關' %int(number)], idx)
            break
        except Exception as e:
            break
            

def sent():
    try:
        # Wait until the logout button is present
        sent_btn = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="wrapper"]/div[2]/header/div[2]/button[3]')))
        sent_btn.click()
        time.sleep(10)
        back_btn = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="wrapper"]/div[2]/div/main/div[1]/div[1]')))
        back_btn.click()
    except Exception:
        time.sleep(5)
        cancel_btn = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="wrapper"]/div[2]/header/div[2]/button[2]')))
        cancel_btn.click()
        print("Logout button not found or took too long to appear.")

def logout():
    try:
        # Wait until the logout button is present
        avatar_btn = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="wrapper"]/div[2]/div[2]/header/div/div[2]/span[3]/div'))) #頭像
        avatar_btn.click()
        logout_btn = wait.until(EC.presence_of_element_located((By.XPATH, '/html/body/div[2]/div/div/div/div[4]/div'))) #登出
        logout_btn.click()
        time.sleep(5)
        alert = driver.switch_to.alert #確認
        alert.accept()
    except Exception:
        print("Logout button not found or took too long to appear.")

def update_fill(formData):
    try:
        trigger = wait.until(EC.element_to_be_clickable((
            By.CSS_SELECTOR,
            "div.HeaderSearch__SearchInputTrigger-sc-1dsd40j-4.gfJFZV"
        )))
        trigger.click()

        search_input = WebDriverWait(driver, 10).until(
            EC.visibility_of_element_located((
                By.CSS_SELECTOR,
                "input[placeholder='搜尋表單數據...']"
            ))
        )
        search_input.clear()
        search_input.send_keys(formData['流程編號'], Keys.ENTER)
        time.sleep(1)
        leave_form_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.CSS_SELECTOR, "tr.k-master-row[data-grid-row-index='0']")))
        leave_form_button.click()

    except:
        print("沒有正確進入表單")
        pass
    WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, '//*[@id="wrapper"]/div[2]/div/main/div[1]/div[2]/a'))).click()
    idx = 4
    counter = 0
    while idx>0:
        try:
            idx += 1
            xpath = f'//*[@id="app"]/div[1]/div[{idx}]'
            element = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.XPATH, xpath))
            )
            element_text = element.find_element(By.XPATH, ".").text.strip()  ## 獲取文本
            normalized_text = element_text.splitlines()[0].strip()  # 清理多餘 只取第一
            pattern = r"^第\d+欄$"

            if re.match(pattern, normalized_text):
                fillData_advancedform(idx, formData[f"AdvancedForm{counter}"]) ## 進階表單
                counter += 1
            else:
                if normalized_text in formData.keys():
                    time.sleep(0.7)
                    column =element.find_element(By.CLASS_NAME, 'column-content')
                    block = column.find_element(By.XPATH, './span[1]/div[1]')
                    block_type = block.get_attribute("class")
                    if block_type == "el-input bizf-fields-textalign is-left" or block_type == "el-textarea bizf-fields-textalign is-left" or block_type == "normalField normalField-write bizf-fields-textalign is-left" or block_type == "el-autocomplete bizf-fields-textalign is-left": 
                        fillData_Text(block, formData[normalized_text]) ## 文字、多行文字、數字、關聯性
                    elif block_type == 'el-select bizf-fields-textalign is-left':
                        fillData_dropdown(block, formData[normalized_text]) ## 下拉選單
                    elif block_type == 'el-radio-group bizf-fields-textalign is-left':
                        fillData_radiogroup(block, formData[normalized_text]) ## 單選
                    elif block_type == 'el-checkbox-group bizf-fields-textalign is-left': 
                        fillData_checkboxgroup(block, formData[normalized_text]) ## 多選
                    elif block_type == "el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--date bizf-fields-textalign is-left" or block_type == "el-date-editor el-input el-input--prefix el-input--suffix el-date-editor--datetime bizf-fields-textalign is-left":
                        fillData_dateandtime(block, formData[normalized_text]) ## 日期與時間      '''
        except Exception as e:
            break


def fill_in(demandList,lock):
    if lock.acquire(blocking=False):
        initialization()
        for i in demandList:
            fillForm(i)
            time.sleep(5)
            sent()
        logout()
        lock.release()

def update(demandList,lock):
    if lock.acquire(blocking=False):
        initialization()
        for i in demandList:
            update_fill(i)
            time.sleep(5)
            #sent()
        logout()
        lock.release()


def judge_redisKey():
    KEYS = ['fill_in_bizform', 'update_bizform']
    raws = r.mget(KEYS)
    # 把有值的 key→value 放進字典
    data = {
        key: raw.decode('utf-8')
        for key, raw in zip(KEYS, raws)
        if raw is not None
    }

    # 用不同邏輯分別處理來自不同 key 的值
    if 'fill_in_bizform' in data:
        val1 = data['fill_in_bizform']
        demandList = json.load(val1.decode('utf-8'))
        fill_in(demandList)

    if 'update_bizform' in data:
        val2 = data['update_bizform']
        demandList = json.load(val1.decode('utf-8'))
        update(demandList)
        
def connect_redis_fill(redis_client, conf):

    redis_data = redis_client.lrange(conf.RPA_fill_in_key, 0, -1)
    redis_client.delete(conf.RPA_fill_in_key)
    obj_list = [json.loads(raw.decode('utf-8')) for raw in redis_data]
    return obj_list

'''
    print(client.list_database_names())

    if(r.ping()):
        print("連接成功")

    r.set("fill_in_bizform")
    r.set("update_bizform")

    schedule.every(10).seconds.do(fill_in)

    p = r.pubsub()
    p.subscribe('channel:Electricity_usage_change', 'channel:update_bizform')
    for msg in p.listen():
        if msg['type'] != 'message':
            continue
        channel = msg['channel'].decode('utf-8')
        if(channel == 'channel:Electricity_usage_change'):
            text = msg['data'].decode('utf-8')
            demandList = json.loads(text)
            fill_in(demandList)
        elif(channel == "channel:update_bizform"):
            text = msg['data'].decode('utf-8')
            demandList = json.loads(text)
            update(demandList)'''

def connect_redis_update(redis_client, conf):
    redis_data = redis_client.lrange(conf.RPA_update_key, 0, -1)
    redis_client.delete(conf.RPA_update_key)
    obj_list = [json.loads(raw.decode('utf-8')) for raw in redis_data]
    return obj_list

def RPA_threads(fill_data,update_data,redis_client, conf):
    while True:
        fill_data = connect_redis_fill(redis_client=redis_client,conf=conf)
        update_data = connect_redis_update(redis_client=redis_client,conf=conf)
        if fill_data:
            fill_in(fill_data)
        if update_data:
            update(update_data)

def main():
    return 0

if __name__ == '__main__':
    main()