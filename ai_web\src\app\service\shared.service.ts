import { Injectable } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, map, startWith, BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SharedService {
  private taxIdSubject = new BehaviorSubject<string>('');
  taxId$ = this.taxIdSubject.asObservable();

  constructor(private router: Router, private route: ActivatedRoute) {
    this.listenToRouteChanges();
  }

  private listenToRouteChanges() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        startWith(null),
        map(() => {
          let currentRoute = this.route.root;
          let taxId = '';

          while (currentRoute.firstChild) {
            currentRoute = currentRoute.firstChild;
            const param = currentRoute.snapshot.paramMap.get('taxId');
            if (param) {
              taxId = param;
            }
          }

          return taxId;
        })
      )
      .subscribe((taxId) => {
        this.taxIdSubject.next(taxId);
      });
  }

  /**
   * 取得路由上的參數值
   * @param key 預設為 'taxId'
   */
  getRouteId(key: string = 'taxId'): string {
    let currentRoute = this.route.root;

    while (currentRoute.firstChild) {
      currentRoute = currentRoute.firstChild;
      const param = currentRoute.snapshot.paramMap.get(key);
      if (param) {
        return param;
      }
    }

    return '';
  }
}
