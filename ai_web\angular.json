{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"ima_ai_web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/ima_ai_web", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["@angular/localize/init", "zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "preserveSymlinks": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/scss/styles.scss"], "scripts": [], "allowedCommonJsDependencies": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "6mb", "maximumError": "7mb"}, {"type": "anyComponentStyle", "maximumWarning": "16kb", "maximumError": "20kb"}], "outputHashing": "all"}, "test_env": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "ima_ai_web:build:production"}, "test_env": {"buildTarget": "ima_ai_web:build:test_env"}, "development": {"buildTarget": "ima_ai_web:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "ima_ai_web:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/scss/styles.scss"], "scripts": []}}}}}}