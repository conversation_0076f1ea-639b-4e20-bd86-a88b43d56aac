import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface SchedulerTask {
  _id?: string;
  project_name: string;
  event_name: string;
  event_type: string;
  status: string;
  last_update: string;
  event_id: string;
  create_time?: string;
  name?: string;  // 兼容舊版
  description?: string;  // 兼容舊版
  nodes?: any[];  // 兼容舊版
  detail?: any;  // 兼容舊版
  project_code?: string;  // 兼容舊版
  place_id?: string;  // 兼容舊版
  progress?: number;  // 兼容舊版
  role?: { [key: string]: string };  // 事件角色權限
  priority?: number;  // 事件優先級
  stage?: string;     // 事件當前階段
  owner?: string;     // 事件負責人
  step_name?: string; // 當前步驟名稱
  routerLink?: string; // 路由鏈接
  canPin?: boolean;    // 是否可以置頂
  notification?: any;  // 通知信息
  project_leader?: string; // 項目負責人
  tpc_officer?: string;    // TPC 負責人
  Acceptance_Number?: string; // 受理號碼
}

@Injectable({
  providedIn: 'root'
})
export class SchedulerService {
  constructor(private http: HttpClient) { }


  getScheduler(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/scheduler/get_scheduler`, {}, { headers })
  }
  get_gantt_data(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/scheduler/get_gantt_data`, {  }, { headers });
  }
}
