import { ProjectService } from './../../../service/project.service';
import {ServiceService} from './../../../service/service.service';
import { Component } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule,FormBuilder, FormGroup, Validators  } from '@angular/forms';
import { ChartjsComponent } from '@coreui/angular-chartjs';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
  ButtonGroupComponent,
  CardFooterComponent,
  ProgressComponent,
  ProgressBarComponent,
  FormCheckComponent,
  ModalFooterComponent,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilHttps,cilColorBorder } from '@coreui/icons';
import { firstValueFrom } from 'rxjs';

interface project {
  project_code: string;
  project_name: string;
  place_id_list: string[];
  service_list : string[];
}
interface ServiceInfo {
  service_id: string;
  service_name: string;
  service_type: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    IconDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,
    ButtonGroupComponent,
    ChartjsComponent,
    CardFooterComponent,
    ProgressComponent,
    ProgressBarComponent,
    FormCheckInputDirective,
    FormCheckComponent,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent {
  icons = { cilHttps ,cilColorBorder};
  project: project = {
    project_code: '',
    project_name: '',
    place_id_list: [],
    service_list : [],
  };
  service_type_dict ={
    '電價管理':'electricity_pricing_service',
    '智慧管理':'electricity_service'
  };
  successMessage :string;
  errorMessage : string;
  service_name :string;
  serviceForm!: FormGroup;
  selectedplaceid: string | null = null;
  project_service_list :string[] = [];
  place_id_service_list :string[];
  to_show_service_list :string[];
  show_service_list :ServiceInfo[];
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService,
    private serviceService : ServiceService,
    private fb: FormBuilder
  ) {
    this.project.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }
  public visible = false;

  load_project_info() {
    this.projectService.get_project_info(this.project.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = {
          project_code: this.project.project_code,
          project_name: data.data.project_name || '',
          place_id_list: data.data.place_id || [],
          service_list :data.data.service_list||[],
        };
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  toggleLiveDemo() {
    this.visible = !this.visible;
  }

  handleLiveDemoChange(event: any) {
    this.visible = event;
  }
  get_place_id_service_list(place_id :string){

    this.serviceService.get_place_id_service_list(place_id).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.place_id_service_list = data.place_id_service_list;
      },
      (error) => {
      }
    );
  }
  get_show_service_dict(to_show_service_list:string[]){
    console.log('to_show_service_list ',to_show_service_list);
    this.serviceService.get_show_service_dict(to_show_service_list).subscribe(
    (data) => {
      localStorage.setItem('token', data.token);
      this.show_service_list = data.result
      console.log('data',this.show_service_list)
    },
    (error) => {
    }
  );
  }

  ngOnInit(): void {
    this.project.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.serviceForm = this.fb.group({
      servicename: ['', ],
      servicetype: ['', ],
      project_code :[this.project.project_code, ],
      place_id :[this.selectedplaceid, ]
      })
    }
    createService(): void {
      if (this.serviceForm.valid) {
        const formData = this.serviceForm.value;

        this.serviceService.create_service(formData).subscribe(
          (response) => {
            this.successMessage = '請求提交成功';
            window.location.reload();
          },
          (error) => {
            this.errorMessage = '有欄位尚未填寫或勾選欄尚未勾選';
          }
        );
      } else {
        console.warn('表單驗證未通過');
      }
    }
   async viewPlaceIdFeatures(place_id:any): Promise<void> {
    this.to_show_service_list = [];
    this.show_service_list = []
    this.selectedplaceid = this.selectedplaceid === place_id ? null : place_id;
    const data = await firstValueFrom(this.serviceService.get_place_id_service_list(place_id));
    const place_id_service_list = data.place_id_service_list;
    console.log('place_id_service_list ',place_id_service_list);
    this.serviceForm.get('place_id')?.setValue(this.selectedplaceid);
    this.to_show_service_list = place_id_service_list.filter((item:string)  => this.project.service_list.includes(item));
    this.get_show_service_dict(this.to_show_service_list);
  }
}
