import { Component, OnInit } from '@angular/core';
import { NgStyle } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { IconDirective } from '@coreui/icons-angular';
import { environment } from '../../../../environments/environment'
import { FormsModule, FormGroup, FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, FormControlDirective, ButtonDirective } from '@coreui/angular';
import { CommonModule } from '@angular/common';

@Component({
  templateUrl: './reset-pwd.component.html',
  styleUrl: './reset-pwd.component.scss',
  standalone: true,
  imports: [ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, FormControlDirective, ButtonDirective,
    CommonModule, FormsModule, ReactiveFormsModule, NgStyle, IconDirective
  ]
})
export class ResetPwdComponent implements OnInit {
  token: string | null = null;
  password: string = '';
  confirmPassword: string = '';
  errorMessage: string = '';
  successMessage: string = '';
  resetForm: FormGroup;
  apiUrl = `${environment.apiBaseUrl}/reset-pwd/reset-password`;
  constructor(private fb: FormBuilder,private route: ActivatedRoute, private http: HttpClient, private router: Router) {
    this.resetForm = this.fb.group({
      token: this.token,
      password: [''],
      confirmPassword: ['']
    });
  }

  ngOnInit(): void {
    this.token = this.route.snapshot.paramMap.get('token') || '';
    if (!this.token) {
      console.error('Token not found');
    }
  }

  onSubmit() {
    const formData = this.resetForm.value;
    this.password = formData.password
    this.confirmPassword = formData.confirmPassword
    if (this.password !== this.confirmPassword) {
      this.errorMessage = 'Passwords do not match';
      return;
    }
    this.http.post(this.apiUrl, {
      token: this.token,
      password: this.password
    }).subscribe(
      (response: any) => {
        this.successMessage = 'Password reset successfully!';
        setTimeout(() => {
          this.router.navigate(['/login']);
        }, 2000);
      },
      (error) => {
        this.errorMessage = 'Invalid or expired token';
      }
    );
  }
}
