import { CreateEnterpriseComponent } from './create-enterprise/create-enterprise.component';
import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'Groups'
    },
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full'
      },
      {
        path: 'overview',
        loadComponent: () => import('./overview/overview.component').then(m => m.OverviewComponent),
        data: {
          title: 'Groups'
        }
      },
      {
        path: 'create_enterprise',
        loadComponent: () => import('./create-enterprise/create-enterprise.component').then(m => m.CreateEnterpriseComponent),
        data: {
          title: 'Create Enterprise'
        }
      }

    ]
  }
];


