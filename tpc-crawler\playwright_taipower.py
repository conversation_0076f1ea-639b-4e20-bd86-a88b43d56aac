
from playwright.sync_api import sync_playwright
from pyvirtualdisplay import Display
import time
import random

with sync_playwright() as p:
    display = Display(backend="xvfb", size=(1920, 1080))
    display.start()
    browser = p.chromium.launch(headless=True)  # 不要 headless，可以看畫面
    context = browser.new_context(
        locale="zh-TW",
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    )
    page = context.new_page()

    # 訪問首頁
    page.goto("https://service.taipower.com.tw/ebpps2/", timeout=60000)
    print("🔗 Page title:", page.title())

    # 等一下載入
    page.wait_for_timeout(3000)

    print("📍 Current URL:", page.url)
    print("🧾 Page Content Start:")
    print(page.content()[:1000])
    page.wait_for_timeout(random.randint(5000, 8000))
    print('123')