from werkzeug.security import generate_password_hash
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required,token_required
from flask import jsonify, request, redirect, url_for, Blueprint, current_app
from sqlalchemy import text,Column, String, CHAR, Integer, Date, DateTime, Numeric,DECIMAL, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from accessories import mail, sqldb,crawler_redis_client, mongo, save_json_to_mongo,update_json_in_mongo, remove_json_in_mongo
from accessories import send_mail
import json
import datetime
bill_analysis_page = Blueprint('bill_analysis', __name__)
@bill_analysis_page.route('/get_user_bill_analysis_place_id_list', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_user_bill_analysis_place_id_list(token):
    if request.method == 'OPTIONS':
        return '', 204
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401
    place_id_list = []
    user_service = get_user_info(token, 'user_service')
    
    if user_service:
        bills_analysis = user_service.get('bills_analysising', {})
    if bills_analysis:
        place_id_list = [{
        "place_id": place_id,
        "earliest_month": periods[0],
        "latest_month": periods[-1]
    }
    for place_id, periods in bills_analysis.items()
]

    return jsonify({
        "token": get_refresh_token(token),
        "place_id_list": place_id_list}), 200
@bill_analysis_page.route('/get_bill_analysis_info', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_bill_analysis_info(token):

    data = request.get_json()
    place_id = data.get('place_id')

    user_service = get_user_info(token, 'user_service')
    
    if user_service:
        bills_analysis = user_service.get('bills_analysising', {})
    if bills_analysis:
        periods = bills_analysis.get(place_id, [])
    return jsonify({
        "token": get_refresh_token(token),
        "periods": periods}), 200
@bill_analysis_page.route('/get_place_id_bill_information', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_place_id_bill_information(token):
    if request.method == 'OPTIONS':
        return '', 204
    data = request.get_json()
    print(data)
    place_id = data.get('place_id')
    engine = sqldb.get_engine()
    Session = sessionmaker(bind=engine)
    session =Session()
    results = session.query(tpcbills).filter_by(place_id=place_id).all()
    reverse_conversion_dict = {}
    for en, zh_list in conversion_dict.items():
        reverse_conversion_dict[en] = zh_list[0] 
    json_data = []
    for row in results:
        row_dict = {k: v for k, v in row.__dict__.items() if not k.startswith('_')}
        translated = {reverse_conversion_dict.get(k, k): v for k, v in row_dict.items()}
        json_data.append(translated)
        print(json_data)
    return json.dumps(json_data, ensure_ascii=False, indent=2,default=str)
@bill_analysis_page.route('/remove_place_id_analysis', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def remove_place_id_analysis(token):
    if request.method == 'OPTIONS':
        return '', 204
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    data = request.get_json()
    place_id = data.get('place_id')
    user_email = get_user_info(token, 'email')
    mongodb_user = mongo.db.user.find_one({"email": user_email})
    if not mongodb_user:
        return jsonify({'message': 'User not found!'}), 404
    if 'bills_analysising' in mongodb_user.get('user_service', {}):
        mongo.db.user.update_one(
            {"email": user_email},
            {"$unset": {f"user_service.bills_analysising.{place_id}": ""}}
        )
    return jsonify({"msg": 'successful'}) ,200




conversion_dict = {
        'place_id': ['電號'],
        'ebpps_name':['用戶名稱'],
        'group':['組別'],
        'ebpps_customer_address': ['用電地址'],
        'ebpps_bill_address': ['通訊地址'],
        'bill_year_month': ['帳單月份', '收費月份'],
        'bill_date': ['本次收費日'],
        'bill_date_next': ['下次收費日'],
        'kwh_total': ['用電度數（度）', '用電度數(度)', '度數'],
        'meter_number':['表號'],
        'fee_total': ['應繳總金額'],
        'status': ['繳費狀況'],
        'payment_deadline': ['繳費期限'],
        'electricity_type': ['電價種類'],
        'feeder_number': ['饋線代號'],
        'group_powerout': ['輪流停電組別'],
        'date_bill_start': ['計費起始日期'],
        'date_bill_end': ['計費截止日期'],
        'multiplier': ['倍率'],
        'date_readmeter': ['抄表日期', '本次抄表日'],
        'date_next_readmeter': ['下次抄表日'],
        'min_kwh': ['底度'],
        'day_used': ['本期用電日數', '用電日數'],
        'kwh_per_day': ['平均用電度數', '日平均度數'],
        'contract_peak': ['經常契約', '經常(尖峰)契約', '經常（尖峰）契約'],
        'kwh_peak': ['計費度數/經常度數', '計費度數/尖峰度數', '計費度數/經常(尖峰)度數', '計費度數/經常（尖峰）度數'],
        'kw_peak': ['經常需量', '經常(尖峰)需量', '經常（尖峰）需量'],
        'kwh_offpeak': ['計費度數/離峰度數'],
        'contract_offpeak': ['離峰契約'],
        'kw_offpeak': ['離峰需量'],
        'contract_semipeak': ['半尖峰(非夏月)契約', '半尖峰(非夏月)需量', '半尖峰（非夏月）契約', '半尖峰（非夏月）需量'],
        'kw_semipeak': ['半尖峰(非夏月)契約', '半尖峰(非夏月)需量', '半尖峰（非夏月）契約', '半尖峰（非夏月）需量'],
        'kwh_semipeak': ['計費度數/半尖峰度數'],
        'kwh_saturday': ['計費度數/週六半尖峰度數'],
        'contract_saturday': ['週六半尖峰契約', '週六半尖峰契容'],
        'kw_saturday': ['週六半尖峰需量'],
        'pf': ['功率因數(%)', '功率因數（％）','功率係數（％）', '功率係數(%)'],
        'adjustment_factor': ['調整係數', '調整係數(%)', '調整係數（％）'],
        'fee_kw': ['基本電費', '基本電費(約定)', '基本電費(合約)', '基本電費（約定）', '基本電費（合約）'],
        'fee_kwh': ['流動電費'],
        'fee_kwfine': ['非約定基本電費', '基本電費(非約定)', '超約附加費', '基本電費（非約定）'],
        'fee_pf': ['功率因數調整費'],
        'fee_tax': ['營業稅'],
        'fee_public': ['分攤公共電費','公共設施電費'],
        'tpc_ach': ['代繳帳號'],
        'time_type': ['時間種類'],
        'company_id': ['用戶營利事業統一編號'],
        'day_deducted': ['本次扣款日期'],
        'day_next_deducted': ['下次扣款日期'],
        'customer_service_hotline': ['客服專線'],
        'tpc_comapny_id': ['本公司營利事業統一編號'],
        'service_department': ['服務單位'],
        'service_department_address': ['服務地址'],
        'energy_saving_reward': ['節電獎勵'],
        'electronic_bill_discount_reduction': ['電子帳單優惠減收金額'],
        'fee_before_tax': ['稅前應繳總金額'],
        'kwh_per_fuel_cost': ['每度燃料成本'],
        'current_period_carbon_emissions': ['本期碳排量'],
        'kwh_per_recyling_fund': ['每度繳交再生基金'],
        'kwh_per_average_price': ['當期每度平均電價'],
        'reduce_kwh': ['減少用電量'],
        'subcontracted_kwh_peak': ['轉供度數/尖峰用電度數'],
        'subcontracted_kwh_semipeak': ['轉供度數/半尖峰用電度數'],
        'subcontracted_kwh_offpeak': ['轉供度數/離峰用電度數'],
        'subcontracted_kwh_saturday': ['轉供度數/週六半尖峰用電度數'],
        'price_adjustment_discount': ['電價減調金額','電價調整金額','加〈減〉收金額','加<減>收金額','加(減)收金額','加（減）收金額','優惠金額'],
        'late_payment_fee': ['遲付費用'],
        'power_outage_deduction': ['停電扣減金額'],
    }

Base = declarative_base()
class tpcbills(Base):
    __tablename__ = 'tpc_bills'
    __table_args__ = (
        {'mysql_charset': 'utf8'},
    )
    place_id = Column(CHAR(11), primary_key=True)
    bill_year_month = Column(CHAR(6), primary_key=True, default='000000')
    bill_date_next = Column(Date)
    bill_date = Column(Date)
    ebpps_url = Column(String(255), default='0')
    ebpps_name = Column(String(31), nullable=False, default='0')
    ebpps_customer_address = Column(String(63), nullable=False, default='0')
    ebpps_bill_address = Column(String(63), default='0')
    electricity_type = Column(String(31), default='0')
    feeder_number = Column(String(5), default='0')
    group_powerout = Column(String(2))
    company_id = Column(CHAR(11))
    date_readmeter = Column(Date)
    date_next_readmeter = Column(Date)
    date_bill_start = Column(Date)
    date_bill_end = Column(Date)
    day_used = Column(Numeric(3, 0), nullable=True)
    min_kwh = Column(Numeric(3, 0), nullable=True)
    fee_total = Column(Numeric(18, 0), nullable=True)
    fee_kw = Column(Numeric(20, 2), nullable=True)
    fee_kwfine = Column(Numeric(20, 2), nullable=True)
    fee_kwh = Column(Numeric(20, 2), nullable=True)
    fee_pf = Column(Numeric(20, 2), nullable=True)
    fee_public = Column(Numeric(20, 2), nullable=True)
    fee_tax = Column(Numeric(20, 2), nullable=True)
    kwh_total = Column(Numeric(18, 0), nullable=True)
    kwh_peak = Column(Numeric(18, 0), nullable=True)
    kwh_semipeak = Column(Numeric(18, 0), nullable=True)
    kwh_offpeak = Column(Numeric(18, 0), nullable=True)
    kwh_saturday = Column(Numeric(18, 0), nullable=True)
    contract_peak = Column(Numeric(18, 0), nullable=True)
    contract_semipeak = Column(Numeric(18, 0), nullable=True)
    contract_offpeak = Column(Numeric(18, 0), nullable=True)
    contract_saturday = Column(Numeric(18, 0), nullable=True)
    kw_peak = Column(Numeric(18, 0), nullable=True)
    kw_semipeak = Column(Numeric(18, 0), nullable=True)
    kw_offpeak = Column(Numeric(18, 0), nullable=True)
    kw_saturday = Column(Numeric(18, 0), nullable=True)
    pf = Column(Numeric(3, 0), nullable=True)
    kwh_per_day = Column(Numeric(18, 2), nullable=True)
    created_at = Column(DateTime)
    status = Column(String(64), nullable=True)
    tpc_ach = Column(String(45), nullable=True)
    payment_deadline = Column(DateTime)
    reduce_kwh = Column(Numeric(18, 0), nullable=True)
    time_type = Column(String(64), nullable=True)
    day_deducted = Column(Date, nullable=True)
    day_next_deducted = Column(Date, nullable=True)
    customer_service_hotline = Column(String(16), nullable=True)
    tpc_comapny_id = Column(String(16), nullable=True)
    service_department = Column(String(64), nullable=True)
    service_department_address = Column(String(128), nullable=True)
    electronic_bill_discount_reduction = Column(DECIMAL(10, 2), nullable=True)
    fee_before_tax = Column(DECIMAL(18, 2), nullable=True)
    kwh_per_fuel_cost = Column(DECIMAL(20, 10), nullable=True)
    current_period_carbon_emissions = Column(DECIMAL(18, 0), nullable=True)
    kwh_per_recyling_fund = Column(DECIMAL(20, 10), nullable=True)
    group = Column(String(8), nullable=True)
    group_name = Column(String(32), nullable=True)
    meter_number = Column(String(32), nullable=True)
    multiplier = Column(Integer, nullable=True)
    energy_saving_reward = Column(Numeric(20, 2), nullable=True)
    kwh_per_average_price = Column(Numeric(20, 10), nullable=True)
    adjustment_factor = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_peak = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_semipeak = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_offpeak = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_saturday = Column(Numeric(20, 10), nullable=True)
    price_adjustment_discount = Column(DECIMAL(10,2), nullable=True)
    late_payment_fee = Column(Numeric(20,10), nullable=True)
    power_outage_deduction = Column(Numeric(20,10), nullable=True)

