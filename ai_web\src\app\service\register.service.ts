import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment'

@Injectable({
  providedIn: 'root'
})
export class RegisterService {
  apiUrl = `${environment.apiBaseUrl}/register/register`;

  constructor(private http: HttpClient) { }

  registerUser(formData: any): Observable<any> {
    return this.http.post(this.apiUrl, formData);
  }
}
