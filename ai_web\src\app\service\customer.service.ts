import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CustomerService {

  constructor(private http: HttpClient) { }
  get_projectplot_list(): Observable<any> {
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null);
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post(`${environment.apiBaseUrl}/customer_service/get_projectplot_list`, {}, { headers });
    }
}
