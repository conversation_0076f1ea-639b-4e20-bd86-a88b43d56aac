import { Component, OnInit } from '@angular/core';
import { SharedModule, team, user } from '../../shared/shared.module';
import { SharedService } from '../../service/shared.service';
import { TeamService } from '../../service/team.service';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { data } from 'jquery';

// 定義 Member 介面，確保成員物件有 role 屬性
interface Member extends user {
  selected?: boolean; // 新增選中狀態屬性
}

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent implements OnInit {
  team: team = {
    taxId: '',
    team_code: '',
    team_name: '',
    accounts: []
  };

  // 成員相關屬性
  adminMembers: Member[] = [];
  regularMembers: Member[] = [];
  accountingMembers: Member[] = [];
  loadingMembers = false;

  // 模態框相關屬性
  editMode = false;
  save_info_done = false;

  availableRoles = [
    { value: 'admin', label: '管理員' },
  ];


  showCard: string = 'info';

  constructor(
    private teamService: TeamService,
    private sharedService: SharedService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // 獲取企業資訊
    this.loadTeamInfo();
  }

  // 載入企業資訊
  loadTeamInfo(): void {
    this.teamService.get_team_info(this.sharedService.getRouteId('team_code')).subscribe(
      (data) => {
        if (data && data.data) {
          this.team = data.data;
        }
      },
      (error) => {
        console.error('獲取團隊資訊失敗:', error);
      }
    );
  }

  setShowCard(card: string) {
    this.showCard = card;
  }

  saveTeam() {
    this.teamService.update_team_info(this.team.team_code, this.team).subscribe(
      (data) => {
        localStorage.setItem("token", data.token)
        this.loadTeamInfo();
        this.save_info_done = true;
      },
      (error) => {

      }
    )
  }

  confirmRemove(member: any) {
    if (confirm(`確定要移除 ${member.email} 嗎？`)) {
      this.removeMember(member);
    }
  }

  removeMember(member: any) {
    this.teamService.remove_member(this.team.team_code, member).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.loadTeamInfo();
      },
      (error) => {

      }
    )
    this.team.accounts = this.team.accounts.filter(
      (m: any) => m.email !== member.email
    );
  }

  reply_application(applicant: any, reply: boolean) {
    this.teamService.reply_entering_application(this.team.team_code, applicant, reply).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.loadTeamInfo();
      },
      (error) => {
        this.router.navigate(['/404']);
      }
      )

  }


  onRoleChange(member: any, event: any) {
    const role = event.target.value;
     // 確保 'member' 這個基礎角色存在
     if (!member.roles.includes('member')) {
      member.roles.push('member');
    }
    if (event.target.checked) {
      // 勾選：加入角色
      if (!member.roles.includes(role)) {
        member.roles.push(role);
      }
    } else {
      // 取消勾選：移除角色
      member.roles = member.roles.filter((r: string) => r !== role);
    }
    this.teamService.edit_member_auth(this.team.team_code, member.email, member.roles).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);

      },
      (error) => {
        if (error.error.roles){
          member.roles = error.error.roles
        }
      }
    )
  }

  toggleEditMode() {
    this.editMode = !this.editMode;
  }

}
