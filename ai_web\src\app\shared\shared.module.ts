import { Component, computed, inject,NgModule , input,viewChild, Version } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// CoreUI Modules
import {
  TableModule,
  GridModule,
  NavModule,
} from '@coreui/angular';

// CoreUI Components & Directives
import {
  AlertComponent,
  ButtonCloseDirective,
  ButtonDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  CardFooterComponent,
  ColComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  ThemeDirective,
  ListGroupDirective,
  ListGroupItemDirective,
  InputGroupComponent,
  InputGroupTextDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormSelectDirective,
  FormCheckComponent, FormCheckInputDirective, FormCheckLabelDirective,
  AvatarComponent,
  ContainerComponent,
  HeaderComponent,
  HeaderNavComponent,
  HeaderTogglerDirective,
  NavItemComponent,
  NavLinkDirective,
  SidebarToggleDirective,
  FormLabelDirective, CollapseDirective,
  CarouselCaptionComponent,
  CarouselComponent,
  CarouselControlComponent,
  CarouselIndicatorsComponent,
  CarouselInnerComponent,
  CarouselItemComponent,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ModalService,ModalToggleDirective} from '@coreui/angular';
import { Subscription } from 'rxjs';
import {

} from '@coreui/angular';

@NgModule({
  imports: [
    AlertComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    NavModule,
    ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    CardFooterComponent,
    ColComponent,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    ThemeDirective,
    ListGroupDirective,
    ListGroupItemDirective,
    InputGroupComponent,
    InputGroupTextDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormSelectDirective,
    IconDirective,
    FormCheckComponent, FormCheckInputDirective, FormCheckLabelDirective,
    CarouselCaptionComponent,
  CarouselComponent,
  CarouselControlComponent,
  CarouselIndicatorsComponent,
  CarouselInnerComponent,
  CarouselItemComponent,
    RouterModule,
    AvatarComponent,
  ContainerComponent,
  HeaderComponent,
  HeaderNavComponent,
  HeaderTogglerDirective,
  NavItemComponent,
  NavLinkDirective,
  SidebarToggleDirective,
  FormLabelDirective, CollapseDirective,
  ],
  declarations: [

  ],
  exports: [
    // Angular
    CommonModule,
    FormsModule,
    ReactiveFormsModule,

    // CoreUI Modules
    TableModule,
    GridModule,
    NavModule,

    // CoreUI Components & Directives
    AlertComponent,
    ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    CardFooterComponent,
    ColComponent,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    ThemeDirective,
    ListGroupDirective,
    ListGroupItemDirective,
    InputGroupComponent,
    InputGroupTextDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormSelectDirective,
    IconDirective,
    FormCheckComponent,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    CarouselCaptionComponent,
  CarouselComponent,
  CarouselControlComponent,
  CarouselIndicatorsComponent,
  CarouselInnerComponent,
  CarouselItemComponent,
    RouterModule,
    AvatarComponent,
  ContainerComponent,
  HeaderComponent,
  HeaderNavComponent,
  HeaderTogglerDirective,
  NavItemComponent,
  NavLinkDirective,
  SidebarToggleDirective,
  FormLabelDirective, CollapseDirective,
  ],
  providers: [
  ],
})
export class SharedModule {}

export interface bank {
  name: '',
  bankCode: '',
  branchCode: '',
  accountNumber: ''
}

export interface credit_card {
  number: '',
  name: '',
  expMonth: '',
  expYear: '',
  cvv: ''
}


export interface AppComment {
  user: string;
  date: string;   // 格式: 'YYYY-MM-DD' 或 ISO 字串
  text: string;
  rating?: number; // (選填) 可選每則評論是否包含分數
}

export interface appItem {
  title: string;
  subtitle: string;
  icon: string;
  type: string;
  user: 'person' | 'team';
  category: string[];
  id?: number;
  images?: string[];
  version?: string;
  description?: string;
  terms?: string;

  // 評分相關
  rating_avg?: number;     // 平均分數
  rating_count?: number;   // 評分數量
  rating_dist?: number[];  // 各星級百分比 [5星, 4星, 3星, 2星, 1星]
  comments?: AppComment[]; // 評論清單
}




export interface enterprise {
  taxId: string;
  name: string;
  address: string;
  telephone: string;
  accounts?: any;
  applications?: { email: string }[] | null;
  created_by?: string;
  payment_method?: 'credit' | 'bank';
  payment_bank?: bank;
  payment_credit_card?: credit_card;
  payout_bank?: bank;
}


export interface team {
  taxId: string,
  team_code: string,
  team_name: string,
  accounts?: any,
  applications?: { email: string }[] | null;
}

interface notification_method {
  web: boolean,
  mail: boolean,
}

interface project_auth {
  accounting: boolean,
  accounting_place_id: string,
  controller: boolean,
  controller_parameters: string[],
  view: boolean,
  view_plot: string[]
}

export interface user {
  firstname: string,
  lastname: string,
  email: string,
  role: string[],
  project?: string[],
  create_time?: string,
  last_updated?: string,
  phone?: string,
  profile_picture?: string,
  notification_method?: notification_method,
  customer_project?: {
    string: project_auth,
  },
  enterprise?: string[],
  payment_method?: 'credit' | 'bank';
  payment_bank?: bank;
  payment_credit_card?: credit_card;
  payout_bank?: bank;
}

export interface project {
  project_code: string,
  project_name: string,
  gateway?: string[],
  state?: string,
  create_time?: string,
  last_updated?: string,
  project_leader: string,
  place_id?: string[]
}

export interface projectPlot {
  project_code: string,
  projectplot_name: string,
  data: any,
  create_time?: string,
  last_updated?: string,
}

export interface projectPlot_link {
  project_code: string,
  name: string,
  serialNo: string,
  type: string,
  source: string,
  target: string,
  create_time?: string,
  last_updated?: string,
}

export interface projectPlot_object {
  project_code: string,
  name: string,
  serialNo: string,
  type: string,
  gateway_id: string | null;
  place_id?: string,
  create_time?: string,
  last_updated?: string,
}


export interface color {
  color: string,
  transparent: boolean
}

export type IntOrPair = number | [number, number];

export interface MAC{
  interface: string;
  address: string;
}

export interface Thresholds {
  Condition: string;
  Action: string;
  Boundary: IntOrPair[];
  Interval: number;
}

export interface Declaration {
  VarName: string;
  Definition: string;
  Rolling_Funct: string;
  Rolling_Len: number;
}

export interface TimeBoundary {
  WeekDay: number[];
  Time: number[][];
  Interval: number;
  Boundary: IntOrPair[];
}

export interface CmderServer {
  ServerAddress: string;
  ServerPort: number;
  Wait: number;
}

export interface FixedIP {
  IP: string;
  Routers: string;
}

export interface WiFi {
  ssid: string;
  identity: string;
  psk: string;
  key_mgmt: string;
  priority: number;
}

export interface Identical_placeID_device {
  slaveAddress: string;
  placeID: string;
}

export interface Host {
  HostName: string;
  SerialNo: string;
  Identical_placeID_list: Identical_placeID_device[];
  LogLevel: string;
  LogFile: string;
  Version: string;
  CmderServer: CmderServer;
  PostDataPeriod: number;
}

export interface Communication {
  WiFi: WiFi[];
  PostServer: CmderServer[];
  FixedIP: FixedIP;
  PhoneNumber: string;
}

export interface Pin {
  PortName: string;
  SlaveAddress: number;
  PinAddress: number;
  PinName: string
}

export interface PinDefinition {
  Pin: string;
  RenameAs: string;
  Read: string;
  Write: string;
  Register_W: string;
}

export interface PinConfig{
  PinIdx: number;
  SensorType: string;
  Definition: PinDefinition[];
}

export interface baseLogic {
  Type: string;
  ExecutionPrd: number;
  InputList: Pin[];
  OutputList: Pin[];
  Arguments: {
    CondCtrlByT2_DvcBoundary: IntOrPair[];
    CondCtrlByT2_TimeBoundary: TimeBoundary[];
    CondCtrlByT2_Declaration: Declaration[];
    CondCtrlByT2_Thresholds: Thresholds[];
    CondCtrlByT2_RedisDB: string;
    DataTransByRedis_VarName: string;
    DataTransByRedis_VarDefinition: string;
    DataTransByRedis_ServerIP: string;
    DataTransByRedis_RedisDB: string;
  }
}

export interface Strategy {
  Logics: baseLogic[];
}

export interface Modbus {
  Driver: { DriverName: string; PortAddress: string; SlaveAddress: number ;PinConfig: PinConfig[];}[];
}

export interface Config {
  Host: Host;
  Communication: Communication;
  Strategy: Strategy;
  Modbus: Modbus;
}

// Event 相關接口
export interface EventData {
  event_id: string;
  event_type: string;
  event_name?: string;
  project_code: string;
  project_name: string;
  stage: string;
  status: string;
  last_updated?: string;
  update_time?: string;
  create_time?: string;
  role?: { [key: string]: string };
  error_msg?: string;
  acceptance_number?: string;
  Order_Acceptance_Date?: string;
  Case_Handler?: string;
  payment_info?: {
    payment_date?: string;
    amount?: number;
  };
  [key: string]: any;
}

export interface EventHistory {
  event_id: string;
  name: string;
  stage: string;
  priority: number;
  status: string;
  start_time: string;
  end_time: string;
  update_time?: string;
  owner: string;
  description?: string;
  error_msg?: string;
  [key: string]: any;
}
