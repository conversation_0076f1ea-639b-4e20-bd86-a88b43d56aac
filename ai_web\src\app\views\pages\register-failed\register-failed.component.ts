import { Component } from '@angular/core';
import { IconDirective } from '@coreui/icons-angular';
import { ContainerComponent, RowComponent, ColComponent, ButtonDirective } from '@coreui/angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-register-failed',
  standalone: true,
  imports: [ContainerComponent, RowComponent, ColComponent, ButtonDirective],
  templateUrl: './register-failed.component.html',
  styleUrl: './register-failed.component.scss'
})
export class RegisterFailedComponent {
  constructor(private router: Router) {
  }
  direct_to_register(){
    this.router.navigate(['/register'])
  }
}
