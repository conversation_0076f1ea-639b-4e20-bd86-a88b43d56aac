import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BillAnalysisService } from '../../../service/bill-analysis.service';
import { CommonModule ,JsonPipe, NgClass, NgIf} from '@angular/common';
import { Router, ActivatedRoute,RouterModule } from '@angular/router';
import { cilSearch } from '@coreui/icons';
import { IconDirective } from '@coreui/icons-angular';
import {
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  FormCheckComponent,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  FormSelectDirective,
  ButtonDirective,
  InputGroupComponent,
  TableModule,
  GridModule,
} from '@coreui/angular';
import {
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
} from '@coreui/angular';
interface project {
  place_id: string;
  earliest_month: string;
  latest_month: string;
}
@Component({
  selector: 'app-bill-analysis-view',
  standalone: true,
  imports: [ButtonDirective,
      ButtonCloseDirective,
      CardBodyComponent,
      CardComponent,
      CardHeaderComponent,
      ColComponent,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TableModule,
      GridModule,
      InputGroupComponent,
      RowComponent,
      TextColorDirective,
      FormControlDirective,
      FormLabelDirective,
      ModalBodyComponent,
      ModalComponent,
      ModalFooterComponent,
      ModalHeaderComponent,
      ModalTitleDirective,
      RouterModule,
      IconDirective,
      FormCheckComponent,
          FormsModule,
          ReactiveFormsModule,
          CommonModule,
          CardBodyComponent,
          CardComponent,
          CardHeaderComponent,
          ColComponent,
          FormCheckInputDirective,
          FormCheckLabelDirective,
          FormLabelDirective,
          FormControlDirective,
          RowComponent,
          FormSelectDirective,
          JsonPipe,
          NgClass,
          NgIf,
          RouterModule,
          ButtonDirective,
          InputGroupComponent,
          TableModule,
          GridModule
],
  templateUrl: './bill-analysis-view.component.html',
  styleUrl: './bill-analysis-view.component.scss'
})
export class BillAnalysisViewComponent {
  icons = { cilSearch };
  errmsg: string;
  target_month: string = '';
  earliest_month: string;
  latest_month: string;
  place_id: string = '';
  searchText: string = '';
  Place_Id_List: project[] = [];
  leave_visible: boolean;
  leave_index: number;
  hovering: boolean[];
  billMonths: string[] = [];
  filteredBills: any[] = [];
  selectedBill: any = null;

  objectKeys = Object.keys;



  constructor( private BillAnalysisService : BillAnalysisService,
    private route: ActivatedRoute,


     ){
      this.errmsg = '';
      this.target_month = '';
      this.earliest_month = '';
      this.latest_month = '';
      this.leave_visible = false;
      this.leave_index = 0;
      this.place_id = this.route.snapshot.paramMap.get('place_id')|| '';
      this.get_bill_analysis_info();
      this.get_place_id_bill_information();
      this.hovering = this.filteredPlaceId().map(() => false);
     }
     get_bill_analysis_info() {
      this.BillAnalysisService.get_bill_analysis_info(this.place_id).subscribe(
        (data) => {
    
          if (data?.periods && Array.isArray(data.periods)) {
            const earliestRoc = data.periods[0];  // 11206
            const latestRoc = data.periods[data.periods.length - 1]; // 11406
            this.earliest_month = this.convertToGregorian(earliestRoc);
            this.latest_month = this.convertToGregorian(latestRoc);
          }
    
          if (data?.token) {
            localStorage.setItem('token', data.token);
          }
        },
        (error) => {
          console.error('API 錯誤:', this.errmsg);
        }
      );
     }
     private convertToGregorian(monthStr: string): string {
      const rocYear = parseInt(monthStr.slice(0, 3), 10);
      const month = monthStr.slice(3, 5);
      const year = rocYear + 1911;
      return `${year}${month}`;
     }
     get_place_id_bill_information() {
      this.BillAnalysisService.get_place_id_bill_information(this.place_id).subscribe(
        (data) => {
          if (data?.token) {
            localStorage.setItem('token', data.token);
          }
              const filtered = data.filter((item: any) => {
            const billMonth = item["帳單月份"];
            if (this.earliest_month && this.latest_month) {
              return billMonth >= this.earliest_month && billMonth <= this.latest_month;
            }
            return true; // 如果沒有指定最早和最晚月份，則返回所有項目
          }
        );
    
          this.filteredBills = filtered;
          this.billMonths = filtered.map((item: any) => item["帳單月份"]);
          console.log('filteredBills:', this.filteredBills);
          console.log('Bill Months:', this.billMonths);
        },
        (error) => {
          this.errmsg = 'Project find error!';
        }
      );
    }

    filteredPlaceId() {
      return this.Place_Id_List.filter((p) =>
        p && p.place_id && p.place_id.toLowerCase().includes(this.searchText.toLowerCase())
      );
    }
    onMonthSelected() {
      const target = String(this.target_month).trim();
          this.filteredBills.forEach(b => console.log(b['帳單月份']));
    
      const matched = this.filteredBills.find(
        bill => String(bill['帳單月份']) === target
      );
      this.selectedBill = matched || null;
    }
    
}
