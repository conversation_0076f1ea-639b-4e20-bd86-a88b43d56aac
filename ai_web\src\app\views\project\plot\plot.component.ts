import { ProjectService } from './../../../service/project.service';
import { DashboardService } from './../../../service/dashboard.service';
import { environment } from '../../../../environments/environment';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import '@joint/plus/joint-plus.css';
import {
  ButtonCloseDirective,
  ButtonDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  ThemeDirective,
} from '@coreui/angular';
import {
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormSelectDirective,
} from '@coreui/angular';
import {
  object_type_list,
  link_type_list,
  serial_no_list,
  link_connectable_object,
} from '../object_type';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { IconDirective } from '@coreui/icons-angular';
import { cilBaby } from '@coreui/icons';

interface project {
  project_code: string;
  project_name: string;
  place_id: string[];
}

interface plot_object {
  type: string;
  name: string;
  place_id: string;
  serialNo: string;
}

interface link {
  type: string;
  name: string;
  source: string;
  target: string;
  source_serialNo: string;
  target_serialNo: string;
  serialNo: string;
}

@Component({
  selector: 'app-plot',
  standalone: true,
  imports: [
    ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    FormControlDirective,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    RowComponent,
    TextColorDirective,
    FormSelectDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    ThemeDirective,
    RouterModule,
    IconDirective,
  ],
  templateUrl: './plot.component.html',
  styleUrl: './plot.component.scss',
})
export class PlotComponent {
  icons = { cilBaby };
  project_code = '';
  errmsg = '';
  titles: string[] = [];
  project: project;
  remove_serialNo = '0';
  remove_place_id = '';
  remove_object_type = '';
  remove_object_name = '';
  remove_link_serialNo = '';
  new_object_type = '';
  new_link_type = '';
  new_link_serialNo = '';
  new_serialNo = '0';
  new_place_id = '';
  new_project_place_id = '';
  new_object_name = '';
  new_link_source = '';
  new_link_target = '';
  new_link_name = '';
  projectplot_list: string[] = [];
  projectobject_list: plot_object[] = [];
  projectlink_list: link[] = [];
  connectableSourceList: plot_object[];
  connectableLinkTypeList: string[];
  connectableTargetList: plot_object[];
  object_type_list = object_type_list;
  link_type_list = link_type_list;
  visible = false;
  create_visible = false;
  create_link_visible = false;
  remove_visible = false;
  edit_visible = false;
  remove_projectplot_visible = false;
  remove_link_visible = false;
  production = environment.production;
  input_name = '';
  remove_projectplot_index = 0;
  available_source_list: string[] = [];
  available_target_list: string[] = [];

  constructor(
    private ProjectService: ProjectService,
    private DashboardService: DashboardService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }

  load_project_info() {
    this.ProjectService.get_project_info(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = {
          project_code: data.data.project_code || '',
          project_name: data.data.project_name || '',
          place_id: data.data.place_id || [],
        };
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
    this.projectplot_list = [];
    this.ProjectService.get_projectplotlist(this.project_code).subscribe(
      (data) => {
        for (let i = 0; i < data.projectplot_list.length; i++) {
          this.projectplot_list[i] = data.projectplot_list[i].projectplot_name;
        }
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }

  edit_projectplot(plot: string) {
    this.router.navigate([`/project/device-dashboard-edit/${this.project_code}/${plot}`]);
  }

  view_projectplot(plot: string) {
    window.open(
      `/project/device-dashboard-view/${this.project_code}/${plot}`,
      '_blank'
    );
  }
  add_projectplot() {
    this.ProjectService.create_projectplot(
      this.project_code,
      this.input_name
    ).subscribe(
      (data) => {
        this.load_project_info();
        this.toggle_create_plot_view();
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  toggle_create_plot_view() {
    this.input_name = '';
    this.visible = !this.visible;
  }
}
