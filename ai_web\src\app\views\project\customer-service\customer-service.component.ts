import { ProjectService } from './../../../service/project.service';
import { Component } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  ButtonCloseDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  CardFooterComponent,
  ColComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  FormCheckInputDirective,
  FormCheckLabelDirective,
  TableModule,
  GridModule,
  InputGroupComponent,
  ModalBodyComponent,
  ModalComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormLabelDirective,
  AvatarModule,
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { cilSmilePlus, cilSearch } from '@coreui/icons';

interface project {
  project_code: string;
  project_name: string;
  place_id: string[];
}
interface customer {
  name: string;
  email: string;
  auth: auth;
}
interface auth {
  view: boolean;
  accounting: boolean;
  controller: boolean;
  view_plot: string[];
  accounting_place_id: string[];
  controller_parameters: string[];
}
@Component({
  selector: 'app-customer-service',
  standalone: true,
  imports: [
    ButtonDirective,
    ButtonCloseDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    CardFooterComponent,
    ColComponent,
    CommonModule,
    DropdownComponent,
    DropdownItemDirective,
    DropdownMenuDirective,
    DropdownToggleDirective,
    FormsModule,
    ReactiveFormsModule,
    FormCheckInputDirective,
    FormCheckLabelDirective,
    TableModule,
    GridModule,
    InputGroupComponent,
    IconDirective,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    AvatarModule,
    RouterModule,
  ],
  templateUrl: './customer-service.component.html',
  styleUrl: './customer-service.component.scss',
})
export class CustomerServiceComponent {
  icons = { cilSmilePlus, cilSearch };
  project: project;
  hovering: boolean = false;
  project_code: string = '';
  searchCustomerText: string = '';
  create_customer_email: string = '';
  create_customer_view: boolean = true;
  create_customer_controller: boolean = false;
  create_customer_accounting: boolean = false;
  customer_list: customer[] = [];
  invite_customer_visible: boolean = false;
  edit_customer_view_auth_visible: boolean = false;
  remove_customer_visible: boolean = false;
  editing_customer: number = 0;
  projectplot_list: string[] = [];
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService
  ) {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }

  load_project_info() {
    this.projectService.get_project_info(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = {
          project_code: data.data.project_code || '',
          project_name: data.data.project_name || '',
          place_id: data.data.place_id || [],
        };
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.project_code =
        this.route.snapshot.paramMap.get('project_code') || '';

      this.get_customer_member(this.project_code);
      this.projectService.get_projectplotlist(this.project_code).subscribe(
        (data) => {
          this.projectplot_list = [];
          for (let i = 0; i < data.projectplot_list.length; i++) {
            this.projectplot_list[i] =
              data.projectplot_list[i].projectplot_name;
          }
          localStorage.setItem('token', data.token);
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
    });
  }

  add_customer() {
    this.projectService
      .add_customer(
        this.create_customer_email,
        this.create_customer_view,
        this.create_customer_accounting,
        this.create_customer_controller,
        this.project_code
      )
      .subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          this.toggle_invite_customer_modal();
          this.get_customer_member(this.project_code);
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
  }
  get_customer_member(project_code: string) {
    this.projectService.get_customer_member(project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.customer_list = [];
        for (let i = 0; i < data.member_list.length; i++) {
          this.customer_list[i] = {
            name: data.member_list[i].name,
            email: data.member_list[i].email,
            auth: data.member_list[i].auth,
          };
        }
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
  edit_customer_view_auth() {
    this.projectService
      .edit_customer_view_auth(
        this.project_code,
        this.customer_list[this.editing_customer]
      )
      .subscribe(
        (data) => {
          this.toggle_edit_customer_view_auth_modal(0);
          this.get_customer_member(this.project_code);
          localStorage.setItem('token', data.token);
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
  }
  isSelected(customer_index: number, plot_name: string) {
    if (this.customer_list.length > 0) {
      var view_auth = this.customer_list[customer_index].auth.view_plot;
      return view_auth.includes(plot_name);
    } else {
      return 0;
    }
  }

  onDayChange(isChecked: boolean, customer_index: number, plot_name: string) {
    if (this.customer_list.length > 0) {
      var view_auth = this.customer_list[customer_index].auth.view_plot;
      if (isChecked) {
        view_auth.push(plot_name);
      } else {
        const index = view_auth.indexOf(plot_name);
        if (index !== -1) {
          view_auth.splice(index, 1);
        }
      }
    }
  }
  remove_customer_auth() {
    this.projectService
      .remove_customer(
        this.project_code,
        this.customer_list[this.editing_customer].email
      )
      .subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          this.get_customer_member(this.project_code);
          this.toggle_remove_customer_modal(0);
        },
        (error) => {
          this.router.navigate(['/404']);
        }
      );
  }
  toggle_invite_customer_modal() {
    this.invite_customer_visible = !this.invite_customer_visible;
  }
  toggle_edit_customer_view_auth_modal(index: number) {
    this.edit_customer_view_auth_visible =
      !this.edit_customer_view_auth_visible;
    this.editing_customer = index;
  }
  toggle_remove_customer_modal(index: number) {
    this.remove_customer_visible = !this.remove_customer_visible;
    this.editing_customer = index;
  }
  filteredCustomers() {
    return this.customer_list.filter((p) =>
      p.name.toLowerCase().includes(this.searchCustomerText.toLowerCase())
    );
  }
}
