from flask import request, Blueprint, jsonify
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required
from bson.objectid import ObjectId
from datetime import datetime, timedelta
from sqlalchemy import text
from flask import current_app
from accessories import sqldb
import json,os


customer_service_page = Blueprint('customer_service', __name__)

@customer_service_page.route('/get_projectplot_list', methods=['OPTIONS', 'POST'])
@roles_required(['customer'])
def get_projectplot_list(token):
    customer_email = get_user_info(token, 'email')
    customer_view_auth = mongo.db.user.find_one({"email": customer_email}).get("customer_project")
    return_list = []
    for project_code, v in customer_view_auth.items():
        view_list = v.get("view_plot")
        for plot_name in view_list:
            return_list.append({"project_code": project_code,
                            "project_name": mongo.db.project.find_one({"project_code": project_code}).get("project_name"),
                            "plot_name": plot_name})
    return jsonify({
        "token": get_refresh_token(token),
        "plot_list": return_list}), 200
