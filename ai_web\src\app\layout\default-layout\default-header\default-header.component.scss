.mail-badge {
  position: absolute;
  top: 8px;
  right: 9px;
  width: 8px;
  height: 8px;
  background-color: blue;
  border-radius: 50%;
}

#liveDemoModal, #settingModal {
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.55);
  position: fixed;
  pointer-events: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

#liveDemoModal .modal-content, #settingModal .modal-content {
  pointer-events: auto;
}

.settings-container {
  max-width: max;
  margin: 3rem auto;
  padding: 2rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
}

td {
  vertical-align: middle;
}

.table td:first-child {
  min-width: 100px;
  white-space: nowrap;
}

.settings-container table {
  width: 100%;
  table-layout: fixed;
  word-wrap: break-word;
}

.profile-picture-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 100%;
}

.preview-container {
  width: 300px;
  height: 500px;
  border-radius: 50%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.btn-back{
  float: right;
  margin-right: 10px;
  margin-top: 10px;
}
.form-group {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .profile-picture-section {
    display: flex;
    align-items: center;
    gap: 1rem;

    .preview-container {
      width: 100px;
      height: 100px;
      border-radius: 50%;

      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .profile-upload {
      flex-grow: 1;

      .file-hint {
        margin-top: 0.5rem;
        font-size: 0.75rem;
      }
    }
  }
}
.disabled-input {
  background-color: #e9ecef;
  cursor: not-allowed;
}
.button-container {
  display: flex;
  justify-content: center;
  gap: 1rem;


}
.form-group label {
  display: block;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
.settings-container {
  margin: 1rem;
  max-height: 85vh;
}

.table td {
  display: block;
  width: 100%;
}

.table td:first-child {
  padding-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  max-width: 100%;
}

.button-container {
  flex-direction: column;
  padding: 1rem;
}

.button-container button {
  width: 100%;
}
}
.notification-switches {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.switch {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 52px;
  height: 28px;
  cursor: pointer;
}

.switch-input {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.switch-label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 20px;
  transition: all 0.2s ease-in;
}

.switch-handle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background: #fff;
  border-radius: 50%;
  border: 1px solid #ddd;
  transition: all 0.2s ease-in;
}

.switch-input:checked + .switch-label {
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.switch-input:checked + .switch-label + .switch-handle {
  left: 26px;
  border-color: #2eb85c;
}

.switch-text {
  font-size: 16px;

}
