import { InventoryService } from './../../../service/inventory.service';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  ButtonDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  InputGroupComponent
} from '@coreui/angular';
import { RowComponent, TextColorDirective, FormControlDirective, FormLabelDirective } from '@coreui/angular';


@Component({
  selector: 'app-lan-installer',
  standalone: true,
  imports: [
    ButtonDirective,
    CardBodyComponent, CardComponent, CardHeaderComponent,
    ColComponent, CommonModule, FormsModule, ReactiveFormsModule,
    TableModule, GridModule, InputGroupComponent,
    RowComponent, TextColorDirective, FormControlDirective, FormLabelDirective],
  templateUrl: './lan-installer.component.html',
  styleUrl: './lan-installer.component.scss'
})
export class LanInstallerComponent {
  gateway_id: string = '';
  lan_IP: string = '';
  lan_IP_uninstall: string = '';
  successMsg: string = '';
  pendingMsg: string = '';
  errMsg: string = '';
  successMsg_uninstall: string = '';
  pendingMsg_uninstall: string = '';
  errMsg_uninstall: string = '';
  scansuccessMsg: string = '';
  scanpendingMsg: string = '';
  scanerrMsg: string = '';
  deviceIPList: string[] = [];

  constructor(private InventoryService: InventoryService){

  }

  install(){
    this.pendingMsg = 'Installing...';
    this.successMsg = '';
    this.errMsg = '';
    this.InventoryService.lan_install(this.gateway_id, this.lan_IP).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.successMsg = '安裝成功！請檢查資料是否正常拋送。';
        this.pendingMsg = '';
        this.errMsg = '';
      },
      (error) => {
        this.successMsg = '';
        this.pendingMsg = '';
        this.errMsg = '安裝失敗！請檢查安裝檔及閘道區網位置。';
      }
    )
  }
  uninstall(){
    this.pendingMsg_uninstall = 'Uninstalling...';
    this.successMsg_uninstall = '';
    this.errMsg_uninstall = '';
    this.InventoryService.lan_uninstall(this.lan_IP_uninstall).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.successMsg_uninstall = '解安裝成功！';
        this.pendingMsg_uninstall = '';
        this.errMsg_uninstall = '';
      },
      (error) => {
        this.successMsg_uninstall = '';
        this.pendingMsg_uninstall = '';
        this.errMsg_uninstall = '解安裝失敗！請檢查閘道區網位置。';
      }
    )
  }
  scan(){
    this.scanpendingMsg = 'Searching...';
    this.scansuccessMsg = '';
    this.scanerrMsg = '';
    this.InventoryService.scan().subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.deviceIPList = []
        for (let ip of data.data){
          this.deviceIPList.push(ip)
        }
        this.scansuccessMsg = 'Done!';
        this.scanpendingMsg = '';
        this.scanerrMsg = '';
      },
      (error) => {
        this.scansuccessMsg = '';
        this.scanpendingMsg = '';
        this.scanerrMsg = 'Server Error!';
      }
    )
  }
}
