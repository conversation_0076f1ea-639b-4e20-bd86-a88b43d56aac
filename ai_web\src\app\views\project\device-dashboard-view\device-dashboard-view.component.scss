#paper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.custom-header {
  background-color: #345830 !important;
  color: white !important;
  height: 60px !important;
}

#stencil {
  position: absolute;
  top: 0;
  width: 190px;
  bottom: 0;
  left: 0;
}

:host ::ng-deep .joint-stencil.joint-theme-modern .group>.group-label {
  padding: 0 5px 0 34px;
  background: #1E3F20;
}
:host ::ng-deep .joint-stencil.joint-theme-modern .group>.group-label:hover, .joint-stencil.joint-theme-modern .groups-toggle>.group-label:hover {
  color: #4A7856;
}
:host ::ng-deep .joint-stencil.joint-theme-modern .group>.group-label, .joint-stencil.joint-theme-modern .groups-toggle>.group-label {
  color: #94ECBE;
}


#toolbar {
  position: absolute;
  top: 15px;
  right: 200px;
}

#toolbar .joint-toolbar {
  padding: 5px;
}


.fixed-text {
  position: fixed;  /* 固定位置 */
  top: 70px;     /* 距離螢幕底部 10px */
  right: 10px;      /* 距離螢幕右側 10px */
  background: rgba(0, 0, 0, 0.7); /* 半透明背景 */
  color: white;     /* 文字顏色 */
  padding: 5px 10px; /* 內距 */
  border-radius: 5px; /* 圓角 */
  font-size: 16px;  /* 字體大小 */
}

.fixed-text2 {
  position: fixed;  /* 固定位置 */
  top: 70px;     /* 距離螢幕底部 10px */
  left: 10px;      /* 距離螢幕右側 10px */
  background: rgba(0, 0, 0, 0.7); /* 半透明背景 */
  color: white;     /* 文字顏色 */
  padding: 5px 10px; /* 內距 */
  border-radius: 5px; /* 圓角 */
  font-size: 20px;  /* 字體大小 */
}
