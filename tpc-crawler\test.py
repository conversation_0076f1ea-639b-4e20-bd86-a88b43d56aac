import time, random, sys,threading
from redis import Redis 
import config 
from tpc_crawler import call_tpc_crawler,get_tpc_bills_crawler_request,get_tpc_yearmonth_crawler_request,get_tpc_delete_place_id_request
from tpc_case_and_invoice_crawler import tpc_case_and_invoice_crawler_thread
from legal_entity_crawler import legal_entity_crawler_thread
from RPA import RPA_threads,connect_redis_fill,connect_redis_update,fill_in,update
from concurrent.futures import ThreadPoolExecutor
from threading import Lock

def main():
    tpc_crawler_lock = Lock()
    update_lock = Lock()
    fill_data_lock = Lock()
    while True:
        if len(sys.argv) > 4:
            if sys.argv[-1] == 'production':
                conf = config.prd_config()
            if sys.argv[-1] == 'test_env':
                conf = config.test_config()
        else:
            conf =config.dev_config()
        executor = ThreadPoolExecutor(max_workers=10)
        # Max threads = 10
        time.sleep(random.uniform(2, 5))
        redis_client = Redis(host=conf.redis_host, port=conf.redis_port, db=conf.redis_db,decode_responses=True)
        fill_data = connect_redis_fill(redis_client=redis_client,conf=conf)
        update_data = connect_redis_update(redis_client=redis_client,conf=conf)
        tpc_yearmonth_crawler_request = get_tpc_yearmonth_crawler_request(redis_client=redis_client,conf=conf)
        tpc_bills_crawler_request = get_tpc_bills_crawler_request(redis_client=redis_client,conf=conf)
        tpc_delete_place_id_request = get_tpc_delete_place_id_request(redis_client=redis_client,conf=conf)
        if fill_data :
            executor.submit(fill_in, fill_data,fill_data_lock)
        if update_data:
            executor.submit(update, update_data,update_lock)
        if tpc_yearmonth_crawler_request or tpc_bills_crawler_request or tpc_delete_place_id_request:
            executor.submit(call_tpc_crawler, tpc_yearmonth_crawler_request, tpc_bills_crawler_request,tpc_delete_place_id_request,conf, redis_client,tpc_crawler_lock)
        
        legal_entity_crawler_threads = threading.Thread(target= legal_entity_crawler_thread,args=(redis_client,conf),name='legal_entity_crawler_thread',daemon=False)
        #legal_entity_crawler_threads.start()
        tpc_case_and_invoice_crawler_threads = threading.Thread(target= tpc_case_and_invoice_crawler_thread,name='tpc_case_and_invoice_crawler_thread',daemon=False)
        #tpc_case_and_invoice_crawler_threads.start()


        time.sleep(random.uniform(2, 5))
            
        time.sleep(random.uniform(2, 5))
if __name__ == '__main__':
    main()