from werkzeug.security import generate_password_hash
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required
from flask import jsonify, request, redirect, url_for, Blueprint, current_app
from sqlalchemy import text,Column, String, CHAR, Integer, Date, DateTime, Numeric,DECIMAL, create_engine,MetaData,Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.dialects.mysql import insert
from accessories import mail, sqldb,crawler_redis_client, mongo, save_json_to_mongo,update_json_in_mongo, remove_json_in_mongo
from accessories import send_mail
import json
import datetime
import redis
from collections import defaultdict
import time
from datetime import datetime, timedelta
from datetime import date
import os
import base64

metadata = MetaData()

crawler_service_page = Blueprint('crawler_service_page', __name__)
@crawler_service_page.route('/submit_tpc_crawler_request', methods=['OPTIONS','POST'])
@role_required(['user'])
def submit_request(token):
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    user_email = get_user_info(token, 'email')
    mongodb_user = mongo.db.user.find_one({"email": user_email})
    if not mongodb_user:
        return jsonify({'message': 'User not found!'}), 404
    if "user_service" not in mongodb_user:
        mongo.db.user.update_one(
                {"email": user_email},
                {"$set": {"user_service": {}}} 
            )
    if 'user_service' in mongodb_user and 'bills_analysising' not in mongodb_user['user_service']  :
        mongo.db.user.update_one(
        {"email": user_email},
        {"$set": {"user_service.bills_analysising": {}}}
        )   
    frontend_data = request.get_json()
    place_id = frontend_data.get('place_id')
    if frontend_data.get('account') == None or frontend_data.get('account') == '':
        frontend_data['account'] = '********'
    if frontend_data.get('password') == None or frontend_data.get('password') == '':
        frontend_data['password'] = '********'
    change_the_lasted_day = False
    contacter_id =100000
    contacter_ids = mongo.db.contacter.find({}, {"contacter_id": 1, "_id": 0})
    contacter_ids_list = [contact["contacter_id"] for contact in contacter_ids]
    while contacter_id in contacter_ids_list and contacter_id > 0:
        contacter_id -= 1
    add_legal_entity = False
    legal_entity_ids = mongo.db.legal_entity.find({}, {"company_id": 1, "_id": 0})
    if legal_entity_ids!= None :
        legal_entity_ids_list = [
            doc["company_id"]
            for doc in legal_entity_ids
            if doc.get("company_id")  ]       
        company_id = frontend_data.get('company_id')
        if company_id not in legal_entity_ids_list :
            add_legal_entity = True
    else :
        add_legal_entity = True
    now = datetime.now()
    the_lasted_year_month = f"{now.year - 1911:03d}{now.month:02d}"
    if 'bills_analysising' in mongodb_user.get('user_service', {}) and mongodb_user.get('user_service', {}) != {}:
        mongodb_place_id = mongodb_user['user_service']['bills_analysising'].keys()
    else:
        mongodb_place_id = []
    if frontend_data.get('place_id') in mongodb_place_id :
        change_the_lasted_day = True
    redis_data = crawler_redis_client.lrange('tpc_crawler_request', 0, -1)
    if redis_data != []:
        send_request = True
        for data in redis_data:
            parsed_data = json.loads(data)  
            data_place_id = parsed_data.get('place_id')
            if frontend_data.get('place_id') == data_place_id :
                return jsonify({'message': '需求已經存在'}), 406
    else:
        send_request = True
    if send_request  :
        api_base_url = current_app.config['API_BASE_URL']
        send_data = {"place_id":f"{frontend_data.get('place_id')}","TPC_online_bills_status":f"{frontend_data.get('TPC_online_bills_status')}","account":f"{frontend_data.get('account')}","password" :f"{frontend_data.get('password')}","month":f"{frontend_data.get('monthselector')}","authentication_method":f"{frontend_data.get('select_authentication_method')}","authentication":f"{frontend_data.get('authentication')}","retry_count":"1","rest_time":"1","email":f'{user_email}',"failure":f"{api_base_url}/bill_analysis/failure/{user_email}"}
        send_data = json.dumps(send_data)
        crawler_redis_client.rpush('tpc_crawler_request', send_data)

    if send_request  and change_the_lasted_day  :
        mongo.db.user.update_one(
        {"email": user_email},
        {"$pop": {f"user_service.bills_analysising.{frontend_data.get('place_id')}": 1}}
        )
        mongo.db.user.update_one(
        {"email": user_email},
        { "$push": {f"user_service.bills_analysising.{frontend_data.get('place_id')}":the_lasted_year_month } }
        )
        print(123)
        
    if send_request  and change_the_lasted_day == False : 
        result =mongo.db.user.update_one(
        {"email": user_email},
        {"$set": {f"user_service.bills_analysising.{place_id}": [f"{(datetime.now().year - 1913):03d}{datetime.now().month:02d}",the_lasted_year_month]}}        )
        print(result)
        print(456)
    return jsonify({"token": get_refresh_token(token)}), 200
@crawler_service_page.route('/failure/<email>/<place_id>/<month>', methods=['GET'])
def bill_crawler_failure_message(email,place_id,month):
    redis_data = crawler_redis_client.lrange('tpc_crawler_request', 0, -1)
    for idx, data in enumerate(redis_data):
        parsed_data = json.loads(data)  
        data_place_id = parsed_data.get('place_id')
        if place_id == data_place_id :
            crawler_redis_client.lrem('tpc_crawler_request', 1, data)  
            redis_data = crawler_redis_client.lrange('bill_crawler_request', 0, -1)
            for idx, data in enumerate(redis_data):
                parsed_data = json.loads(data)  
                data_place_id = parsed_data.get('place_id')
                crawler_redis_client.lrem('bill_crawler_request', 1, data)  
            send_mail('<EMAIL>',email,"系統訊息通知", f"系統訊息通知:「電號{place_id}電子帳單抓取失敗，請再作確認")   
    return jsonify({"msg": 'successful'}), 200
@crawler_service_page.route('/save_tpc_bill_information', methods=['POST'])
def bill_crawler_save_information():
    engine = sqldb.get_engine()
    Session = sessionmaker(bind=engine)
    session =Session()
    saving_data = request.form.to_dict()
    place_id = saving_data.get('place_id')
    yearmonth = saving_data.get('bill_year_month')
    year = str(int(yearmonth[:4])-1911)
    month = yearmonth[4:]
    print(f"{place_id}/{year}/{month}")
    target_month = f'{year}{month}'
    redis_data = crawler_redis_client.lrange('bill_crawler_request', 0, -1)
    save_to_sql =False
    for idx, data in enumerate(redis_data):
        parsed_data = json.loads(data)  
        redis_place_id = parsed_data.get('place_id')
        redis_month = parsed_data.get('month')
        redis_email = parsed_data.get('email')
        redis_month = redis_month.strip()
        if place_id == redis_place_id and target_month == redis_month :
            save_to_sql = True
            break
    if save_to_sql :
        create_table = text("""CREATE TABLE IF NOT EXISTS `tpc_bills` (
            `place_id` char(11) NOT NULL,
            `ebpps_url` varchar(255) DEFAULT '0',
            `ebpps_name` varchar(31) NOT NULL DEFAULT '0',
            `ebpps_customer_address` varchar(63) NOT NULL DEFAULT '0',
            `ebpps_bill_address` varchar(63) DEFAULT '0',
            `electricity_type` varchar(31) DEFAULT '0',
            `feeder_number` varchar(5) DEFAULT '0',
            `group_powerout` varchar(2) DEFAULT NULL,
            `company_id` char(11) DEFAULT NULL,
            `bill_year_month` char(6) NOT NULL DEFAULT '000000',
            `bill_date_next`  date DEFAULT NULL,
            `bill_date` date DEFAULT NULL,
            `date_readmeter` date DEFAULT NULL,
            `date_next_readmeter` date DEFAULT NULL,
            `date_bill_start` date DEFAULT NULL,
            `date_bill_end` date DEFAULT NULL,
            `day_used` decimal(3,0) DEFAULT NULL,
            `min_kwh` decimal(3,0) DEFAULT NULL,
            `fee_total` decimal(18,0) DEFAULT NULL,
            `fee_kw` decimal(20,2) DEFAULT NULL,
            `fee_kwfine` decimal(20,2) DEFAULT NULL,
            `fee_kwh` decimal(20,2) DEFAULT NULL,
            `fee_pf` decimal(20,2) DEFAULT NULL,
            `fee_public` decimal(20,2) DEFAULT NULL,
            `fee_tax` decimal(20,2) DEFAULT NULL,
            `kwh_total` decimal(18,0) DEFAULT NULL,
            `kwh_peak` decimal(18,0) DEFAULT NULL,
            `kwh_semipeak` decimal(18,0) DEFAULT NULL,
            `kwh_offpeak` decimal(18,0) DEFAULT NULL,
            `kwh_saturday` decimal(18,0) DEFAULT NULL,
            `contract_peak` decimal(18,0) DEFAULT NULL,
            `contract_semipeak` decimal(18,0) DEFAULT NULL,
            `contract_offpeak` decimal(18,0) DEFAULT NULL,
            `contract_saturday` decimal(18,0) DEFAULT NULL,
            `kw_peak` decimal(18,0) DEFAULT NULL,
            `kw_semipeak` decimal(18,0) DEFAULT NULL,
            `kw_offpeak` decimal(18,0) DEFAULT NULL,
            `kw_saturday` decimal(18,0) DEFAULT NULL,
            `subcontracted_kwh_peak` int DEFAULT NULL,
            `subcontracted_kwh_semipeak` int DEFAULT NULL,
            `subcontracted_kwh_offpeak` int DEFAULT NULL,
            `subcontracted_kwh_saturday` int DEFAULT NULL,
            `pf` decimal(3,0) DEFAULT NULL,
            `adjustment_factor`  decimal(20,10) DEFAULT NULL,
            `kwh_per_day` decimal(18,2) DEFAULT NULL,
            `created_at` datetime DEFAULT NULL,
            `status` varchar(64) DEFAULT NULL,
            `tpc_ach` varchar(45) DEFAULT NULL,
            `payment_deadline` datetime DEFAULT NULL,
            `reduce_kwh` decimal(18,0) DEFAULT NULL,
            `time_type` varchar(64) DEFAULT NULL,
            `day_deducted` date DEFAULT NULL,
            `day_next_deducted` date DEFAULT NULL,
            `customer_service_hotline` varchar(16) DEFAULT NULL,
            `tpc_comapny_id` varchar(16) DEFAULT NULL,
            `service_department` varchar(64) DEFAULT NULL,
            `service_department_address` varchar(128) DEFAULT NULL,
            `electronic_bill_discount_reduction` decimal(10,2) DEFAULT NULL,
            `fee_before_tax` decimal(18,2) DEFAULT NULL,
            `kwh_per_fuel_cost` decimal(20,10) DEFAULT NULL,
            `current_period_carbon_emissions` decimal(18,0) DEFAULT NULL,
            `kwh_per_recyling_fund` decimal(20,10) DEFAULT NULL,
            `group` varchar(8) DEFAULT NULL,
            `group_name` varchar(32) DEFAULT NULL,
            `meter_number` varchar(32) DEFAULT NULL,
            `multiplier` int(11) DEFAULT NULL,
            `energy_saving_reward` decimal(20,2) DEFAULT NULL,
            `kwh_per_average_price` decimal(20,10) DEFAULT NULL,
            `price_adjustment_discount` decimal(10,2) DEFAULT NULL, 
            `late_payment_fee` decimal(20,10) DEFAULT NULL,
            `power_outage_deduction` decimal(20,10) DEFAULT NULL,

            PRIMARY KEY (`place_id`, `bill_year_month`),
            KEY `idx_place_id` (`place_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
            """
        )
        try:
            with engine.connect() as connection:
                result = connection.execute(create_table)
            existing = session.query(tpcbills).filter_by(
            place_id=saving_data['place_id'],
            bill_year_month=saving_data['bill_year_month']
            ).first()

            if not existing:
                bill = tpcbills(**saving_data)
                session.add(bill)
                session.commit()
        finally:
            a=crawler_redis_client.lrem('bill_crawler_request', 1, data)      
            send_mail('<EMAIL>',redis_email,"系統訊息通知", f"電號{place_id}的{redis_month}電子帳單已抓取完成")  
            redis_data = crawler_redis_client.lrange('bill_crawler_request', 0, -1)
            lrem_tpc_year_month_request = True
            for idx, data in enumerate(redis_data):
                parsed_data = json.loads(data)  
                redis_place_id = parsed_data.get('place_id')
                if redis_place_id == place_id :
                    lrem_tpc_year_month_request = False
            if lrem_tpc_year_month_request :
                redis_data = crawler_redis_client.lrange('tpc_crawler_request', 0, -1)
                for idx, data in enumerate(redis_data):
                    parsed_data = json.loads(data)  
                    redis_place_id = parsed_data.get('place_id')
                    if redis_place_id == place_id :
                        crawler_redis_client.lrem('tpc_crawler_request', 1, place_id)
            session.close()
            print('ok')
    return jsonify({"msg": 'successful'}) ,200
@crawler_service_page.route('/save_legal_entity_information', methods=['POST'])
def legal_entity_crawler_save_information():
    if request.method == 'OPTIONS':
        return '', 204
    data = request.get_json(force=True)
    data = json.loads(data)
    company_basic_info = data.get('company_basic_information')
    company_id = company_basic_info.get('company_id')
    redis_data = crawler_redis_client.lrange('legal_entity_crawler_request', 0, -1)
    save_to_mongo =False
    for idx,  in enumerate(redis_data):
        parsed_data = json.loads(data)  
        redis_legal_entity_id = parsed_data.get('company_id')
        redis_month = redis_month.strip()
        if company_id == redis_legal_entity_id  :
            save_to_mongo = True
    if save_to_mongo :
        save_json_to_mongo(data, 'legal_entity',company_id)
        crawler_redis_client.lrem('legal_entity_crawler_request', 1, company_id)
    return jsonify({"msg": 'successful'}), 200
@crawler_service_page.route('/submit_legal_entity_request/<company_id>', methods=['GET'])
def submit_legal_entity_request(company_id):
    if request.method == 'OPTIONS':
        return '', 204
    crawler_redis_client.rpush('legal_entity_crawler_request', company_id)
    return jsonify({"msg": 'successful'}), 200
    
@crawler_service_page.route('/update_bizform/<token>', methods=['POST', 'OPTIONS'])
def update_bizform(token):
    if request.method == 'OPTIONS':
        return '', 204
    data = request.get_json()
    place_id = data.get("place_id")
    appNumber = data.get("appNumber")
    collection = mongo.db["event"]
    doc = collection.find_one({"place_id": place_id}, {"Process Form Number": 1})
    docs = dict(doc)
    Process_Form_Number = docs["Process Form Number"]
    _id = docs["_id"]
    tmp = {}
    tmp["流程編號"] = Process_Form_Number
    tmp["受理號碼"] = appNumber
    tmp["接案人"] = get_user_info(token, 'firstname')
    today_str = datetime.now().strftime("%Y/%m/%d")
    tmp["接單日期"] = today_str
    update_json_in_mongo(
        {"Acceptance_Number": appNumber, "Case_Handler": tmp["接案人"], "Order_Acceptance_Date": tmp["接單日期"], "Execution Status": "已送件"},
        collection_name="event",
        doc_name=_id,
        save_history=True  # 可選要不要留歷史紀錄
    )
    data_list = [tmp]
    print(data_list)
    message = json.dumps(data_list, ensure_ascii=False)
    crawler_redis_client.set('update_bizform', message) 
    return jsonify({'status':'ok'}), 200

Base = declarative_base()
class tpcbills(Base):
    __tablename__ = 'tpc_bills'
    __table_args__ = (
        {'mysql_charset': 'utf8'},
    )
    place_id = Column(CHAR(11), primary_key=True)
    bill_year_month = Column(CHAR(6), primary_key=True, default='000000')
    bill_date_next = Column(Date)
    bill_date = Column(Date)
    ebpps_url = Column(String(255), default='0')
    ebpps_name = Column(String(31), nullable=False, default='0')
    ebpps_customer_address = Column(String(63), nullable=False, default='0')
    ebpps_bill_address = Column(String(63), default='0')
    electricity_type = Column(String(31), default='0')
    feeder_number = Column(String(5), default='0')
    group_powerout = Column(String(2))
    company_id = Column(CHAR(11))
    date_readmeter = Column(Date)
    date_next_readmeter = Column(Date)
    date_bill_start = Column(Date)
    date_bill_end = Column(Date)
    day_used = Column(Numeric(3, 0), nullable=True)
    min_kwh = Column(Numeric(3, 0), nullable=True)
    fee_total = Column(Numeric(18, 0), nullable=True)
    fee_kw = Column(Numeric(20, 2), nullable=True)
    fee_kwfine = Column(Numeric(20, 2), nullable=True)
    fee_kwh = Column(Numeric(20, 2), nullable=True)
    fee_pf = Column(Numeric(20, 2), nullable=True)
    fee_public = Column(Numeric(20, 2), nullable=True)
    fee_tax = Column(Numeric(20, 2), nullable=True)
    kwh_total = Column(Numeric(18, 0), nullable=True)
    kwh_peak = Column(Numeric(18, 0), nullable=True)
    kwh_semipeak = Column(Numeric(18, 0), nullable=True)
    kwh_offpeak = Column(Numeric(18, 0), nullable=True)
    kwh_saturday = Column(Numeric(18, 0), nullable=True)
    contract_peak = Column(Numeric(18, 0), nullable=True)
    contract_semipeak = Column(Numeric(18, 0), nullable=True)
    contract_offpeak = Column(Numeric(18, 0), nullable=True)
    contract_saturday = Column(Numeric(18, 0), nullable=True)
    kw_peak = Column(Numeric(18, 0), nullable=True)
    kw_semipeak = Column(Numeric(18, 0), nullable=True)
    kw_offpeak = Column(Numeric(18, 0), nullable=True)
    kw_saturday = Column(Numeric(18, 0), nullable=True)
    pf = Column(Numeric(3, 0), nullable=True)
    kwh_per_day = Column(Numeric(18, 2), nullable=True)
    created_at = Column(DateTime)
    status = Column(String(64), nullable=True)
    tpc_ach = Column(String(45), nullable=True)
    payment_deadline = Column(DateTime)
    reduce_kwh = Column(Numeric(18, 0), nullable=True)
    time_type = Column(String(64), nullable=True)
    day_deducted = Column(Date, nullable=True)
    day_next_deducted = Column(Date, nullable=True)
    customer_service_hotline = Column(String(16), nullable=True)
    tpc_comapny_id = Column(String(16), nullable=True)
    service_department = Column(String(64), nullable=True)
    service_department_address = Column(String(128), nullable=True)
    electronic_bill_discount_reduction = Column(DECIMAL(10, 2), nullable=True)
    fee_before_tax = Column(DECIMAL(18, 2), nullable=True)
    kwh_per_fuel_cost = Column(DECIMAL(20, 10), nullable=True)
    current_period_carbon_emissions = Column(DECIMAL(18, 0), nullable=True)
    kwh_per_recyling_fund = Column(DECIMAL(20, 10), nullable=True)
    group = Column(String(8), nullable=True)
    group_name = Column(String(32), nullable=True)
    meter_number = Column(String(32), nullable=True)
    multiplier = Column(Integer, nullable=True)
    energy_saving_reward = Column(Numeric(20, 2), nullable=True)
    kwh_per_average_price = Column(Numeric(20, 10), nullable=True)
    adjustment_factor = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_peak = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_semipeak = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_offpeak = Column(Numeric(20, 10), nullable=True)
    subcontracted_kwh_saturday = Column(Numeric(20, 10), nullable=True)
    price_adjustment_discount = Column(DECIMAL(10,2), nullable=True)
    late_payment_fee = Column(Numeric(20,10), nullable=True)
    power_outage_deduction = Column(Numeric(20,10), nullable=True)

class invoice_data(Base):
    __tablename__ = 'invoice_data'

    invoice_number = Column(String(20), primary_key=True)
    invoice_date = Column(Date)
    total_sales_amount = Column(DECIMAL(15,2))
    taxable_sales_amount = Column(DECIMAL(15,2))
    zero_tax_sales_amount = Column(DECIMAL(15,2))
    tax_exempt_sales_amount = Column(DECIMAL(15,2))
    vat_amount = Column(DECIMAL(15,2))
    total_amount = Column(DECIMAL(15,2))
    tax_type = Column(String(20))
    invoice_status = Column(String(50))
    last_modified_time = Column(DateTime)
    carrier_type_code = Column(String(20))
    carrier_code = Column(String(100))
    format_code = Column(String(10))
    item_type = Column(String(10))
    invoice_category = Column(String(50))
    buyer_id = Column(String(20))
    buyer_name = Column(String(100))
    seller_id = Column(String(20))
    seller_name = Column(String(100))
    send_date = Column(Date)
    sender_name = Column(String(100))
    duplicate_flag = Column(String(10))

@crawler_service_page.route("/receive_case_result", methods=["POST"])
def receive_case_result():
    print("處理 Redis 資料")
    errors = []
    while True:
        raw_data = crawler_redis_client.lpop("case_inquiry_result_queue")
        if not raw_data:
            print("📭 Redis queue 無資料")
            break
        try:
            result = json.loads(raw_data)
            result["status"] = "done"  
            app_number = result.get("Acceptance_Number")
            doc = mongo.db.events.find_one({"Acceptance_Number": app_number})
            doc_id = doc["_id"]
            create_time = doc.get("create_time")
            now = datetime.now()
            if now - create_time > timedelta(days=14):  # 案件超過兩周進入這狀態
                if result.get("Payment_Status") != "已繳":
                    print("案件匯款後沒有跳已結束有問題，請進行確認")
                    result = update_event_status(
                        acceptance_number=app_number,
                        status="error",
                        stage="payment",
                        error_msg="案件匯款後沒有跳已結束有問題，請進行確認"
                    )
                else:
                    print("案件已兩周沒有進度，未變成結束狀態，請進行確認")
                    result = update_event_status(
                        acceptance_number=app_number,
                        status="error",
                        stage="power_acceptance",
                        error_msg="案件已兩周沒有進度，未變成結束狀態，請進行確認"
                    )
            else:
                if result.get("Payment_Status") == "已繳":
                    result["status"] = "in_process"
                    result["stage"] = "invoice"
                    crawler_redis_client.rpush(
                        'invoice_inquiry_crawler_key',
                        json.dumps({
                            "place_id": result["place_id"],
                            "tax_ID": "********",
                            "invoice_year_month": result["Application_Date"][:5]
                        })
                    )
                    update_json_in_mongo(
                        data=result,
                        collection_name="events",
                        doc_name=doc_id,
                        save_history=True
                    )
                elif result.get("Outstanding_Payment") == "0":
                    result["status"] = "done"
                    result["stage"] = "payment"
                    update_json_in_mongo(
                        data=result,
                        collection_name="events",
                        doc_name=doc_id,
                        save_history=True
                    )
                    time.sleep(10)
                    remove_json_in_mongo("events", doc_id, True)
                else:
                    result["status"] = "in_process"
                    result["stage"] = "payment"
                    update_json_in_mongo(
                        data=result,
                        collection_name="events",
                        doc_name=doc_id,
                        save_history=True
                    )
            print("✅ 已儲存案件：", app_number)

        except Exception as e:
            print("❌ 處理失敗：", e)
            errors.append({"data": raw_data, "error": str(e)})

    return jsonify({
        "msg": "successful",

    }), 200
    
@crawler_service_page.route("/receive_invoice_data", methods=["POST"])
def receive_invoice_data():
    errors = []

    # 1️⃣ 取出 Redis queue 所有資料
    raw_data_list = []
    while True:
        raw = crawler_redis_client.lpop("invoice_data_queue")
        if not raw:
            break
        raw_data_list.append(raw)

   
    grouped_by_place_id = defaultdict(list)
    for raw_data in raw_data_list:
        try:
            data = json.loads(raw_data)
            place_id = data.get("place_id")
            if place_id:
                grouped_by_place_id[place_id].append(data)
            else:
                print("⚠️ 無 place_id，略過資料")
        except Exception as e:
            print(f"❌ JSON decode 錯誤：{e}")
            errors.append({"raw_data": raw_data, "error": str(e)})

    
    for place_id, data_list in grouped_by_place_id.items():
        try:

            combined_data = {
            "status": "done",
            "stage": "invoice",
            "invoices": data_list
             }
   

            # 取出 MongoDB 中該事件 _id
            doc = mongo.db.events.find_one({"place_id": place_id})
            if not doc:
                raise ValueError(f"❌ 無對應事件，place_id={place_id}")

            doc_id = doc["_id"]

            update_json_in_mongo(
                data=combined_data,
                collection_name="events",
                doc_name=doc_id,
                save_history=True
            )
            time.sleep(10)
            remove_json_in_mongo("events",doc_id,True)
            check_invoice_data()
            print(f"✅ 已更新事件：{place_id}，合併 {len(data_list)} 筆")

        except Exception as e:
            print("❌ MongoDB 更新失敗：", e)
            errors.append({"place_id": place_id, "error": str(e)})
    
    return jsonify({
        "msg": "successful",
    }), 200


def check_invoice_data():
    now = datetime.now()
    seven_days = timedelta(days=7)
    redis_key = "invoice_time_queue"
    pushed = False


    invoice_time_doc = mongo.db.invoice_time.find_one({})

    if not invoice_time_doc:
     
        start_time = now - seven_days
        end_time = now

        mongo.db.invoice_time.insert_one({
            "start_time": start_time.strftime("%Y-%m-%d"),
            "end_time": end_time.strftime("%Y-%m-%d"),
            "created_at": now.strftime("%Y-%m-%d %H:%M:%S")
        })

       
        task_payload = {
            "start_time": start_time.strftime("%Y-%m-%d"),
            "end_time": end_time.strftime("%Y-%m-%d")
        }
        crawler_redis_client.rpush(redis_key, json.dumps(task_payload))
        pushed = True
        print("✅ 新增 invoice_time 並推入 redis")

    else:
   
        try:
            last_end = datetime.strptime(invoice_time_doc["end_time"], "%Y-%m-%d")
        except Exception as e:
            print("❌ 日期格式錯誤:", e)
            return jsonify({"error": "日期格式錯誤"}), 400

        if now - last_end >= seven_days:
           
            new_start = last_end
            new_end = now

            update_json_in_mongo(
                data={
                    "start_time": new_start.strftime("%Y-%m-%d"),
                    "end_time": new_end.strftime("%Y-%m-%d"),
                    "last_updated": now.strftime("%Y-%m-%d %H:%M:%S")
                },
                collection_name="invoice_time",
                doc_name=invoice_time_doc["_id"], 
                save_history=True
            )

      
            task_payload = {
                "start_time": new_start.strftime("%Y-%m-%d"),
                "end_time": new_end.strftime("%Y-%m-%d")
            }
            crawler_redis_client.rpush(redis_key, json.dumps(task_payload))
            pushed = True
            print("✅ 更新 invoice_time 並推入 redis")

    return jsonify({
        "msg": "successful",
    }), 200
    
@crawler_service_page.route('/insert_invoice2_sql_data', methods=['POST'])
def insert_invoice2_sql_data():
    engine = sqldb.get_engine()
    Session = sessionmaker(bind=engine)
    session = Session()

    redis_data = crawler_redis_client.lrange('invoice_sql_data', 0, -1)

    create_table = text("""
        CREATE TABLE IF NOT EXISTS `invoice_data` (
            `invoice_number` VARCHAR(20) NOT NULL,
            `invoice_date` DATE DEFAULT NULL,
            `total_sales_amount` DECIMAL(15,2) DEFAULT NULL,
            `taxable_sales_amount` DECIMAL(15,2) DEFAULT NULL,
            `zero_tax_sales_amount` DECIMAL(15,2) DEFAULT NULL,
            `tax_exempt_sales_amount` DECIMAL(15,2) DEFAULT NULL,
            `vat_amount` DECIMAL(15,2) DEFAULT NULL,
            `total_amount` DECIMAL(15,2) DEFAULT NULL,
            `tax_type` VARCHAR(20) DEFAULT NULL,
            `invoice_status` VARCHAR(50) DEFAULT NULL,
            `last_modified_time` DATETIME DEFAULT NULL,
            `carrier_type_code` VARCHAR(20) DEFAULT NULL,
            `carrier_code` VARCHAR(100) DEFAULT NULL,
            `format_code` VARCHAR(10) DEFAULT NULL,
            `item_type` VARCHAR(10) DEFAULT NULL,
            `invoice_category` VARCHAR(50) DEFAULT NULL,
            `buyer_id` VARCHAR(20) DEFAULT NULL,
            `buyer_name` VARCHAR(100) DEFAULT NULL,
            `seller_id` VARCHAR(20) DEFAULT NULL,
            `seller_name` VARCHAR(100) DEFAULT NULL,
            `send_date` DATE DEFAULT NULL,
            `sender_name` VARCHAR(100) DEFAULT NULL,
            `duplicate_flag` VARCHAR(10) DEFAULT NULL,
            PRIMARY KEY (`invoice_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
    """)

    try:
        with engine.connect() as conn:
            conn.execute(create_table)

        for idx, data in enumerate(redis_data):
            parsed_data = json.loads(data)
            invoice_number = parsed_data.get('invoice_number')
            year_month = parsed_data.get('invoice_date', '')[:7]

            # 避免重複插入
            existing = session.query(invoice_data).filter_by(invoice_number=invoice_number).first()
            if not existing:
                for k, v in parsed_data.items():
                    if k.endswith("_amount") or k.startswith("fee_"):
                        try:
                            parsed_data[k] = float(v)
                        except:
                            parsed_data[k] = 0.0
                    elif k.endswith("_date") or k == "send_date":
                        try:
                            parsed_data[k] = datetime.strptime(v, "%Y/%m/%d").date()
                        except:
                            parsed_data[k] = None
                    elif k == "last_modified_time":
                        try:
                            parsed_data[k] = datetime.strptime(v, "%Y/%m/%d %H:%M:%S")
                        except:
                            parsed_data[k] = None
                    else:
                        parsed_data[k] = str(v)
                        
                new_invoice = invoice_data(**parsed_data)
                session.add(new_invoice)
                session.commit()

                crawler_redis_client.lrem("invoice_sql_data", 1, data)

    except Exception as e:
        session.rollback()
        print(f"❌ 發票寫入失敗: {str(e)}")
    finally:
        session.close()

    return jsonify({"msg": "發票處理完成"}), 200



@crawler_service_page.route("/upload_invoice_pdf", methods=["POST"])
def upload_invoice_pdf():
    try:
        queue_name = "tpc_invoice_pdf_upload"
     
        while True:
            task_raw = crawler_redis_client.lpop(queue_name)
            if not task_raw:
                break  
            try:
                task = json.loads(task_raw)
                invoice_number = task.get("invoice_number")
                start_time = task.get("start_time")
                end_time = task.get("end_time")
                pdf_base64 = task.get("pdf_base64")

                if not all([invoice_number, start_time, end_time, pdf_base64]):
                    continue  

                folder_name = f"{start_time}__{end_time}"
                save_dir = os.path.join(current_app.config['INVOICE_UPLOAD_FOLDER'], folder_name)
                os.makedirs(save_dir, exist_ok=True)

                base_filename = f"{invoice_number}.pdf"
                filename = base_filename
                file_path = os.path.join(save_dir, filename)
                while os.path.exists(file_path):
                    filename = f"{invoice_number}.pdf"
                    file_path = os.path.join(save_dir, filename)

                with open(file_path, "wb") as f:
                    f.write(base64.b64decode(pdf_base64))


            except Exception as e:
                print(f"❌ 儲存發票時發生錯誤: {e}")
                continue 

        return jsonify({"message": "All tasks processed"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500

###底下注意
@crawler_service_page.route("/update_event", methods=["POST"])
def update_event():
    """
    通用事件狀態更新與通知 API
    接收 JSON:
    {
        "acceptance_number": "受理號碼",
        "status": "狀態 (pending/in_process/done/error/canceled)",
        "stage": "階段 (payment/invoice/application等)",
        "error_msg": "錯誤訊息（可選，僅 error 狀態時填）"
    }
    """
    try:
        data = request.get_json()
        print(f"📥 收到狀態更新請求: {data}")
        acceptance_number = data['acceptance_number'].strip()
        status = data['status']
        stage = data['stage']
        error_msg = data.get('error_msg', "") 
        valid_statuses = ['pending', 'in_process', 'done', 'error', 'canceled']
        if status not in valid_statuses:
            return jsonify({"error": f"無效的狀態值: {status}"}), 400
        event_doc = mongo.db.events.find_one({"Acceptance_Number": acceptance_number})
        if not event_doc:
            return jsonify({"error": f"找不到受理號碼為 '{acceptance_number}' 的事件"}), 404
        update_data = {
            "status": status,
            "stage": stage,
            "error_msg": error_msg if status == "error" else None 
        }
        update_json_in_mongo(update_data, "events", event_doc["_id"], save_history=True)
        role_data = event_doc.get("role", {})
        responsible_email = role_data.get("project leader") 
        project_name = event_doc.get("project_name", "未知專案") 
        if status == "error" and error_msg and responsible_email:
            subject = f"爬蟲狀態通知 - 錯誤: 案件 {project_name}"
            content = f"案件受理號碼: {acceptance_number}<br>" \
                      f"階段: {stage}<br>" \
                      f"狀態: {status}<br>" \
                      f"錯誤訊息: {error_msg}"
            send_mail('<EMAIL>', responsible_email, subject, content)
        return jsonify({
            "message": "狀態更新成功",
            "acceptance_number": acceptance_number,
            "status": status,
            "stage": stage,
            "doc_id": str(event_doc["_id"])
        }), 200

    except Exception as e:
        print(f"❌ 更新事件狀態時發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "error": f"{str(e)}"
        }), 500
        
def update_event_status(acceptance_number, status, stage, error_msg=""):  ##程式內使用
    try:
        valid_statuses = ['pending', 'in_process', 'done', 'error', 'canceled']
        if status not in valid_statuses:
            raise ValueError(f"無效的狀態值: {status}")
        
        event_doc = mongo.db.events.find_one({"Acceptance_Number": acceptance_number})
        if not event_doc:
            raise ValueError(f"找不到受理號碼為 '{acceptance_number}' 的事件")
        
        update_data = {
            "status": status,
            "stage": stage,
            "error_msg": error_msg if status == "error" else None 
        }
        update_json_in_mongo(update_data, "events", event_doc["_id"], save_history=True)

        role_data = event_doc.get("role", {})
        responsible_email = role_data.get("project leader")
        project_name = event_doc.get("project_name", "未知專案")

        if status == "error" and error_msg and responsible_email:
            subject = f"爬蟲狀態通知 - 錯誤: 案件 {project_name}"
            content = f"案件受理號碼: {acceptance_number}<br>" \
                      f"階段: {stage}<br>" \
                      f"狀態: {status}<br>" \
                      f"錯誤訊息: {error_msg}"
            send_mail('<EMAIL>', responsible_email, subject, content)

        return {
            "message": "狀態更新成功",
            "acceptance_number": acceptance_number,
            "status": status,
            "stage": stage,
            "doc_id": str(event_doc["_id"])
        }
    except Exception as e:
        print(f"❌ 更新事件狀態時發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return {
            "error": f"{str(e)}"
        }
