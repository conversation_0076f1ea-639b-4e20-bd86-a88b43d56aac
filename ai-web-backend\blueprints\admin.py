from flask import request, Blueprint, jsonify, current_app, send_file
from accessories import mongo, sqldb, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required, token_required
from sqlalchemy import text
import csv
import os
from io import BytesIO
admin_page = Blueprint('admin', __name__)
@admin_page.route('/get_users_roles', methods=['OPTIONS', 'POST'])
@role_required(['admin'])
def get_users_roles(token):
    roles = mongo.db.user.distinct("role")
    users_cursor = mongo.db.user.find({}, {"_id": 1, "firstname": 1, "lastname": 1, "email": 1, "role": 1})
    users = []
    for user in users_cursor:
        user["_id"] = str(user["_id"])
        users.append(user)
    return jsonify({"token": get_refresh_token(token), "roles": roles, "users": users})

@admin_page.route('/update_user_role', methods=['OPTIONS', 'POST'])
@role_required(['admin'])
def update_user_role(token):
    data = request.json
    update_users = data['update_users']
    for user in update_users:
        email = user.get('email')
        roles = user.get('roles')
        update_user = mongo.db.user.find_one({"email": email})
        update_json_in_mongo(data={"role": roles}, collection_name="user", doc_name=update_user["_id"])
    return jsonify({"token": get_refresh_token(token)}), 200
