import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TabsModule } from '@coreui/angular';
import { ProjectService } from '../../../service/project.service';
import { event_config_list, event_type_trans } from '../../scheduler/event_config';
import { EventData, EventHistory, SharedModule } from '../../../shared/shared.module';
@Component({
  selector: 'app-event-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TabsModule,
    SharedModule
  ],
  templateUrl: './event-details.component.html',
  styleUrl: './event-details.component.scss'
})
export class EventDetailsComponent implements OnInit {
  // 僅保留必要屬性
  eventId = '';
  eventType = '';
  eventDate = '';
  lastUpdateTime = '';
  eventStatus = '';
  eventStage = '';
  projectName = '';
  project_code = '';
  event_detail: EventHistory[] = [];
  eventInfo: EventData = {} as EventData;
  currentEventConfig: any = null;
  stages: any[] = [];
  currentUserRoles: string[] = [];
  errorMsg = '';
  errorInput = '';
  acceptanceNumber = '';
  isLoading = false;
  paymentDate = '';
  paymentAmount: number | null = null;
  showConfirmModal = false;
  confirmType: 'acceptance' | 'payment' | '' = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private ProjectService: ProjectService
  ) {}

  ngOnInit(): void {
    this.eventId = this.route.snapshot.paramMap.get('event_id') || '';
    if (!this.eventId) return this.showError('無效的事件 ID');
    this.get_events_detail(this.eventId);
  }

  get_events_detail(event_id: string): void {
    this.isLoading = true;
    this.ProjectService.get_events_detail(event_id).subscribe({
      next: (data: any) => {
        this.isLoading = false;
        this.event_detail = data.eventdetail || [];
        if (data.event_info) {
          const info = data.event_info;
          this.eventInfo = info;
          this.eventType = info.event_type || '';
          this.eventDate = info.create_time || '';
          this.lastUpdateTime = info.update_time || '';
          this.eventStatus = info.status || '';
          this.eventStage = info.stage || '';
          this.projectName = info.project_name || '';
          this.project_code = info.project_code || '';
          this.errorMsg = info.error_msg || '';
          this.acceptanceNumber = info.acceptance_number || '';
        }
        if (data.user_roles && Array.isArray(data.user_roles)) {
          this.currentUserRoles = data.user_roles;
        }
        this.loadEventConfig();
      },
      error: () => {
        this.isLoading = false;
        this.showError('獲取事件詳情失敗，請稍後再試');
      }
    });
  }

  // 通用：欄位是否只讀
  isReadOnlyField(type: 'acceptance' | 'payment'): boolean {
    return type === 'acceptance'
      ? !!this.acceptanceNumber
      : !!(this.getStageFieldValue('payment', 'payment_date') && this.getStageFieldValue('payment', 'amount'));
  }
  // 通用：取得欄位值
  getFieldValue(type: 'acceptance' | 'payment', field?: string): any {
    return type === 'acceptance' ? this.acceptanceNumber : this.getStageFieldValue('payment', field || '');
  }
  // 通用：取得流程欄位值
  getStageFieldValue(stageKey: string, fieldName: string): any {
    const detail = this.event_detail.find(d => d.stage === stageKey || d.stage === event_type_trans(stageKey));
    if (detail && detail[fieldName] !== undefined) return detail[fieldName];
    if (this.eventInfo && this.eventInfo['form_data']) {
      try {
        const formData = JSON.parse(this.eventInfo['form_data'] || '{}');
        if (formData[fieldName] !== undefined) return formData[fieldName];
      } catch {}
    }
    return null;
  }
  // 通用：角色判斷
  hasRole(role: string): boolean { return this.currentUserRoles.includes(role); }
  hasAnyRole(roles: string[]): boolean { return roles.some(r => this.currentUserRoles.includes(r)); }
  // 通用：顯示錯誤訊息
  showError(message: string): void { this.errorMsg = message; setTimeout(() => { this.errorMsg = ''; }, 5000); }
  // 通用：流程步驟名稱
  getStageZh(stageKey: string): string { return event_type_trans(stageKey, true) || stageKey; }
  // 通用：事件類型翻譯
  getEventTypeTranslation(eventType: string): string { return event_type_trans(eventType, true) || eventType; }
  // 通用：狀態顯示
  getStatusClass(status: string): string {
    return status === 'completed' || status === 'done' ? 'bg-success'
      : status === 'running' || status === 'in_process' ? 'bg-info'
      : status === 'pending' ? 'bg-warning'
      : status === 'failed' || status === 'error' ? 'bg-danger'
      : status === 'canceled' ? 'bg-secondary'
      : 'bg-info';
  }
  getStatusDisplay(status: string, stage?: string): string {
    const translatedStatus = event_type_trans(status, true) || status;
    return stage ? `${event_type_trans(stage, true) || stage}${translatedStatus}` : translatedStatus;
  }
  // 通用：日期格式
  formatDateTime(dateTimeString: string): string {
    if (!dateTimeString) return '未設定';
    try { const date = new Date(dateTimeString); return isNaN(date.getTime()) ? dateTimeString : date.toISOString().split('T')[0]; }
    catch { return dateTimeString; }
  }
  formatDateTimeFull(dateTimeString: string): string {
    if (!dateTimeString) return '未設定';
    try {
      const date = new Date(dateTimeString);
      if (isNaN(date.getTime())) return dateTimeString;
      const pad = (n: number) => n.toString().padStart(2, '0');
      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}_${pad(date.getHours())}-${pad(date.getMinutes())}-${pad(date.getSeconds())}`;
    } catch { return dateTimeString; }
  }
  getLastUpdateTime(): string {
    const timeStamps: string[] = [];
    if (this.event_detail?.length) this.event_detail.forEach(d => d['last_updated'] && timeStamps.push(d['last_updated']));
    if (this.lastUpdateTime) timeStamps.push(this.lastUpdateTime);
    if (!timeStamps.length) return '未設定';
    const latestUpdate = timeStamps.filter(t => t && t !== 'null' && t !== 'undefined').sort((a, b) => new Date(b).getTime() - new Date(a).getTime())[0];
    return latestUpdate ? this.formatDateTime(latestUpdate) : '未設定';
  }
  // 通用：格式化受理號碼
  formatAcceptanceNumber(): void {
    if (typeof this.acceptanceNumber !== 'string') { this.acceptanceNumber = ''; return; }
    let numericValue = this.acceptanceNumber.replace(/\D/g, '').substring(0, 10);
    this.acceptanceNumber = numericValue.length >= 2 ? `${numericValue.substring(0, 2)}-${numericValue.substring(2)}` : numericValue;
  }
  // 通用：modal 控制
  openConfirmModal(type: 'acceptance' | 'payment'): void { this.confirmType = type; this.showConfirmModal = true; }
  closeConfirmModal(): void { this.showConfirmModal = false; this.confirmType = ''; }
  handleConfirmModalVisibility(visible: boolean): void { this.showConfirmModal = visible; if (!visible) this.confirmType = ''; }
  getConfirmModalTitle(): string { return this.confirmType === 'acceptance' ? '確認提交受理號碼' : this.confirmType === 'payment' ? '確認提交匯款資料' : '確認提交'; }
  confirmSubmit(): void {
    if (this.confirmType === 'acceptance') this.updateAcceptanceNumber();
    else if (this.confirmType === 'payment') this.submitPaymentInfo();
    this.closeConfirmModal();
  }
  // 通用：API
  updateAcceptanceNumber(): void {
    if (!this.acceptanceNumber.trim()) return;
    this.isLoading = true;
    this.ProjectService.updateAcceptanceNumber(this.eventId, this.acceptanceNumber).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.success !== false) this.get_events_detail(this.eventId);
        else this.showError(res.message || '更新受理號碼失敗');
      },
      error: () => { this.isLoading = false; this.showError('更新受理號碼失敗'); }
    });
  }
  submitPaymentInfo(): void {
    if (!this.paymentDate || !this.paymentAmount) return this.showError('請填寫完整的匯款資訊');
    this.isLoading = true;
    this.ProjectService.updatePaymentInfo(this.eventId, this.paymentDate, this.paymentAmount).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res) this.get_events_detail(this.eventId);
        else this.showError('更新匯款資訊失敗');
      },
      error: () => { this.isLoading = false; this.showError('更新匯款資訊失敗，請稍後再試'); }
    });
  }
  // 載入事件配置
  loadEventConfig(): void {
    this.currentEventConfig = event_config_list.find(config => config.title === this.eventType);
    this.stages = this.currentEventConfig?.stages ? Object.entries(this.currentEventConfig.stages).map(([key, value]) => ({ key, ...value as object })) : [];
  }
}
