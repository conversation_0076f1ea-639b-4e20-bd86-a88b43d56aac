import { UserService } from './../../../../service/user.service';
import { Component, computed, inject,NgModule , input,viewChild } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AuthService } from '../../../../service/auth.service'
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { environment } from './../../../../../environments/environment'
import { MailboxService } from './../../../../service/mailbox.service';
import { ModalService,ModalToggleDirective} from '@coreui/angular';
import { OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import {
  AvatarComponent,
  ColorModeService,
  ContainerComponent,
  DropdownComponent,
  DropdownItemDirective,
  DropdownMenuDirective,
  DropdownToggleDirective,
  HeaderComponent,
  HeaderNavComponent,
  HeaderTogglerDirective,
  NavItemComponent,
  NavLinkDirective,
  SidebarToggleDirective,
  TextColorDirective,FormLabelDirective, FormCheckInputDirective,
  ThemeDirective,InputGroupTextDirective,
  ButtonCloseDirective, ButtonDirective,
  CardBodyComponent, CardComponent,
  CardHeaderComponent, ColComponent, TableModule,
  GridModule, ModalBodyComponent, ModalComponent,
  ModalFooterComponent, ModalHeaderComponent,CollapseDirective,NavModule,FormCheckComponent, InputGroupComponent,ModalTitleDirective, RowComponent, FormControlDirective, FormSelectDirective
} from '@coreui/angular';
import { IconDirective } from '@coreui/icons-angular';
import { data } from 'jquery';
interface User {
  firstname: string;
  lastname: string;
  phone: string;
  email: string;
  profile_picture?: string;
  notification_method: string[];
  role: string[];
}

@Component({
  selector: 'app-enterprise-header',
  templateUrl: './enterprise-header.component.html',
  styleUrls: ['./enterprise-header.component.scss'],
  standalone: true,
  imports: [ButtonDirective, FormsModule, CommonModule, ReactiveFormsModule, CardBodyComponent,
      CardComponent,FormCheckComponent,
      CardHeaderComponent,TableModule,
    ColComponent, GridModule, NavModule, ContainerComponent, HeaderComponent, HeaderNavComponent, HeaderTogglerDirective, NavItemComponent, NavLinkDirective, SidebarToggleDirective, IconDirective, AvatarComponent,
  InputGroupComponent, RouterLink, RowComponent,
  TextColorDirective, InputGroupTextDirective, FormControlDirective,
  FormLabelDirective, FormCheckInputDirective, ThemeDirective,
  DropdownComponent, DropdownToggleDirective, DropdownMenuDirective,
  DropdownItemDirective, FormSelectDirective, ModalBodyComponent, ModalComponent,
  ModalFooterComponent, ModalHeaderComponent, ModalTitleDirective, ButtonCloseDirective, CollapseDirective]
})
export class EnterpriseHeaderComponent extends HeaderComponent implements  OnInit, OnDestroy  {
  public profile_picture: string = '';
  public userRoles: string[] = [];
  isMailboxOpen = false;
  hasUnreadMail: boolean = false;
  isSettingOpen = false;
  isSettingModalVisible: boolean = false;
  previewImage: string | null = null;
  showConfirmDialog = false;
  originalFormValue: any;
  selectedFile: File | null = null;
  formData: FormGroup;
  production = environment.production;
  apiBaseUrl = environment.apiBaseUrl;
  filetooLarge = false;
  filenotJPG = false;
  visible = false;
  private mailSubscription!: Subscription;
  user: User = {
    firstname: '',
    lastname: '',
    phone: '',
    email: '',
    profile_picture: undefined,
    notification_method: [],
    role: []
  };

  readonly #colorModeService = inject(ColorModeService);
  readonly colorMode = this.#colorModeService.colorMode;

  readonly colorModes = [
    { name: 'light', text: 'Light', icon: 'cilSun' },
    { name: 'dark', text: 'Dark', icon: 'cilMoon' },
    { name: 'auto', text: 'Auto', icon: 'cilContrast' }
  ];

  readonly icons = computed(() => {
    const currentMode = this.colorMode();
    return this.colorModes.find(mode => mode.name === currentMode)?.icon ?? 'cilSun';
  });
  constructor(private authService: AuthService,  private router: Router, private userService: UserService,private fb: FormBuilder,private MailboxService:MailboxService,private modalService: ModalService) {
    super();
    this.get_self_info();
    this.getUserAvatar();
    this.getUserRoles();
    this.checkUnreadMail();
  }
  ngOnInit(): void {
    this.mailSubscription = this.MailboxService.unreadMail$.subscribe((status) => {
      this.hasUnreadMail = status;
    });
  }

  ngOnDestroy(): void {
    if (this.mailSubscription) {
      this.mailSubscription.unsubscribe();
    }
  }
  get_self_info() {
    this.userService.get_self_info().subscribe(
      (data) => {
        this.user = data.user;
        localStorage.setItem('token', data.token);
      },
      (error) => {

      }
    );
  }
  getUserRoles() {
    this.userService.getUserRoles().subscribe(
      (data) => {
        this.userRoles = data.roles;
        localStorage.setItem('token', data.token);
      },
      (error) => {
        this.userRoles = [];
      }
    );
  }

  onlogout() {
    this.router.navigate(['/login']);
    localStorage.clear();
    this.authService.logout();
  }

  sidebarId = input('sidebar1');
  checkUnreadMail() {
    this.MailboxService.getUnreadMailCount().subscribe(response => {
      this.hasUnreadMail = response.unread_count > 0;
    });
  }
  OpenMailbox() {
    this.isMailboxOpen = true;
    this.checkUnreadMail();
    this.router.navigate(['/mailbox']);
  }
  getUserAvatar(){
    this.userService.getUserAvatar().subscribe(
      (blob: Blob) => {
        if (blob) {
          const objectUrl = URL.createObjectURL(blob);
          this.profile_picture = objectUrl;
        }
      },
      (error) => {
        console.error('載入頭像失敗:', error);
      }
    );
  }

}


