import { Component } from '@angular/core';
import { Ng<PERSON>tyle } from '@angular/common';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { AuthService } from '../../../service/auth.service';
import { IconDirective } from '@coreui/icons-angular';
import { ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, FormControlDirective, ButtonDirective } from '@coreui/angular';
import { CommonModule } from '@angular/common';

@Component({
  templateUrl: './forget-pwd.component.html',
  styleUrl: './forget-pwd.component.scss',
  standalone: true,
  imports: [ReactiveFormsModule, ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, IconDirective, FormControlDirective, ButtonDirective, NgStyle,
    CommonModule
  ]
})
export class ForgetPwdComponent {
  forgetForm: FormGroup;
  errorMessage: String = '';
  successMessage: String = '';
  constructor(private fb: FormBuilder, private authService: AuthService, private router: Router) {
    this.forgetForm = this.fb.group({
      email: ['']
    });
  }
  onSubmit() {
    if (this.forgetForm.valid) {
      const formData = this.forgetForm.value;
      this.authService.forgotPassword(formData).subscribe(
        response => {
          this.successMessage = '已發送信件，請至信箱查看，即將跳轉回登入頁面......';
          this.errorMessage = '';
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 3000);
        },
        error => {
          this.successMessage = '';
          this.errorMessage = '用戶不存在';
        }
      );
    }
  }
}
