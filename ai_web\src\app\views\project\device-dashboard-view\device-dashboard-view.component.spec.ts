import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeviceDashboardViewComponent } from './device-dashboard-view.component';

describe('DeviceDashboardViewComponent', () => {
  let component: DeviceDashboardViewComponent;
  let fixture: ComponentFixture<DeviceDashboardViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DeviceDashboardViewComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DeviceDashboardViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
