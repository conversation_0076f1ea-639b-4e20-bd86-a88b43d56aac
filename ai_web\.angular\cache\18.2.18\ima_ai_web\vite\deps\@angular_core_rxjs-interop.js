import {
  outputFromObservable,
  outputToObservable,
  takeUntilDestroyed,
  toObservable,
  toSignal
} from "./chunk-2JH4JFPZ.js";
import "./chunk-UWUQMFUU.js";
import "./chunk-VMI3K6GE.js";
import "./chunk-5KXDAEEK.js";
import "./chunk-WD6C567C.js";
import "./chunk-HM5YLMWO.js";
import "./chunk-DZYXDVEG.js";
export {
  outputFromObservable,
  outputToObservable,
  takeUntilDestroyed,
  toObservable,
  toSignal
};
//# sourceMappingURL=@angular_core_rxjs-interop.js.map
