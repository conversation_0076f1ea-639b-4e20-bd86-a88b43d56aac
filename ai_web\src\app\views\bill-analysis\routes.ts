import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'bill-analysis'
    },
    children: [
      {
        path: 'submit-crawler-request',
        loadComponent: () => import('./tpccrawler/tpccrawler.component').then(m => m.TPCcrawlerComponent),
        data: {
          title: 'submit-crawler-request'
        }
      },
      {       
        path: 'bill-analysis-overview',
        loadComponent: () => import('./bill-analysis-overview/bill-analysis-overview.component').then(m => m.BillAnalysisViewComponent),
        data: {
          title: 'bill-analysis-overview'}
        },
        {
          path: ':place_id',
          loadComponent: () => import('./bill-analysis-view/bill-analysis-view.component').then(m => m.BillAnalysisViewComponent),
          data: {
            title: 'bill-analysis-view'
          }
        },
    ]
  }
];


