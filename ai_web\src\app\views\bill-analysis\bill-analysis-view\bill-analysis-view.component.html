    <c-row class="mb-4">
        <label [sm]="2" cCol cLabel="col" for="target_month">
        選擇欲觀察月份
        </label>
        <c-col [sm]="10">
        <select  aria-label="target_month"  [(ngModel)]="target_month" (change)="onMonthSelected()"cSelect>
            <option *ngFor="let month of billMonths" [value]="month">
                {{ month }}
              </option>
        </select>
        </c-col>
    </c-row>
    <ng-container *ngIf="selectedBill">
      <c-row class="g-4 px-3">
        <ng-container *ngFor="let key of objectKeys(selectedBill)">
          <ng-container *ngIf="selectedBill[key] !== null">
            <c-col xs="12" sm="6" md="4" lg="3">
              <c-card class="h-100 shadow-sm border-0">
                <c-card-body class="text-center">
                  <h6 class="fw-bold mb-1" style="font-size: 1.1rem;">{{ key }}</h6>
                  <p class="text-muted mb-2" style="font-size: 1.3rem;">{{ selectedBill[key] }}</p>
                </c-card-body>
              </c-card>
            </c-col>
          </ng-container>
        </ng-container>
      </c-row>
    </ng-container>
