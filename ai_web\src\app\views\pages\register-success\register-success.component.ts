import { Component } from '@angular/core';
import { ContainerComponent, RowComponent, ColComponent, ButtonDirective } from '@coreui/angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-register-success',
  standalone: true,
  imports: [ContainerComponent, RowComponent, ColComponent, ButtonDirective],
  templateUrl: './register-success.component.html',
  styleUrl: './register-success.component.scss'
})
export class RegisterSuccessComponent {
  constructor(private router: Router) {
  }
  direct_to_login(){
    this.router.navigate(['/login'])
  }
}
