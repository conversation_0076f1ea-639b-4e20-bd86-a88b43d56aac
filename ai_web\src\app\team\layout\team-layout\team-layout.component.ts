import { SharedService } from './../../../service/shared.service';
import { TeamService } from '../../../service/team.service';
import { ProjectService } from '../../../service/project.service';
import { UserService } from '../../../service/user.service';
import { Component, inject } from '@angular/core';
import { SharedModule, team } from "../../../shared/shared.module"
import { RouterLink, RouterOutlet, ActivatedRoute, Router } from '@angular/router';
import { NgScrollbar } from 'ngx-scrollbar';
import { CommonModule } from '@angular/common';
import { ProgressComponent } from '@coreui/angular';
import { OnInit } from '@angular/core';
import {
  ContainerComponent,
  ColorModeService,
  ShadowOnScrollDirective,
  SidebarBrandComponent,
  SidebarComponent,
  SidebarHeaderComponent,
  SidebarNavComponent
} from '@coreui/angular';
import { TeamFooterComponent, TeamHeaderComponent } from '.';
import { navItems } from './_nav';

@Component({
  selector: 'app-dashboard',
  templateUrl: './team-layout.component.html',
  styleUrls: ['./team-layout.component.scss'],
  standalone: true,
  imports: [
    SidebarComponent,
    SidebarHeaderComponent,
    SidebarBrandComponent,
    RouterLink,
    NgScrollbar,
    SidebarNavComponent,
    TeamHeaderComponent,
    ShadowOnScrollDirective,
    ContainerComponent,
    RouterOutlet,
    TeamFooterComponent,
    CommonModule,
    ProgressComponent,
    SharedModule
  ]
})
export class TeamLayoutComponent implements OnInit {
  public navItems : any;
  public userRoles: string[] = [];
  team: team = {
    taxId: '',
    team_code: '',
    team_name: '',
    accounts: []
  };
  team_code: string = '';
  readonly #colorModeService = inject(ColorModeService);
  readonly colorMode = this.#colorModeService.colorMode;
  constructor(private userService: UserService, private ProjectService: ProjectService, private route: ActivatedRoute,
    private router: Router, private TeamService: TeamService, private sharedService: SharedService
  ) {
    this.team_code = this.sharedService.getRouteId('team_code');
  }
  ngOnInit() {
    this.TeamService.get_team_info(this.team_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.team = data.data;
        this.TeamService.get_user_team_roles(this.team_code).subscribe(
          (data) => {
            this.userRoles = data.roles;
            localStorage.setItem('token', data.token);
            this.filterNavItems();
          },
          (error) => {
            this.userRoles = [];
          }
        );
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }


  filterNavItems() {
    this.navItems = navItems
      .filter(item => this.isRoleAllowed(item))  // 先過濾主項目
      .map(item => {
        if (item.children) {
          // 過濾每個 item 的 children
          item.children = item.children.filter(child => this.isRoleAllowed(child));
        }
        if (item.url?.includes(':team_code') || false) {
          return {
            ...item,
            url: item.url.replace(':team_code', this.team_code)
          };
        }

        return item;
      });
  }

  isRoleAllowed(item: any): boolean {
    if (!item.roles || !Array.isArray(item.roles)) {
      return false;
    }
    // 判斷主項目或子項目的角色是否包含當前用戶的角色
    const allowed = item.roles.some((role: string) => this.userRoles.includes(role));
    return allowed;
  }


  onScrollbarUpdate($event: any) {
    // 可以根據需要處理滾動條事件
  }
}

