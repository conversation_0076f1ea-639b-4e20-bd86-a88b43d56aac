from flask import request, Blueprint, jsonify, current_app, send_from_directory
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo, redis_client, send_mail
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required
from blueprints.mqtt_worker import send_mqtt_message_without_response, send_mqtt_message_with_response
from bson.objectid import ObjectId
from flask_redis import FlaskRedis
import time, re, os, json, ast
from packaging import version
from config import Config

home_page = Blueprint('home', __name__)

@home_page.route('/get_home_statistics', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def get_home_statistics(token):
    project_number = mongo.db.project.count_documents({})
    gateway_number = mongo.db.gateway.count_documents({})
    
    ret = {
        "project_number": project_number,
        
    }
    

    return jsonify({
        "token": get_refresh_token(token),
        "data": ret
        }), 200
