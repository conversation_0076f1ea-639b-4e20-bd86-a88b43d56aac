import {
  init_joint,
  joint_exports
} from "./chunk-YXSYGMJK.js";
import {
  __commonJS,
  __toCommonJS
} from "./chunk-DZYXDVEG.js";

// node_modules/@joint/shapes-vsm/dist/joint-vsm-shapes.js
var require_joint_vsm_shapes = __commonJS({
  "node_modules/@joint/shapes-vsm/dist/joint-vsm-shapes.js"(exports, module) {
    ((e, t) => {
      "object" == typeof exports && "undefined" != typeof module ? t(exports, (init_joint(), __toCommonJS(joint_exports))) : "function" == typeof define && define.amd ? define(["exports", "@joint/core"], t) : t(((e = "undefined" != typeof globalThis ? globalThis : e || self).joint = e.joint || {}, e.joint.shapes = e.joint.shapes || {}), e.joint);
    })(exports, function(e, r) {
      let t = "#222138", a = "#ffffff", i = "#c6c7e2", l = 10;
      class s extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMCustomerSupplier",
            size: {
              width: 120,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                d: "M 0 30 V calc(h) h calc(w) v -calc(h) l -calc(0.33 * w) 30 v -30 l -calc(0.33 * w) 30 v -30 z"
              },
              label: {
                text: "Customer Supplier",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  height: -40,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h + 15)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class c extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMCustomerSupplier",
            size: {
              width: 120,
              height: 80
            },
            thickness: 15,
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a
              },
              label: {
                text: "Workcell",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                textWrap: {
                  width: -20
                },
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            this.hasChanged("thickness") && this.resetThickness(t2);
          }), this.resetThickness();
        }
        resetThickness(e2) {
          var t2 = this.get("thickness") || 0;
          this.attr(["body", "d"], `M 0 0 H calc(w) V calc(h) h ${-t2} V ${t2} H ${t2} V calc(h) H 0 z`, e2);
        }
      }
      class n extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMTriangleInventory",
            size: {
              width: 80,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                d: "M calc(0.5*w) 0 calc(w) calc(h) H 0 Z"
              },
              icon: {
                stroke: t,
                fill: i,
                strokeWidth: 2,
                d: "M calc(0.5*w-2) calc(0.4*h) V calc(0.8*h) h 4 V calc(0.4*h) Z"
              },
              label: {
                text: "Triangle Inventory",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5*w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "path",
            selector: "icon"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class o extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMRoundedInventory",
            size: {
              width: 80,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                x: 0,
                y: 0,
                d: "M 0 calc(h) C 0 calc(0.5 * h) 0 0 calc(0.5*w) 0 C calc(w) 0 calc(w) calc(0.5 * h) calc(w) calc(h) Z"
              },
              icon: {
                stroke: t,
                fill: i,
                strokeWidth: 2,
                d: "M calc(0.5*w-2) calc(0.4*h) V calc(0.8*h) h 4 V calc(0.4*h) Z"
              },
              label: {
                text: "Rounded Inventory",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "path",
            selector: "icon"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class h extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMKaizenBurst",
            size: {
              width: 120,
              height: 120
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                refD: "M 70 104 60 89 46 106 39 86 3 106 25 78 0 72 20 54 3 39 31 42 27 22 43 32 48 4 59 37 74 10 78 39 101 20 94 48 119 47 99 62 116 75 93 80 101 95 79 91 81 120 Z"
              },
              label: {
                text: "Kaizen Burst",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: "50%",
                  height: "50%",
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class d extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMOperator",
            size: {
              width: 40,
              height: 40
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                rx: "calc(0.5 * w)",
                ry: "calc(0.5 * h)",
                cx: "calc(0.5 * w)",
                cy: "calc(0.5 * h)"
              },
              cap: {
                fill: "none",
                stroke: t,
                strokeWidth: 4,
                strokeLinecap: "round",
                d: "M -5 calc(0.5 * h) A calc(0.5 * w + 5) calc(0.5 * h + 5) 0 1 1 calc(w+5) calc(0.5 * h)"
              },
              label: {
                text: "Operator",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "ellipse",
            selector: "body"
          }, {
            tagName: "path",
            selector: "cap",
            attributes: {
              fill: "none"
            }
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class p extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMMaterialPull",
            size: {
              width: 60,
              height: 60
            },
            attrs: {
              body: {
                stroke: "none",
                fill: "transparent",
                rx: "calc(0.5 * w)",
                ry: "calc(0.5 * h)",
                cx: "calc(0.5 * w)",
                cy: "calc(0.5 * h)"
              },
              arrow: {
                fill: "none",
                stroke: t,
                strokeWidth: 4,
                d: "M calc(w) calc(0.5*h) A calc(0.5*w) calc(0.5*h) 0 1 0 calc(0.5*w) calc(h)",
                targetMarker: {
                  type: "path",
                  stroke: t,
                  "stroke-width": 2,
                  d: "M 0 -6 -10 0 0 6 Z"
                }
              },
              label: {
                text: "Material Pull",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "ellipse",
            selector: "body"
          }, {
            tagName: "path",
            selector: "arrow",
            attributes: {
              fill: "none"
            }
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class u extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMFIFOLane",
            size: {
              width: 120,
              height: 60
            },
            attrs: {
              body: {
                fill: a,
                width: "calc(w)",
                height: "calc(h)"
              },
              outline: {
                strokeWidth: 2,
                stroke: t,
                d: "M 0 0 H calc(w) M calc(w) calc(h) H 0"
              },
              rectIcon: {
                x: "calc(0.15*w - calc(0.15 * s))",
                y: "calc(0.5*h - calc(0.15 * s))",
                width: "calc(0.3 * s)",
                height: "calc(0.3 * s)"
              },
              ellipseIcon: {
                cx: "calc(0.85 * w)",
                cy: "calc(0.5 * h)",
                rx: "calc(0.15 * s)",
                ry: "calc(0.15 * s)"
              },
              triangleIcon: {
                d: "M calc(0.5*w) calc(0.5*h - calc(0.15 * s)) l -calc(0.15 * s) calc(0.3 * s) h calc(0.3 * s) z"
              },
              icons: {
                stroke: t,
                fill: i,
                strokeWidth: 2
              },
              label: {
                text: "FIFO Line",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5*w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "path",
            selector: "outline"
          }, {
            tagName: "rect",
            selector: "rectIcon",
            groupSelector: "icons"
          }, {
            tagName: "path",
            selector: "triangleIcon",
            groupSelector: "icons"
          }, {
            tagName: "ellipse",
            selector: "ellipseIcon",
            groupSelector: "icons"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class g extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMKanbanPost",
            size: {
              width: 80,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 4,
                stroke: t,
                strokeLinecap: "round",
                fill: "transparent",
                d: "M 0 0 V calc(0.6 * h) H calc(w) V 0 M calc(0.5*w) calc(0.6 * h) V calc(h) M calc(0.25*w) calc(h) H calc(0.75*w)"
              },
              label: {
                text: "Kanban Post",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class f extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSequencePullBall",
            size: {
              width: 60,
              height: 60
            },
            attrs: {
              inner: {
                strokeWidth: 2,
                stroke: t,
                fill: i,
                rx: "calc(0.3 * w)",
                ry: "calc(0.3 * h)",
                cx: "calc(0.5 * w)",
                cy: "calc(0.5 * h)"
              },
              outer: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                rx: "calc(0.5 * w)",
                ry: "calc(0.5 * h)",
                cx: "calc(0.5 * w)",
                cy: "calc(0.5 * h)"
              },
              label: {
                text: "Sequence Pull Ball",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "ellipse",
            selector: "outer"
          }, {
            tagName: "ellipse",
            selector: "inner"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class m extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMLoadLevelling",
            size: {
              width: 120,
              height: 60
            },
            attrs: {
              body: {
                fill: a,
                width: "calc(w)",
                height: "calc(h)"
              },
              outline: {
                strokeWidth: 2,
                stroke: t,
                d: "M 0 0 H calc(w) M calc(w) calc(h) H 0"
              },
              circle1Icon: {
                cx: "calc(0.375 * w)",
                cy: "calc(0.5 * h)",
                r: "calc(0.1 * s)"
              },
              circle2Icon: {
                cx: "calc(0.9 * w)",
                cy: "calc(0.5 * h)",
                r: "calc(0.1 * s)"
              },
              cross1Icon: {
                transform: "translate(calc(0.1*w),calc(.5*h))",
                d: "M -calc(.1*s) -calc(.1*s) L calc(.1*s) calc(.1*s) M -calc(.1*s) calc(.1*s) L calc(.1*s) -calc(.1*s)"
              },
              cross2Icon: {
                transform: "translate(calc(0.625*w),calc(.5*h))",
                d: "M -calc(.1*s) -calc(.1*s) L calc(.1*s) calc(.1*s) M -calc(.1*s) calc(.1*s) L calc(.1*s) -calc(.1*s)"
              },
              icons: {
                stroke: t,
                fill: "none",
                strokeWidth: 2
              },
              label: {
                text: "Load Levelling",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "path",
            selector: "outline"
          }, {
            tagName: "path",
            selector: "cross1Icon",
            groupSelector: "icons"
          }, {
            tagName: "path",
            selector: "cross2Icon",
            groupSelector: "icons"
          }, {
            tagName: "circle",
            selector: "circle1Icon",
            groupSelector: "icons"
          }, {
            tagName: "circle",
            selector: "circle2Icon",
            groupSelector: "icons"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class b extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSignalKanban",
            size: {
              width: 80,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                x: 0,
                y: 0,
                d: "M 0 0 H calc(w) L calc(0.5*w) calc(h) Z"
              },
              icon: {
                stroke: t,
                strokeWidth: 2,
                fill: i,
                x: "calc(0.5 * w)",
                y: "calc(0.4 * h)",
                textAnchor: "middle",
                textVerticalAnchor: "middle",
                text: "S",
                fontSize: 36,
                fontWeight: "bold",
                fontFamily: "sans-serif"
              },
              label: {
                text: "Signal Kanban",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "icon"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class x extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMProductionKanban",
            size: {
              width: 120,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                x: 0,
                y: 0,
                d: "M 0 0 H calc(w - 20) l 20 20 V calc(h) H 0 Z"
              },
              label: {
                text: "Production Kanban",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  height: -20,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class k extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMProductionKanban",
            size: {
              width: 120,
              height: 80
            },
            attrs: {
              bodies: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                x: 0,
                y: 0,
                d: "M 0 0 H calc(w - 20) l 20 20 V calc(h) H 0 Z"
              },
              bodyMiddle: {
                transform: "translate(4, -4)"
              },
              bodyBottom: {
                transform: "translate(8, -8)"
              },
              label: {
                text: "Production Batch Kanban",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  height: -20,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "bodyBottom",
            groupSelector: "bodies"
          }, {
            tagName: "path",
            selector: "bodyMiddle",
            groupSelector: "bodies"
          }, {
            tagName: "path",
            selector: "body",
            groupSelector: "bodies"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      function y(e2, t2) {
        var a2 = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : 20, i2 = a2 / 2;
        return {
          type: "pattern",
          attrs: {
            width: a2,
            height: a2,
            stroke: e2,
            fill: t2,
            "stroke-width": 2
          },
          markup: [{
            tagName: "rect",
            attributes: {
              width: a2,
              height: a2,
              stroke: "none"
            }
          }, {
            tagName: "path",
            attributes: {
              fill: "none",
              d: `M 0 ${2 * i2} L ${2 * i2} 0 M ${i2} ${3 * i2} L ${3 * i2} ${i2} M -${i2} ${i2} L ${i2} -` + i2
            }
          }]
        };
      }
      class w extends x {
        defaults() {
          var e2 = super.defaults();
          return Object.assign(Object.assign({}, e2), {
            type: "VSMMaterialKanban",
            attrs: r.util.defaultsDeep({
              body: {
                fill: y(i, a)
              },
              label: {
                text: "Material Kanban"
              }
            }, e2.attrs)
          });
        }
      }
      class S extends k {
        defaults() {
          var e2 = super.defaults();
          return Object.assign(Object.assign({}, e2), {
            type: "VSMMaterialBatchKanban",
            attrs: r.util.defaultsDeep({
              body: {
                fill: y(i, a)
              },
              label: {
                text: "Material Batch Kanban"
              }
            }, e2.attrs)
          });
        }
      }
      class M extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSupermarketParts",
            size: {
              width: 120,
              height: 80
            },
            attrs: {
              body: {
                width: "calc(w)",
                height: "calc(h)",
                strokeWidth: 2,
                stroke: t,
                fill: y(i, a)
              },
              label: {
                text: "Supermarket Parts",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -10,
                  height: -10,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class V extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMProductionControl",
            size: {
              width: 120,
              height: 80
            },
            attrs: {
              body: {
                width: "calc(w)",
                height: "calc(h)",
                strokeWidth: 2,
                stroke: t,
                fill: a
              },
              label: {
                text: "Production Control",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  height: -20,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class z extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSupermarket",
            size: {
              width: 80,
              height: 80
            },
            count: 2,
            attrs: {
              body: {
                strokeWidth: 4,
                stroke: t,
                strokeLinecap: "round",
                fill: "transparent"
              },
              label: {
                text: "Supermarket",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            this.hasChanged("count") && this.resetCount(t2);
          }), this.resetCount();
        }
        resetCount(e2) {
          var t2 = this.get("count") || 0;
          let a2 = "M 0 0 H calc(w) V calc(h) H 0";
          var i2 = 1 / (t2 + 1);
          let l2 = i2;
          for (let e3 = 0; e3 < t2; e3++) a2 += ` M calc(w) calc(${l2}*h) H 0`, l2 += i2;
          this.attr(["body", "d"], a2, e2);
        }
      }
      class j extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSafetyStock",
            size: {
              width: 40,
              height: 80
            },
            count: 2,
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a
              },
              label: {
                text: "Safety Stock",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            this.hasChanged("count") && this.resetCount(t2);
          }), this.resetCount();
        }
        resetCount(e2) {
          var t2 = this.get("count") || 0, a2 = 1 / (t2 + 1);
          let i2 = "M 0 0 H calc(w) V calc(h) H 0 Z", l2 = a2;
          for (let e3 = 0; e3 < t2; e3++) i2 += ` M calc(w) calc(${l2}*h) H 0`, l2 += a2;
          this.attr(["body", "d"], i2, e2);
        }
      }
      class L extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMGoSee",
            size: {
              width: 120,
              height: 40
            },
            attrs: {
              body: {
                strokeWidth: 4,
                stroke: t,
                strokeLinecap: "round",
                fill: i,
                refD: "M 64 200 C 64 296 80 328 144 328 C 208 328 224 296 224 200 C 224 200 208 184 144 184 C 80 184 64 200 64 200 Z M 448 200 C 448 296 432 328 368 328 C 304 328 288 296 288 200 C 288 200 304 184 368 184 C 432 184 448 200 448 200 Z M 448 200 L 464 200 M 64 200 L 48 200 M 224 232 C 224 207 251 192 272 204 C 282 210 288 221 288 232 C 288 207 261 192 240 204 C 230 210 224 221 224 232"
              },
              label: {
                text: "Go see",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class N extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMTimelineWaiting",
            size: {
              width: 120,
              height: 40
            },
            attrs: {
              line: {
                strokeWidth: 3,
                stroke: t,
                strokeLinecap: "round",
                fill: "transparent",
                d: "M 0 0 H calc(w) V calc(h)"
              },
              label: {
                text: "Timeline Waiting",
                textVerticalAnchor: "bottom",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  maxLineCount: 2,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: -l,
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "line"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class O extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMTimelineProcessing",
            size: {
              width: 120,
              height: 40
            },
            attrs: {
              line: {
                strokeWidth: 3,
                stroke: t,
                strokeLinecap: "round",
                fill: "transparent",
                d: "M 0 calc(h) H calc(w) V 0"
              },
              label: {
                text: "Timeline Processing",
                textVerticalAnchor: "bottom",
                textAnchor: "middle",
                textWrap: {
                  width: -10,
                  maxLineCount: 2,
                  ellipsis: true
                },
                x: "calc(0.5*w)",
                y: "calc(h-10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "line"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class W extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMTimelineTotal",
            size: {
              width: 120,
              height: 80
            },
            length: 80,
            attrs: {
              body: {
                fill: a,
                stroke: t,
                strokeWidth: 2,
                width: "calc(w)",
                height: "calc(h)"
              },
              line: {
                strokeWidth: 3,
                stroke: t,
                strokeLinecap: "round",
                fill: "transparent",
                d: `M -${length} calc(0.5 * h) H calc(w)`
              },
              label: {
                text: "Timeline Processing",
                textVerticalAnchor: "bottom",
                textAnchor: "middle",
                textWrap: {
                  height: null
                },
                y: "calc(0.5*h-10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              },
              labelTotalWaiting: {
                text: "Total Waiting",
                textVerticalAnchor: "bottom",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  height: "calc(h/2-20)",
                  ellipsis: true
                },
                x: "calc(0.5*w)",
                y: "calc(0.5*h-10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              },
              labelTotalProcessing: {
                text: "Total Processing",
                textVerticalAnchor: "bottom",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  height: "calc(h/2-20)",
                  ellipsis: true
                },
                x: "calc(0.5*w)",
                y: "calc(h - 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "path",
            selector: "line"
          }, {
            tagName: "text",
            selector: "label"
          }, {
            tagName: "text",
            selector: "labelTotalWaiting"
          }, {
            tagName: "text",
            selector: "labelTotalProcessing"
          }];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            this.hasChanged("length") && this.resetLength(t2);
          }), this.resetLength();
        }
        resetLength(e2) {
          var t2 = this.get("length") || 0;
          this.attr({
            line: {
              d: `M -${t2} calc(0.5 * h) H calc(w)`
            },
            label: {
              x: -t2 / 2,
              textWrap: {
                width: t2 - 10
              }
            }
          }, e2);
        }
      }
      class C extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMResourcePlanning",
            size: {
              width: 80,
              height: 80
            },
            tilt: 10,
            attrs: {
              body: {
                fill: a,
                stroke: t,
                strokeWidth: 2
              },
              top: {
                cx: "calc(0.5*w)",
                rx: "calc(0.5*w)",
                fill: a,
                stroke: t,
                strokeWidth: 2
              },
              label: {
                text: "MRP/ERP",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5*w)",
                y: "calc(h+10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "body"
          }, {
            tagName: "ellipse",
            selector: "top"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            (this.hasChanged("tilt") || this.hasChanged("size")) && this.resetTilt(t2);
          }), this.resetTilt();
        }
        resetTilt(e2) {
          var t2 = this.get("tilt") || 0;
          return this.attr({
            body: {
              d: this.getLateralAreaPathData(t2)
            },
            top: {
              cy: t2,
              ry: t2
            }
          }, e2);
        }
        getLateralAreaPathData(e2) {
          var {
            width: t2,
            height: a2
          } = this.size();
          let i2 = 0.551784 * (t2 / 2), l2 = 0.551784 * e2, s2 = 0;
          let r2 = s2, c2 = s2 + t2 / 2, n2 = s2 + t2;
          var t2 = 0 + e2, o2 = t2 - e2;
          let h2 = 0 + a2 - e2, d2 = 0 + a2;
          e2 = (e3) => ["M", r2, h2 - e3, "C", s2, h2 + l2 - e3, c2 - i2, d2 - e3, c2, d2 - e3, "C", c2 + i2, d2 - e3, n2, h2 + l2 - e3, n2, h2 - e3];
          return [...e2(0), "L", n2, t2, "C", n2, t2 - l2, c2 + i2, o2, c2, o2, "C", c2 - i2, o2, r2, t2 - l2, r2, t2, "Z", ...e2(5), ...e2(10)].join(" ");
        }
      }
      class A extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMDedicatedProcess",
            size: {
              width: 120,
              height: 120
            },
            attrs: {
              body: {
                width: "calc(w)",
                height: "calc(h)",
                stroke: t,
                strokeWidth: 2,
                fill: a
              },
              header: {
                width: "calc(w)",
                height: 30,
                stroke: t,
                strokeWidth: 2,
                fill: a
              },
              label: {
                text: "Dedicated Process",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  maxLineCount: 2,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: 15,
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "rect",
            selector: "header"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class P extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSharedProcess",
            size: {
              width: 120,
              height: 120
            },
            attrs: {
              body: {
                width: "calc(w)",
                height: "calc(h)",
                stroke: t,
                strokeWidth: 2,
                fill: y(i, a)
              },
              header: {
                width: "calc(w)",
                height: 30,
                stroke: t,
                strokeWidth: 2,
                fill: a
              },
              label: {
                text: "Shared Process",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  width: -20,
                  maxLineCount: 2,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: 15,
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "rect",
            selector: "header"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class F extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMSubprocess",
            size: {
              width: 120,
              height: 80
            },
            thickness: 10,
            attrs: {
              body: {
                width: "calc(w)",
                height: "calc(h)",
                stroke: t,
                strokeWidth: 2,
                fill: a
              },
              stripes: {
                stroke: t,
                strokeWidth: 2,
                fill: i
              },
              label: {
                text: "Subprocess",
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                textWrap: {
                  height: -20,
                  ellipsis: true
                },
                x: "calc(0.5 * w)",
                y: "calc(0.5 * h)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "rect",
            selector: "body"
          }, {
            tagName: "path",
            selector: "stripes"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            this.hasChanged("thickness") && this.resetThickness(t2);
          }), this.resetThickness();
        }
        resetThickness(e2) {
          var t2 = this.get("thickness") || 0;
          this.attr({
            stripes: {
              d: `M 0 0 V calc(h) h ${t2} V 0 Z M calc(w) 0 V calc(h) h -${t2} V 0 Z`
            },
            label: {
              textWrap: {
                width: 2 * -(l + t2)
              }
            }
          }, e2);
        }
      }
      class T extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMDataBox",
            count: 3,
            size: {
              width: 120,
              height: 120
            },
            attrs: {
              body: {
                width: "calc(w)",
                height: "calc(h)"
              },
              boxes: {
                width: "calc(w)",
                fill: a,
                stroke: t,
                strokeWidth: 2
              },
              labels: {
                text: "",
                x: "calc(0.5 * w)",
                textWrap: {
                  width: -20,
                  ellipsis: true
                },
                textVerticalAnchor: "middle",
                textAnchor: "middle",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [];
        }
        initialize() {
          super.initialize(...arguments), this.on("change", (e2, t2) => {
            this.hasChanged("count") && this.buildMarkup(t2);
          }), this.buildMarkup();
        }
        toJSON() {
          var e2 = super.toJSON();
          return delete e2.markup, e2;
        }
        getCleanedAttrs() {
          let a2 = this.get("count") || 0, i2 = Object.assign({}, this.attr());
          return Object.keys(i2).forEach((e2) => {
            var t2;
            e2.startsWith("_") && (t2 = /_(\d+)$/.exec(e2)) && parseInt(t2[1]) >= a2 && delete i2[e2];
          }), i2;
        }
        buildMarkup(e2) {
          var e2 = Object.assign({
            dry: true
          }, e2), t2 = this.get("count"), a2 = [{
            tagName: "rect",
            selector: "body",
            groupSelector: ""
          }], i2 = this.getCleanedAttrs(), l2 = 1 / t2;
          for (let e3 = 0; e3 < t2; e3++) {
            var s2 = "_box_" + e3, r2 = "_label_" + e3;
            a2.push({
              tagName: "rect",
              selector: s2,
              groupSelector: "boxes"
            }, {
              tagName: "text",
              selector: r2,
              groupSelector: "labels"
            }), i2[s2] = Object.assign(Object.assign({}, i2[s2]), {
              y: `calc(${l2 * e3}*h)`,
              height: `calc(${l2}*h)`
            }), i2[r2] = Object.assign(Object.assign({}, i2[r2]), {
              y: `calc(${l2 * (e3 + 0.5)}*h)`,
              textWrap: {
                height: 100 * l2 + "%"
              }
            });
          }
          this.set("markup", a2, e2), this.set("attrs", i2, e2);
        }
        setLabelAttr(e2, t2, a2) {
          this.attr(["_label_" + e2], t2, a2);
        }
        setBoxAttr(e2, t2, a2) {
          this.attr(["_box_" + e2], t2, a2);
        }
      }
      class v extends r.dia.Element {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMTruck",
            size: {
              width: 120,
              height: 80
            },
            attrs: {
              body: {
                strokeWidth: 2,
                stroke: t,
                fill: a,
                refD: "M 248 120 L 248 120 C 248 119 248 119 248 119 L 248 119 C 248 119 248 118 248 118 L 248 118 L 248 117 L 248 117 L 234 82 C 231 76 225 72 219 72 L 184 72 L 184 64 C 184 60 180 56 176 56 L 24 56 C 15 56 8 63 8 72 L 8 184 C 8 193 15 200 24 200 L 37 200 C 43 224 73 232 90 215 C 95 211 98 206 99 200 L 157 200 C 163 224 193 232 210 215 C 215 211 218 206 219 200 L 232 200 C 241 200 248 193 248 184 L 248 120 Z M 184 88 L 219 88 L 228 112 L 184 112 Z M 24 72 L 168 72 L 168 136 L 24 136 Z M 68 208 C 56 208 48 195 54 184 C 60 173 76 173 82 184 C 83 186 84 189 84 192 C 84 201 77 208 68 208 Z M 188 208 C 176 208 168 195 174 184 C 180 173 196 173 202 184 C 203 186 204 189 204 192 C 204 201 197 208 188 208 Z"
              },
              background: {
                fill: i,
                refD: "M 248 120 L 248 120 C 248 119 248 119 248 119 L 248 119 C 248 119 248 118 248 118 L 248 118 L 248 117 L 248 117 L 234 82 C 231 76 225 72 219 72 L 184 72 L 184 64 C 184 60 180 56 176 56 L 24 56 C 15 56 8 63 8 72 L 8 184 C 8 193 15 200 24 200 L 37 200 C 43 224 73 232 90 215 C 95 211 98 206 99 200 L 157 200 C 163 224 193 232 210 215 C 215 211 218 206 219 200 L 232 200 C 241 200 248 193 248 184 L 248 120 Z"
              },
              label: {
                text: "Truck Shipment",
                textVerticalAnchor: "top",
                textAnchor: "middle",
                x: "calc(0.5 * w)",
                y: "calc(h + 10)",
                fontSize: 13,
                fontFamily: "sans-serif",
                fill: t
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "background"
          }, {
            tagName: "path",
            selector: "body"
          }, {
            tagName: "text",
            selector: "label"
          }];
        }
      }
      class E extends r.dia.Link {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMShipment",
            attrs: {
              line: {
                connection: true,
                stroke: t,
                strokeWidth: 8,
                strokeLinejoin: "round",
                strokeLinecap: "round",
                targetMarker: {
                  type: "path",
                  d: "M 0 -10 0 -10 -20 0 0 10 0 10"
                }
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "line",
            attributes: {
              fill: "none"
            }
          }];
        }
      }
      class I extends r.dia.Link {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMMaterialFlow",
            attrs: {
              line: {
                connection: true,
                stroke: a,
                strokeWidth: 16,
                strokeLinejoin: "round",
                strokeLinecap: "square",
                targetMarker: {
                  type: "path",
                  stroke: t,
                  "stroke-width": 2,
                  d: "M 0 -8 0 -20 -30 0 0 20 0 8"
                }
              },
              outline: {
                connection: true,
                stroke: t,
                strokeWidth: 20,
                strokeLinecap: "square",
                strokeLinejoin: "round"
              },
              material: {
                connection: true,
                stroke: t,
                strokeWidth: 10,
                strokeLinecap: "butt",
                strokeLinejoin: "round",
                strokeDasharray: "15,5"
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "outline",
            attributes: {
              fill: "none"
            }
          }, {
            tagName: "path",
            selector: "line",
            attributes: {
              fill: "none"
            }
          }, {
            tagName: "path",
            selector: "material",
            attributes: {
              fill: "none"
            }
          }];
        }
      }
      class H extends r.dia.Link {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMInformationFlow",
            attrs: {
              line: {
                connection: true,
                stroke: a,
                strokeWidth: 16,
                strokeLinejoin: "round",
                strokeLinecap: "square",
                targetMarker: {
                  type: "path",
                  stroke: t,
                  "stroke-width": 2,
                  d: "M 0 -8 0 -20 -30 0 0 20 0 8"
                }
              },
              outline: {
                connection: true,
                stroke: t,
                strokeWidth: 20,
                strokeLinecap: "square",
                strokeLinejoin: "round"
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "outline",
            attributes: {
              fill: "none"
            }
          }, {
            tagName: "path",
            selector: "line",
            attributes: {
              fill: "none"
            }
          }];
        }
      }
      class $ extends r.dia.Link {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMElectronicInformationFlow",
            attrs: {
              line: {
                connection: true,
                stroke: t,
                strokeWidth: 2,
                strokeLinejoin: "round",
                targetMarker: {
                  type: "path",
                  d: "M 10 -5 0 0 10 5 8 0 z"
                }
              },
              wrapper: {
                connection: true,
                strokeWidth: 10,
                strokeLinejoin: "round"
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "wrapper",
            attributes: {
              fill: "none",
              cursor: "pointer",
              stroke: "transparent",
              "stroke-linecap": "round"
            }
          }, {
            tagName: "path",
            selector: "line",
            attributes: {
              fill: "none",
              "pointer-events": "none"
            }
          }];
        }
      }
      class K extends r.dia.LinkView {
        findPath(e2, t2, a2) {
          var e2 = super.findPath(e2, t2, a2), t2 = e2.segmentIndexAt(0.5), a2 = e2.getSegment(t2), i2 = a2.tangentAt(0.5), l2 = i2.start, i2 = i2.clone().setLength(10).rotate(l2, 90).parallel(10).end, l2 = i2.reflection(l2), s2 = r.g.Path;
          return e2.replaceSegment(t2, [s2.createSegment("L", i2), s2.createSegment("L", l2), s2.createSegment("L", a2.end)]), e2;
        }
      }
      class Z extends r.dia.Link {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMManualInfo",
            attrs: {
              line: {
                connection: true,
                stroke: "#333333",
                strokeWidth: 2,
                strokeLinejoin: "round",
                targetMarker: {
                  type: "path",
                  d: "M 10 -5 0 0 10 5 z"
                }
              },
              wrapper: {
                connection: true,
                strokeWidth: 10,
                strokeLinejoin: "round"
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "wrapper",
            attributes: {
              fill: "none",
              cursor: "pointer",
              stroke: "transparent",
              "stroke-linecap": "round"
            }
          }, {
            tagName: "path",
            selector: "line",
            attributes: {
              fill: "none",
              "pointer-events": "none"
            }
          }];
        }
      }
      class B extends r.dia.Link {
        defaults() {
          return Object.assign(Object.assign({}, super.defaults), {
            type: "VSMPullArrow",
            attrs: {
              line: {
                connection: true,
                stroke: "#333333",
                strokeWidth: 2,
                strokeDasharray: "10,3",
                strokeLinejoin: "round",
                targetMarker: {
                  type: "path",
                  d: "M 10 -5 0 0 10 5 z"
                }
              },
              wrapper: {
                connection: true,
                strokeWidth: 10,
                strokeLinejoin: "round"
              }
            }
          });
        }
        preinitialize() {
          this.markup = [{
            tagName: "path",
            selector: "wrapper",
            attributes: {
              fill: "none",
              cursor: "pointer",
              stroke: "transparent",
              "stroke-linecap": "round"
            }
          }, {
            tagName: "path",
            selector: "line",
            attributes: {
              fill: "none",
              "pointer-events": "none"
            }
          }];
        }
      }
      e.VSMCustomerSupplier = s, e.VSMDataBox = T, e.VSMDedicatedProcess = A, e.VSMElectronicInformationFlow = $, e.VSMElectronicInformationFlowView = K, e.VSMFIFOLane = u, e.VSMGoSee = L, e.VSMInformationFlow = H, e.VSMKaizenBurst = h, e.VSMKanbanPost = g, e.VSMLoadLevelling = m, e.VSMManualInfo = Z, e.VSMMaterialBatchKanban = S, e.VSMMaterialFlow = I, e.VSMMaterialKanban = w, e.VSMMaterialPull = p, e.VSMOperator = d, e.VSMProductionBatchKanban = k, e.VSMProductionControl = V, e.VSMProductionKanban = x, e.VSMPullArrow = B, e.VSMResourcePlanning = C, e.VSMRoundedInventory = o, e.VSMSafetyStock = j, e.VSMSequencePullBall = f, e.VSMSharedProcess = P, e.VSMShipment = E, e.VSMSignalKanban = b, e.VSMSubprocess = F, e.VSMSupermarket = z, e.VSMSupermarketParts = M, e.VSMTimelineProcessing = O, e.VSMTimelineTotal = W, e.VSMTimelineWaiting = N, e.VSMTriangleInventory = n, e.VSMTruck = v, e.VSMWorkcell = c;
    });
  }
});
export default require_joint_vsm_shapes();
//# sourceMappingURL=@joint_shapes-vsm.js.map
