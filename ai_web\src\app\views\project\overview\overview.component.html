<!-- 專案標題與建立按鈕 -->
<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <h2 class="mb-0"><strong>專案</strong></h2>
  </c-col>
  <c-col class="text-end">
    <button
      cButton
      color="primary"
      class="text-white mx-2"
      (click)="openModal()"
    >
      新增專案
    </button>
  </c-col>
</c-row>

<!-- 搜尋欄 -->
<c-row class="mb-4 px-3">
  <c-col xs="12" md="6">
    <div class="input-group shadow-sm">
      <span class="input-group-text bg-white border-end-0">
        <svg [cIcon]="icons.cilSearch" size="xl" title="List Icon"></svg>
      </span>
      <input
        cInput
        type="text"
        class="form-control border-start-0"
        placeholder="搜尋專案名稱..."
        [(ngModel)]="searchText"
      />
    </div>
  </c-col>
</c-row>

<!-- 專案卡片列表 -->
<c-row class="g-4 px-3">
  <c-col xs="12" md="6" lg="4" *ngFor="let project of filteredProjects()">
    <c-card class="h-100 shadow-sm border-0">
      <c-card-body>
        <h5 class="fw-bold mb-1">{{ project.name }}</h5>
        <p class="text-muted mb-2">建立人：{{ project.leader }}</p>
        <p class="text-muted mb-2">專案碼：{{ project.code }}</p>
        <p class="text-muted mb-2">建立時間：{{ project.create_time }}</p>
        <div class="d-flex justify-content-end gap-2">
          <button
            cButton
            color="primary"
            class="text-white"
            size="sm"
            [routerLink]="['/project', project.code]"
          >
            進入
          </button>
        </div>
      </c-card-body>
    </c-card>
  </c-col>

  <c-col xs="12" md="6" lg="4">
    <c-card
      class="h-100 border border-primary border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center"
      style="cursor: pointer; min-height: 200px"
      (click)="openModal()"
    >
      <div>
        <h1 class="mt-2 mb-0 text-primary fw-bold">+</h1>
      </div>
    </c-card>
  </c-col>
</c-row>

<!-- Modal -->
<c-modal [(visible)]="modalVisible" backdrop="static" keyboard="false">
  <c-modal-header>
    <h5 class="modal-title">建立新專案</h5>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      (click)="closeModal()"
    ></button>
  </c-modal-header>

  <c-modal-body>
    <div class="mb-3">
      <label for="projectCode" class="form-label fw-bold">專案代碼</label>
      <input
        cInput
        type="text"
        class="form-control shadow-sm"
        id="projectCode"
        [(ngModel)]="new_projectcode"
        placeholder="請輸入專案代碼"
      />
    </div>

    <div class="mb-3">
      <label for="projectName" class="form-label fw-bold">專案名稱</label>
      <input
        cInput
        type="text"
        class="form-control shadow-sm"
        id="projectName"
        [(ngModel)]="new_projectname"
        placeholder="請輸入專案名稱"
      />
    </div>
  </c-modal-body>

  <c-modal-footer class="d-flex justify-content-end">
    <button cButton color="primary" variant="ghost" (click)="closeModal()">
      取消
    </button>
    <button
      cButton
      color="primary"
      class="text-white"
      (click)="add_project(); closeModal()"
    >
      建立
    </button>
  </c-modal-footer>
</c-modal>
