import { ProjectService } from './../../../service/project.service';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import '@joint/plus/joint-plus.css';
import {
  ButtonCloseDirective,
  ButtonDirective,
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  ModalBodyComponent,
  ModalComponent,
  ModalFooterComponent,
  ModalHeaderComponent,
  ModalTitleDirective,
  ThemeDirective,
} from '@coreui/angular';
import {
  RowComponent,
  TextColorDirective,
  FormControlDirective,
  FormSelectDirective,
} from '@coreui/angular';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { IconDirective } from '@coreui/icons-angular';
import {
  cilBar<PERSON>hart,
  cilMap,
  cilBolt,
  cilChartLine,
  cilGroup,
  cilListRich,
  cilSettings,
} from '@coreui/icons';

interface project {
  project_code: string;
  project_name: string;
  place_id: string[];
}

@Component({
  selector: 'app-project-view',
  standalone: true,
  imports: [
    ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    FormControlDirective,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    RowComponent,
    TextColorDirective,
    FormSelectDirective,
    ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    ThemeDirective,
    RouterModule,
    IconDirective,
  ],
  templateUrl: './project-view.component.html',
  styleUrl: './project-view.component.scss',
})
export class ProjectViewComponent {
  project_code = '';
  project: project;
  icons = {
    cilBarChart,
    cilMap,
    cilBolt,
    cilChartLine,
    cilGroup,
    cilListRich,
    cilSettings,
  };
  hovering = [false, false, false, false, false, false, false];

  constructor(
    private ProjectService: ProjectService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.load_project_info();
  }

  load_project_info() {
    this.ProjectService.get_project_info(this.project_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.project = {
          project_code: data.data.project_code || '',
          project_name: data.data.project_name || '',
          place_id: data.data.place_id || [],
        };
      },
      (error) => {
        this.router.navigate(['/404']);
      }
    );
  }
}
