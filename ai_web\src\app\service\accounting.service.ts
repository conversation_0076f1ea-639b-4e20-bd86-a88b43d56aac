import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AccountingService {

  constructor(private http: HttpClient) { }
  
  // 檢查企業是否有銀行帳戶資訊
  check_bank_account(taxId: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(
      `${environment.apiBaseUrl}/enterprise/check_bank_account`,
      { taxId: taxId },
      { headers }
    );
  }

  // 更新企業銀行帳戶資訊
  update_bank_account(taxId: string, bankInfo: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(
      `${environment.apiBaseUrl}/enterprise/update_bank_account`,
      { 
        taxId: taxId, 
        account: {
          'TaiwanBusinessBank': bankInfo
        }
      },
      { headers }
    );
  }

  // 獲取帳務管理數據
  get_accounting_events(taxId: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(
      `${environment.apiBaseUrl}/enterprise/get_accounting_events`,
      { taxId: taxId },
      { headers }
    );
  }
}
