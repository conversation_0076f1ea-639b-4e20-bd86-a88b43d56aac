import os
import time
import re
import requests
import base64
import sys
import subprocess
import pandas as pd
import xlwt
import json
import random
from datetime import datetime
from PIL import Image
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException, TimeoutException, UnexpectedAlertPresentException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import ddddocr
from config import config, prd_config, dev_config
from redis import Redis
import socket

# 台灣銀行企業網銀爬蟲
# Redis 任務格式：
# {
#     "acceptance_number": "受理號碼",    // 例如: "00-********"
#     "amount": 12345.67           // 付款金額
# }
# 
# 狀態定義：
# - pending: 待處理
# - in_process: 處理中  
# - done: 已完成
# - error: 錯誤
# - canceled: 已取消

class TwBankCrawler:
    def __init__(self, account='imaOne', password='imapione', taxid=********):
        """初始化台灣企業銀行爬蟲類別"""
        self.driver = None
        self.login_success = False
        self.account = account
        self.password = password
        self.taxid = taxid
        self.ocr = ddddocr.DdddOcr()    # 初始化 OCR
        # 載入 tpc_bank_info
        with open(os.path.join(os.path.dirname(__file__), 'json', 'tpc_bank.json'), 'r', encoding='utf-8') as f:
            self.tpc_bank_info = json.load(f)
    
    def handle_any_alert(self):
        """統一處理所有類型的彈出警告"""
        try:
            alert = self.driver.switch_to.alert
            alert_text = alert.text
            alert.accept()
            time.sleep(2)
            
            # 根據警告內容判斷處理方式
            timeout_keywords = ["逾15分鐘未操作", "請重新登入", "連線逾時"]
            login_keywords = ["本電腦已登入", "使用者已登出", "已登出"]
            
            if any(keyword in alert_text for keyword in timeout_keywords + login_keywords):
                return self.handle_session_timeout()
            elif "請選擇EXCEL格式的文件" in alert_text:
                return {"status": "wrong_excel_format", "success": False, "message": alert_text}
            else:
                return {"status": "other_alert", "success": True, "message": alert_text}
                
        except Exception as e:
            if "no such alert" in str(e).lower():
                return {"status": "no_alert", "success": True}
            else:
                return self.handle_session_timeout()

    def handle_session_timeout(self):
        """統一處理會話超時和登入問題"""
        try:
            self.driver.quit()
        except Exception:
            pass
        
        time.sleep(3)
        try:
            login_success, self.driver = self.login_web()
            return {"status": "session_recovered", "success": login_success}
        except Exception:
            return {"status": "fatal_error", "success": False}

    def clean_temp_captcha_files(self):
        """清理臨時驗證碼文件"""
        temp_dir = 'temp_captcha'
        if os.path.exists(temp_dir):
            for file in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            try:
                os.rmdir(temp_dir)
            except Exception:
                pass
                
    def handle_captcha(self, driver, captcha_img_id, captcha_input_id, max_retries=10):
        """
        修正的驗證碼處理函數
        解決 WebElement.save_screenshot 不存在的問題
        增加最大重試次數到10次
        """
        for attempt in range(max_retries):
            try:
                print(f"🔍 嘗試處理驗證碼 (第 {attempt + 1}/{max_retries} 次)...")
                
                # 等待驗證碼圖片元素出現
                captcha_img_element = WebDriverWait(driver, 15).until( 
                    EC.visibility_of_element_located((By.ID, captcha_img_id))
                )
                
                # 確保圖片已完全載入
                driver.execute_script(
                    "return arguments[0].complete && typeof arguments[0].naturalWidth != 'undefined' && arguments[0].naturalWidth > 0", 
                    captcha_img_element
                )
                
                # 建立暫存資料夾
                os.makedirs('temp_captcha', exist_ok=True)
                captcha_path_debug = f'temp_captcha/captcha_element_{attempt}.png'
                
                # 方法1: 使用 screenshot_as_png 獲取元素截圖
                try:
                    # 獲取元素截圖 (PNG 格式)
                    element_screenshot = captcha_img_element.screenshot_as_png
                    
                    with open(captcha_path_debug, 'wb') as f:
                        f.write(element_screenshot)
                    
                    print(f"✅ 使用 screenshot_as_png 保存驗證碼圖片")
                    
                except Exception as e1:
                    print(f" 使用 screenshot_as_png 保存驗證碼圖片error")
                # 讀取截圖並進行 OCR 識別
                with open(captcha_path_debug, 'rb') as f:
                    captcha_bytes = f.read()
                captcha_text = self.ocr.classification(captcha_bytes)
                print(f"🔤 OCR 識別結果: '{captcha_text}'")
                # 填入驗證碼
                captcha_input_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, captcha_input_id))
                )
                captcha_input_element.clear()
                captcha_input_element.send_keys(captcha_text)
                time.sleep(1)
                return captcha_text
            except Exception as e:
                print(f"❌ 處理驗證碼失敗 (第 {attempt + 1} 次): {e}")
                
                # 如果不是最後一次嘗試，刷新頁面重試
                if attempt < max_retries - 1:
                    print("🔄 刷新頁面以獲取新的驗證碼並重試...")
                    driver.refresh()
                    time.sleep(3)
                else:
                    print(f"🚫 已達到最大重試次數 ({max_retries})，未能成功處理驗證碼")
        
        return None

    def setup_chrome_options(self):
        """設置Chrome瀏覽器選項"""
        options = Options()
        option_list = [
            '--disable-gpu', '--no-sandbox', '--disable-dev-shm-usage',
            '--disable-extensions', '--disable-infobars', '--disable-cache',
            '--window-size=1920,1080', '--lang=zh-TW',
            '--disable-blink-features=AutomationControlled',
            '--ignore-certificate-errors'
        ]
        for option in option_list:
            options.add_argument(option)
        options.add_experimental_option('excludeSwitches', ['enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        return options
        
    def login_web(self, account='imaOne', password='imapione', taxid=********):
        """登入台灣企業銀行網站"""
        options = self.setup_chrome_options()
        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        self.driver.set_window_size(1920, 1080)
        self.driver.get("https://portal.tbb.com.tw/tbbportal/")
        time.sleep(10)
        
        max_login_attempts = 10
        self.login_success = False
        
        for login_attempt in range(max_login_attempts):
            try:
                # 填入登入資訊
                input_fields = [
                    ("UserID_E", taxid),
                    ("UserAccount_E", account),
                    ("UserPassword_E", password)
                ]
                
                for field_id, value in input_fields:
                    element = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, field_id))
                    )
                    element.clear()
                    element.send_keys(value)
                    time.sleep(1)
                self.handle_captcha(self.driver, "captchaImg_E", "txtVerifyCode_E", max_retries=10)
                # 點擊登入按鈕
                login_button = self.driver.find_element(By.XPATH, "//img[contains(@onclick, 'doSubmit(document.fm)')]")
                login_button.click()
                
                # 檢查登入結果
                time.sleep(5)
                target_url = "https://portal.tbb.com.tw/tbbportal/Login/index-in.jsp"
                if target_url in self.driver.current_url or "index-in.jsp" in self.driver.current_url:
                    self.login_success = True
                    break
                    
                self.handle_login_failure()
                
            except Exception:
                if login_attempt < max_login_attempts - 1:
                    self.driver.refresh()
                    time.sleep(3)
                    
        self.clean_temp_captcha_files()
        return self.login_success, self.driver

    def handle_login_failure(self):
        """統一處理登入失敗情況"""
        try:
            # 嘗試找到並點擊登出或退出按鈕
            button_xpaths = [
                "//a[contains(@onclick, 'logout()')]",
                "/html/body/center/form/table/tbody/tr[4]/td/input"
            ]
            
            for xpath in button_xpaths:
                try:
                    button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, xpath))
                    )
                    button.click()
                    time.sleep(3)
                    break
                except Exception:
                    continue
            
            # 確保回到登入頁面
            if "CustomerLogin.jsp" not in self.driver.current_url:
                self.driver.get("https://portal.tbb.com.tw/tbbportal/")
                time.sleep(5)
                
        except Exception:
            try:
                self.driver.get("https://portal.tbb.com.tw/tbbportal/")
                time.sleep(5)
            except Exception:
                pass

    def click_corporate_ban(self):
        """點擊企業功能按鈕並處理新視窗"""
        try:
            original_handles = self.driver.window_handles
            btn_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="item07_1"]/table/tbody/tr[1]/td/a'))
            )
            btn_button.click()
            
            # 等待新視窗
            for _ in range(15):
                time.sleep(1)
                if len(self.driver.window_handles) > len(original_handles):
                    break
            
            # 切換到新視窗
            new_handles = self.driver.window_handles
            if len(new_handles) > len(original_handles):
                new_handle = [h for h in new_handles if h not in original_handles][0]
                self.driver.switch_to.window(new_handle)
                time.sleep(5)
                return self.handle_error_page()
            return False
            
        except Exception:
            return self.handle_error_page()

    def navigate_to_file_import_page(self):
        """導航至檔案匯入頁面"""
        target_url = "https://portal.tbb.com.tw/nCNB/C03/01/01/02/01.html"
        self.driver.get(target_url)
        time.sleep(5)
        if not self.handle_error_page():
            return False
        return "檔案匯入編輯" in self.driver.page_source

    def get_bank_info_by_case_number(self, case_number):
        """根據案件編號獲取銀行資訊"""
        if not case_number or not isinstance(case_number, str):
            return None
        match = re.match(r'(\d{2})-\d+', case_number)
        if not match:
            return None
        area_code = match.group(1)
        
        for bank in self.tpc_bank_info:
            if bank.get('area_code') == area_code:
                return bank.copy()
        return None

    # 1. 合併 Excel 產生邏輯
    def create_excel(self, case_numbers, payment_accounts, invoice_counter=1):
        """根據案件數量自動產生 Excel"""
        header_mapping = {
            'transaction_id': '交易資料-0001\n(發票資料-0002)',
            'payment_date': '付款日期\n(發票號碼)',
            'payment_amount': '付款金額\n(發票金額)',
            'payment_account': '付款帳號\n(發票日期)',
            'payer_name': '付款人戶名',
            'payer_bank_code': '付款行總行代碼',
            'payer_branch_code': '付款行分行代碼',
            'receiving_account': '收款帳號',
            'account_name': '收款戶名',
            'bank_code': '收款行總行代碼',
            'branch_code': '收款行分行代碼',
            'identifier_code': '識別碼類別',
            'tax_id': '收款人識別碼',
            'fee_type': '手續費負擔別',
            'receiver_notify': '收款人通知',
            'fax_number': '傳真號碼',
            'email': 'Email帳號',
            'reserved': '保留欄位\n(請勿輸入)',
            'notes': '備註',
            'memo': '附言',
        }
        now_day = datetime.now().strftime('%Y%m%d')
        output_path = f"{now_day}.xls"
        all_data = []
        if not payment_accounts or len(payment_accounts) == 0:
            print("錯誤：未提供付款帳戶資訊")
            return False, invoice_counter, None
        if len(case_numbers) == 1:
            payment_accounts = [payment_accounts[0]]
        elif len(payment_accounts) == 1:
            payment_accounts = [payment_accounts[0]] * len(case_numbers)
        elif len(payment_accounts) != len(case_numbers):
            print("錯誤：付款帳戶資訊數量與案件數量不匹配")
            return False, invoice_counter, None
        for i, case_number in enumerate(case_numbers):
            bank_info = self.get_bank_info_by_case_number(case_number)
            if not bank_info:
                print(f"錯誤：無法獲取案件 {case_number} 的銀行資訊")
                continue
            payment_account = payment_accounts[i]["account"]
            invoice_date = payment_accounts[i]["invoice_date"]
            amount = payment_accounts[i].get("amount", 0)
            payer_name = payment_accounts[i].get("payer_name", "")
            invoice_number = f'Y{str(invoice_counter).zfill(5)}'
            invoice_counter += 1
            transaction_data = {
                'transaction_id': '0001',
                'payment_date': now_day,
                'payment_amount': str(amount),
                'payment_account': payment_account,
                'payer_name': payer_name,
                'payer_bank_code': bank_info.get('payer_bank_code'),
                'payer_branch_code': bank_info.get('payer_branch_code'),
                'receiving_account': bank_info.get('receiving_account', ""),
                'account_name': bank_info.get('account_name', ""),
                'bank_code': bank_info.get('bank_code', ""),
                'branch_code': bank_info.get('branch_code', ""),
                'identifier_code': bank_info.get('identifier_code', ""),
                'tax_id': bank_info.get('tax_id', ""),
                'fee_type': bank_info.get('fee_type', ""),
                'receiver_notify': '0',
                'fax_number': bank_info.get('fax_number', ''),
                'email': '', 'reserved': '', 'notes': '', 'memo': '',
            }
            invoice_data = transaction_data.copy()
            invoice_data.update({
                'transaction_id': '0002',
                'payment_date': invoice_number,
                'payment_amount': str(amount),
                'payment_account': invoice_date,
                'payer_name': '', 'payer_bank_code': '', 'payer_branch_code': '',
                'receiving_account': '', 'account_name': '', 'bank_code': '',
                'branch_code': '', 'identifier_code': '', 'tax_id': '',
                'fee_type': '', 'receiver_notify': '', 'fax_number': '',
            })
            all_data.extend([transaction_data, invoice_data])
        try:
            df = pd.DataFrame(all_data)
            df_renamed = df.rename(columns=header_mapping)
            desired_order = [
                '交易資料-0001\n(發票資料-0002)', '付款日期\n(發票號碼)', '付款金額\n(發票金額)',
                '付款帳號\n(發票日期)', '付款人戶名', '付款行總行代碼', '付款行分行代碼',
                '收款帳號', '收款戶名', '收款行總行代碼', '收款行分行代碼',
                '識別碼類別', '收款人識別碼', '手續費負擔別', '收款人通知',
                '傳真號碼', 'Email帳號', '保留欄位\n(請勿輸入)', '備註', '附言'
            ]
            final_columns = [col for col in desired_order if col in df_renamed.columns]
            df_final = df_renamed[final_columns]
            workbook = xlwt.Workbook(encoding='utf-8')
            worksheet = workbook.add_sheet('Sheet1')
            for col_idx, column_name in enumerate(df_final.columns):
                worksheet.write(0, col_idx, column_name)
            for row_idx, row in enumerate(df_final.values):
                for col_idx, value in enumerate(row):
                    worksheet.write(row_idx + 1, col_idx, value)
            workbook.save(output_path)
            if os.path.exists(output_path):
                return True, invoice_counter, output_path
            return False, invoice_counter, None
        except Exception as e:
            print(f"Excel 產生失敗: {e}")
            return False, invoice_counter, None

    # 2. 拆分上傳與送審
    def upload_excel(self, excel_file_path):
        try:
            if not os.path.exists(excel_file_path) or not excel_file_path.endswith('.xls'):
                return False
            if not self.handle_error_page():
                return False
            file_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@type='FILE' and @name='uploadFile']"))
            )
            file_input.send_keys(os.path.abspath(excel_file_path))
            time.sleep(2)
            if not self.handle_error_page():
                return False
            try:
                filename_input = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//input[@name='reqFileName']"))
                )
                current_date = datetime.now().strftime("%Y%m%d")
                filename_input.clear()
                filename_input.send_keys(current_date)
                time.sleep(1)
            except Exception:
                pass
            # 點擊確認
            try:
                confirm_btn = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//input[@type='button' and @value='確認']"))
                )
                confirm_btn.click()
                time.sleep(3)
                if not self.handle_error_page():
                    return False
            except Exception as e:
                print(f"點擊確認失敗: {e}")
                return False
            return True
        except Exception as e:
            print(f"上傳 Excel 失敗: {e}")
            return False

    def submit_excel(self):
        try:
            # 點擊送審
            try:
                submit_btn = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//input[@type='button' and @value='送審']"))
                )
                submit_btn.click()
                time.sleep(3)
                if not self.handle_error_page():
                    return False
            except Exception as e:
                print(f"點擊送審失敗: {e}")
                return False
            return "檔案匯入編輯" in self.driver.page_source
        except Exception as e:
            print(f"送審失敗: {e}")
            return False

    def handle_error_page(self):
        """統一錯誤頁面處理機制"""
        # 處理彈出警告
        alert_result = self.handle_any_alert()
        
        # 會話問題需要重新登入流程
        if alert_result["status"] == "session_recovered":
            if alert_result["success"]:
                return self.click_corporate_ban() and self.navigate_to_file_import_page()
            return False
        
        # Excel格式錯誤
        if alert_result["status"] == "wrong_excel_format":
            return False
        
        # 檢查頁面錯誤信息
        error_patterns = [
            "錯誤代碼", "系統錯誤", "發生異常", "請重新登入",
            "無法處理您的請求", "連線已中斷", "操作逾時",
            "本電腦已登入臺灣企銀網路銀行", "使用者已登入", "系統維護中", "連線逾時"
        ]
        
        if any(pattern in self.driver.page_source for pattern in error_patterns):
            # 嘗試點擊各種按鈕
            button_xpaths = [
                "//input[@type='button' and (@value='確定' or @value='確認' or @value='返回')]",
                "//button[contains(text(), '確定') or contains(text(), '確認') or contains(text(), '返回')]",
                "//a[contains(@onclick, 'logout()') or contains(text(), '登出')]",
                "//input[@type='button' and @value='登出']"
            ]
            
            for xpath in button_xpaths:
                try:
                    buttons = self.driver.find_elements(By.XPATH, xpath)
                    if buttons:
                        buttons[0].click()
                        time.sleep(2)
                        try:
                            self.driver.switch_to.alert.accept()
                            time.sleep(1)
                        except Exception:
                            pass
                        
                        if not any(pattern in self.driver.page_source for pattern in error_patterns):
                            return self.recover_to_correct_page()
                except Exception:
                    continue
            
            # 按鈕無效，嘗試重新登入
            return self.handle_session_timeout()["success"] and self.click_corporate_ban() and self.navigate_to_file_import_page()
        
        return True

    def recover_to_correct_page(self):
        """恢復到正確頁面"""
        current_url = self.driver.current_url
        if "tbbportal/Login/index-in.jsp" in current_url:
            return self.click_corporate_ban() and self.navigate_to_file_import_page()
        elif "C03/01/01/02/01.html" in current_url:
            return True
        elif "nCNB.html" in current_url:
            return self.navigate_to_file_import_page()
        return False

    def logout_system(self):
        """登出系統"""
        try:
            self.handle_error_page()
            logout_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(@onclick, 'logout()')]"))
            )
            logout_button.click()
            time.sleep(2)
            return True
        except Exception:
            self.driver.get("https://portal.tbb.com.tw/tbbportal/")
            time.sleep(2)
            return True


def update_event_status(acceptance_number, status, stage, conf=None, error_msg=None):
    """
    呼叫後端 /crawler/update_event API 更新事件狀態。
    """
    if conf is None:
        if len(sys.argv) > 2:
            conf = prd_config()
        else:
            conf = dev_config()
    update_data = {
        "acceptance_number": acceptance_number,
        "status": status,
        "stage": stage
    }
    if error_msg:
        update_data["error_msg"] = error_msg
    api_url = f"{conf.api_base}/crawler/update_event"
    try:
        resp = requests.post(api_url, json=update_data, headers={"Content-Type": "application/json"}, timeout=30)
        return resp.status_code == 200
    except Exception as e:
        print(f"[update_event_status] API 呼叫失敗: {e}")
        return False


def tw_bank_crawler_service():
    """
    台灣銀行爬蟲服務，從 Redis 取得受理號碼和金額進行處理
    1. 從 Redis 取得任務
    2. 執行爬蟲處理（整合原 process_cases 流程）
    3. 根據結果呼叫 update_event_status API
    4. 從 Redis 隊列移除任務
    """
    if len(sys.argv) > 2:
        conf = prd_config()
    else:
        conf = dev_config()
    redis_client = Redis(
        host=conf.redis_host,
        port=conf.redis_port,
        db=conf.redis_db,
        decode_responses=True
    )
    task_key = 'tw_bank_crawler_request'
    while True:
        queue_length = redis_client.llen(task_key)
        if queue_length == 0:
            time.sleep(random.uniform(5, 10))
            continue
        task_data = redis_client.lindex(task_key, 0)
        try:
            task = json.loads(task_data)
        except Exception as e:
            print(f"⚠️ 無效的 Redis 任務資料，移除: {task_data}. 錯誤: {e}")
            redis_client.lrem(task_key, 1, task_data)
            continue
        acceptance_number = task.get('acceptance_number')
        amount = task.get('amount')
        if not acceptance_number or not amount or int(amount) <= 0:
            print(f"⚠️ 無效的任務內容 (受理號碼或金額缺失/無效)，移除: {task}. 受理號碼: {acceptance_number}, 金額: {amount}")
            redis_client.lrem(task_key, 1, task_data)
            continue
        amount = int(amount)
        #update_event_status(acceptance_number, "in_process", "payment", conf)
        error_msg = None
        success = False # 最終的成功狀態，預設為 False
        crawler = TwBankCrawler()
        print(f"🚀 開始處理案件 {acceptance_number}")
        
        # 使用一個旗標來控制每個步驟是否繼續執行
        current_task_successful = True 
        excel_path = None # 初始化 excel_path 變數
        
        try:
            # Step 1: 登入
            if current_task_successful:
                print("🔑 開始登入台灣銀行...")
                login_success, _ = crawler.login_web()
                if not login_success:
                    error_msg = "帳密錯誤"
                    current_task_successful = False
                else:
                    print("✅ 登入成功，初始化企業功能...")

            # Step 2: 點擊企業網銀
            if current_task_successful: # 只有在登入成功後才執行
                if not crawler.click_corporate_ban():
                    error_msg = "未進入企業功能"
                    current_task_successful = False
                else:
                    print("✅ 企業功能初始化完成")

            # Step 3: 導航檔案匯入
            if current_task_successful: # 只有在進入企業功能後才執行
                if not crawler.navigate_to_file_import_page():
                    error_msg = "未進入檔案匯入頁"
                    current_task_successful = False
                else:
                    print("✅ 已進入檔案匯入頁")

            # Step 4: 產生 Excel
            if current_task_successful: # 只有在導航成功後才執行
                # 這裡的付款帳號 "寫死的付款帳號" 需要替換為實際的帳號
                payment_accounts_data = [{
                    "account": "寫死的付款帳號", # 這個值應該從某處配置或資料庫獲取
                    "invoice_date": datetime.now().strftime('%Y%m%d'),
                    "amount": amount,
                    "payer_name": "今時科技股份有限公司",
                }]
                success_excel, invoice_counter, temp_excel_path = crawler.create_excel([acceptance_number], payment_accounts_data)
                if not success_excel or not temp_excel_path:
                    error_msg = "Excel 產生失敗"
                    current_task_successful = False
                else:
                    excel_path = temp_excel_path # 將結果賦值給 excel_path
                    print(f"✅ Excel 檔案創建成功: {excel_path}")

            # Step 5: 上傳 Excel
            if current_task_successful: # 只有在 Excel 產生成功後才執行
                if not crawler.upload_excel(excel_path):
                    error_msg = "Excel 上傳失敗"
                    current_task_successful = False
                else:
                    print(f"✅ Excel 上傳成功")

            # Step 6: 送審
            if current_task_successful: # 只有在 Excel 上傳成功後才執行
                if not crawler.submit_excel():
                    error_msg = "送審失敗"
                    current_task_successful = False
                else:
                    print(f"✅ 送審成功")
                    success = True # 如果所有步驟都成功，則將最終成功旗標設為 True

        except Exception as e:
            error_msg = f"爬蟲執行中發生未預期錯誤: {e}"
            print(f"❌ 爬蟲執行中發生未預期錯誤: {e}")
            import traceback
            traceback.print_exc()
            success = False # 確保在發生未預期錯誤時，最終成功旗標為 False

        finally:
            # 清理操作，不論成功或失敗都會執行
            try:
                if crawler.driver:
                    crawler.logout_system()
                    crawler.driver.quit()
            except Exception as e:
                print(f"⚠️ 清理瀏覽器時發生錯誤: {e}")

            try:
                crawler.clean_temp_captcha_files()
            except Exception as e:
                print(f"⚠️ 清理驗證碼檔案時發生錯誤: {e}")

        # 更新事件狀態
        if success:
            print(f"✅ 案件 {acceptance_number} 處理完成。呼叫 update_event_status: done")
            update_event_status(acceptance_number, "done", "payment", conf)
        else:
            final_error_msg = error_msg if error_msg else "未知錯誤"
            print(f"❌ 案件 {acceptance_number} 處理失敗。呼叫 update_event_status: error, msg={final_error_msg}")
            update_event_status(acceptance_number, "error", "payment", conf, error_msg=final_error_msg)

        # 從 Redis 佇列中移除已處理的任務
        redis_client.lrem(task_key, 1, task_data)
        time.sleep(random.uniform(2, 5))

            
if __name__ == "__main__":
    print("tw_bank_crawler_service")
    tw_bank_crawler_service()