import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DeviceService {

  constructor(private http: HttpClient) { }

  search(database: string, gateway_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device/show_table`, {database: database, gateway_id: gateway_id}, { headers });
  }

  download_sql(database: string, device_id: string, start_date: string, end_date: string, order: string, limit: number): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device/download_sql`, {database: database, device_id: device_id, start_date: start_date, end_date: end_date, order: order, limit: limit}, { headers: headers,  responseType: 'blob' });
  }

  search_sql(database: string, device_id: string, start_date: string, end_date: string, order: string, limit: number): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device/search_sql`, {database: database, device_id: device_id, start_date: start_date, end_date: end_date, order: order, limit: limit}, { headers });
  }

  load_project_device(project_name: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/device/load_project_device`, {project_name: project_name}, { headers });
  }
}
