from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.wait import Web<PERSON>river<PERSON>ait
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import undetected_chromedriver as uc #刪掉
import time, random, logging, requests, re,json,traceback
from redis import Redis 

def driver_start (conf): ## 寫在worker啟動裡面 #記得告知其他人
        service = Service(ChromeDriverManager().install())
        options = uc.ChromeOptions()
        time.sleep(3)
        options.add_argument("--headless=new")  
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-application-cache")
        options.add_argument("--disk-cache-size=1")
        options.add_argument("--disable-cache")
        options.add_argument("--window-size=1920,1080")
        options.add_argument("--lang=zh-TW")
        options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36")
        options.add_experimental_option("prefs", {
            "intl.accept_languages": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7"
        })


        driver = webdriver.Chrome(service=service, options=options)

        driver.get(conf.legal_entity_crawler_URL)  
def search_company_id(comapny_id,driver):
    try:
        legal_entity_type_map = {
            "cmpyType": "公司",
            "brCmpyType": "分公司",
            "busmType": "商業",
            "factType": "工廠",
            "lmtdType": "有限合夥"
        }
        for id_ in list(legal_entity_type_map.keys()):
            checkbox = driver.find_element(By.CSS_SELECTOR, f'input[type="checkbox"][value="{id_}"]')
            if not checkbox.is_selected():
                checkbox.click()
        search_box =driver.find_element(By.ID, "qryCond")
        search_box.clear()
        search_box.send_keys(comapny_id)
        search_box.send_keys(Keys.ENTER)
        time.sleep(random.uniform(2, 5))
        driver.find_element(By.XPATH, '//a[contains(text(), "舊版表格格式")]').click()
        time.sleep(random.uniform(2, 5))
        rows = driver.find_elements(By.CSS_SELECTOR, "#eslist-table tbody tr")

        for row in rows:
            cells = row.find_elements(By.TAG_NAME, "td")
            if len(cells) < 3:
                continue  
            id_text = cells[2].text.strip().replace("\n", "").replace(" ", "")
            if id_text == comapny_id:
                link = cells[3].find_element(By.TAG_NAME, "a")
                print(f"✅ 找到統一編號 {comapny_id}，點擊連結：{link.text}")
                link.click()
                break
            data_type = cells[0].text.strip().replace("\n", "").replace(" ", "")
        link.click()            
        return data_type
    except Exception as enter_company_id_error:
        logging.error(f'enter_company_id_error:{enter_company_id_error}')
        return enter_company_id_error
def get_company_basic_information(type,driver,conf):
    try:
        table = driver.find_element(By.XPATH, f"//h3[contains(text(), {type})]/following-sibling::div//table")
        rows = table.find_elements(By.XPATH, "./tbody/tr")
        company_basic_information = {}
        company_basic_information_en = {}

        for row in rows:
            tds = row.find_elements(By.TAG_NAME, 'td')
            if len(tds) == 2:
                key = tds[0].text.strip()   
                value = tds[1].text.strip()  
            else :
                key = tds[0].text.strip() ## 不應該有特例
                if key == '負責人姓名':
                    value = tds[2].text.strip()
                elif key =='特別股股東被選為董事、監察人之禁止或限制或當選一定名額之權利      無':
                    continue
                    
                elif key == '':
                    continue
                elif key == '依據行政院主計總處『行業統計分類』\n110年1月第11次修訂':
                    continue
                else :                 
                    value = tds[1].text.strip()
            if "所營事業資料" in key:
                value = [line.strip() for line in value.split('\n') if line.strip()]
            elif "登記現況" in key:
                value = re.sub(r'「查詢最新營業狀況請至\s*財政部稅務入口網\s*」', '', value).strip()
            elif "商業名稱" in key:
                match = re.match(r'^([\u4e00-\u9fa5\w（）()、．.]+)', value)
                value = match.group(1) if match else None
            elif "所營事業項目" in key:
                value = [line.strip() for line in value.split('\n') if line.strip()]
            elif "有限合夥名稱" in key :
                match = re.match(r'^([\u4e00-\u9fa5（）()、．.a-zA-Z0-9]+)', value)
                value = match.group(1) if match else value
            elif "所在地" in key:
                match = re.match(r'^([\u4e00-\u9fa5\d\-號樓之A-Za-z()（）]+)', value)
                value = match.group(1) if match else value
            elif key == '最近一次登記狀況\n核准日期及文號':
                key = key.split('\n')[0]
            elif "約定解散事由" in key :
                items = re.split(r'(?=\d+\.)', value)
                value = [item.strip() for item in items if item.strip()]
            elif "有限合夥人" in key :
                value = [part.strip() for part in value.split(' ') if part.strip()]
            elif "普通合夥人姓名" in key :
                value = [part.strip() for part in value.split(' ') if part.strip()]
            elif '分公司名稱'in key or'工廠名稱'in key or '工廠登記編號'in key or '工廠地址' in key or'統一編號' in  key:
                value  = value.split()[0]  

            company_basic_information[key] = value
        for key, value in company_basic_information.items():
            for en_key in conf.legal_entity_conversion_dict.keys():
                ch_key_list = conf.legal_entity_conversion_dict.get(en_key)
                if key in ch_key_list:
                    new_key = en_key
                    break
            else:
                new_key = key 
            company_basic_information_en[new_key] = value
        return company_basic_information_en
    except Exception as e:
        print(e)
        traceback.print_exc() 
        return None
def get_board_members_information(driver):
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, '//h3[strong[contains(text(), "董監事資料")]]'))
        )
        rows = driver.find_elements(By.XPATH,
            '//h3[strong[contains(text(), "董監事資料")]]/following-sibling::div[@class="table-responsive"][1]//table/tbody/tr'
        )
        results = []
        for row in rows:
            cells = row.find_elements(By.TAG_NAME, 'td')
            if len(cells) == 5:
                item = {
                "position": cells[1].get_attribute("textContent").strip(),
                "name": cells[2].get_attribute("textContent").strip(),
                "representing_entity": cells[3].get_attribute("textContent").strip(),
                "shares_held": cells[4].get_attribute("textContent").strip().replace(",", "")
            }
                results.append(item)
        return results
    except Exception as e:
        return None

def get_history_information(driver):
    try :
        table = driver.find_element(By.XPATH, "//h3[contains(text(), '歷史資料')]/following-sibling::div//table")
        rows = table.find_elements(By.TAG_NAME, "tr")[1:]  # 排除表頭
        history_data = []
        for row in rows:
            cells = row.find_elements(By.TAG_NAME, "td")
            if len(cells) < 6:
                continue  # 避免錯誤資料
            record = {
                "approval_date":  cells[1].get_attribute("textContent").strip(),
                "company_id":  cells[2].get_attribute("textContent").strip(),
                "company_name":  cells[3].get_attribute("textContent").strip(),
                "capital_amount":  cells[4].get_attribute("textContent").strip(),
                "paid_in_capital":  cells[5].get_attribute("textContent").strip() if len(cells) == 7 else "",
                "company_location":  cells[6].get_attribute("textContent").strip()if len(cells) == 7 else cells[5].text.strip()
            }

            history_data.append(record)
        return history_data
    except Exception as e:
        return None



def get_branch_company_information(driver,conf):
    table = driver.find_element(By.XPATH, "//h3[contains(text(), '分公司資料')]/following-sibling::div//table")
    rows = table.find_elements(By.TAG_NAME, "tr")
    company_basic_information = {}
    company_basic_information_en = {}
    for row in rows:
        tds = row.find_elements(By.TAG_NAME, "td")
        if len(tds) == 2:
            key = tds[0].text.strip()
            value = tds[1].text.strip()
            company_basic_information[key] = value
    for key, value in company_basic_information.items():
        for en_key in conf.legal_entity_conversion_dict.keys():
            ch_key_list = conf.legal_entity_conversion_dict.get(en_key)
            if key in ch_key_list:
                new_key = en_key
                break
        else:
            new_key = key 
        company_basic_information_en[new_key] = value
    return company_basic_information_en

def get_business_information(driver,conf):
    table = driver.find_element(By.XPATH, "//h3[contains(text(), '商業登記基本資料')]/following-sibling::div//table")
    rows = table.find_elements(By.TAG_NAME, "tr")
    company_basic_information = {}
    company_basic_information_en = {}
    for row in rows:
        tds = row.find_elements(By.TAG_NAME, "td")
        if len(tds) == 2:
            key = tds[0].text.strip()
            value = tds[1].text.strip()
            company_basic_information[key] = value
    for key, value in company_basic_information.items():
        for en_key in conf.legal_entity_conversion_dict.keys():
            ch_key_list = conf.legal_entity_conversion_dict.get(en_key)
            if key in ch_key_list:
                new_key = en_key
                break
        else:
            new_key = key 
        company_basic_information_en[new_key] = value
    return company_basic_information_en

def get_factory_information(driver,conf):
    table = driver.find_element(By.XPATH, "//h3[contains(text(), '工廠基本資料')]/following-sibling::div//table")
    rows = table.find_elements(By.TAG_NAME, "tr")
    company_basic_information = {}
    company_basic_information_en = {}
    for row in rows:
        tds = row.find_elements(By.TAG_NAME, "td")
        if len(tds) == 2:
            key = tds[0].text.strip()
            value = tds[1].text.strip()
            company_basic_information[key] = value
    for key, value in company_basic_information.items():
        for en_key in conf.legal_entity_conversion_dict.keys():
            ch_key_list = conf.legal_entity_conversion_dict.get(en_key)
            if key in ch_key_list:
                new_key = en_key
                break
        else:
            new_key = key 
        company_basic_information_en[new_key] = value
    return company_basic_information_en

def save_legal_entity_crawler_information(legal_entity_information,conf):
    url=f'{conf.api_base}/crawler/save_legal_entity_information'
    a = requests.post(url= url,json =legal_entity_information)
def get_legal_entity_crawler_request(redis_client, conf):
    legal_entity_crawler_request_list = redis_client.lrange(conf.legal_entity_crawler_key, 0, -1)
    return legal_entity_crawler_request_list

def call_legal_entity_crawler(legal_entity_crawler_request_list,conf):
    driver = driver_start(conf=conf)
    r = Redis(host=conf.redis_host, port=conf.redis_port, db=conf.redis_db,decode_responses=True)
    time.sleep(random.uniform(2, 5))
    try:
        for legal_entity_crawler_request in legal_entity_crawler_request_list:
            legal_entity_crawler_request = json.loads(legal_entity_crawler_request)
            company_id =legal_entity_crawler_request.get('company_id')
            legal_entity_type = legal_entity_crawler_request.get('legal_entity_type')
            type = search_company_id(company_id,legal_entity_type)
            time.sleep(random.uniform(2, 5))
            board_members_information = None
            history_information = None
            if type == '公司': 
                company_basic_information = get_company_basic_information(type='公司基本資料')
                board_members_information = get_board_members_information()
                history_information = get_history_information()

            if type == '分公司':
                company_basic_information = get_company_basic_information(type='分公司資料')
            if type == '商業':
                company_basic_information = get_company_basic_information(type='商業登記基本資料')
            if type == '工廠':
                company_basic_information = get_company_basic_information(type='工廠基本資料')
            if type == '有限合夥':
                company_basic_information = get_company_basic_information(type='有限合夥登記基本資料')
            saving_information ={'company_basic_information': company_basic_information,
                    'board_members_information': board_members_information ,
                    'history_information': history_information }
            print(saving_information)
            saving_information = json.dumps(saving_information, ensure_ascii=False)
            save_legal_entity_crawler_information(saving_information)
            time.sleep(random.uniform(2, 5))
            driver.find_element(By.XPATH, "//a[contains(@href, 'requery()')]").click()
            time.sleep(random.uniform(2, 5))
    except :
        driver.quit()
        driver_start()
    finally:
        if driver:
            driver.quit()
        
def legal_entity_crawler_thread(redis_client, conf):
    while True:
        legal_entity_crawler_request_list = get_legal_entity_crawler_request(redis_client, conf)
        if legal_entity_crawler_request_list:
            call_legal_entity_crawler(legal_entity_crawler_request_list, conf)            
        time.sleep(random.uniform(2, 5))  # 等待一段時間後再檢查


                
