import { Component, OnInit } from '@angular/core';
import { SharedModule, enterprise, user, team } from '../../../shared/shared.module';
import { SharedService } from '../../../service/shared.service';
import { EnterpriseService } from '../../../service/enterprise.service';
import { GroupService } from '../../../service/group.service';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss'
})
export class OverviewComponent {
  my_team_list: team[] = [];
  enterprise_team_list: team[] = [];
  taxId: string;
  showCard: string = 'my_team';
  searchMyTeamText: string = '';
  searchEnterpriseTeamText: string = '';
  constructor(private router: Router,
    private sharedService: SharedService,
    private groupService: GroupService,
    private enterpriseService: EnterpriseService
  ){
    this.taxId = this.sharedService.getRouteId();
    this.groupService.get_user_team().subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        for (let team of data.teams){
          if (team.taxId == this.taxId){
            this.my_team_list.push({
              taxId: team.taxId,
              team_name: team.team_name,
              team_code: team.team_code,
              accounts: []
            });
          }
        }
      },
      (error) => {

      }
    )
    this.enterpriseService.get_enterprise_team(this.taxId).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        for (let team of data.enterprise_team){
          if (team.taxId == this.taxId){
            this.enterprise_team_list.push({
              taxId: team.taxId,
              team_name: team.team_name,
              team_code: team.team_code,
              accounts: []
            });
          }
        }
      },
      (error) => {

      }
    )

  }
  create_team(){
    this.router.navigate([`/enterprise/${this.taxId}/team/create-team`]);
  }
  setShowCard(input: string, card_index: number = 0): void {
    this.showCard = input;
  }
  filteredMyTeam() {
    return this.my_team_list.filter((p) =>
      p.team_name.toLowerCase().includes(this.searchMyTeamText.toLowerCase())
    );
  }
  filteredEnterpriseTeam() {
    return this.enterprise_team_list.filter((p) =>
      p.team_name.toLowerCase().includes(this.searchEnterpriseTeamText.toLowerCase())
    );
  }
  isNotInMyTeamList(teamCode: string): boolean {
    return !this.my_team_list?.some(myTeam => myTeam.team_code === teamCode);
  }
  apply_entering_team(team_code: string){
    this.groupService.apply_entering_team(team_code).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
      },
      (error) => {

      }
    )
  }
}
