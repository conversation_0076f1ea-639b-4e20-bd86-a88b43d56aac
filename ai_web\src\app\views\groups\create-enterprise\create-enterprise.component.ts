import { GroupService } from './../../../service/group.service';
import { Component, OnInit } from '@angular/core';
import { GatewayService } from '../../../service/gateway.service';
import { ProjectService } from '../../../service/project.service';
import { DashboardService } from '../../../service/dashboard.service';
import { SharedModule, enterprise, team } from '../../../shared/shared.module';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';

@Component({
  selector: 'app-create-enterprise',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-enterprise.component.html',
  styleUrl: './create-enterprise.component.scss'
})
export class CreateEnterpriseComponent implements OnInit {
  new_enterprise: enterprise = {
    taxId: '',
    name: '',
    address: '',
    telephone: ''
  }
  enterprise_list: enterprise[] = [];
  create_enterprise_modal_visible = false;
  apply_enterprise_modal_visible = false;
  registered = false;
  agreeTerms: boolean = false;

  step = 0;
  constructor(private GroupService: GroupService,
    private router: Router,
  ){}
  ngOnInit(): void {
    this.GroupService.get_user_enterprise().subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        for (let enterprise of data.enterprises){
          this.enterprise_list.push({
            taxId: enterprise.taxId,
            name: enterprise.name,
            address: enterprise.address,
            telephone: enterprise.telephone,
        });

      }
      console.log(this.enterprise_list);
      },
      (error) => {
        console.error('Error fetching user enterprises:', error);
      }
    );
  }
  // create-enterprise.component.ts

isEnterpriseExist(): boolean {
  if (!this.enterprise_list || !this.new_enterprise) {
    return false;
  }
  return this.enterprise_list.some(
    (e) => e.taxId === this.new_enterprise.taxId
  );
  }

  searchEnterprise(){
    if (this.new_enterprise.taxId.length != 8){
      alert('請輸入正確的統一編號');
      return;
    }
    this.GroupService.searchEnterpriseId(this.new_enterprise.taxId).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        if (data.data.length === 0) { // 如果找不到enterprise，開放填寫enterpriseId
          this.step = 1;
        }
        else if (!data.registered) { // 如果找到enterprise沒有註冊IMA-One，則請user輸入補足
          this.step = 2;
          this.new_enterprise.name = data.data[0].busiNm;
          this.new_enterprise.address = data.data[0].address;
        } else { // 如果找到enterprise並已經註冊IMA-One，則自動填入

          this.step = 3;
          this.registered = true;
          this.new_enterprise.name = data.data[0].busiNm;
          this.new_enterprise.address = data.data[0].address;

        }
      },
      (error) => {

      }
    )
  }
  toggle_createEnterprise(){
    this.create_enterprise_modal_visible = !this.create_enterprise_modal_visible;
  }
  toggle_applyEnterprise(){
    this.apply_enterprise_modal_visible = !this.apply_enterprise_modal_visible;
  }
  createEnterprise(){
    this.GroupService.create_enterprise(this.new_enterprise).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.toggle_createEnterprise();
        this.router.navigate(['/groups/overview']);
      },
      (error) => {
      }
    )
  }
  applyEnteringEnterprise(){
    this.GroupService.apply_entering_enterprise(this.new_enterprise.taxId).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.toggle_createEnterprise();
        this.router.navigate(['/groups/overview']);
      },
      (error) => {
      }
    )
  }
}
