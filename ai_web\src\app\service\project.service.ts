import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  constructor(private http: HttpClient) { }

  get_project_list(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_project_list`, {}, { headers });
  }
  create_project(project_code: any, project_name: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/create_project`, {project_code: project_code, project_name: project_name}, { headers });
  }
  leave_project(project_code: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/leave_project`, {project_code: project_code}, { headers });
  }
  get_projectplotlist(project_code: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_projectplotlist`, {project_code: project_code}, { headers });
  }
  create_place_id(project_code: string, place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/create_place_id`, {project_code: project_code, place_id: place_id}, { headers });
  }
  get_project_place_id(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_project_place_id`, {project_code: project_code}, { headers });
  }
  create_projectplot(project_code: any, projectplot_name: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/create_projectplot`, {project_code: project_code, projectplot_name: projectplot_name}, { headers });
  }
  remove_projectplot(project_code: any, projectplot_name: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/remove_projectplot`, {project_code: project_code, projectplot_name: projectplot_name}, { headers });
  }
  remove_place_id(project_code: string, place_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/remove_place_id`, {project_code: project_code, place_id: place_id}, { headers });
  }
  remove_customer(project_code: string, customer_email: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/remove_customer`, {project_code: project_code, customer_email: customer_email}, { headers });
  }
  create_service(service: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/create_service`, { service}, { headers });
  }

  get_baseline_cost(baselineUsage: number, projectName: string, placeId: string, serviceName: string, date: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_baseline_cost`, { baselineUsage, projectName, placeId, serviceName, date }, { headers });
  }

  get_place_bills(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    console.log(headers);
    return this.http.post(`${environment.apiBaseUrl}/project/get_place_bills`, {}, { headers });
  }
  calculate_baseline(type: string, place_id: string, retrospectStartDate: string, historicalUsagePattern: string, baselineType: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    console.log(headers);
    return this.http.post(`${environment.apiBaseUrl}/project/calculate_baseline`, { type, place_id, retrospectStartDate, historicalUsagePattern, baselineType }, { headers });

  }
  get_device_reference(database: string, tableName: string, id: string, deviceName: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    console.log(headers);
    return this.http.post(`${environment.apiBaseUrl}/project/get_device_reference`, { database, tableName, id, deviceName }, { headers });
  }
  create_bill(billData: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    console.log(headers);
    return this.http.post(`${environment.apiBaseUrl}/project/create_bill`, { billData }, { headers });
  }
  get_adiut_data(re_place_id: string, servicename: string, period: number): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    console.log(headers);
    return this.http.post(`${environment.apiBaseUrl}/project/get_adiut_data`, { re_place_id, servicename, period }, { headers });
  }


  get_project_info(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_project_info`, {project_code: project_code}, { headers });
  }
  get_contract_info(project_code: string , contract_id: number): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_contract_info`, {project_code: project_code, contract_id: contract_id}, { headers });
  }


  send_project_collaboration(project_code:{},receiver:string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/role_interaction/send_project_collaboration_mail`, { project_code,  receiver }, { headers });
  }

  aduit_service_update(data: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/aduit_service_update`, { data }, { headers });
  }

  get_performance_data(electricityId: string, serviceName: string, startPeriod: number, endPeriod: number): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_performance_data`, { electricity_id: electricityId, service_name: serviceName, start_period: startPeriod, end_period: endPeriod }, { headers });
  }
  get_project_member(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_project_member`, {project_code: project_code}, { headers });
  }
  get_customer_member(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_customer_member`, {project_code: project_code}, { headers });
  }
  add_customer(customer_mail: string, view: boolean, accounting: boolean, controller: boolean, project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/add_customer`, {customer_mail: customer_mail, view: view, accounting: accounting, controller: controller, project_code: project_code}, { headers });
  }
  edit_customer_view_auth(project_code: string, customer: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/edit_customer_view_auth`, {project_code: project_code, customer: customer}, { headers });
  }
  get_events_list(project_code: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_events_list`, {project_code: project_code}, { headers });
  }

  get_events_detail(event_id: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_events_detail`, {event_id: event_id}, { headers });
  }
  // 回報事件錯誤
  report_event_error(eventId: string, stageKey: string, errorMsg: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of({ success: false, message: 'No token found' });
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    
    console.log(`API 呼叫: report_event_error，event_id: ${eventId}, stage: ${stageKey}, error_msg: ${errorMsg}`);
    
    return this.http.post(`${environment.apiBaseUrl}/project/report_event_error`, 
      { event_id: eventId, stage: stageKey, error_msg: errorMsg }, 
      { headers }
    );
  }

  // 更新受理號碼
  update_acceptance_number(eventId: string, acceptanceNumber: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of({ success: false, message: 'No token found' });
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    
    console.log(`API 呼叫: update_acceptance_number，event_id: ${eventId}, acceptance_number: ${acceptanceNumber}`);
    
    return this.http.post(`${environment.apiBaseUrl}/project/update_acceptance_number`, 
      { event_id: eventId, acceptance_number: acceptanceNumber }, 
      { headers }
    );
  }
  get_invoices_list(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_invoices_list`, {}, { headers });
  }

  get_invoices_pdf(invoiceNumber: string): Observable<Blob | null> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
  
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/project/get_invoices_pdf`, {invoice_number: invoiceNumber}, {headers, responseType: 'blob'});
  }
  
  // 更新受理號碼
  updateAcceptanceNumber(eventId: string, acceptanceNumber: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of({ success: false, message: '未找到令牌，請重新登入' });
    }
    
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    
    return this.http.post(`${environment.apiBaseUrl}/project/update_acceptance_number`, {event_id: eventId, acceptance_number: acceptanceNumber}, { headers })
      
  }
  updatePaymentInfo(eventId: string, paymentDate: string, amount: number): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of({ success: false, message: '未找到令牌，請重新登入' });
    }
    
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    
    return this.http.post(`${environment.apiBaseUrl}/project/update_payment_info`, {
      event_id: eventId, 
      payment_date: paymentDate,
      amount: amount
    }, { headers });
  }
  
  // 通用階段資訊更新
  updateStageInfo(eventId: string, stageKey: string, formData: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of({ success: false, message: '未找到令牌，請重新登入' });
    }
    
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    
    // 準備要提交的數據
    const requestData = {
      event_id: eventId,
      stage: stageKey,
      ...formData
    };
    
    return this.http.post(`${environment.apiBaseUrl}/project/update_stage_info`, requestData, { headers });
  }
  
  // 更新受理資訊
  updateAcceptanceInfo(eventId: string, acceptanceData: any): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of({ success: false, message: '未找到令牌，請重新登入' });
    }
    
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    
    // 準備要提交的數據
    const requestData = {
      event_id: eventId,
      ...acceptanceData
    };
    
    return this.http.post(`${environment.apiBaseUrl}/project/update_acceptance_info`, requestData, { headers });
  }
}
