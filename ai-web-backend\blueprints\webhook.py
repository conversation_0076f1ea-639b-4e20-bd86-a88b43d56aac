from flask import request, Blueprint, jsonify, current_app, send_file, send_from_directory, abort, url_for
from accessories import mongo,update_json_in_mongo,save_json_to_mongo,sqldb,crawler_redis_client
webhook_page = Blueprint('webhook', __name__)
@webhook_page.route('/XOB1_AN', methods=['OPTIONS', 'POST'])
def XOB1_application_number_webhook():
    if request.method == 'OPTIONS':
        return '', 204
    data = request.get_json()
    token = request.headers.get("X-BizForm-Token")
    if token != '41wcfrZWRIwmEW7CYSwEtLpAgd4iIMfR':
        return jsonify({"error": "Unauthorized"}), 401
    document = data.get('document')
    attributes = document.get('attributes', [])
    for attribute in attributes:
        if attribute.get('displayName') == '受理號碼':
            ProcessFormNumber = attribute.get('value')[0]
        if attribute.get('displayName') == '統編戶名':
            bizform_place_id = attribute.get('value')[0][:11]
            print(123)
    events_list = [
        doc for doc in mongo.db.event.find(
            {
                "event_type": "electricity_plan_change",
                "Process Form Number": {"$exists": False}
            },
            {"_id": 0}
        )
    ]
    for event in events_list:
        
        print(123)


