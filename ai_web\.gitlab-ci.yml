image: node:20

stages:
  - build and deploy

build:
  stage: build and deploy
  before_script:
    - apt-get update && apt-get install -y openssh-client tar

  script:
    - echo "Setting up SSH..."
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ima@************* "mkdir -p /home/<USER>/ai_web_temp"

    # 傳輸檔案到本機
    - npm install
    - npm install -g @angular/cli
    - npm add joint-plus.tgz
    - npm add joint-vsm-shapes.tgz
    - ng build --configuration test_env
    - tar -czvf ima_ai_web.tar.gz -C dist ima_ai_web
    - scp ima_ai_web.tar.gz ima@*************:/home/<USER>/ai_web_temp/

    # 在本機解壓縮並重啟 Nginx
    - |
      ssh -o StrictHostKeyChecking=no ima@************* << 'EOF'
        sudo mkdir -p /home/<USER>/ai_web_temp
        cd /home/<USER>/ai_web_temp
        tar -xzvf ima_ai_web.tar.gz
        sudo rm -rf /usr/share/nginx/html/ima_ai_web
        sudo mv ima_ai_web /usr/share/nginx/html/
        sudo rm -r /home/<USER>/ai_web_temp
        sudo systemctl restart nginx
      EOF
