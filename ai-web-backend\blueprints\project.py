from flask import request, Blueprint, jsonify, send_file
from accessories import mongo,sqldb,crawler_redis_client, mongo, save_json_to_mongo,update_json_in_mongo, remove_json_in_mongo,send_mail,redis_client
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required, role_required
from bson.objectid import ObjectId
from flask import current_app
from datetime import datetime
from sqlalchemy import text
import os
from sqlalchemy.orm import sessionmaker
import json

project_page = Blueprint('project', __name__)

@project_page.route('/get_project_list', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_project_list(token):    
    projectcodeList = get_user_info(token, 'project')
    projectList=[]
    if projectcodeList:
        projectList = [{"project_code": mongo.db.project.find_one({"project_code": x}).get('project_code'),
                        "project_name": mongo.db.project.find_one({"project_code": x}).get('project_name'),
                        "project_leader": mongo.db.project.find_one({"project_code": x}).get('project_leader'),
                        "state": mongo.db.project.find_one({"project_code": x}).get('state'),
                        "create_time": mongo.db.project.find_one({"project_code": x}).get('create_time')} for x in projectcodeList]
    ## fix proeject leader to email here
    ## remove this section after all the leader data are changed to email
    ## section start
    fix_project_leader = False
    for i in projectList:
        if '@' not in i["project_leader"]:
            fix_project_leader = True
            leader_name = i['project_leader']
            leader_email = mongo.db.user.find_one({"firstname": leader_name[:leader_name.index(',')], 'lastname': leader_name[leader_name.index(',')+2:]}).get('email')
            id = mongo.db.project.find_one({"project_code": i.get('project_code')}).get('_id')
            update_json_in_mongo({"project_leader": leader_email}, 'project', id)
    if fix_project_leader:
        projectList = [{"project_code": mongo.db.project.find_one({"project_code": x}).get('project_code'),
                        "project_name": mongo.db.project.find_one({"project_code": x}).get('project_name'),
                        "project_leader": mongo.db.project.find_one({"project_code": x}).get('project_leader'),
                        "state": mongo.db.project.find_one({"project_code": x}).get('state'),
                        "create_time": mongo.db.project.find_one({"project_code": x}).get('create_time')} for x in projectcodeList]
    ## section end
    return jsonify({
        "token": get_refresh_token(token),
        "Project_List": projectList}), 200


@project_page.route('/create_project', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_project(token):
    data = request.get_json()
    project_code = data.get('project_code')
    project_name = data.get('project_name')

    if not project_code or not project_name:
        return jsonify({'message': 'Project code and name are required!'}), 400

    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    if not user:
        return jsonify({'message': 'User not found!'}), 404
    if mongo.db.project.find_one({"project_code": project_code}):
        return jsonify({'message': 'Project code existed!'}), 404
    
    user_project_list = user['project']
    user_project_list.append(project_code)

    update_json_in_mongo({"project": user_project_list}, 'user', user["_id"])

    save_json_to_mongo({
        'project_code': project_code,
        'project_name': project_name,
        'project_leader': user['email'],
        'gateway': [],
        'state': '未生效'
    }, 'project', ObjectId())

    return jsonify({"token": get_refresh_token(token)}), 200


@project_page.route('/remove_project', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_project(token):
    data = request.get_json()
    project_code = data.get('project_code')

    if not project_code:
        return jsonify({'message': 'Project code and name are required!'}), 400

    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    project = mongo.db.project.find_one({"project_code": project_code})
    if not user:
        return jsonify({'message': 'User not found!'}), 404
    user_project_list = user['project']
    user_project_list.remove(project_code)


    update_json_in_mongo({"project": user_project_list}, 'user', user["_id"])
    remove_json_in_mongo('project', project["_id"])

    return jsonify({"token": get_refresh_token(token)}), 200

@project_page.route('/leave_project', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def leave_project(token):
    data = request.get_json()
    project_code = data.get('project_code')

    if not project_code:
        return jsonify({'message': 'Project code and name are required!'}), 400

    user_email = get_user_info(token, 'email')
    user = mongo.db.user.find_one({"email": user_email})
    project = mongo.db.project.find_one({"project_code": project_code})
    if not user:
        return jsonify({'message': 'User not found!'}), 404
    user_project_list = user['project']
    user_project_list.remove(project_code)
    update_json_in_mongo({"project": user_project_list}, 'user', user["_id"])
    return jsonify({"token": get_refresh_token(token)}), 200

@project_page.route('/get_project_info', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'customer'])
def get_project_info(token):
    data = request.get_json()
    project_code = data.get('project_code')

    if not project_code:
        return jsonify({'message': 'Project code are required!'}), 400
    
    user_email = get_user_info(token, 'email')
    if project_code not in mongo.db.user.find_one({"email": user_email}).get("project"):
        return jsonify({'message': 'Project code existed!'}), 404

    ret = mongo.db.project.find_one({"project_code": project_code}, {"_id": 0})

    return jsonify({
        "token": get_refresh_token(token),
        "data": ret}), 200

@project_page.route('/get_projectplotlist', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_projectplotlist(token):
    data = request.get_json()
    project_code = data.get('project_code')

    if not project_code:
        return jsonify({'message': 'Project code and name are required!'}), 400
    projectplot_list = []
    projectplot_list = list(mongo.db.projectPlot.find({"project_code": project_code}, {"_id": 0}))

    return jsonify({"token": get_refresh_token(token), 
                    "projectplot_list": projectplot_list}), 200


@project_page.route('/get_project_member', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_project_member(token):
    data = request.get_json()
    project_code = data.get('project_code')
    owner = mongo.db.project.find_one({"project_code": project_code}).get("project_leader")
    if not project_code:
        return jsonify({'message': 'Project code and name are required!'}), 400
    project_member_list = []
    for i in list(mongo.db.user.find({"project": {"$in": [project_code]}})):
        if i.get('email') == owner:
            role = 'project leader'
        else:
            role = 'team member'
        project_member_list.append({
            'name': i.get('firstname') + ', ' + i.get('lastname'),
            'email': i.get('email'),
            'role': role
        })
    return jsonify({"token": get_refresh_token(token), 
                    "member_list": project_member_list}), 200

@project_page.route('/get_customer_member', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_customer_member(token):
    data = request.get_json()
    project_code = data.get('project_code')
    customer_member_list = []
    for i in list(mongo.db.user.find({f"customer_project.{project_code}": {"$exists": True}})):
        customer_member_list.append({
            'name': f"{i.get('firstname', '')}, {i.get('lastname', '')}",
            'email': i.get('email'),
            'auth': i.get('customer_project').get(project_code)
        })
    return jsonify({"token": get_refresh_token(token), 
                    "member_list": customer_member_list}), 200

@project_page.route('/create_projectplot', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_projectplot(token):
    data = request.get_json()
    project_code = data.get('project_code')
    projectplot_name = data.get('projectplot_name')

    if not project_code:
        return jsonify({'message': 'Project code and name are required!'}), 400
    if mongo.db.projectPlot.find_one({"project_code": project_code, 'projectplot_name': projectplot_name}):
        return jsonify({'message': 'Project name existed!'}), 400
    
    save_json_to_mongo({
        'project_code': project_code,
        'projectplot_name': projectplot_name,
        'data': {}
    },'projectPlot', ObjectId())

    return jsonify({"token": get_refresh_token(token)}), 200


@project_page.route('/remove_projectplot', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_projectplot(token):
    data = request.get_json()
    project_code = data.get('project_code')
    projectplot_name = data.get('projectplot_name')
    project = mongo.db.projectPlot.find_one({"project_code": project_code, 'projectplot_name': projectplot_name})
    if not project_code:
        return jsonify({'message': 'Project code and name are required!'}), 400
    
    remove_json_in_mongo('projectPlot', project["_id"])

    for fname in os.listdir(current_app.config['PJPLOTBG_UPLOAD_FOLDER']):
        name, ext = os.path.splitext(fname)
        if name == f"bg_{project_code}_{projectplot_name}":
            os.remove(os.path.join(current_app.config['PJPLOTBG_UPLOAD_FOLDER'], fname))

    for i in list(mongo.db.user.find({f"customer_project.{project_code}.view_plot": {"$in": [projectplot_name]}})):
        customer_project = i.get("customer_project")
        customer_project[project_code]["view_plot"].remove(projectplot_name)
        update_json_in_mongo({f"customer_project": customer_project}, 'user', i.get("_id"))
        
    return jsonify({"token": get_refresh_token(token)}), 200




@project_page.route('/add_customer', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def add_customer(token):
    data = request.get_json()
    customer_mail = data.get('customer_mail')
    view = data.get('view')
    accounting = data.get('accounting')
    controller = data.get('controller')
    project_code = data.get('project_code')
    customer_info = mongo.db.user.find_one({'email': customer_mail})
    customer_id = customer_info.get('_id')
    if not customer_info:
        return jsonify({'message': 'Email not found'}), 401
    customer_role = customer_info.get('role')
    customer_project = customer_info.get('customer_project', {})
    customer_project[project_code] = {
        "view": view,
        "accounting": accounting,
        "controller": controller,
        "view_plot": [],
        "accounting_place_id": [],
        "controller_parameters": []
    }
    if 'customer' not in customer_role:
        customer_role.append('customer')
        update_json_in_mongo({'role': customer_role, 'customer_project': customer_project}, 'user', customer_id)
    else:
        update_json_in_mongo({'customer_project': customer_project}, 'user', customer_id)
    return jsonify({"token": get_refresh_token(token)}), 200

    
@project_page.route('/edit_customer_view_auth', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def edit_customer_view_auth(token):
    data = request.get_json()
    project_code = data.get("project_code")
    customer_info = data.get("customer")
    customer = mongo.db.user.find_one({"email": customer_info.get("email")})
    customer_auth_data = customer.get("customer_project")
    customer_auth_data[project_code] = customer_info.get("auth")
    update_json_in_mongo({"customer_project": customer_auth_data}, 'user', customer.get("_id"))
    return jsonify({"token": get_refresh_token(token)}), 200

@project_page.route('/remove_customer', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_customer(token):
    data = request.get_json()
    project_code = data.get('project_code')
    customer_email = data.get('customer_email')
    customer = mongo.db.user.find_one({"email": customer_email})
    customer_project = customer.get('customer_project')
    customer_project.pop(project_code, None)
    customer_role_list = get_user_info(token, 'role')

    if len(customer_project) == 0:
        customer_role_list.remove('customer')
    update_json_in_mongo({'customer_project': customer_project,
                            'role': customer_role_list}, 'user', customer.get("_id"))
    
    return jsonify({"token": get_refresh_token(token)}), 200


@project_page.route('/create_place_id', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_place_id(token):
    data = request.get_json()
    project_code = data.get('project_code')
    place_id = data.get('place_id')
    id = mongo.db.project.find_one({'project_code': project_code}).get("_id")
    place_id_list = mongo.db.project.find_one({'project_code': project_code}).get("place_id") or []
    if place_id in place_id_list:
        return jsonify({'message': 'Repeat place_id!'}), 401
    
    '''
    電號資料庫建立後，這裡檢查是否重複創建
    
    '''
    place_id_list.append(place_id)
    update_json_in_mongo({"place_id": place_id_list}, 'project', id)

    return jsonify({"token": get_refresh_token(token)}), 200 


@project_page.route('/remove_place_id', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_place_id(token):
    data = request.get_json()
    project_code = data.get('project_code')
    place_id = data.get('place_id')
    id = mongo.db.project.find_one({'project_code': project_code}).get("_id")
    place_id_list = mongo.db.project.find_one({'project_code': project_code}).get("place_id") or []
    if place_id not in place_id_list:
        return jsonify({'message': 'Place_id not found!'}), 401
    
    '''
    電號資料庫建立後，這裡檢查是否重複創建
    
    '''
    place_id_list.remove(place_id)
    update_json_in_mongo({"place_id": place_id_list}, 'project', id)
    

    return jsonify({"token": get_refresh_token(token)}), 200 

@project_page.route('/get_events_list', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_events_list(token):
    data = request.get_json()
    event_list = mongo.db.events.find()
    event_data = [{'Application_Date': events.get('last_update'), 'Application_Item': events.get('event_name'), 'STATUS': events.get('event_type'), 'event_id': events.get('event_id')} for events in event_list]
    print(event_data)
    return jsonify({"token": get_refresh_token(token), "event_data": event_data}), 200

@project_page.route('/get_events_detail', methods=['OPTIONS', 'POST'])
@role_required(['project leader', 'TPC_Officer'])
def get_events_detail(token):
    data = request.get_json()
    event_id = data.get('event_id') 
    # 獲取當前用戶資訊
    user_email = get_user_info(token, 'email')
    user_roles = get_user_info(token, 'role')
    
    # 獲取事件基本資訊
    event_info = mongo.db.events.find_one({'event_id': event_id})
    
    # 獲取事件歷史詳情並確保包含錯誤信息
    event_detail = list(mongo.db.events_history.find({'event_id': event_id}))
    # 檢查是否需要從主表同步狀態
    main_status = event_info.get('status')
    main_stage = event_info.get('stage')
    main_error_msg = event_info.get('error_msg')
    
    # 格式化事件歷史詳情
    formatted_event_detail = []
    for i in event_detail:
        detail = {
            'name': i.get('name'), 
            'create_time': i.get('create_time'),  
            'last_updated': i.get('last_updated'),
            'status': i.get('status'), 
            'event_id': i.get('event_id'),
            'stage': i.get('stage'), 
            'error_msg': i.get('error_msg')
        }
        
        # 如果當前階段與主表階段相同，同步狀態和錯誤信息
        if i.get('stage') == main_stage:
            # 同步狀態和錯誤信息
            detail['status'] = main_status
            if main_error_msg:
                detail['error_msg'] = main_error_msg
            
            # 同步更新數據庫，確保歷史記錄與主表一致
            if i.get('status') != main_status or (main_error_msg and i.get('error_msg') != main_error_msg):
                update_data = {'status': main_status}
                if main_error_msg:
                    update_data['error_msg'] = main_error_msg
                #update_json_in_mongo(update_data, 'event_history', i.get('_id'))
        
        formatted_event_detail.append(detail)
    
    # 格式化事件基本信息
    event_basic_info = {
        'event_type': event_info.get('event_type'),
        'create_time': event_info.get('create_time'),
        'status': main_status,
        'stage': main_stage,
        'project_name': event_info.get('project_name'),
        'project_code': event_info.get('project_code'),
        'update_time': event_info.get('last_updated'),
        'last_updated': event_info.get('last_updated'),
        'acceptance_number': event_info.get('Acceptance_Number'),
        'payment_info': event_info.get('payment_info'),
        'error_msg': main_error_msg
    }
    
    return jsonify({
        "token": get_refresh_token(token), 
        "eventdetail": formatted_event_detail,
        "event_info": event_basic_info,
        "user_roles": user_roles,
        "acceptance_number": event_info.get('Acceptance_Number')
    }), 200


@project_page.route('/report_event_error', methods=['OPTIONS', 'POST'])
@role_required(['TPC_Officer'])
def report_event_error(token):
    data = request.get_json()
    event_id = data.get('event_id')
    error_msg = data.get('error_msg')
    officer = get_user_info(token,'email')
    
    # 獲取事件信息
    event = mongo.db.events.find_one({'event_id': event_id})
    event_info_id = event.get("_id")
    project_name = event.get('project_name')
    project_leader = event.get('role', {}).get('project leader')
    
    # 只更新事件主表，update_json_in_mongo 會自動處理歷史記錄
    update_data = {
        "status": "error",
        "error_msg": error_msg
    }
    update_json_in_mongo(update_data, 'events', event_info_id)
    send_mail(officer, project_leader, "用電變更-台電受理錯誤", f"{project_name}的台電受理錯誤 - 錯誤原因: <br>{error_msg}")
    return jsonify({'token': get_refresh_token(token),}), 200


@project_page.route('/update_acceptance_number', methods=['OPTIONS', 'POST'])
@role_required(['TPC_Officer'])
def update_acceptance_number(token):
    data = request.get_json()
    event_id = data.get('event_id')
    acceptance_number = data.get('acceptance_number')
    event = mongo.db.events.find_one({'event_id': event_id})
    event_id_obj = event.get("_id")
    thress_name = event.get('place_id_info',{}).get('company_id_name')
    update_data = {
        "Acceptance_Number": acceptance_number,
        "status": "completed"
    }
    update_json_in_mongo(update_data, "events", event_id_obj)
    crawler_redis_client.rpush("case_inquiry_crawler_key",json.dumps({"thress_name": thress_name, "acceptance_number": acceptance_number}))
    return jsonify({"token": get_refresh_token(token)})
  




strings = [
        'place_id', 'bill_date', 'period', 'period_total',
        'baseline_price', 'baseline_kwh', 'baseline_fee',
        'original_kwh', 'original_fee', 'contract_price',
        'contract_percentage', 'actual_price', 'actual_kwh',
        'actual_fee', 'total_benefit', 'ima_serviceCharge',
        'customer_benefit', 'status'
]


@project_page.route('/create_service', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_service():
    if request.method == 'OPTIONS':
        return '', 204
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401

    data = request.get_json()
    print("接收到的原始數據:", data)
    
    if 'service' in data:
        service_wrapper = data.get('service', {})
        
        # 建立要保存到 MongoDB 的文檔
        service_document = {
            "project_name": service_wrapper.get('project_name'),
            "place_id": service_wrapper.get('place_id'),
            "project_address": service_wrapper.get('project_address'),
            "equipment_subsidy": service_wrapper.get('equipment_subsidy'),
            "depreciation_per_period": service_wrapper.get('depreciation_per_period'),
            "reasonable_capacity": service_wrapper.get('reasonable_capacity'),
            "electricity_management": service_wrapper.get('electricity_management', {}),
            "total_periods": service_wrapper.get('total_periods'),
            "totalPeriods": service_wrapper.get('total_periods'),
            "start_time": service_wrapper.get('start_time'),
            "default_description": service_wrapper.get('default_description', ''),
            "default_profit": service_wrapper.get('default_profit'),
            "service_type": service_wrapper.get('service', {}).get('service_type')
        }
        
        # 處理服務細節
        service_details = service_wrapper.get('service', {}).get('DETAILS', {})
        if service_wrapper.get('service', {}).get('service_type') == '電價':
            service_document["DETAILS"] = {
                "isRetroactive": service_details.get('isRetroactive'),
                "baselineStart": service_details.get('baselineStart'),
                "baselineEnd": service_details.get('baselineEnd'), 
                "retrospectDate": service_details.get('retrospectDate'),
                "minUsageKwh": service_details.get('minUsageKwh'),
                "pastUsageMode": service_details.get('pastUsageMode'),
                "baselineMethod": service_details.get('baselineMethod'),
                "profitSharing": service_details.get('profitSharing')
            }
        
        print("準備保存到MongoDB的文檔:", service_document)

        try:
            save_json_to_mongo(
                service_document,
                'service',
                ObjectId()
            )
        except Exception as e:
            print(f"保存服務時發生錯誤: {str(e)}")
            return jsonify({'message': '服務創建失敗！'}), 500

    return jsonify({"token": get_refresh_token(token)}), 200
    
  
@project_page.route('/get_baseline_cost', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_baseline_cost():
    if request.method == 'OPTIONS':
        return '', 204
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401

    data = request.get_json()
    baseline_usage = data.get('baselineUsage')
    project_name = data.get('projectName')
    place_id = data.get('placeId')
    service_type = data.get('serviceName')
    date = data.get('date')
    
    print(baseline_usage, project_name, place_id, service_type, date)
    
    return jsonify({
        "token": get_refresh_token(token)
    }), 200
    

@project_page.route('/get_place_bills', methods=['OPTIONS', 'POST']) ## project 更改從這開始----------------------------------------------
@roles_required(['project leader'])
def get_place_bills():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401

    # SQL 查詢
    return jsonify({
        "token": get_refresh_token(token)
    }), 200

@project_page.route('/calculate_baseline', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def calculate_baseline():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    
    import_electricity_price()

    data = request.get_json()
    type_value = data.get('type')  # 改為 baselineType
    place_id = data.get('place_id')
    retrospect_date = data.get('retrospectStartDate')  # 格式應為 'YYYY-MM-DD'
    usage_mode = data.get('usageMode')
    formatted_date = retrospect_date.replace('-', '')
    baselineType = data.get('baselineType')
    
    print(f"基線類型: {type_value}, 場所編號: {place_id}, 日期: {formatted_date}, 用電模式: {usage_mode} , 時間time: {retrospect_date}")
    
    # 建立基線物件
    Baseline_object = {
        'summer_cost': None,
        'non_summer_cost': None,
        'summer_kwh': None,
        'non_summer_kwh': None,
        'summer_avg_cost': None,
        'non_summer_avg_cost': None,
        'total_kwh': None,
        'min_kwh': None
    }
    
   
    if baselineType == 'b':  # 回推基線
            # 使用 Pushback_baseline_cost
            (
                Baseline_object['summer_cost'],
                Baseline_object['non_summer_cost'],
                Baseline_object['total_kwh'],
                Baseline_object['summer_avg_cost'],
                Baseline_object['non_summer_avg_cost'],
                Baseline_object['min_kwh']
            ) = Pushback_baseline_cost(place_id, formatted_date, '20241026')
            
    elif baselineType == 'p':  # 年度基線
        # 分割並處理日期範圍
        start_date, end_date = retrospect_date.split('-')
        print(f"年度基線計算範圍: {start_date} 到 {end_date}")
        
        # 使用 Year_baseline_cost
        (
            Baseline_object['summer_cost'],
            Baseline_object['summer_kwh'], 
            Baseline_object['non_summer_cost'],
            Baseline_object['non_summer_kwh'],
            Baseline_object['summer_avg_cost'],
            Baseline_object['non_summer_avg_cost'],
            Baseline_object['min_kwh'],
            Baseline_object['monthly_records']
        ) = Year_baseline_cost(start_date, end_date, place_id, '20241026')
        print(Baseline_object)
   

    return jsonify({
            "token": get_refresh_token(token),
            "Baseline_object": Baseline_object
        }), 200
     

@project_page.route('/get_device_reference', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_device_reference():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401

    data = request.get_json()
    database = data.get('database')
    table_name = data.get('tableName')
    id_field = data.get('id')
    device_name = data.get('deviceName')

    # SQL 查詢
    print("\n=== SQL Query Results ===")
    engine = sqldb.get_engine(current_app, bind='196')
    
    try:
        with engine.connect() as connection:
            query = text(f"""
                SELECT {device_name} 
                FROM ima_thing.{id_field}
                WHERE id = :table_name
                LIMIT 1;
            """)
            
            result = connection.execute(query, {
                "table_name": table_name
            })
            
            row = result.fetchone()
            value = getattr(row, device_name) if row else None
            
            print(f"找到值: {value}")
            
    except Exception as e:
        print(f"查詢錯誤: {str(e)}")
        return jsonify({
            "token": get_refresh_token(token),
            "message": "查詢失敗",
            "error": str(e)
        }), 500

    return jsonify({
        "token": get_refresh_token(token),
        "value": value
    }), 200

@project_page.route('/create_bill', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_bill():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401

    data = request.get_json()
    print("接收到的帳單數據:", data)
    
    if 'billData' in data:
        bill_data = data.get('billData', {})
        
        # 建立要保存到 MongoDB 的帳單文檔
        bill_document = {
            "project_code": bill_data.get('projectCode'),
            "project_name": bill_data.get('projectName'),
            "place_id": bill_data.get('placeId'),
            "service_type": bill_data.get('serviceName'),
            "period": bill_data.get('period'),
            "date": bill_data.get('date'),
            "billing_cycle": bill_data.get('billingCycle'),
            "actual_price": bill_data.get('actual_price'),
            "actual_kwh": bill_data.get('actual_kwh'), 
            "actual_fee": bill_data.get('actual_fee'),
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
          
        }
        
        print("準備保存到MongoDB的帳單文檔:", bill_document)

        try:
            save_json_to_mongo(
                bill_document,
                'Tpc_bills',
                ObjectId()
            )
        except Exception as e:
            print(f"保存帳單時發生錯誤: {str(e)}")
            return jsonify({'message': '帳單創建失敗！'}), 500

    return jsonify({
            "token": get_refresh_token(token),
            "message": "帳單創建成功"
        }), 200
    ## project 更改從這結束-------------------------------------------------------------------------------------------------------------------




@project_page.route('/get_contract_info', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_contract_info():
    if request.method == 'OPTIONS':
        return '', 204
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    
    data = request.get_json()
    project_code = data.get('project_code')
    contract_id = data.get('contract_id')

    if not project_code:
        return jsonify({'message': 'Project code are required!'}), 400
    
    if not contract_id:
        return jsonify({'message': 'Contract id are required!'}), 400
    
    user_email = get_user_info(token, 'email')
    if project_code not in mongo.db.user.find_one({"email": user_email}).get("project"):
        return jsonify({'message': 'Project code existed!'}), 404

    ret_project = mongo.db.project.find_one({"project_code": project_code}, {"_id": 0})
    ret_contract = mongo.db.contract.find_one({"contract_id": contract_id}, {"_id": 0})

    return jsonify({
        "token": get_refresh_token(token),
        "data": {ret_project, ret_contract}}), 200
             
             

@project_page.route('/get_adiut_data', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_adiut_data():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    
    data = request.get_json()
    print(data)
    place_id = data.get('re_place_id')
    service_type = data.get('servicename')
    period = data.get('period')

    recent_bill = mongo.db.ima_bill.find({"place_id": place_id})

    recent_bill_data = []
    
   

    recent_bill_data = [
        {field: bill.get(field) for field in strings}
        for bill in recent_bill
    ]
    
    record_bill_data = [bill for bill in recent_bill_data if bill.get('status') == 1]
    current_data = [bill for bill in recent_bill_data if bill.get('status') == 0]
    
    print(f"歷史帳單數量: {len(record_bill_data)}")
    print(f"當期帳單數量: {len(current_data)}")
    print("\n歷史帳單資料:")
    for bill in record_bill_data:
        print(bill)
    print("\n當期帳單資料:")  
    for bill in current_data:
        print(bill)
        #pass
    return jsonify({
        "token": get_refresh_token(token),
        "record_bill_data": record_bill_data,
        "current_data": current_data
        }), 200


@project_page.route('/aduit_service_update', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def aduit_service_update():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    
    data = request.get_json().get('data')
    print(data)
    place_id = data.get('place_id')
    service_type = data.get('service_type')
    period = data.get('period')
    baseline_price = data.get('baselinePrice')
    baseline_kwh = data.get('baselineKwh')
    actual_price = data.get('actualPrice')
    actual_kwh = data.get('actualKwh')
    profit_sharing = data.get('profitSharing')
    
    print(place_id, service_type, period, baseline_price, baseline_kwh, actual_price, actual_kwh, profit_sharing)   
    
    ima_bill = mongo.db.ima_bill.find_one({"place_id": place_id , "status": 0})
    service = mongo.db.service.find_one({"place_id": place_id , "service_type": service_type})
    print(service)

    print("更新成功")
        
    update_json_in_mongo({
        "place_id": place_id,
        "status": 1,
        "period": period,
      
    }, 'ima_bill', ima_bill["_id"])
  
    period_str = str(period)  # 將數字轉換為字符串
    
    # 获取现有的change数据
    existing_change = service.get('change', {})
    
    # 构建新的期数数据包
    new_period_data = {
        "baseline_price": baseline_price if baseline_price else None,
        "baseline_kwh": baseline_kwh if baseline_kwh else None,
        "actual_price": actual_price if actual_price else None,
        "actual_kwh": actual_kwh if actual_kwh else None,
        "profit_sharing": profit_sharing if profit_sharing else None
    }
    
    # 過濾掉None值
    new_period_data = {k:v for k,v in new_period_data.items() if v is not None}
    
    # 将新的期数数据添加到现有change数据中
    existing_change[period_str] = new_period_data
    
    # 更新資料庫
    update_data = {
        "change": existing_change
    }
    
    update_json_in_mongo(update_data, 'service', service["_id"])
    return jsonify({"token": get_refresh_token(token),}), 200
    
    
@project_page.route('/get_performance_data', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_performance_data():
    if request.method == 'OPTIONS':
        return '', 204
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith("Bearer "):
        return jsonify({'message': 'Token is missing or invalid!'}), 401

    token = auth_header.split(" ")[1]
    if not verify_token(token):
        return jsonify({'message': 'Invalid token!'}), 401
    
    data = request.get_json()
    print(data)
    place_id = data.get('electricity_id')
    serviceName = data.get('service_name')
    startPeriod = data.get('start_period')
    endPeriod = data.get('end_period')
    print(f"Query conditions: place_id={place_id}, service_name={serviceName}, start_period={startPeriod}, end_period={endPeriod}")
    
    performance_data_list = []
    bill_data = mongo.db.ima_bill.find({"place_id": place_id, "status": 1})
    
    
 

    performance_data_list = [
        {field: bill.get(field) for field in strings}
        for bill in bill_data
    ]
    # 打印封裝後的資料
    print("\n=== Performance Data ===")
    for item in performance_data_list:
        print(f"Record: {item}")
    return jsonify({
        "token": get_refresh_token(token),
        "performance_data": performance_data_list
    }), 200


@project_page.route('/get_invoices_list', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_invoices_list(token):
    if request.method == 'OPTIONS':
        return '', 204


    try:
        engine = sqldb.get_engine()
        Session = sessionmaker(bind=engine)
        session = Session()
        result = session.execute(
            text("SELECT invoice_number FROM web_test.invoice_data")
        )
        invoice_numbers = [row.invoice_number for row in result.fetchall()]

        return jsonify({
            "invoice_numbers": invoice_numbers
        }), 200

    except Exception as e:
        return jsonify({
            "token": get_refresh_token(token)
        }), 500


@project_page.route("/project/get_invoices_pdf", methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_invoices_pdf(token):
    if request.method == 'OPTIONS':
        return '', 204
    
    data = request.get_json()
    invoice_number = data.get("invoice_number")

    root_dir = current_app.config.get("INVOICE_UPLOAD_FOLDER", "./images/invoice")

    if not invoice_number:
        return jsonify({"error": "Missing invoice_number"}), 400

    for folder in os.listdir(root_dir):
        folder_path = os.path.join(root_dir, folder)
        if not os.path.isdir(folder_path):
            continue

        file_path = os.path.join(folder_path, f"{invoice_number}.pdf")
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True)

    return jsonify({ "token": get_refresh_token(token)}), 404

@project_page.route('/update_payment_info', methods=['OPTIONS', 'POST'])
@role_required(['project leader'])
def update_payment_info(token):
    data = request.get_json()
    event_id = data.get('event_id')
    payment_date = data.get('payment_date')
    amount = data.get('amount')
    print(f"event_id: {event_id}, payment_date: {payment_date}, amount: {amount}")
    
    # 獲取事件信息
    event = mongo.db.events.find_one({'event_id': event_id})
    event_id_obj = event.get("_id")
    acceptance_number = event.get("Acceptance_Number")
    # 更新事件主表
    update_data = {
        "payment_info": {
            "payment_date": payment_date,
            "amount": amount
        },
    }
    update_json_in_mongo(update_data, "events", event_id_obj,save_history=False )#再想想怎麼做
    # 將任務發送到 Redis 隊列以供爬蟲處理
    if acceptance_number:
        crawler_task = {
            "acceptance_number": acceptance_number,
            "amount": float(amount)
        }
        crawler_redis_client.rpush("tw_bank_crawler_request", json.dumps(crawler_task))
    return jsonify({"token": get_refresh_token(token)})
