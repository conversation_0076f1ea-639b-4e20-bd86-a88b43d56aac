<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>選擇閘道</strong>
      </c-card-header>
      <c-card-body>
        <form>
          <c-row class="align-items-end mb-3">
            <c-col xs="12" md="6" class="mb-3 mb-md-0">
              <label for="database" class="form-label">資料庫</label>
              <select [(ngModel)]="database" name="database" class="form-select">
                <option value="195">195</option>
                <option value="196">196</option>
                <option value="197">197</option>
              </select>
            </c-col>
            <c-col xs="12" md="6">
              <label for="gateway_id" class="form-label">主機ID</label>
              <input
                [(ngModel)]="gateway_id"
                name="gateway_ID"
                class="form-control"
                placeholder="99999999999_0"
                required
              />
            </c-col>
          </c-row>

          <div *ngIf="errMsg" class="alert alert-danger">{{ errMsg }}</div>
          <div *ngIf="pendingMsg" class="alert alert-info">{{ pendingMsg }}</div>
          <div *ngIf="successMsg" class="alert alert-success">{{ successMsg }}</div>

          <div class="d-flex justify-content-end">
            <button
              cButton
              color="primary"
              variant="outline"
              (click)="search()"
            >
              查詢
            </button>
          </div>
        </form>
      </c-card-body>
    </c-card>

    <div *ngIf="show_table.length > 0">
      <c-card-body>
        <c-row *ngFor="let device of show_table" class="mb-4">
          <c-col xs="12">
            <c-card>
              <c-card-header>
                <strong>{{ device.device_id }}</strong>
              </c-card-header>
              <c-card-body class="table-responsive">
                <table cTable [striped]="true" bordered class="mb-0 w-100">
                  <thead>
                    <tr cTableColor="dark">
                      <th *ngFor="let record of device.data" class="text-nowrap">
                        <ng-container *ngFor="let column of getColumnNames(record)">
                          {{ column }}
                        </ng-container>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <ng-container *ngFor="let record of device.data">
                        <td *ngFor="let value of getColumnValues(record)" class="break-word">
                          {{ value === null ? 'None' : value }}
                        </td>
                      </ng-container>
                    </tr>
                  </tbody>
                </table>
              </c-card-body>
            </c-card>
          </c-col>
        </c-row>
      </c-card-body>
    </div>
  </c-col>
</c-row>
