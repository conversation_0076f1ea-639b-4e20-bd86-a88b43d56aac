<!--sidebar-->
<c-sidebar
  #sidebar1="cSidebar"
  class="d-print-none sidebar sidebar-fixed border-end"
  colorScheme="dark"
  id="sidebar1"
  visible
>
<c-sidebar-header >
  <c-sidebar-brand [routerLink]="['/home']" class="sidebar-brand d-flex align-items-center">
    <img src="assets/imalogo_white.ico" alt="Your Logo" class="sidebar-brand-full mx-1" height="32" width="32">
    <div *ngIf="!sidebar1.unfoldable" class="d-flex flex-column ms-2" style="cursor: pointer;">
      <span class="fw-bold" style="font-size: 1rem;">IMA-One</span>
      <small class="text-muted" style="line-height: 1;">{{enterprise.name}}</small>
    </div>
  </c-sidebar-brand>

</c-sidebar-header>
  <ng-scrollbar #scrollbar="ngScrollbar" (updated)="onScrollbarUpdate(scrollbar.state)" class="overflow" pointerEventsMethod="scrollbar" visibility="hover">
    <c-sidebar-nav #overflow [navItems]="navItems" dropdownMode="close" compact />
  </ng-scrollbar>
</c-sidebar>

<!--main-->
<div class="wrapper d-flex flex-column min-vh-100">
  <!--app-header-->
  <app-enterprise-header
    [cShadowOnScroll]="'sm'"
    class="mb-4 d-print-none header header-sticky p-0 shadow-sm"
    position="sticky"
    sidebarId="sidebar1"
  />
  <!--app-body-->
  <div class="body flex-grow-1">
    <c-container breakpoint="lg" class="h-auto px-4">
      <router-outlet />
    </c-container>
  </div>
  <!--app footer-->
  <app-enterprise-footer />
</div>
