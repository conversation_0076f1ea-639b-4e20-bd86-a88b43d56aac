image: python:3.9

before_script:
  - apt-get update
  - apt-get install -y build-essential libffi-dev python3-dev python3-pip

stages:
  - build and deploy
  
build and deploy:
    stage: build and deploy
    script:
        - echo "Setting up SSH..."
        - mkdir -p ~/.ssh
        - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
        - chmod 600 ~/.ssh/id_rsa
        - ssh -o StrictHostKeyChecking=no ima@************* "mkdir -p /home/<USER>/tpc_crawler"
        - ssh -o StrictHostKeyChecking=no ima@************* "sudo rm -r /home/<USER>/tpc_crawler"
        - scp -r . ima@*************:/home/<USER>/tpc_crawler
        - |
          ssh ima@************* << 'EOF'
          cd /home/<USER>/tpc_crawler
          mkdir -p vPic
          python3 -m venv venv
          . venv/bin/activate
          pip install -r requirements.txt
          playwright install --with-deps
          sudo apt update && sudo apt install -y \
          libnss3 libatk1.0-0 libatk-bridge2.0-0 libcups2 libxcomposite1 \
          libxdamage1 libxrandr2 libgbm1 libxss1 libasound2 
          wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
          sudo apt install -y ./google-chrome-stable_current_amd64.deb
          sudo apt install tesseract-ocr -y
          sudo apt update && sudo apt install -y fonts-wqy-zenhei fonts-noto-cjk
          sudo apt install -y xvfb
          sudo apt install -y tinyproxy
          EOF
