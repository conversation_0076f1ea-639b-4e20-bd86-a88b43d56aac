import { DeviceService } from '../../../service/device.service';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  CardBodyComponent,
  CardComponent,
  CardHeaderComponent,
  ColComponent,
  TableModule,
  GridModule,
  InputGroupComponent
} from '@coreui/angular';
import { RowComponent, TextColorDirective, FormControlDirective, FormLabelDirective, FormCheckInputDirective, FormSelectDirective, ButtonDirective } from '@coreui/angular';

interface TableData {
  [key: string]: any[];
}

@Component({
  selector: 'app-history',
  standalone: true,
  imports: [
    ButtonDirective,
    CardBodyComponent,
    CardComponent,
    CardHeaderComponent,
    ColComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TableModule,
    GridModule,
    InputGroupComponent,
    RowComponent,
    TextColorDirective,
    FormControlDirective,
    FormLabelDirective,
    FormCheckInputDirective,
    FormSelectDirective],
  templateUrl: './history.component.html',
  styleUrl: './history.component.scss'
})
export class HistoryComponent {
  database: string = '197';
  device_id: string = '';
  successMsg: string = '';
  pendingMsg: string = '';
  errMsg: string = '';
  start_date: string = '';
  end_date: string = '';
  order: string = 'DESC';
  limit: number = 1000;
  table: TableData[] = [];
  tableKeys: string[] = [];
  keys: string[] = [];
  constructor(private deviceService: DeviceService) {}
  download_sql() {
    this.pendingMsg = 'Generating File...';
    this.successMsg = '';
    this.errMsg = '';
    this.deviceService.download_sql(this.database, this.device_id, this.start_date, this.end_date, this.order, this.limit).subscribe(
      (response: Blob) => {
        this.successMsg = 'Success!';
        this.pendingMsg = '';
        this.errMsg = '';
        const downloadUrl = window.URL.createObjectURL(response);
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = 'device_' + this.device_id + '_' + this.start_date + '_' + this.end_date + '.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      },
      (error) => {
        this.successMsg = '';
        this.pendingMsg = '';
        this.errMsg = 'Data not found, connection error or authorization error!';
      }
    );
  }
  search_sql() {
    this.pendingMsg = 'Searching...';
    this.successMsg = '';
    this.errMsg = '';
    this.deviceService.search_sql(this.database, this.device_id, this.start_date, this.end_date, this.order, this.limit).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        let responseData = data.data;
        this.keys = [];
        const extractKeys = (obj: any, parentKey = '') => {
          if (Array.isArray(obj)) {
            obj.forEach((item: any) => {
              extractKeys(item, parentKey);
            });
          } else if (typeof obj === 'object' && obj !== null) {
            for (const key in obj) {
              if (obj.hasOwnProperty(key)) {
                const fullKey = parentKey ? `${parentKey}.${key}` : key;
                this.keys.push(fullKey);
                extractKeys(obj[key], fullKey);
              }
            }
          }
        };
        responseData.forEach((item: any) => {
          extractKeys(item);
        });
        this.keys = Array.from(new Set(this.keys));
        let maxLength = Math.max(...responseData.map((obj: any) => {
          return this.keys.reduce((maxLen, key) => {
            const keysArray = key.split('.');
            let value = obj;
            for (const k of keysArray) {
              value = value ? value[k] : undefined;
            }
            return Array.isArray(value) ? Math.max(maxLen, value.length) : maxLen;
          }, 0);
        }));
        let transformedData: any[] = [];
        for (let i = 0; i < maxLength; i++) {
          let row: { [key: string]: any } = {};

          this.keys.forEach((key: string) => {
            const keysArray = key.split('.');
            let value = responseData.find((obj: any) => {
              let temp = obj;
              for (const k of keysArray) {
                temp = temp ? temp[k] : undefined;
              }
              return temp !== undefined;
            });

            if (value) {
              let tempValue = value;
              for (const k of keysArray) {
                tempValue = tempValue ? tempValue[k] : undefined;
              }
              row[key] = Array.isArray(tempValue) && tempValue.length > i ? tempValue[i] : tempValue || null;
            } else {
              row[key] = null;
            }
          });

          transformedData.push(row);
        }
        this.table = transformedData;
        this.tableKeys = this.keys;
        this.successMsg = 'Success!';
        this.pendingMsg = '';
        this.errMsg = '';
        console.log('Transformed Data:', transformedData);
      },
      (error) => {
        this.successMsg = '';
        this.pendingMsg = '';
        this.errMsg = 'Data not found, connection error or authorization error!';
      }
    );
  }




  rowIndex(): number[] {
    return Array.from(
      Array(this.table[0][this.tableKeys[0]].length).keys()
    );
  }

  getColumnData(columnKey: string, index: number): any {
    return this.table[index][columnKey];
  }

}


