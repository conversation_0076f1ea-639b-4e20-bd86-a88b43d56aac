import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders,  HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';
import { enterprise } from '../shared/shared.module';

@Injectable({
  providedIn: 'root'
})
export class GroupService {

  constructor(private http: HttpClient) { }
  searchEnterpriseId(enterpriseId: string): Observable<any> {
  const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null); // 這裡回傳 Observable，確保型別一致
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<any>(`${environment.apiBaseUrl}/group/searchEnterpriseId`, {enterpriseId}, { headers });
  }
  searchZipCode(address: string): Observable<any> {
    const url = 'http://zip5.5432.tw/zip5json.py';
    const params = new HttpParams().set('adrs', address);

    return this.http.get<any>(url, { params });
  }
  create_enterprise(enterprise: enterprise): Observable<any> {
    const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null); // 這裡回傳 Observable，確保型別一致
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post<any>(`${environment.apiBaseUrl}/group/create_enterprise`, {enterprise}, { headers });
  }
  apply_entering_enterprise(taxId: string): Observable<any> {
    const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null); // 這裡回傳 Observable，確保型別一致
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post<any>(`${environment.apiBaseUrl}/group/apply_entering_enterprise`, {taxId: taxId}, { headers });
  }
  get_user_enterprise(): Observable<any> {
    const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null); // 這裡回傳 Observable，確保型別一致
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post<any>(`${environment.apiBaseUrl}/group/get_user_enterprise`, { }, { headers });
  }
  get_user_team(): Observable<any> {
    const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null); // 這裡回傳 Observable，確保型別一致
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post<any>(`${environment.apiBaseUrl}/group/get_user_team`, { }, { headers });
  }
  apply_entering_team(team_code: string): Observable<any> {
    const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found in localStorage');
        return of(null); // 這裡回傳 Observable，確保型別一致
      }
      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
      return this.http.post<any>(`${environment.apiBaseUrl}/group/apply_entering_team`, { team_code }, { headers });
  }
 }

