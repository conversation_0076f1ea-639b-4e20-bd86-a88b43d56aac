from flask import Flask, jsonify, request
from flask_cors import CORS
import sys
from accessories import sqldb, mail, redis_client, token_store, crawler_redis_client, mongo, login_manager
from blueprints.login import login_page
from blueprints.register import register_page
from blueprints.forget_pwd import forgetpwd_page
from blueprints.reset_pwd import resetpwd_page
from blueprints.api import api_page
from blueprints.project import project_page
from blueprints.gateway import gateway_page
from blueprints.inventory import inventory_page
from blueprints.device import device_page
from blueprints.device_dashboard import device_dashboard_page
from blueprints.mailbox import mailbox_page
from blueprints.bill_analysis import bill_analysis_page
from blueprints.role_interaction import role_interaction_page
from blueprints.download import download_page
from blueprints.customer_service import customer_service_page
from blueprints.enterprise import enterprise_page
from blueprints.baseline_module import baseline_page
from blueprints.discover import discover_page
from blueprints.admin import admin_page
from blueprints.group import group_page
from blueprints.team import team_page
from blueprints.home import home_page
from blueprints.crawler import crawler_service_page
from blueprints.webhook import webhook_page
from blueprints.scheduler import scheduler_page
from blueprints.service import service_page
from blueprints.progress import progress_page

from config import Config, ProductionConfig, DevelopmentConfig, TestConfig

# Initialize Flask app
app = Flask(__name__)

# Load configuration based on environment
cfg = Config()
productionCfg = ProductionConfig()
developmentCfg = DevelopmentConfig()
testEnvCfg = TestConfig()
app.config.from_object(cfg)
if len(sys.argv) > 8:
    if sys.argv[-1] == 'production':
        app.config.from_object(productionCfg)
    if sys.argv[-1] == 'test_env':
        app.config.from_object(testEnvCfg)
else:
    app.config.from_object(developmentCfg)

# Enable CORS
CORS(app, resources={r"/*": {"origins": app.config['DOMAIN_NAME']}},
     methods=["GET", "POST", "OPTIONS"],
     allow_headers=["Content-Type", "Authorization"], supports_credentials=True)

# Register blueprints
app.register_blueprint(login_page, url_prefix='/login')
app.register_blueprint(register_page, url_prefix='/register')
app.register_blueprint(forgetpwd_page, url_prefix='/forget-pwd')
app.register_blueprint(resetpwd_page, url_prefix='/reset-pwd')
app.register_blueprint(api_page, url_prefix='/api')
app.register_blueprint(project_page, url_prefix='/project')
app.register_blueprint(gateway_page, url_prefix='/gateway')
app.register_blueprint(inventory_page, url_prefix='/inventory')
app.register_blueprint(device_page, url_prefix='/device')
app.register_blueprint(device_dashboard_page, url_prefix='/device_dashboard')
app.register_blueprint(mailbox_page, url_prefix='/mailbox')
app.register_blueprint(bill_analysis_page,url_prefix='/bill_analysis')
app.register_blueprint(role_interaction_page,url_prefix='/role_interaction')
app.register_blueprint(download_page,url_prefix='/download')
app.register_blueprint(customer_service_page,url_prefix='/customer_service')
app.register_blueprint(admin_page, url_prefix='/admin')
app.register_blueprint(group_page, url_prefix='/group')
app.register_blueprint(team_page, url_prefix='/team')
app.register_blueprint(enterprise_page, url_prefix='/enterprise')
app.register_blueprint(discover_page, url_prefix='/discover')
app.register_blueprint(baseline_page, url_prefix='/baseline_module')
app.register_blueprint(home_page, url_prefix='/home')
app.register_blueprint(crawler_service_page, url_prefix='/crawler')
app.register_blueprint(webhook_page, url_prefix='/webhook')
app.register_blueprint(scheduler_page,url_prefix ='/scheduler')
app.register_blueprint(service_page, url_prefix='/service')
app.register_blueprint(progress_page, url_prefix='/progress')

sqldb.init_app(app)
mail.init_app(app)
redis_client.init_app(app)
crawler_redis_client.init_app(app)
token_store.init_app(app)
mongo.init_app(app)
login_manager.init_app(app)
login_manager.login_view = '/login'


with app.app_context():
    sqldb.create_all()

@app.route('/')
def index():
    return "IMA One Flask App is Running!"

if __name__ == '__main__':
    while True:
        try:
            app.run()
        except Exception as e:
            print(e)
        

