import { Component } from '@angular/core';
import { Routes } from '@angular/router';
import { AuthGuard } from './../../views/auth.guard';

export const routes: Routes = [
  {
    path: '',
    canActivate: [AuthGuard],
    data: {
      title: 'Project'
    },
    children: [
      {
        path: '',
        redirectTo: '',
        pathMatch: 'full'
      },
      {
        path: '',
        canActivate: [AuthGuard],
        loadComponent: () => import('./overview/overview.component').then(m => m.OverviewComponent),
        data: {
          title: 'Overview'
        }
      },
      {
        path: ':project_code',
        loadComponent: () => import('./project-view/project-view.component').then(m => m.ProjectViewComponent),
        data: {
          title: 'Project View'
        }
      },
      {
        path: ':project_code/edit',
        loadComponent: () => import('./project-edit/project-edit.component').then(m => m.ProjectEditComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/list',
        loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/tpc-bills/:place_id',
        loadComponent: () => import('./tpc-bills/tpc-bills.component').then(m => m.TpcBillsComponent),
        data: {
          title: 'TPC Bills'
        }
      },
      {
        path: ':project_code/place-id-info/:place_id',
        loadComponent: () => import('./place-id-info/place-id-info.component').then(m => m.PlaceIdInfoComponent),
        data: {
          title: 'Place ID Info'
        }
      },
      {
        path: ':project_code/plot',
        loadComponent: () => import('./plot/plot.component').then(m => m.PlotComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/dashboard',
        loadComponent: () => import('./dashboard/dashboard.component').then(m => m.DashboardComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/engineering',
        loadComponent: () => import('./engineering/engineering.component').then(m => m.EngineeringComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/performance',
        loadComponent: () => import('./performance/performance.component').then(m => m.PerformanceComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/customer-service',
        loadComponent: () => import('./customer-service/customer-service.component').then(m => m.CustomerServiceComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/event-details',
        loadComponent: () => import('./event-details/event-details.component').then(m => m.EventDetailsComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: ':project_code/:place_id/:service_id/servicedashboard',
        loadComponent: () => import('./service-dashboard/service-dashboard.component').then(m => m.ServiceDashboardComponent),
        data: {
          title: 'Project Edit'
        }
      },
      {
        path: 'event/:event_id/info',
        loadComponent: () => import('./event-details/event-details.component').then(m => m.EventDetailsComponent),
        data: {
          title: 'Event Details'
        }
      },

    ]
  }
];


