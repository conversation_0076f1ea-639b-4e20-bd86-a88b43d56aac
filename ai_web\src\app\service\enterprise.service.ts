import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../environments/environment'
import { Observable, of } from 'rxjs';
import { enterprise, credit_card, bank } from '../shared/shared.module';

@Injectable({
  providedIn: 'root'
})
export class EnterpriseService {

  constructor(private http: HttpClient) { }

  // 獲取企業基本資訊
  get_enterprise_info(taxId: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/get_enterprise_info`, {taxId: taxId}, { headers });
  }
  update_enterprise_info(taxId: string, enterprise: enterprise): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/update_enterprise_info`, {taxId: taxId, enterprise: enterprise}, { headers });
  }

  edit_member_auth(taxId: string, applicant: string, roles: string[]): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/edit_member_auth`, {taxId: taxId, applicant: applicant, roles: roles}, { headers });
  }

  reply_entering_application(taxId: string, applicant: string, reply: boolean): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/reply_entering_application`, {taxId: taxId, applicant: applicant, reply: reply}, { headers });
  }

  remove_member(taxId: string, member: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/remove_member`, {taxId: taxId, member: member}, { headers });
  }

  get_user_enterprise_roles(taxId: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/get_user_enterprise_roles`, {taxId: taxId}, { headers });
  }
  create_team(taxId: string, team_name: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/create_team`, {taxId: taxId, team_name: team_name}, { headers });
  }
  get_enterprise_team(taxId: string): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/get_enterprise_team`, {taxId: taxId}, { headers });
  }
  update_enterprise_payment(taxId: string, payment_method: 'credit' | 'bank', credit_card: credit_card, bank: bank): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/update_enterprise_payment`, {taxId: taxId,
      payment_method: payment_method,
      credit_card: credit_card,
      bank: bank}, { headers });
  }
  update_enterprise_payout(taxId: string, bank: bank): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/enterprise/update_enterprise_payout`, {taxId: taxId,
      bank: bank}, { headers });
  }
}
