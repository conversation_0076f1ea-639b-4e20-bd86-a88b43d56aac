:host {
  .ng-scrollbar {
    --scrollbar-padding: 1px;
    --scrollbar-size: 5px;
    --scrollbar-thumb-color: var(--cui-gray-500, #999);
    --scrollbar-thumb-hover-color: var(--cui-gray-400, #999);
    --scrollbar-hover-size: calc(var(--scrollbar-size) * 1.5);
  }
}

// ng-scrollbar css variables
//.cui-scrollbar {
//  --scrollbar-border-radius: 7px;
//  --scrollbar-padding: 1px;
//  --scrollbar-viewport-margin: 0;
//  --scrollbar-track-color: transparent;
//  --scrollbar-wrapper-color: transparent;
//  --scrollbar-thumb-color: rgba(0, 0, 0, 0.2);
//  --scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);
//  --scrollbar-size: 5px;
//  --scrollbar-hover-size: var(--scrollbar-size);
//  --scrollbar-thumb-transition: height ease-out 150ms, width ease-out 150ms;
//  --scrollbar-track-transition: height ease-out 150ms, width ease-out 150ms;
//}
.sidebar-brand-full {
  width: 32px;  /* 固定宽度 */
  height: 32px; /* 固定高度 */
  object-fit: cover; /* 保持图片比例，避免变形 */
}

.sidebar-brand {
  display: flex;
  align-items: center;
}

.sidebar-brand h5 {
  transition: opacity 0.3s ease;
}

.sidebar.sidebar-unfoldable .sidebar-brand h5 {
  opacity: 0; /* 侧边栏折叠时隐藏文字 */
  visibility: hidden; /* 保证文字在折叠时不占据空间 */
}
