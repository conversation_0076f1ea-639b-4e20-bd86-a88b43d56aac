<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>查詢設備歷史資料</strong>
      </c-card-header>
      <c-card-body>
        <c-input-group class="mb-3">
          <c-row class="g-3">
            <c-col xs="12" md="6" lg="4">
              <label for="database" class="form-label">資料庫</label>
              <select [(ngModel)]="database" cSelect class="form-control" id="database">
                <option value="195">195</option>
                <option value="196">196</option>
                <option value="197">197</option>
              </select>
            </c-col>
            <c-col xs="12" md="6" lg="4">
              <label for="device_ID" class="form-label">設備ID</label>
              <input [(ngModel)]="device_id" name="device_ID" cFormControl class="form-control" placeholder="99999999999_010se" required />
            </c-col>
            <c-col xs="12" md="6" lg="4">
              <label for="start_date" class="form-label">起始日期(選填)</label>
              <input [(ngModel)]="start_date" name="start_date" cFormControl class="form-control" placeholder="YYYY-MM-DD" />
            </c-col>
            <c-col xs="12" md="6" lg="4">
              <label for="end_date" class="form-label">結束日期(選填)</label>
              <input [(ngModel)]="end_date" name="end_date" cFormControl class="form-control" placeholder="YYYY-MM-DD" />
            </c-col>
            <c-col xs="12" md="6" lg="4">
              <label for="limit" class="form-label">搜尋資料筆數 (~10000)</label>
              <input type="number" [(ngModel)]="limit" name="limit" cFormControl class="form-control" placeholder="1000" />
            </c-col>
            <c-col xs="12" md="6" lg="4">
              <label class="form-label d-block">排序(依時間)</label>
              <div class="form-check form-check-inline">
                <input cFormCheckInput type="radio" [(ngModel)]="order" value="DESC" name="sort_order" id="desc" checked />
                <label cFormCheckLabel for="desc">降冪</label>
              </div>
              <div class="form-check form-check-inline">
                <input cFormCheckInput type="radio" [(ngModel)]="order" value="ASC" name="sort_order" id="asc" />
                <label cFormCheckLabel for="asc">升冪</label>
              </div>
            </c-col>
          </c-row>
        </c-input-group>
        <div *ngIf="errMsg" class="alert alert-danger">
          {{ errMsg }}
        </div>
        <div *ngIf="pendingMsg" class="alert alert-info">
          {{ pendingMsg }}
        </div>
        <div *ngIf="successMsg" class="alert alert-success">
          {{ successMsg }}
        </div>
        <button cButton color="primary" (click)="search_sql()" style="float: right;" class="mx-2 text-white">查詢</button>
        <button cButton color="primary" (click)="download_sql()" style="float: right;" variant="ghost">下載</button>
      </c-card-body>
      <c-card-body *ngIf="table.length > 0">
        <c-row class="mb-4">
          <div class="table-responsive">
            <table bordered cTable class="mb-3">
              <thead>
                <tr cTableColor="dark">
                  <th *ngFor="let col of tableKeys">{{ col }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of table">
                  <td *ngFor="let col of tableKeys">{{ row[col] }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </c-row>
      </c-card-body>
    </c-card>


  </c-col>
</c-row>

