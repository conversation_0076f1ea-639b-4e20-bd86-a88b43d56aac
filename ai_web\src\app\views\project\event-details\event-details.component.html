<div class="container-fluid">
  <div *ngIf="isLoading" class="d-flex justify-content-center my-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">載入中...</span>
    </div>
  </div>

  <c-card class="mb-4" *ngIf="!isLoading">
    <c-card-header>
      <h5 class="mb-0">基本資訊</h5>
    </c-card-header>
    <c-card-body>
      <c-row>
        <c-col sm="6">
          <div class="mb-3">
            <label class="form-label fw-bold">事件類型</label>
            <p class="form-control-plaintext">{{ getEventTypeTranslation(eventType) || '未知' }}</p>
          </div>
        </c-col>
        <c-col sm="6">
          <div class="mb-3">
            <label class="form-label fw-bold">申請日期</label>
            <p class="form-control-plaintext">{{ formatDateTime(eventDate) || '未知' }}</p>
          </div>
        </c-col>
      </c-row>
      <c-row>
        <c-col sm="6">
          <div class="mb-3">
            <label class="form-label fw-bold">事件狀態</label>
            <p class="form-control-plaintext">
              <span [class]="'badge ' + getStatusClass(eventStatus)">
                {{ getStatusDisplay(eventStatus, eventStage) || 'unknown' }}
              </span>
            </p>
          </div>
        </c-col>
        <c-col sm="6">
          <div class="mb-3">
            <label class="form-label fw-bold">專案名稱</label>
            <p class="form-control-plaintext">{{ projectName || project_code }}</p>
          </div>
        </c-col>
      </c-row>
      <c-row>
        <c-col sm="6">
          <div class="mb-3">
            <label class="form-label fw-bold">最近更新時間</label>
            <p class="form-control-plaintext">{{ getLastUpdateTime() }}</p>
          </div>
        </c-col>
      </c-row>
      <c-row>
        <c-col sm="12">
          <div *ngIf="errorMsg" class="alert alert-danger mt-2">
            <strong>錯誤訊息：</strong>
            <span>{{ errorMsg }}</span>
          </div>
        </c-col>
      </c-row>
    </c-card-body>
  </c-card>
    <!-- 用電變更申請資料清單區塊 -->
    <c-card class="mb-4" *ngIf="!isLoading">
      <c-card-header>
        <h5 class="mb-0">{{ getEventTypeTranslation(eventType) }} -資料清單</h5>
      </c-card-header>
      <c-card-body>
        <!-- 受理號碼 -->
        <c-row class="mb-3">
          <c-col sm="2">
            <label class="form-label fw-bold">受理號碼</label>
          </c-col>
          <c-col sm="6">
            <ng-container *ngIf="isReadOnlyField('acceptance'); else editableAcceptanceNumber">
              <p class="form-control-plaintext mb-0">{{ acceptanceNumber || '—' }}</p>
            </ng-container>
            <ng-template #editableAcceptanceNumber>
              <div class="input-group">
                <input type="text" class="form-control" [(ngModel)]="acceptanceNumber" (input)="formatAcceptanceNumber()" maxlength="11" placeholder="01-12345678" />
                <button class="btn btn-outline-primary" (click)="openConfirmModal('acceptance')" [disabled]="!acceptanceNumber || !acceptanceNumber.trim()">送出受理號碼</button>
              </div>
            </ng-template>
          </c-col>
        </c-row>
        <!-- 匯款資料 -->
        <c-row class="mb-3">
          <c-col sm="2">
            <label class="form-label fw-bold">匯款資料</label>
          </c-col>
          <c-col sm="10">
            <ng-container *ngIf="isReadOnlyField('payment'); else editablePayment">
              <p class="form-control-plaintext mb-0">
                匯款日期：{{ getStageFieldValue('payment', 'payment_date') || '—' }}<br />
                匯款金額 (TWD)：{{ getStageFieldValue('payment', 'amount') || '—' }}
              </p>
            </ng-container>
            <ng-template #editablePayment>
              <c-row>
                <c-col sm="4">
                  <input type="date" class="form-control form-control-sm" [(ngModel)]="paymentDate" />
                </c-col>
                <c-col sm="4">
                  <input type="number" class="form-control form-control-sm" [(ngModel)]="paymentAmount" placeholder="請輸入匯款金額" />
                </c-col>
                <c-col sm="4">
                  <button type="button" class="btn btn-outline-success btn-sm" (click)="openConfirmModal('payment')" [disabled]="!paymentDate || !paymentAmount">送出匯款資料</button>
                </c-col>
              </c-row>
            </ng-template>
          </c-col>
        </c-row>
      </c-card-body>
    </c-card>
    <!-- 事件流程記錄 -->
    <c-card class="mb-4" *ngIf="!isLoading">
      <c-card-header>
        <h5 class="mb-0">事件流程記錄</h5>
      </c-card-header>
      <c-card-body>
        <div *ngIf="event_detail.length > 0">
          <div *ngFor="let detail of event_detail; let i = index" class="border rounded p-3 mb-3"
               [ngClass]="{
                 'bg-light': detail.status === 'pending',
                 'bg-success bg-opacity-10': detail.status === 'completed',
                 'bg-warning bg-opacity-10': detail.status === 'in_process',
                 'bg-danger bg-opacity-10': detail.status === 'error'
               }">
            <h5>{{ getStageZh(detail.stage) || '未知步驟' }}</h5>
            <c-row>
              <c-col sm="6">
                <div class="mb-2">
                  <label class="form-label fw-bold text-success">狀態</label>
                  <span class="badge bg-info">進行中</span>
                </div>
                <div class="mb-2">
                  <label class="form-label fw-bold text-primary">開始時間</label>
                  <p class="mb-1">{{ formatDateTimeFull(detail['start_time'] || detail['create_time'] || '') || '未設定' }}</p>
                </div>
              </c-col>
              <c-col sm="6">
                <div class="mb-2">
                  <label class="form-label fw-bold text-danger">最後更新時間</label>
                  <p class="mb-1">{{ formatDateTimeFull(detail['end_time'] || detail['last_updated'] || '') || '尚未更新' }}</p>
                </div>
              </c-col>
            </c-row>
          </div>
        </div>
        <div *ngIf="event_detail.length === 0 && stages.length > 0">
          <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle-fill me-2"></i> 此事件尚未有詳細記錄
          </div>
        </div>  
      </c-card-body>
    </c-card>
    <!-- Modal 區塊（只保留一組，結構正確） -->
    <c-modal [visible]="showConfirmModal" (visibleChange)="handleConfirmModalVisibility($event)">
      <c-modal-header>
        <h5 cModalTitle>{{ getConfirmModalTitle() }}</h5>
        <button (click)="closeConfirmModal()" cButtonClose></button>
      </c-modal-header>
      <c-modal-body>
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>
          請確認您要提交的資料：
        </div>
        <!-- 受理號碼確認 -->
        <div *ngIf="confirmType === 'acceptance'">
          <p><strong>受理號碼：</strong>{{ acceptanceNumber }}</p>
        </div>
        <!-- 匯款資料確認 -->
        <div *ngIf="confirmType === 'payment'">
          <p><strong>匯款日期：</strong>{{ paymentDate }}</p>
          <p><strong>匯款金額：</strong>NT$ {{ paymentAmount }}</p>
        </div>
        <p class="text-muted">提交後資料將無法修改，請仔細核對。</p>
      </c-modal-body>
      <c-modal-footer>
        <button cButton color="secondary" (click)="closeConfirmModal()">取消</button>
        <button cButton color="primary" (click)="confirmSubmit()">確認提交</button>
      </c-modal-footer>
    </c-modal>
</div>