import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of ,BehaviorSubject} from 'rxjs';

import { environment } from '../../environments/environment'
import { credit_card, bank, user } from '../shared/shared.module';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private profilePictureSource = new BehaviorSubject<string>('');  // 用戶頭像URL
  currentProfilePicture = this.profilePictureSource.asObservable();
  public userFirstName: string = '';
  public userLastName: string = '';
  profile_picture: string = '';
  constructor(private http: HttpClient) {}

  getUserRoles(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in localStorage');
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/get-user-roles`, {}, { headers });
  }
  get_self_info(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/get_self_info`, {}, { headers });
  }
  getUserAvatar(): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/get-user-avatar`, {}, {headers,responseType: 'blob' });
  }
  update_payment(payment_method: 'credit' | 'bank', credit_card: credit_card, bank: bank): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/update_payment`, {payment_method , credit_card, bank}, {headers });
  }
  update_payout(bank: bank): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/update_payout`, {bank}, {headers });
  }
  update_self_settings(user: user): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/update_self_settings`, { user }, {headers });
  }
  update_user_avatar(file: FormData): Observable<any> {
    const token = localStorage.getItem('token');
    if (!token) {
      return of(null);
    }
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post(`${environment.apiBaseUrl}/api/update_user_avatar`,  file, {headers });
  }
}
