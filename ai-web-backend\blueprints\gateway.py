from flask import request, Blueprint, jsonify, current_app, send_from_directory
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo, redis_client, send_mail
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required
from blueprints.mqtt_worker import send_mqtt_message_without_response, send_mqtt_message_with_response
from bson.objectid import ObjectId
from flask_redis import FlaskRedis
import time, re, os, json, ast
from packaging import version
from config import Config

gateway_page = Blueprint('gateway', __name__)

@gateway_page.route('/get_gateway_list', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_gateway_list(token):
    data = request.get_json()
    project_code = data.get('project_code')
    gateway_list = []
    for i in mongo.db.project.find_one({"project_code": project_code})['gateway']:
        gateway_list.append(
            {
                "project_code": project_code,
                "project_name": mongo.db.project.find_one({"project_code": project_code}).get('project_name'),
                "gateway_id": i,
                "version": mongo.db.gateway.find_one({"Host.HostName": i[:i.index("_")], "Host.SerialNo": i[i.index("_")+1:]})['Host']['Version']
            }
        )
    return jsonify({
        "token": get_refresh_token(token),
        "Gateway_List": gateway_list}), 200

@gateway_page.route('/get_all_gateway_list', methods=['OPTIONS', 'POST'])
@roles_required(['admin'])
def get_all_gateway_list(token):
    gateway_list = []
    for i in list(mongo.db.gateway.find()):
        gateway_list.append(
            {
                "gateway_id": i.get("Host").get("HostName") + "_" + i.get("Host").get("SerialNo"),
                "version": i.get("Host").get('Version')
            }
        )
    return jsonify({
        "token": get_refresh_token(token),
        "Gateway_List": gateway_list}), 200

@gateway_page.route('/get_project_gateway_list', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_project_gateway_list(token):
    data = request.get_json()
    project_code = data.get('project_code')
    gateway_list = []
    for i in mongo.db.project.find_one({"project_code": project_code}).get('gateway'):
        gateway_list.append(
            {
                "gateway_id": i,
                "version": mongo.db.gateway.find_one({"Host.HostName": i[:i.index("_")], "Host.SerialNo": i[i.index("_")+1:]})['Host']['Version']
            }
        )
    return jsonify({
        "token": get_refresh_token(token),
        "Gateway_List": gateway_list}), 200

@gateway_page.route('/create_gateway', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_gateway(token):
    data = request.get_json()
    project_code = data.get('project_code')
    place_id = data.get('place_id')
    object_serialNo = data.get('object_serialNo')
    
    if not project_code or not place_id:
        return jsonify({'message': 'Project code and name are required!'}), 400

    project = mongo.db.project.find_one({"project_code": project_code})
    serial_list = current_app.config['SERIAL_LIST']
    same_place_id_list = list(mongo.db.gateway.find({"Host.HostName": place_id}))
    new_serial = next((serial for serial in serial_list if serial not in {item.get("Host", {}).get("SerialNo") for item in same_place_id_list if "Host" in item and "SerialNo" in item["Host"]}), None)
    if not project:
        return jsonify({'message': 'User not found!'}), 404
    
    
    latest_version = max(os.listdir(current_app.config['HT1_ADDRESS']), key=version.parse)
    
    target = mongo.db.projectPlot_object.find_one({"project_code": project_code, "serialNo": object_serialNo})
    target['gateway_id'] = place_id + '_' + new_serial
    update_json_in_mongo(target, 'projectPlot_object', target.get('_id'))
    
    project_gateway_list = project['gateway']
    project_gateway_list.append(place_id + '_' + new_serial)
    update_json_in_mongo({"gateway": project_gateway_list}, 'project', project["_id"])

    save_json_to_mongo({
      "Host":{
        "HostName": place_id,
        "SerialNo": new_serial,
        "Identical_placeID_dict": {},
        "LogLevel": "INFO",
        "LogFile": "./log/HT1.log",
        "Version": latest_version,
        "CmderServer": {
            "ServerAddress": "imabox-server3.ima-ems.com",
            "ServerPort": 8883,
            "Wait": 60
        }
      },
      "Communication":{
        "WiFi": [
            {
                "ssid": "imaoffice2_5G",
                "psk": "ima42838254",
                "key_mgmt": "WPA-PSK",
                "priority": 4
            },
            {
                "ssid": "imaoffice1",
                "psk": "ima42838254",
                "key_mgmt": "WPA-PSK",
                "priority": 5
            }
        ],
        "PostServer": [
            {
                "ServerAddress": "imabox-server3.ima-ems.com",
                "ServerPort": 8883,
                "Wait": 60
            }
        ]
      },
      "Strategy":{
        "Logics":[]
      },
      "Modbus":{
        "Driver":
        [
            {
                "DriverName": "ADTek-CPM10",
                "PortAddress": "/dev/ttyUSB0",
                "SlaveAddress": 1
            }
        ]
      }
      }, 'gateway', ObjectId())
    
    return jsonify({"token": get_refresh_token(token),
                    "serialNo": new_serial}), 200


@gateway_page.route('/remove_gateway', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def remove_gateway(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    project_code = data.get('project_code')
    gateway = mongo.db.gateway.find_one({"Host.HostName": gateway_id[:gateway_id.index("_")], "Host.SerialNo": gateway_id[gateway_id.index("_")+1:]})
    id = gateway["_id"]
    if not gateway_id or not project_code:
        return jsonify({'message': 'gateway_id and name are required!'}), 400

    project = mongo.db.project.find_one({"project_code": project_code})
    if not project:
        return jsonify({'message': 'User not found!'}), 404

    project_gateway_list = project['gateway']
    project_gateway_list.remove(gateway_id)

    update_json_in_mongo({"gateway": project_gateway_list}, 'project', project["_id"])
    remove_json_in_mongo('gateway', id)
    return jsonify({"token": get_refresh_token(token)}), 200


@gateway_page.route('/load_config', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def load_config(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    if not gateway_id:
        return jsonify({'message': 'gateway_id and name are required!'}), 400
    
    test = False
    for i in get_user_info(token, 'project'):
        if gateway_id in mongo.db.project.find_one({'project_code': i}).get('gateway'):
            test = True
            break
    if not test:
        return jsonify({'message': 'Invalid user!'}), 400

    config = mongo.db.gateway.find_one({"Host.HostName": gateway_id[:gateway_id.index("_")],
                                        "Host.SerialNo": gateway_id[gateway_id.index("_")+1:]})

    if not config:
        return jsonify({'message': 'config not found!'}), 404
    config = dict(filter(lambda item: item[0] in ['Host', 'Communication', 'Strategy', 'Modbus'], config.items()))

    return jsonify({"token": get_refresh_token(token), "config": config}), 200


@gateway_page.route('/submit_config', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def submit_config(token):
    data = request.get_json()
    config = data.get('config')
    set_remote = data.get('set_remote')

    if not config:
        return jsonify({'message': 'config not found!'}), 400

    logic_list = config["Strategy"]["Logics"]
    for i in range(len(logic_list)):
        type_prefix = logic_list[i]["Type"]
        arguments = logic_list[i]["Arguments"]
        new_arguments = {}
        for key, value in arguments.items():
            if key.startswith(type_prefix):
                new_key = key[len(type_prefix) + 1:]
                new_arguments[new_key] = value

        logic_list[i]["Arguments"] = new_arguments
    config["Strategy"]["Logics"] = logic_list
    identical_placeID_dict = {
        str(item["slaveAddress"]): str(item["placeID"])
        for item in config["Host"]["Identical_placeID_list"]
    }
    config["Host"]["Identical_placeID_dict"] = identical_placeID_dict
    del config["Host"]["Identical_placeID_list"]
    for idx, wifi in enumerate(config["Communication"]["WiFi"]):
        if wifi["key_mgmt"] == "NONE":
            if "psk" in config["Communication"]["WiFi"][idx]:
                del config["Communication"]["WiFi"][idx]["psk"]
        


    hostID = str(config["Host"]["HostName"]) + str(config["Host"]["SerialNo"])
    if set_remote:
        send_mqtt_message_without_response(f"{hostID}/cmd", 'SetConfig', [config])
    cfg = mongo.db.gateway.find_one({"Host.HostName": config["Host"]["HostName"], "Host.SerialNo": config["Host"]["SerialNo"]})
    gateway_objectID = cfg["_id"]
    
    update_json_in_mongo(config, 'gateway', gateway_objectID)

    return jsonify({"token": get_refresh_token(token), "config": config}), 200


@gateway_page.route('/create_SSH', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def create_SSH(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')

    if not gateway_id:
        return jsonify({'message': 'gateway_id not found!'}), 400

    topic = gateway_id.replace("_", "")
    current_time = time.time()
    for ssh_port in range(8924, 8934):
        key = f"ssh_port_{ssh_port}"
        port_data = redis_client.get(key)
        if not port_data:
            redis_client.set(key, current_time)
            redis_client.expire(key, 3600)
            send_mqtt_message_without_response(f"{topic}/cmd", 'CreateSSHChannel', [ssh_port, 22, "operation-server.ima-ems.com", 22, "pi", "imapi1225"])
            return jsonify({"token": get_refresh_token(token), "ssh_msg": f"Create connection at port {ssh_port}"}), 200
    oldest_port = None
    oldest_time = current_time
    for ssh_port in range(8924, 8934):
        key = f"ssh_port_{ssh_port}"
        port_data = redis_client.get(key)
        if port_data:
            port_time = float(port_data)
            if current_time - port_time > 3600:
                oldest_port = ssh_port
                break
        else:
            oldest_port = ssh_port
            break

    if oldest_port is not None:
        redis_client.delete(f"ssh_port_{oldest_port}")
        redis_client.set(f"ssh_port_{oldest_port}", current_time)
        redis_client.expire(f"ssh_port_{oldest_port}", 3600)
        send_mqtt_message_without_response(f"{topic}/cmd", 'CreateSSHChannel', [oldest_port, 22, "operation-server.ima-ems.com", 22, "pi", "imapi1225"])
        return jsonify({"token": get_refresh_token(token), "ssh_msg": f"Create connection at port {oldest_port} (recycled)"}), 200
    return jsonify({'message': 'No available SSH ports!'}), 400



@gateway_page.route('/check_connection', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def check_connection(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    topic = gateway_id.replace("_", "")
    if not gateway_id:
        return jsonify({'message': 'gateway_id not found!'}), 400
    
    ret = send_mqtt_message_with_response(f"{topic}/cmd", "Version", [])

    if isinstance(ret, str) and re.match("^\\d+\\.\\d+\\.\\d+$", ret):
        return jsonify({"token": get_refresh_token(token),
                        'msg': True}), 200
    
    return jsonify({"token": get_refresh_token(token),
                    "msg": False}), 200



@gateway_page.route('/get_pin_data', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_pin_data(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    topic = gateway_id.replace("_", "")
    if not gateway_id:
        return jsonify({'message': 'gateway_id not found!'}), 400
    portName = data.get('portName')
    slaveAddress = data.get('slaveAddr')
    pinAddress = data.get('pinAddr')
    pinName = data.get('pinName')
    ret = send_mqtt_message_with_response(f"{topic}/cmd", "GetPinData", [portName, slaveAddress, pinAddress, pinName])
    
    return jsonify({"token": get_refresh_token(token),
                    "msg": ret}), 200

@gateway_page.route('/get_MACAddress', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_MACAddress(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    topic = gateway_id.replace("_", "")
    if not gateway_id:
        return jsonify({'message': 'gateway_id not found!'}), 400
    ret = send_mqtt_message_with_response(f"{topic}/cmd", "GetMACAddress", [])
    
    return jsonify({"token": get_refresh_token(token),
                    "msg": ret}), 200


@gateway_page.route('/get_gateway_config_by_place_id', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_gateway_config_by_place_id(token):
    data = request.get_json()
    place_id = data.get('place_id')
    project_code = data.get('project_code')
    user_projects = get_user_info(token, 'project')
    if project_code not in user_projects:
        return jsonify({'message': 'Invalid user!'}), 401
    if place_id not in mongo.db.project.find_one({"project_code": project_code}).get('place_id'):
        return jsonify({'message': 'Invalid place_id!'}), 401
    
    gateway_config_list = list(mongo.db.gateway.find({"Host.HostName": place_id}, {"_id": 0, "create_time": 0, "last_updated": 0})) or []
    return_list = []
    gateway_id_list = mongo.db.project.find_one({"project_code": project_code}).get("gateway")
    for i in gateway_config_list:
        if i['Host']['HostName'] + '_' + i['Host']['SerialNo'] in gateway_id_list:
            return_list.append(i['Host']['HostName'] + '_' + i['Host']['SerialNo'])
    return jsonify({"token": get_refresh_token(token),
                    "data": return_list}), 200

@gateway_page.route('/get_remote_config', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_remote_config(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    try:
        ret = ast.literal_eval(send_mqtt_message_with_response(f"{gateway_id.replace('_', '')}/cmd", "GetConfig", []))
        ret = dict(filter(lambda item: item[0] in ['Host', 'Communication', 'Strategy', 'Modbus'], ret.items()))
        
        return jsonify({"token": get_refresh_token(token),
                        "config": ret}), 200
    except Exception as e:
        return jsonify({"token": get_refresh_token(token)}), 401

@gateway_page.route('/get_log_list', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def get_log_list(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    try:
        ret = send_mqtt_message_with_response(f"{gateway_id.replace('_', '')}/cmd", "GetLogList", [])   
        return jsonify({"token": get_refresh_token(token),
                        "list": ret}), 200
    except Exception as e:
        return jsonify({"token": get_refresh_token(token)}), 401


@gateway_page.route('/update_available', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def update_available(token):
    data = request.get_json()
    Version = data.get('version')
    version_pattern = r'v?\d+\.\d+\.\d+(-\S+)?'
    for subfolder_name in os.listdir(current_app.config['HT1_ADDRESS']):
        subfolder_path = os.path.join(current_app.config['HT1_ADDRESS'], subfolder_name)
        if os.path.isdir(subfolder_path):
            if not re.match(version_pattern, subfolder_name):
                try:
                    os.rmdir(subfolder_path)
                except OSError as e:
                    continue
        if subfolder_name == '.DS_Store':
            try:
                os.remove(subfolder_path)
            except OSError as e:
                continue
    latest_version = max(os.listdir(current_app.config['HT1_ADDRESS']), key=version.parse)
    ret = False
    if version.parse(Version) < version.parse(latest_version) and version.parse(Version) >= version.parse("1.0.4"):
        ret = True
    return jsonify({"token": get_refresh_token(token),
                    "data": ret,
                    "latest_version": latest_version}), 200

@gateway_page.route('/update_gateway', methods=['OPTIONS', 'POST'])
@roles_required(['project leader'])
def update_gateway(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    version = data.get('version')
    update_cmd = f"""
HOSTNAME=$(cat /etc/hostname)
curl -o /home/<USER>/download_ht1_main "{current_app.config['API_BASE_URL']}/download/ht1/{version}"
sudo sed -i 's/\"Version\": *\"[0-9]\\+\\.[0-9]\\+\\.[0-9]\\+\"/\"Version\": \"{version}\"/' /home/<USER>/HT1/json/cfg.json
mv /home/<USER>/download_ht1_main /home/<USER>/HT1/main
chmod +x /home/<USER>/HT1/main
sudo reboot -f now
"""
    send_mqtt_message_without_response(f"{gateway_id.replace('_', '')}/cmd", "ExecuteScript", [update_cmd])
    return jsonify({"token": get_refresh_token(token)}), 200

@gateway_page.route('/publish_version', methods=['OPTIONS', 'POST'])
def publish_gateway_version():
    if request.method == 'OPTIONS':
        return '', 204
    data = request.get_json()
    version = data.get('version')
    tag_message = data.get('message')
    for i in list(mongo.db.user.find({"role": {"$in": ['project leader']}})):
        send_mail('<EMAIL>', i.get("email"), f"New gateway version (v{version}) is released!\n", f"Release Notes: \n{tag_message}\nPlease update your gateway version in config page.")
    return jsonify({"msg": 'successful'}), 200

