export const event_config_list = [
  {
    "title": "electricity_plan_change",
    "subtitle": "project_code", // event collection 底下的key
    "description": "project_name",
    "key": "event_id",
    "stages": {
      "申請": {//application
        "priority": {
          "project leader": 3,
          "TPC_Officer": 5
        },
        "participants": {
          "project leader": { // 記錄在mongoDB event底下
            "routerLink": "/project/event/%KEY%/info", // 用%xxx%中間大寫去replace成指定值，hardcoding
          },
          "TPC_Officer": {
            "routerLink": "/project/event/%KEY%/info",
          },
        },
      },
      "電力公司受理": {//power_acceptance
        "priority": {
          "project leader": 3,
          "TPC_Officer": 1
        },
        "participants": {
          "project leader": {
            "routerLink": "/project/event/%KEY%/info",
          },
          "TPC_Officer": {
            "routerLink": "/project/event/%KEY%/info",
            "pin": true,
          },
         
        },
        "notification": {
          "project leader": {
            "msg": "案件%TITLE% - %DESCRIPTION%已受理"
          }
        },
        "fields": {
          "Acceptance_Number": {
            "label": "受理號碼",
            "type": "text",
            "required": true
          },
          "Order_Acceptance_Date": {
            "label": "受理日期",
            "type": "date",
            "required": true
          }
        }
      },
      "匯款": {//payment
        "priority": {
          "project leader": 1,
        },
        "participants": {
          "project leader": {
            "routerLink": "/project/event/%KEY%/info",
            "action": "payment",  // 標記此階段需要匯款操作
            "actionButton": "填寫匯款資訊"
          },
        },
        "notification": {
          "TPC_Officer": {
            "msg": "案件%TITLE% - %DESCRIPTION%已完成匯款"
          }
        },
        "fields": {
          "payment_date": {
            "label": "匯款日期",
            "type": "date",
            "required": true
          },
          "amount": {
            "label": "匯款金額",
            "type": "number",
            "required": true
          }
        }
      },
      "生效": {//effective
        "priority": {
          "project leader": 3,
          "TPC_Officer": 5
        },
        "participants": {
          "project leader": {
            "routerLink": "/project/event/%KEY%/info",
          },
        },
      },
      "發票開立": {//invoice
        "priority": {
          "project leader": 2,
          "TPC_Officer": 5
        },
        "participants": {
          "project leader": {
            "routerLink": "/project/event/%KEY%/info",
          },
          "notification": {
            "project leader": {
              "msg": "案件%TITLE% - %DESCRIPTION%已發票開立"
            }
          }
        },
      }
    }
  },

]

export function event_type_trans(name: string, rev: boolean = false): string | null { 
  // rev = false: 中翻英, rev = true: 英翻中

  const zhToEn: Record<string, string> = {
    // 流程 stages 名稱
    '申請': 'application',
    '電力公司受理': 'power_acceptance',
    '匯款': 'payment',
    '生效': 'effective',
    '發票開立': 'invoice',

    
    // 事件類型
    '用電變更申請': 'electricity_plan_change',
    
    // 狀態
    '等待中': 'pending',
    '進行中': 'in_process',
    '已完成': 'completed',
    '取消': 'canceled',
    '錯誤': 'error',
    
  };

  const enToZh: Record<string, string> = Object.fromEntries(
    Object.entries(zhToEn).map(([zh, en]) => [en, zh])
  );

  if (rev) {
    return enToZh[name] || null;
  } else {
    return zhToEn[name] || null;
  }
}


/*
**************************************
mongoDB event collection
**************************************

{
  "_id": ObjectId(),
  "event_id": time(year/month/day/hour/minute/second/milisecond) (檢查唯一),
  "event_type": "electricity_plan_change",
  "project_code": "PROJECT_CODE",
  "project_name": "PROJECT_NAME",
  "role": {
    "project leader": "email",
    "TPC_Officer": "email"
  },
  "original_type": {
    "type": "低壓需量",
    "time": "二段式",
    "contract_peak": 49,
    "contract_semipeak": 10,
    "contract_offpeak": 0,
    "contract_saturday": 0
  },
  "changed_type": {
    "type": "低壓需量",
    "time": "三段式",
    "contract_peak": 45,
    "contract_semipeak": 0,
    "contract_offpeak": 0,
    "contract_saturday": 0
  },
  "place_id_info": {
    "統編戶名(英文)": "42838254今時科技股份有限公司",
    "現場用途(英文)": "辦公室",
    ...
  },
  "stage": "電力公司受理(英文)",
  "status": "in_process", // 核心狀態: pending, in_process, done, canceled, error
  "create_time": "datetime",
  "update_time": "datetime",
  "Acceptance_Number": "",
  "Case_Handler": "",
  "Order_Acceptance_Date": "",
  "Execution_Status": "",
  "Process_Form_Number": "",
  "place_id": ""
}

// 核心狀態定義
// pending - 待處理
// in_process - 處理中  
// done - 已完成
// canceled - 已取消
// error - 錯誤

*/
