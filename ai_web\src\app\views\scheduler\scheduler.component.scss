// 行事曆樣式
:host ::ng-deep {
  // FullCalendar 客製化
  .fc {
    --fc-border-color: #ebedef;
    --fc-button-bg-color: #321fdb;
    --fc-button-border-color: #321fdb;
    --fc-button-hover-bg-color: #5141e0;
    --fc-button-hover-border-color: #4937de;
    --fc-button-active-bg-color: #4937de;
    --fc-button-active-border-color: #4331d8;
    --fc-event-bg-color: #321fdb;
    --fc-event-border-color: #321fdb;
    --fc-today-bg-color: rgba(50, 31, 219, 0.07);
    --fc-now-indicator-color: #e55353;
    
    // 表格邊框和背景樣式
    .fc-scrollgrid {
      border-radius: 0.25rem;
    }
    
    // 事件樣式
    .fc-event {
      border-radius: 6px;
      font-size: 0.875rem;
      padding: 3px 6px;
      margin: 2px 0;
      border-left-width: 3px;
      min-height: 70px; // 確保事件有足夠高度
      
      .fc-event-title {
        font-weight: 600;
        line-height: 1.4;
      }
      
      .fc-event-time {
        font-weight: 400;
        opacity: 0.8;
      }
    }
    
    // 商務風格事件
    .fc-event-business {
      border-radius: 8px !important;
      border-width: 2px !important;
      font-weight: 600 !important;
      min-height: 70px !important;
      padding: 6px 10px !important;
      line-height: 1.4 !important;
      
      .fc-event-title {
        font-weight: 700 !important;
        font-size: 14px !important;
      }
    }
    
    // 週末日期顏色
    .fc-day-sat, .fc-day-sun {
      color: #e55353;
    }
    
    // 月曆視圖中的日期
    .fc-daygrid-day-top {
      padding-top: 4px;
      padding-right: 8px;
    }
    
    // 日期格子高度調整
    .fc-daygrid-day-frame {
      min-height: 120px; // 增加日期格子高度以容納更大的事件
    }
    
    // 事件容器調整
    .fc-daygrid-event-harness {
      margin-bottom: 2px; // 事件間距
    }
    
    // 今天的特殊標示
    .fc-day-today {
      background-color: rgba(50, 31, 219, 0.05) !important;
    }
  }
}

.custom-nav {
  .nav-divider {
    color: #bbb;
    margin: 0 0.5rem;
    font-size: 1.25rem;
    vertical-align: middle;
    transform: translateY(5px);
  }
}

// 表格行互動效果
.cursor-pointer {
  cursor: pointer;
}

// 事件列表可點擊行的效果
tbody tr.cursor-pointer {
  &:hover {
    background-color: rgba(50, 31, 219, 0.05);
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    transition: all 0.2s ease;
  }
  
  &:active {
    background-color: rgba(50, 31, 219, 0.1);
  }
}

// 增加對於不可點擊行的樣式
tbody tr.disabled {
  opacity: 0.6;
  background-color: #f8f9fa;
  cursor: not-allowed;
  
  &:hover {
    background-color: #f8f9fa;
    box-shadow: none;
  }
}

ㄏ
.no-access-indicator {
  display: inline-block;
  margin-left: 8px;
  font-size: 12px;
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

// 已被選中事件的樣式
.selected-event {
  background-color: rgba(50, 31, 219, 0.1) !important;
  border-left: 3px solid #321fdb;
}

// 高亮顯示當前選擇的事件
.event-row-selected {
  background-color: rgba(50, 31, 219, 0.1) !important;
  border-left: 4px solid #321fdb;
  
  &:hover {
    background-color: rgba(50, 31, 219, 0.15) !important;
  }
}

// 行事曆容器高度 - 響應式優化
full-calendar {
  display: block;
  width: 100%;
  overflow: hidden;
  
  // 手機版
  height: calc(100vh - 320px);
  min-height: 350px;
  
  // 平板版
  @media (min-width: 768px) {
    height: calc(100vh - 280px);
    min-height: 450px;
  }
  
  // 桌面版
  @media (min-width: 1024px) {
    height: calc(100vh - 270px);
    min-height: 500px;
  }
  
  // 大螢幕
  @media (min-width: 1200px) {
    height: calc(100vh - 250px);
    min-height: 600px;
  }
}

// 甘特圖樣式
.gantt-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
  background: white;
}

.gantt-header {
  display: flex;
  border-bottom: 2px solid #dee2e6;
  background-color: #f8f9fa;
  height: 80px;
  z-index: 10;
  position: sticky;
  top: 0;
  width: 100%;
  overflow: hidden; // 確保頭部不會超出容器
}

.gantt-task-column {
  width: 300px;
  flex-shrink: 0;
  border-right: 2px solid #dee2e6;
  display: flex;
  flex-direction: column;
}

.gantt-task-header {
  height: 80px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.gantt-timeline-header {
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
  background-color: #f8f9fa;
  flex: 1; // 確保填滿可用空間
  
  // 隱藏頭部滾動條，但保持滾動功能
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.gantt-time-scale {
  display: flex;
  height: 80px;
}

.gantt-time-period {
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #dee2e6;
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
  background-color: #f8f9fa;
  
  &.today {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    font-weight: 600;
  }
  
  &.weekend {
    background-color: rgba(248, 249, 250, 0.8);
    color: #6c757d;
  }
}

.gantt-body {
  display: flex;
  overflow: visible; // 改為 visible 讓子容器處理滾動
  position: relative;
  min-height: 200px;
}

.gantt-task-list {
  width: 300px;
  flex-shrink: 0;
  border-right: 2px solid #dee2e6;
  background-color: white;
}

.gantt-event-group {
  border-bottom: 1px solid #e9ecef;
}

.gantt-event-row {
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #f1f3f4;
  background-color: #fafbfc;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #e3f2fd;
  }
  
  &:active {
    background-color: #bbdefb;
  }
}

.gantt-event-title {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #495057;
}

.gantt-steps {
  background-color: white;
}

.gantt-step-row {
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #f8f9fa;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #e8f5e8;
  }
  
  &:active {
    background-color: #c8e6c9;
  }
}

.gantt-step-title {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #6c757d;
}

// 時間軸滾動容器 - 唯一的滾動條
.gantt-timeline-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
  background-color: white;
  min-height: 200px; // 確保有最小高度
  
  // 美觀的滾動條樣式 - 唯一可見的滾動條
  &::-webkit-scrollbar {
    height: 12px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 6px;
    border: 2px solid #f8f9fa;
    
    &:hover {
      background: #495057;
    }
    
    &:active {
      background: #343a40;
    }
  }
}

.gantt-timeline {
  position: relative;
  background-color: white;
  min-width: 100%;
  height: 100%; // 確保填滿容器高度
}

.gantt-grid {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1; // 網格線在最底層
}

.gantt-grid-line {
  position: absolute;
  width: 1px;
  background-color: #e9ecef;
  border-right: 1px solid #f1f3f4;
  z-index: 1; // 確保網格線在橫條後面
}



.gantt-bars {
  position: relative;
  z-index: 10; // 橫條區域在網格線之上
}

.gantt-event-bars {
  position: relative;
}

.gantt-step-bars {
  position: relative;
}

.gantt-step-bar-container {
  position: absolute;
  height: 32px; // 減少高度避免跨越行
  width: 100%;
  display: flex;
  align-items: center;
  padding: 2px 8px; // 減少垂直間距
  z-index: 8; // 確保容器在網格線之上
}



.gantt-progress-bar {
  position: relative;
  height: 28px;
  border-radius: 14px;
  z-index: 10;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  min-width: 80px; // 確保有足夠空間顯示文字
  background-color: #6c757d; // 預設顏色
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
    z-index: 15;
  }
  
  &:active {
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  // 狀態顏色
  &.status-completed {
    background-color: #28a745;
  }
  
  &.status-running {
    background-color: #007bff;
  }
  
  &.status-pending {
    background-color: #ffc107;
  }
  

}





// 步驟名稱顯示樣式
.gantt-step-name {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  z-index: 20;
  pointer-events: none;
  
  .step-name-text {
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
  }
  

}



// 響應式調整
@media (max-width: 768px) {
  .gantt-task-column,
  .gantt-task-list {
    width: 200px;
  }
  
  .gantt-task-header,
  .gantt-event-title,
  .gantt-step-title {
    font-size: 0.8rem;
  }
  

}

@media (max-width: 576px) {
  .gantt-task-column,
  .gantt-task-list {
    width: 150px;
  }
  
  .gantt-task-header {
    padding: 0 8px;
  }
  
  .gantt-event-row,
  .gantt-step-row {
    padding: 0 8px;
  }
}

// ========== 時間滑桿樣式 ==========

// 滑桿基本樣式
.gantt-time-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  background: linear-gradient(to right, #007bff 0%, #6c757d 100%);
  border-radius: 4px;
  outline: none;
  transition: background 0.2s ease;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #007bff;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    }
  }
  
  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #007bff;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    }
  }
  
  &::-ms-thumb {
    width: 20px;
    height: 20px;
    background: #007bff;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
  }
}

// 滑桿刻度標籤樣式
.slider-ticks {
  pointer-events: none; // 防止標籤干擾滑桿操作
  
  .tick-label {
    text-align: center;
    transition: all 0.2s ease;
    font-size: 9px !important;
    color: #6c757d;
    font-weight: 400;
    white-space: nowrap; // 防止文字換行
    
    &.text-primary {
      color: #007bff !important;
      font-weight: 600 !important;
      transform: scale(1.1);
      font-size: 10px !important;
    }
    
    // 過去時間的特殊樣式
    &:first-child span {
      color: #dc3545; // 過去時間用紅色（第一個選項是過去30天）
    }
  }
}  // 商務化行事曆樣式
  .fc-event-business {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    transition: all 0.2s ease;
    
    &:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
      transform: translateY(-1px);
    }
    
    .fc-event-title {
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
  
  // 行事曆整體容器美化 - 響應式設計
  .calendar-business-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: none;
    
    // 響應式設計
    @media (min-width: 768px) {
      padding: 24px;
    }
    
    .fc-toolbar {
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 10px;
      
      // 調整標題和按鈕佈局
      .fc-toolbar-chunk {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .fc-toolbar-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        margin: 0 16px;
        
        @media (min-width: 768px) {
          font-size: 1.5rem;
        }
      }
      
      .fc-button {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        border-radius: 6px;
        font-weight: 500;
        padding: 6px 12px;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        
        @media (min-width: 768px) {
          padding: 8px 16px;
          font-size: 1rem;
        }
        
        &:hover {
          background: linear-gradient(45deg, #0056b3, #004085);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }
        
        &.fc-today-button {
          background: linear-gradient(45deg, #28a745, #1e7e34);
          
          &:hover {
            background: linear-gradient(45deg, #1e7e34, #155724);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
          }
        }
      }
    }
    
    .fc-scrollgrid {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      font-size: 0.875rem;
      
      @media (min-width: 768px) {
        font-size: 1rem;
      }
    }
    
    .fc-col-header-cell {
      background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
      font-weight: 600;
      color: #495057;
      border-bottom: 2px solid #dee2e6;
      padding: 8px 4px;
      
      @media (min-width: 768px) {
        padding: 12px 8px;
      }
    }
    
    .fc-daygrid-day {
      transition: background-color 0.2s ease;
      min-height: 80px;
      
      @media (min-width: 768px) {
        min-height: 120px;
      }
      
      &:hover {
        background-color: rgba(0, 123, 255, 0.05);
      }
    }
    
    .fc-today {
      background: linear-gradient(135deg, rgba(0, 123, 255, 0.08) 0%, rgba(0, 123, 255, 0.12) 100%);
      
      .fc-daygrid-day-number {
        background: #007bff;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin: 4px;
        font-size: 0.75rem;
        
        @media (min-width: 768px) {
          width: 24px;
          height: 24px;
          font-size: 0.875rem;
        }
      }
    }
    
    // 事件樣式改善
    .fc-event {
      border-radius: 4px;
      margin: 1px;
      padding: 2px 4px;
      font-size: 0.75rem;
      line-height: 1.2;
      
      @media (min-width: 768px) {
        font-size: 0.875rem;
        padding: 3px 6px;
      }
      
      // 改善顏色對比度
      &.fc-event-business {
        border-width: 2px;
        border-style: solid;
        
        .fc-event-title {
          color: white;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
      }
    }
  }
  
  // 修正下拉選單被遮擋問題
  .table-responsive {
    overflow: visible !important;
  }
  
  c-dropdown {
    position: relative;
    
    .dropdown-menu {
      z-index: 1050 !important;
      position: absolute !important;
      top: 100% !important;
      left: 0 !important;
      min-width: 200px;
      max-height: 300px;
      overflow-y: auto;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      
      .dropdown-item {
        padding: 8px 16px;
        transition: background-color 0.2s ease;
        
        &:hover {
          background-color: #f8f9fa;
        }
        &:active {
          background-color: #007bff;
          color: white;
        }
      }
    }
  }
  
  // 行事曆額外響應式優化
  @media (max-width: 576px) {
  // 超小螢幕優化
  full-calendar {
    .fc-toolbar {
      .fc-toolbar-title {
        font-size: 1rem !important;
        margin: 0 8px !important;
      }
      
      .fc-button {
        padding: 4px 8px !important;
        font-size: 0.75rem !important;
      }
    }
    
    .fc-daygrid-day {
      min-height: 60px !important;
    }
    
    .fc-event {
      font-size: 0.7rem !important;
      padding: 1px 3px !important;
    }
  }
}

// 確保行事曆不被其他元素遮擋
.calendar-container {
  position: relative;
  z-index: 1;
  overflow: hidden;
  width: 100%;
}

// 行事曆滾動條樣式
.fc-scroller {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #321fdb;
    border-radius: 3px;
    
    &:hover {
      background: #4937de;
    }
  }
}

// 行事曆 Y 軸固定，防止超出螢幕
.fc-view-harness {
  overflow-y: auto !important;
  max-height: 100% !important;
}

.fc-daygrid-body {
  overflow-y: auto !important;
}

// 事件狀態標籤增強
c-badge {
  font-weight: 500;
  letter-spacing: 0.3px;
  padding: 4px 8px;
  font-size: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  
  &.badge-primary {
    background: linear-gradient(135deg, #321fdb, #4937de);
  }
  
  &.badge-success {
    background: linear-gradient(135deg, #2eb85c, #20a144);
  }
  
  &.badge-warning {
    background: linear-gradient(135deg, #f9b115, #e09400);
  }
  
  &.badge-danger {
    background: linear-gradient(135deg, #e55353, #d13b3b);
  }
  
  &.badge-info {
    background: linear-gradient(135deg, #39f, #0080ff);
  }
}


