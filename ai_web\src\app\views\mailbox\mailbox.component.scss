.mail-preview {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.subject {
  font-size: 16px; /* 大字體顯示標題 */
}
.mail-content {
  width: 100%; /* 確保內容區域佔滿父容器寬度 */
  white-space: pre-wrap; /* 保留空格並允許換行 */
  word-wrap: break-word; /* 超過邊界時自動換行 */
  overflow-wrap: break-word; /* 防止長單字或 URL 溢出 */
  max-width: 100%; /* 確保最大寬度不受限制 */
  overflow: auto; /* 防止內容溢出並能滾動 */
}



.content-preview {
  font-size: 12px; /* 小字體顯示內文 */
  color: #6c757d; /* 淺灰色文字 */
  white-space: normal; /* 改為正常換行 */
  overflow: hidden; /* 隱藏超過的部分 */
  text-overflow: ellipsis; /* 使用...顯示超出部分 */
  max-width: 300px; /* 設定最大寬度，根據需求調整 */
}
.status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.bg-gray { background-color: #6c757d; } /* 灰色 */
.bg-blue { background-color: #007bff; } /* 藍色 */

.status-dot {
  width: 8px;
  height: 8px;
  background-color: blue;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  left: 10px;
}

/* 設定表格行樣式，讓點點正確對齊 */
.mail-row {
  position: relative;
}
.font-weight-bold {
  font-weight: bold;
}