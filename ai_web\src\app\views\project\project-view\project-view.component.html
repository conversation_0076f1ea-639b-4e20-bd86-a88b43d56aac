<!-- 專案標題與建立按鈕 -->
<c-row class="align-items-center mb-4 px-3">
  <c-col>
    <div *ngIf="project">
      <h2 class="mb-0"><strong>{{ project.project_name }}</strong></h2>
    </div>
  </c-col>
</c-row>


<!-- 專案卡片列表 -->
<c-row class="g-4 px-3">
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #ca4a00; color: #ca4a00; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'dashboard']"
      (mouseenter)="hovering[0] = true"
      (mouseleave)="hovering[0] = false"
      [ngStyle]="hovering[0] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilBarChart" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">服務儀表板</p>
      </div>
    </c-card>
  </c-col>
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #0d6efd; color: #0d6efd; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'plot']"
      (mouseenter)="hovering[1] = true"
      (mouseleave)="hovering[1] = false"
      [ngStyle]="hovering[1] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilMap" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">專案規劃</p>
      </div>
    </c-card>
  </c-col>
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #c59700; color: #c59700; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'engineering']"
      (mouseenter)="hovering[2] = true"
      (mouseleave)="hovering[2] = false"
      [ngStyle]="hovering[2] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilBolt" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">設備工程</p>
      </div>
    </c-card>
  </c-col>
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #00a408; color: #00a408; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'performance']"
      (mouseenter)="hovering[3] = true"
      (mouseleave)="hovering[3] = false"
      [ngStyle]="hovering[3] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilChartLine" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">節能績效</p>
      </div>
    </c-card>
  </c-col>
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #00a69b; color: #00a69b; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'customer-service']"
      (mouseenter)="hovering[4] = true"
      (mouseleave)="hovering[4] = false"
      [ngStyle]="hovering[4] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilGroup" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">客戶服務</p>
      </div>
    </c-card>
  </c-col>
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #b30098; color: #b30098; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'list']"
      (mouseenter)="hovering[5] = true"
      (mouseleave)="hovering[5] = false"
      [ngStyle]="hovering[5] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilListRich" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">資訊清單</p>
      </div>
    </c-card>
  </c-col>
  <c-col xs="12" md="6" lg="4">
    <c-card
      class="project-card h-100 border border-2 border-dashed shadow-sm text-center d-flex justify-content-center align-items-center py-3"
      style="cursor: pointer; min-height: 140px; border-color: #393939; color: #393939; transition: transform 0.2s ease, box-shadow 0.2s ease;"
      [routerLink]="['/project/', project_code, 'edit']"
      (mouseenter)="hovering[6] = true"
      (mouseleave)="hovering[6] = false"
      [ngStyle]="hovering[6] ? {
        transform: 'scale(1.03)',
        boxShadow: '0 0.5rem 1rem rgba(0,0,0,0.15)'
      } : {}"
    >
      <div>
        <svg [cIcon]="icons.cilSettings" style="width: 2.5rem; height: 2.5rem;"></svg>
        <p class="mt-2 mb-0 fw-bold small">設定</p>
      </div>
    </c-card>
  </c-col>
</c-row>
