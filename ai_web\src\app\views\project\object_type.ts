export const object_type_list: string[] = [
  '閘道', '馬達', '冰水主機', '空壓機', '配電盤',
  '無線基地台', '路由器', '儲存槽', '電表',
  '變頻器', '輸入輸出', '流量計',
  '溫度計', '乾濕球', '壓差計',
  '濕度計', '自定義'
]

export const link_type_list: string[] = [
  '網路線', 'RS485', '訊號線', '動力線', '液體管路', '氣體管路'
]

export const serial_no_list: string[] = [
  '0','1','2','3','4','5','6','7','8','9',
  'a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z',
  'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',
  'aa','ab','ac','ad','ae','af','ag','ah','ai','aj','ak','al','am','an','ao','ap','aq','ar','as','at','au','av','aw','ax','ay','az',
  'aA','aB','aC','aD','aE','aF','aG','aH','aI','aJ','aK','aL','aM','aN','aO','aP','aQ','aR','aS','aT','aU','aV','aW','aX','aY','aZ',

]

export function type_name_trans(name: string, rev: boolean = false): string | null { //rev=false: 中翻英, rev=true: 英翻中
  const zhToEn: Record<string, string> = {
    '閘道': 'Gateway',
    '馬達': 'Motor',
    '冰水主機': 'Chiller',
    '空壓機': 'AirCompressor',
    '配電盤': 'Panel',
    '無線基地台': 'Wi-Fi_AP',
    '路由器': 'Router',
    '儲存槽': 'Tank',
    '電表': 'PowerMeter',
    '變頻器': 'Inverter',
    '輸入輸出': 'IO',
    '流量計': 'FlowMeter',
    '溫度計': 'TempSensor',
    '乾濕球': 'Psychrometer',
    '壓差計': 'DiffPresGauge',
    '濕度計': 'Hygrometer',
    '自定義': 'Self-defined',
    '網路線': 'EtherNet',
    'RS485': 'RS485',
    '訊號線': 'Signal',
    '動力線': 'MotorCable',
    '液體管路': 'LiquidPipe',
    '氣體管路': 'AirPipe'
  };

  const enToZh = Object.fromEntries(
    Object.entries(zhToEn).map(([k, v]) => [v, k])
  );

  if (!rev) {
    return zhToEn[name] || null;
  } else {
    return enToZh[name] || null;
  }
}


export const link_connectable_object: { [key: string]: string[] } = {
  'EtherNet': [
    'Gateway',
    'Wi-Fi_AP',
    'Router',
    'Self-defined',
  ],
  'RS485': [
    'Gateway',
    'AirCompressor',
    'PowerMeter',
    'Inverter',
    'IO',
    'FlowMeter',
    'Self-defined',
  ],
  'Signal': [
    'Motor',
    'Chiller',
    'AirCompressor',
    'IO',
    'TempSensor',
    'Psychrometer',
    'DiffPresGauge',
    'Hygrometer',
    'Self-defined',
  ],
  'MotorCable': [
    'Motor',
    'Inverter',
    'Self-defined',
  ],
  'LiquidPipe': [
    'Motor',
    'Chiller',
    'Tank',
    'FlowMeter',
    'TempSensor',
    'Psychrometer',
    'DiffPresGauge',
    'Self-defined',
  ],
  'AirPipe': [
    'Motor',
    'AirCompressor',
    'Tank',
    'FlowMeter',
    'TempSensor',
    'Psychrometer',
    'DiffPresGauge',
    'Hygrometer',
    'Self-defined',
  ]
};
