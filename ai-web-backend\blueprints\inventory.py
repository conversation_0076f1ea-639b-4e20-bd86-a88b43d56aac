from flask import request, Blueprint, jsonify
from accessories import mongo, update_json_in_mongo, save_json_to_mongo, remove_json_in_mongo
from blueprints.api import get_user_info, get_refresh_token, verify_token, roles_required
import subprocess, os, re, paramiko, json
from scp import SCPClient

inventory_page = Blueprint('inventory', __name__)

@inventory_page.route('/lan_install', methods=['OPTIONS', 'POST'])
@roles_required(['warehouse'])
def lan_install(token):
    data = request.get_json()
    gateway_id = data.get('gateway_id')
    lan_IP = data.get('lan_IP')
    if '_' not in gateway_id:
        return jsonify({"error": "Invalid gateway_id format"}), 400

    host_name, serial_no = gateway_id.split('_')

    gateway_data = mongo.db.gateway.find_one({
        "Host.HostName": str(host_name),
        "Host.SerialNo": str(serial_no)
    })
    gateway_data = dict(filter(lambda item: item[0] in ['Host', 'Communication', 'Strategy', 'Modbus'], gateway_data.items()))
    if not gateway_data:
        return jsonify({"error": "Gateway not found"}), 404
    
    json_config_path = f'/tmp/{gateway_id}_cfg.json'
    with open(json_config_path, 'w') as json_file:
        json.dump({k: v for k, v in gateway_data.items() if k != "_id" and k != "create_time" and k != "last_updated"}, json_file)
    version = gateway_data['Host']['Version']
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(lan_IP, username='pi', password='imapi1225')
    with SCPClient(ssh.get_transport()) as scp:
        scp.put(f'../HT1/{version}', recursive=True, remote_path='/home/<USER>')
        stdin, stdout, stderr = ssh.exec_command(f'mv /home/<USER>/{version}/ /home/<USER>/HT1/ && mkdir /home/<USER>/HT1/json')
        print(stdout.read().decode('utf-8'))
        scp.put(json_config_path, remote_path='/home/<USER>/HT1/json/cfg.json')
    os.remove(json_config_path)
    systemd_service = f"""
[Unit]
Description=HT1 Main Service
After=network.target

[Service]
ExecStart=sudo /home/<USER>/HT1/main
WorkingDirectory=/home/<USER>/HT1
Restart=on-failure
RestartSec=5s
StartLimitBurst=3
User=pi

[Install]
WantedBy=multi-user.target
"""
    ssh.exec_command(f"echo '{systemd_service}' | sudo tee /etc/systemd/system/ht1-main.service > /dev/null && \
                        sudo systemctl daemon-reload && \
                        sudo systemctl start ht1-main && \
                        sudo systemctl restart ht1-main && \
                        sudo systemctl enable ht1-main && \
                        sudo restart -f now")
    ssh.close()
    return jsonify({
        "token": get_refresh_token(token),
        "msg": "Done!"}), 200


@inventory_page.route('/scan_lan', methods=['OPTIONS', 'POST'])
@roles_required(['warehouse'])
def scan_lan(token):
    nmap_command = "/usr/bin/nmap -p 6379 --open ***********/24"
    result = subprocess.run(nmap_command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        return jsonify({'message': 'find error'}), 401
    ip_pattern = re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b')
    data = ip_pattern.findall(result.stdout)
    return jsonify({
        "token": get_refresh_token(token),
        "data": data}), 200


@inventory_page.route('/lan_uninstall', methods=['OPTIONS', 'POST'])
@roles_required(['warehouse'])
def lan_uninstall(token):
    data = request.get_json()
    lan_IP = data.get('lan_IP')
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(lan_IP, username='pi', password='imapi1225')
    ssh.exec_command(f"sudo rm -r /home/<USER>/HT1 && \
                        sudo rm  /etc/systemd/system/ht1-main.service && \
                        sudo systemctl daemon-reload && \
                        sudo rm /etc/hostname && \
                        echo '999999999990' | sudo tee /etc/hostname && \
                        sudo reboot -f now")
    ssh.close()
    return jsonify({
        "token": get_refresh_token(token),
        "msg": "Done!"}), 200