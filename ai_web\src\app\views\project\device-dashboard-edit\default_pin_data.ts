export const default_pin_data = {
  data: {
    'SD400': [
      {
        PinIdx: 0,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg000",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 1,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg001",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 2,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg002",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 3,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg003",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 4,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg004",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 5,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg005",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 6,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg006",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 7,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg007",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 8,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg008",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 9,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg009",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 10,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg010",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 11,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg011",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 12,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg012",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 13,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg013",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 14,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg014",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 15,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg015",
            Write: "",
            Register_W: ""
          }
        ]
      },
    ],
    'IMA-AI': [
      {
        PinIdx: 0,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg000",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 1,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg001",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 2,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg002",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 3,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg003",
            Write: "",
            Register_W: ""
          }
        ]
      }
    ],
    'JY-DAM0222': [
      {
        PinIdx: 0,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg000",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 1,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg001",
            Write: "",
            Register_W: ""
          }
        ]
      }
    ],
    'THSXX': [
      {
        PinIdx: 0,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "struct.unpack(‘>f’, struct.pack(‘>HH’, Reg001, Reg000))[0]",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 1,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "struct.unpack(‘>f’, struct.pack(‘>HH’, Reg003, Reg002))[0]",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 2,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "struct.unpack(‘>f’, struct.pack(‘>HH’, Reg005, Reg004))[0]",
            Write: "",
            Register_W: ""
          }
        ]
      }
    ],
    'BAT-08': [
      {
        PinIdx: 0,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg000",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 1,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg001",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 2,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg002",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 3,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg003",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 4,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg004",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 5,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg005",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 6,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg006",
            Write: "",
            Register_W: ""
          }
        ]
      },
      {
        PinIdx: 7,
        SensorType: 'A',
        Definition: [
          {
            Pin: "value",
            RenameAs: "",
            Read: "Reg007",
            Write: "",
            Register_W: ""
          }
        ]
      }
    ]
  }
}

export const AI_Type = [
  'temperature',
  'humidity',
  'pressure'
]

export const SE_In_Type = [
  'kwh','kvarh','voltage','current','kw',
  'kva','kvah','kvar','pf','current_A',
  'current_B','current_C'
]

export const SF_Type = [
  'Flow', 'Consumption', 'Revconsumption', 'Direction'
]
export const CI_In_Type = [
  'kwh','voltage','current','kw',
  'hz','error','alert','invstatus',
  'set_hz','on_off','reset'
]

export const CI_Out_Type = [
  'set_hz','on_off','reset'
]

export const DI_Type = [
  'value'
]

export const DO_Type = [
  'value'
]

export const GW_Type = [
  'Threads'
]

export const SR_In_Type = [
  'DIn01', 'DIn02', 'DIn03', 'DIn04', 'DOut01', 'DOut02', 'ByPass'
]

export const SR_Out_Type = [
  'DOut01', 'DOut02'
]
export interface Driver {
  label: string;
  value: string;
}

export const driver_list: Driver[] = [
  { label: '七泰電表', value: 'ChiTai-CTEC713PAC' },
  { label: '銓盛電表', value: 'ADTek-CPM10' },
  { label: '銓盛4迴路電表', value: 'ADTek_ADPPMA' },
  { label: '銓盛8迴路電表', value: 'ADTek_AEMDRB' },
  { label: '銓盛MV電表', value: 'ADTek-CPM10_MV' },
  { label: '台科電電表', value: 'DAE-PM210' },
  { label: '銓盛電表', value: 'ADTek-CPM12' },
  { label: '永岳電表', value: 'YONGYEA_ME631' },
  { label: '施耐德PM21XX電表', value: 'SCHNEIDER_PM21XX' },
  { label: '士林電表', value: 'Shihlin_SPM3' },
  { label: '丹佛斯變頻器', value: 'DanFoss' },
  { label: '光寶變頻器', value: 'LiteOnEVO6800' },
  { label: '寧茂變頻器', value: 'RM6G1' },
  { label: '東元變頻器', value: 'TECO' },
  { label: '台達變頻器', value: 'DELTA' },
  { label: '16AI', value: 'SD400' },
  { label: '4AI', value: 'IMA-AI' },
  { label: '2AI-2DI-2DO', value: 'JY-DAM0222' },
  { label: '4DI-4DO', value: 'JY-DAM0404' },
  { label: '4DI-2DO', value: 'JY-DAM0204' },
  { label: '16DI-8DO', value: 'JY-DAM0816' },
  { label: '16DI', value: 'SD500' },
  { label: '8DI-8DO', value: 'SD700' },
  { label: 'IMA-C', value: 'IMA-C' },
  { label: '3AI', value: 'THSXX' },
  { label: '16DO', value: 'SD600' },
  { label: '8AI', value: 'BAT-08' },
  { label: 'SUTO流量計', value: 'SUTO-FLOW' }
];

export const allowed_input_pinName: {
  data: Record<string, string[][]>;
} = {
  data: {
    "ChiTai-CTEC713PAC":[SE_In_Type],
    "ADTek-CPM10":[SE_In_Type],
    "ADTek_ADPPMA":[SE_In_Type,SE_In_Type,SE_In_Type,SE_In_Type],
    "ADTek_AEMDRB":[SE_In_Type,SE_In_Type,SE_In_Type,SE_In_Type,SE_In_Type,SE_In_Type,SE_In_Type,SE_In_Type],
    "ADTek-CPM10_MV":[SE_In_Type],
    "DAE-PM210":[SE_In_Type],
    "ADTek-CPM12":[SE_In_Type],
    "YONGYEA_ME631":[SE_In_Type],
    "SCHNEIDER_PM21XX":[SE_In_Type],
    "Shihlin_SPM3":[SE_In_Type],
    "DanFoss":[CI_In_Type],
    "LiteOnEVO6800":[CI_In_Type],
    "RM6G1":[CI_In_Type],
    "TECO":[CI_In_Type],
    "DELTA":[CI_In_Type],
    "SD400":[AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type,AI_Type],
    "IMA-AI":[AI_Type,AI_Type,AI_Type,AI_Type],
    "JY-DAM0222":[AI_Type,AI_Type,DI_Type, DI_Type, DO_Type, DO_Type],
    "JY-DAM0404":[DI_Type, DI_Type, DI_Type, DI_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "JY-DAM0204":[DI_Type, DI_Type, DI_Type, DI_Type, DO_Type, DO_Type],
    "JY-DAM0816":[DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type,DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "SD500": [DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type],
    "SD700":[DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DI_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "IMA-C":[SR_In_Type],
    "THSXX":[AI_Type, AI_Type, AI_Type],
    "SD600":[DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "BAT-08":[AI_Type, AI_Type, AI_Type, AI_Type, AI_Type, AI_Type, AI_Type, AI_Type],
    "SUTO-FLOW":[SF_Type],
  }
}

export const allowed_output_pinName: {
  data: Record<string, string[][]>;
} = {
  data: {
    "ChiTai-CTEC713PAC":[[]],
    "ADTek-CPM10":[[]],
    "ADTek_ADPPMA":[[]],
    "ADTek_AEMDRB":[[]],
    "ADTek-CPM10_MV":[[]],
    "DAE-PM210":[[]],
    "ADTek-CPM12":[[]],
    "YONGYEA_ME631":[[]],
    "SCHNEIDER_PM21XX":[[]],
    "Shihlin_SPM3":[[]],
    "DanFoss":[CI_Out_Type],
    "LiteOnEVO6800":[CI_Out_Type],
    "RM6G1":[CI_Out_Type],
    "TECO":[CI_Out_Type],
    "DELTA":[CI_Out_Type],
    "SD400":[[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]],
    "IMA-AI":[[],[],[],[]],
    "JY-DAM0222":[[],[],[],[],DO_Type, DO_Type],
    "JY-DAM0404":[[],[],[],[],DO_Type, DO_Type, DO_Type, DO_Type],
    "JY-DAM0204":[[], [], [], [], DO_Type, DO_Type],
    "JY-DAM0816":[[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "SD700":[[], [], [], [], [], [], [], [], DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "IMA-C":[SR_Out_Type],
    "THSXX":[[], [], []],
    "SD600":[DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type, DO_Type],
    "BAT-08":[[], [], [], [], [], [], [], [], ],
    "SUTO-FLOW":[[]],
  }
}
