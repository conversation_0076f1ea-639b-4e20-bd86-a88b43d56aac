import { DashboardService } from './../../../service/dashboard.service';
import { ProjectService } from './../../../service/project.service';
import { DeviceService } from './../../../service/device.service';

import {
  OnInit,
  Component,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { dia, shapes, ui } from '@joint/plus';
import { Router, ActivatedRoute } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { setTheme } from '@joint/plus';
import { CommonModule } from '@angular/common';
import {
  TableModule,
  GridModule,
  HeaderComponent,
  NavModule
} from '@coreui/angular';
import * as joint from 'jointjs';
import '@joint/plus/joint-plus.css';
import * as vsmshapes from '@joint/shapes-vsm';
import { type_name_trans } from '../object_type';
import { object_type_list } from '../object_type';

const mergedShapes = {
  ...shapes,
  ...vsmshapes,
};

interface plot_object {
  type: string;
  name: string;
  place_id: string;
  serialNo: string;
}
interface link {
  type: string;
  name: string;
  source: string;
  target: string;
  source_serialNo: string;
  target_serialNo: string;
  serialNo: string;
}

interface color {
  threshold: number | null;
  color: string;
  transparent: boolean;
}

@Component({
  selector: 'app-device-dashboard-view',
  standalone: true,
  imports: [
    TableModule,
    GridModule,
  HeaderComponent,
    NavModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
  ],

  templateUrl: './device-dashboard-view.component.html',
  styleUrl: './device-dashboard-view.component.scss',
})
export class DeviceDashboardViewComponent implements OnInit {
  // Declaration
  @ViewChild('canvas') canvas: ElementRef;
  project_code = '';
  projectplot_name = '';
  place_id_list: string[] = [];
  namespace = shapes;
  graph: dia.Graph;
  commandManager: dia.CommandManager;
  visible = false;
  modal_title = '';
  paper: dia.Paper;
  paperScroller: ui.PaperScroller;
  rect1: shapes.standard.Rectangle;
  selection: ui.Selection;
  elements_Gateway: Object[] = [];
  elements_Infas: Object[] = [];
  elements_RS485: Object[] = [];
  elements_IO: Object[] = [];
  elements_Tools: Object[] = [];
  reader: FileReader;
  imageUrl = '';
  nav: ui.Navigator;
  halo: ui.Halo;
  selection_handles: ui.Selection.Options;
  currentEditingElement: HTMLElement | null = null;
  modal_type: string;
  editing_object: string;
  editing_object_id: string;
  pending_save: boolean;
  new_serialNo: string;
  new_place_id: string;
  new_object_name: string;
  object_type_list = object_type_list;
  projectobject_list: plot_object[] = [];
  projectlink_list: link[] = [];
  selected_object_setting_index: number;
  realTime_list: string[];
  update_msg: string;
  imageAddress: string;
  plotname_visible: boolean = false;
  updatedTime_visible: boolean = false;
  constructor(
    private DashboardService: DashboardService,
    private route: ActivatedRoute,
    private el: ElementRef,
    private ProjectService: ProjectService,
    private DeviceService: DeviceService,
    private router: Router
  ) {
    this.load_project_info();
    this.update_msg = '取得遠端資料中...';
  }
  load_project_info() {
    this.projectobject_list = [];
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.DashboardService.get_projectplot_object(this.project_code).subscribe(
      (data) => {
        for (let i = 0; i < data.object_list.length; i++) {
          this.projectobject_list.push({
            type: data.object_list[i].type || '',
            name: data.object_list[i].name,
            place_id: data.object_list[i].place_id,
            serialNo: data.object_list[i].serialNo,
          });
        }
        localStorage.setItem('token', data.token);
        this.projectlink_list = [];
        this.DashboardService.get_projectplot_link(this.project_code).subscribe(
          (data) => {
            for (let i = 0; i < data.link_list.length; i++) {
              const matchedSource = this.projectobject_list.find(
                (obj) => obj.serialNo === data.link_list[i].source
              );
              const source_name = matchedSource ? matchedSource.name : '';
              const matchedTarget = this.projectobject_list.find(
                (obj) => obj.serialNo === data.link_list[i].target
              );
              const target_name = matchedTarget ? matchedTarget.name : '';
              this.projectlink_list.push({
                type: type_name_trans(data.link_list[i].type, true) || '',
                name: data.link_list[i].name,
                source: source_name,
                source_serialNo: data.link_list[i].source,
                target: target_name,
                target_serialNo: data.link_list[i].target,
                serialNo: data.link_list[i].serialNo,
              });
            }
            this.filter_invalid_object();
            localStorage.setItem('token', data.token);
          },
          (error) => {

          }
        );
      },
      (error) => {

      }
    );

    this.ProjectService.get_project_info(this.project_code).subscribe(
      (data) => {
        this.place_id_list = data.place_id;
        localStorage.setItem('token', data.token);
      },
      (error) => {

      }
    );
  }
  ngOnInit(): void {

    this.namespace = mergedShapes;
    this.pending_save = false;
    this.graph = new dia.Graph({}, { cellNamespace: this.namespace });
    this.project_code = this.route.snapshot.paramMap.get('project_code') || '';
    this.projectplot_name =
    this.route.snapshot.paramMap.get('projectplot_name') || '';
    this.modal_type = '';
    this.editing_object = '';
    this.editing_object_id = '';
    this.new_serialNo = '0';
    this.new_place_id = '';
    this.new_object_name = '';
    this.imageAddress = '';
    this.realTime_list = [];
    this.selected_object_setting_index = -1;
    this.DashboardService.get_plot(
      this.project_code,
      this.projectplot_name
    ).subscribe(
      (data) => {
        localStorage.setItem('token', data.token);
        this.load_graph(data.data);
        this.plotname_visible = this.graph.get('config')?.plotNameVisible ?? true;
        this.updatedTime_visible = this.graph.get('config')?.updatedTimeVisible ?? true;
        const period = this.graph.get('config')?.updatedTimePeriod;
        const validPeriod = (typeof period === 'number' && period >= 30 && period <= 300) ? period : 60;
        setInterval(() => this.update_all_realTime_data(), validPeriod * 1000);
        for (const rawCell of data.data.cells) {
          const model = this.graph.getCell(rawCell.id);
          if (rawCell['config']?.showInViewMode === false){
            model?.remove();
          } else if ((rawCell['config']?.showTableName || '').length == 17 && (rawCell['config']?.showColumnName || '').length>0) {
            this.realTime_list.push(
              `${rawCell.id}:${rawCell['config']?.showTableName}:${rawCell['config']?.showColumnName}`
            );
            this.get_realTime_data(
              rawCell['config']?.showTableName,
              rawCell['config']?.showColumnName,
              rawCell['config']?.timeFormat || '%m:%d;%H:%M'
            ).then(({ value: realTimeValue, id: dataTime }) => {
              const combinedText = `${rawCell['config'].showNameVisible ?? true ? rawCell['config']?.showName + '\n' : ''
              }${rawCell['config'].tableColumnVisible ?? true ? rawCell['config'].showColumnName + ': ' : ''
              }${this.formatToDecimals(realTimeValue, rawCell['config'].dataFloatNumber ?? 0)
              }${rawCell['config'].timeVisible ?? true ? '\n' + dataTime : ''}`;
              model?.attr({
                label: {
                  text: combinedText,
                  style: {
                    whiteSpace: 'nowrap',
                    fontSize: rawCell['attrs'].label['font-size'] || 14,
                  },
                },
              });
              if (rawCell['config'].dynamic_fillColor_available){
                model?.attr({
                  body: {
                    fill: this.get_dynamic_color(Number(realTimeValue), model.get('config')?.dynamic_fillColor)
                  }
                });
              }


              this.paper.findViewByModel(model)?.update();
              this.update_time_msg();
            });
          } else if ((rawCell['config']?.showName || '').length > 0) {
            model?.attr({
              label: {
                text: rawCell['config']?.showName,
                style: {
                  whiteSpace: 'nowrap',
                  fontSize: rawCell['attrs'].label['font-size'] || 14,
                },
              },
            });
          }

        }

      },
      (error) => {

      }
    );
    this.paper = new dia.Paper({
      el: document.getElementById('my-paper'),
      model: this.graph,
      width: 2000,
      height: 2000,
      gridSize: 10,
      background: { color: '#F5F5F5' },
      cellViewNamespace: this.namespace,
      defaultRouter: { name: 'orthogonal' },
      defaultConnector: { name: 'straight', args: { cornerType: 'line' } },
      interactive: {
        elementMove: false,
        labelMove: false,
        linkMove: false,
      },
    });
    this.paperScroller = new ui.PaperScroller({
      paper: this.paper,
      scrollWhileDragging: true,
    });
    this.selection = new ui.Selection({
      paper: this.paper,
    });


    this.selection.removeHandle('rotate');
    document
      .getElementById('paper')!
      .appendChild(this.paperScroller.render().el);
    this.paperScroller.centerContent();
    this.paperScroller.center();
    setTheme('modern');
    let lastDistance: number | null = null;

    this.paperScroller.el.addEventListener(
      'touchmove',
      (e: TouchEvent) => {
        if (e.touches.length >= 2) {
          e.preventDefault(); // 阻止預設縮放行為

          const dx = e.touches[0].clientX - e.touches[1].clientX;
          const dy = e.touches[0].clientY - e.touches[1].clientY;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (lastDistance !== null) {
            const delta = distance - lastDistance;

            // 只有超過一定距離變化才進行縮放
            if (Math.abs(delta) > 1) {
              let scaleFactor = 1 + delta / 300; // 放大或縮小的比例，數字越大越平滑

              scaleFactor = Math.max(0.95, Math.min(scaleFactor, 1.05));

              this.paperScroller.zoom(this.paperScroller.zoom() * scaleFactor, {
                max: 5,
                min: 0.2,
                ox: (e.touches[0].clientX + e.touches[1].clientX) / 2,
                oy: (e.touches[0].clientY + e.touches[1].clientY) / 2,
                absolute: true
              });
            }
          }

          lastDistance = distance;
        }
      },
      { passive: false }
    );

    this.paperScroller.el.addEventListener(
      'touchend',
      (e: TouchEvent) => {
        if (e.touches.length < 2) {
          lastDistance = null;
        }
      },
      { passive: true }
    );
    let pinchTimeout: any = null; // 用於存儲節流的計時器
    this.paper.on('paper:pinch', (_evt, ox, oy, scale) => {
      if (pinchTimeout) return; // 如果計時器存在，則跳過此次觸發

      pinchTimeout = setTimeout(() => {
        pinchTimeout = null; // 清除計時器，允許下一次觸發
      }, 10); // 設置觸發間隔為 100 毫秒

      this.paperScroller.zoom(this.paperScroller.zoom() * scale, {
        min: 0.2,
        max: 5,
        ox,
        oy,
        absolute: true,
      });
    });
      // 手指按下事件
    this.paper.on('blank:pointerdown', (evt, x, y) => {
      if (lastDistance === null){
        this.paperScroller.startPanning(evt);
      }
    });
    this.paper.on('cell:pointerdown', (cellView, evt, x, y) => {
      if (lastDistance === null) {
        this.paperScroller.startPanning(evt);
      }
    });

  }
  load_graph(graph: any) {
    if (graph.cells) {
      this.graph.fromJSON(graph);
      if (graph.backgroundImage.length > 0) {
        this.DashboardService.get_pjplot_image(
          this.project_code,
          this.projectplot_name
        ).subscribe(
          (data) => {
            const reader = new FileReader();
            reader.onload = (e: ProgressEvent<FileReader>) => {
              const fileURL = URL.createObjectURL(data);
              this.imageAddress = fileURL;
              const img = new Image();
              img.onload = () => {
                const imgWidth = img.width;
                const imgHeight = img.height;
                this.paper.el.style.backgroundImage = `url(${fileURL})`;
                this.paper.el.style.backgroundSize = 'cover';
                this.paper.el.style.backgroundRepeat = 'no-repeat';
                this.paper.el.style.backgroundPosition = 'center';
                this.paper.el.style.backgroundBlendMode = 'overlay';
                this.paper.el.style.backgroundColor =
                  `rgba(255, 255, 255, ${this.graph.get('config')?.backgroundColorAlpha || 0})`;
                this.paper.setDimensions(
                  ((2000 * imgWidth) / imgHeight) * this.paperScroller.zoom(),
                  2000 * this.paperScroller.zoom()
                );
              };
              img.src = fileURL;
            };
            reader.readAsDataURL(data);
          },
          (error) => {

          }
        );
      }
    }
  }

  filter_invalid_object() {
    const cells = this.graph.get('cells') || [];
    for (const cell of cells) {
      const serialNo = cell.get('config')?.serialNo || '';

      if (!serialNo) {
        if (cell.get('type') === 'standard.TextBlock') continue;
        this.graph.removeCells([cell]);
        continue;
      }

      const inObjects = this.projectobject_list.some(
        (obj) => obj.serialNo === serialNo
      );
      const inLinks = this.projectlink_list.some(
        (link) => link.serialNo === serialNo
      );
      var revLinks = false;
      if (serialNo.split(':').length > 1) {
        revLinks = this.projectlink_list.some(
          (link) =>
            link.serialNo ===
            `${serialNo.split(':')[0]}:${serialNo.split(':')[2]}:${
              serialNo.split(':')[1]
            }`
        );
      }

      if (!inObjects && !inLinks && !revLinks) {
        this.graph.removeCells([cell]);
      }
    }
  }

  // Functions
  getImageDimensions(url: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
        });
      };
      img.onerror = (error) => {
        reject('Failed to load image');
      };
      img.src = url;
    });
  }

  async update_all_realTime_data() {
    for (let str of this.realTime_list) {
      const id = str.split(':')[0];
      const model = this.graph.getCell(id);
      const tableName = str.split(':')[1];
      const columnName = str.split(':')[2];
      this.get_realTime_data(
        tableName,
        columnName,
        model.get('config')?.timeFormat || '%m:%d;%H:%M'
      ).then(({ value: realTimeValue, id: dataTime }) => {
        const combinedText = `${model.get('config')?.showNameVisible ?? true ? model.get('config')?.showName + '\n' : ''
        }${model.get('config')?.tableColumnVisible ?? true ? columnName + ': ' : ''
        }${this.formatToDecimals(realTimeValue, model.get('config')?.dataFloatNumber ?? 0)
        }${model.get('config')?.timeVisible ?? true ? '\n' + dataTime : ''}`;

        model?.attr({
          label: {
            text: combinedText,
          },
        });
        if (model.get('config')?.dynamic_fillColor_available){
          model?.attr({
            body: {
              fill: this.get_dynamic_color(Number(realTimeValue), model.get('config')?.dynamic_fillColor)
            }
          });
        }
      });


      this.paper.findViewByModel(model)?.update();
    }
    this.update_time_msg();
  }

  get_realTime_data(
    table_name: string,
    column_name: string,
    time_format: string = '%m:%d;%H:%M'
  ): Promise<{ value: string; id: string }> {
    return new Promise((resolve) => {
      this.DeviceService.search('197', table_name).subscribe(
        (data) => {
          localStorage.setItem('token', data.token);
          for (let column of data.data[0].data) {
            if (column_name in column) {
              resolve({
                value: column[column_name].toString(),
                id: this.formatDate(data.data[0].data[0].id, time_format),
              });
              return;
            }
          }
          resolve({ value: '', id: '' });
        },
        (error) => {
          resolve({ value: 'Disconnected', id: '' });
        }
      );
    });
  }
  getCurrentTime(): string {
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份是從0開始，+1 以便顯示 1 到 12
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');

    return `${month}-${day} ${hour}:${minute}:${second}`;
  }
  formatDate(dateString: string, format: string = '%m:%d;%H:%M'): string {
    const date = new Date(dateString);
    date.setHours(date.getHours() - 8); //校正時差
    // 提取月份、日期、小時、分鐘
    const month = date.toLocaleString('en-US', { month: 'short' }); // 取得縮寫的月份 (如：'Mar' 或 'Apr')
    const day = String(date.getDate()).padStart(2, '0'); // 取得日期，並確保是兩位數
    const hour = String(date.getHours()).padStart(2, '0'); // 取得小時，並確保是兩位數
    const minute = String(date.getMinutes()).padStart(2, '0'); // 取得分鐘，並確保是兩位數
    if (format === '%H:%M'){
      return `${hour}:${minute}`;
    } else {
      return `${month}:${day};${hour}:${minute}`;
    }

  }
  formatToDecimals(input: string, digit: number): string {
    const num = parseFloat(input);
    if (isNaN(num)) {
      return 'Invalid number';
    }
    return num.toFixed(digit); // 回傳字串
  }
  update_time_msg() {
    this.update_msg = `最近更新時間：${this.getCurrentTime()}`;
  }

  get_dynamic_color(value: number, dynamic_color: { threshold?: number; color: string ; transparent: boolean}[]): string {
    let ret = dynamic_color[0].transparent ? 'transparent' : dynamic_color[0].color;
    for (let i = 1; i < dynamic_color.length; i++) {
      const color = dynamic_color[i];
      if (color.threshold !== undefined && value > color.threshold) {
        if (color.transparent){
          ret = 'transparent'
        } else {
          ret = color.color;
        }

        break
      }
    }

    return ret;
  }

}
