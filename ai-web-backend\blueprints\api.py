from flask import request,url_for, Blueprint, jsonify, current_app, send_from_directory, abort
from accessories import mongo, update_json_in_mongo
from werkzeug.utils import secure_filename
from pathlib import Path
import jwt
from accessories import mongo
from datetime import datetime, timedelta
from functools import wraps
import os, json
import secrets
import string


api_page = Blueprint('api', __name__)
def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'OPTIONS':
            return '', 204  # 讓預檢通過，不驗證 token
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith("Bearer "):
            return jsonify({'message': 'Token is missing or invalid!'}), 401
        token = auth_header.split(" ")[1]
        if not verify_token(token):
            return jsonify({'message': 'Invalid token!'}), 401
        return f(token, *args, **kwargs)
    return decorated_function

def role_required(roles):
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 預檢請求
            if request.method == 'OPTIONS':
                return '', 204  

            # Token 驗證
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith("Bearer "):
                return jsonify({'message': 'Token is missing or invalid!'}), 401
            token = auth_header.split(" ")[1]
            if not verify_token(token):
                return jsonify({'message': 'Invalid token!'}), 401

            try:
                # 解析 token 取得角色
                current_user_roles = get_user_info(token, 'role')
            except Exception as e:
                return jsonify({'message': 'Token is invalid!'}), 401

            # OR邏輯：有交集就允許
            if not set(roles).intersection(set(current_user_roles)):
                return jsonify({'message': 'Permission denied!'}), 403

            return f(token, *args, **kwargs)
        return decorated_function
    return wrapper

def roles_required(roles):
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 預檢請求
            if request.method == 'OPTIONS':
                return '', 204  

            # Token 驗證
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith("Bearer "):
                return jsonify({'message': 'Token is missing or invalid!'}), 401
            token = auth_header.split(" ")[1]
            if not verify_token(token):
                return jsonify({'message': 'Invalid token!'}), 401

            try:
                # 解析 token 取得角色
                current_user_roles = get_user_info(token, 'role')
            except Exception as e:
                return jsonify({'message': 'Token is invalid!'}), 401

            # AND邏輯：必須全部符合
            if not set(roles).issubset(set(current_user_roles)):
                return jsonify({'message': 'Permission denied!'}), 403

            return f(token, *args, **kwargs)
        return decorated_function
    return wrapper


def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == 'OPTIONS':
            return '', 204  # 讓預檢通過，不驗證 token
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith("Bearer "):
            return jsonify({'message': 'Token is missing or invalid!'}), 401
        token = auth_header.split(" ")[1]
        if not verify_token(token):
            return jsonify({'message': 'Invalid token!'}), 401
        return f(token, *args, **kwargs)
    return decorated_function

def enterprise_role_required(roles):
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method == 'OPTIONS':
                return '', 204

            # 取得 Token
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith("Bearer "):
                return jsonify({'message': 'Token is missing!'}), 401
            token = auth_header.split(" ")[1]

            try:
                # 取得 taxId
                taxId = None
                if request.is_json:
                    taxId = request.get_json().get("taxId")
                if not taxId:
                    return jsonify({'message': 'taxId is missing!'}), 400

                # 取得當前用戶 email
                current_user_email = get_user_info(token, 'email')

                # 查找該 taxId 對應的 enterprise 資訊
                group = mongo.db.enterprise.find_one({"taxId": taxId})
                if not group:
                    return jsonify({'message': 'Enterprise not found!'}), 404

                current_user_roles = []
                for account in group['accounts']:
                    if account.get("email") == current_user_email:
                        current_user_roles = account.get('roles')

                # OR 邏輯（至少一個角色符合）
                if not set(roles).intersection(set(current_user_roles)):
                    return jsonify({'message': 'Permission denied!'}), 403
            except Exception as e:
                return jsonify({'message': 'Token is invalid!'}), 401

            return f(taxId, token, *args, **kwargs)
        return decorated_function
    return wrapper


def enterprise_roles_required(roles):
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method == 'OPTIONS':
                return '', 204

            # 取得 Token
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith("Bearer "):
                return jsonify({'message': 'Token is missing!'}), 401
            token = auth_header.split(" ")[1]

            try:
                # 取得 taxId
                taxId = None
                if request.is_json:
                    taxId = request.get_json().get("taxId")
                if not taxId:
                    taxId = request.args.get("taxId")
                if not taxId:
                    return jsonify({'message': 'taxId is missing!'}), 400

                # 取得當前用戶 email
                current_user_email = get_user_info(token, 'email')

                # 查找該 taxId 對應的 enterprise 資訊
                group = mongo.db.enterprise.find_one({"taxId": taxId})
                if not group:
                    return jsonify({'message': 'Enterprise not found!'}), 404

                current_user_roles = []
                for account in group['accounts']:
                    if account.get("email") == current_user_email:
                        current_user_roles = account.get('roles')

                # AND 邏輯（必須全部符合）
                if not set(roles).issubset(set(current_user_roles)):
                    return jsonify({'message': 'Permission denied!'}), 403

            except Exception as e:
                return jsonify({'message': 'Token is invalid!'}), 401

            return f(taxId, token, *args, **kwargs)
        return decorated_function
    return wrapper


def team_role_required(roles):
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method == 'OPTIONS':
                return '', 204

            # 取得 Token
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith("Bearer "):
                return jsonify({'message': 'Token is missing!'}), 401
            token = auth_header.split(" ")[1]

            try:
                # 取得 taxId
                team_code = None
                if request.is_json:
                    team_code = request.get_json().get("team_code")
                if not team_code:
                    return jsonify({'message': 'team_code is missing!'}), 400

                # 取得當前用戶 email
                current_user_email = get_user_info(token, 'email')
                # 查找該 team_code 對應的 team 資訊
                group = mongo.db.team.find_one({"team_code": team_code})
                if not group:
                    return jsonify({'message': 'Enterprise not found!'}), 404

                current_user_roles = []
                for account in group['accounts']:
                    if account.get("email") == current_user_email:
                        current_user_roles = account.get('roles')

                # OR 邏輯（至少一個角色符合）
                if not set(roles).intersection(set(current_user_roles)):
                    return jsonify({'message': 'Permission denied!'}), 403
            except Exception as e:
                return jsonify({'message': 'Token is invalid!'}), 401

            return f(team_code, token, *args, **kwargs)
        return decorated_function
    return wrapper


def team_roles_required(roles):
    def wrapper(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method == 'OPTIONS':
                return '', 204

            # 取得 Token
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith("Bearer "):
                return jsonify({'message': 'Token is missing!'}), 401
            token = auth_header.split(" ")[1]

            try:
                # 取得 taxId
                team_code = None
                if request.is_json:
                    team_code = request.get_json().get("team_code")
                if not team_code:
                    team_code = request.args.get("team_code")
                if not team_code:
                    return jsonify({'message': 'team_code is missing!'}), 400

                # 取得當前用戶 email
                current_user_email = get_user_info(token, 'email')

                # 查找該 team_code 對應的 team 資訊
                group = mongo.db.team.find_one({"team_code": team_code})
                if not group:
                    return jsonify({'message': 'Team not found!'}), 404

                current_user_roles = []
                for account in group['accounts']:
                    if account.get("email") == current_user_email:
                        current_user_roles = account.get('roles')

                # AND 邏輯（必須全部符合）
                if not set(roles).issubset(set(current_user_roles)):
                    return jsonify({'message': 'Permission denied!'}), 403

            except Exception as e:
                return jsonify({'message': 'Token is invalid!'}), 401

            return f(team_code, token, *args, **kwargs)
        return decorated_function
    return wrapper



def verify_token(token):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    exp = decoded_token['exp']
    exp = datetime.strptime(exp, "%Y%m%d%H%M%S")
    if datetime.now()<exp:
        return True
    return False

def get_refresh_token(token):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    user_email = decoded_token['user']
    new_token =  jwt.encode({'user': user_email, 'exp': datetime.strftime(datetime.now() + timedelta(hours=24), format="%Y%m%d%H%M%S")}, current_app.config['SECRET_KEY'])
    return new_token

def check_user_access(token, route):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    user_email = decoded_token['user']
    user = mongo.db.user.find_one({"email": user_email})
    user_role = user['role']
    for i in user_role:
        try:
            for j in current_app.config['ROUTE_ROLE_MAPPING'].keys():
                if route[:len(j)] == j and i in current_app.config['ROUTE_ROLE_MAPPING'][route[:len(j)]]:
                    return True
        except Exception:
            continue
    return False

def find_nth(s, char, n):
    index = -1
    for _ in range(n):
        index = s.find(char, index + 1)
        if index == -1:
            return -1
    return index

def check_enterprise_user_access(groupId, token, route):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    user_email = decoded_token['user']
    enterprise = mongo.db.enterprise.find_one({"taxId": groupId})
    user_roles = []
    for account in enterprise.get("accounts"):
        if account.get("email") == user_email:
            user_roles = account.get("roles")
    for i in user_roles:
        try:
            third_slash_index = find_nth(route, '/', 3)
            sub_route = route[third_slash_index:] if third_slash_index != -1 else ''
            for j in current_app.config['ROUTE_ENTERPRISE_ROLE_MAPPING'].keys():
                if sub_route[:len(j)] == j and i in current_app.config['ROUTE_ENTERPRISE_ROLE_MAPPING'][j]:
                    return True
        except Exception:
            continue
    return False

def check_team_user_access(teamId, token, route):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    user_email = decoded_token['user']
    team = mongo.db.team.find_one({"team_code": teamId})
    user_roles = []
    for account in team.get("accounts"):
        if account.get("email") == user_email:
            user_roles = account.get("roles")
    for i in user_roles:
        try:
            third_slash_index = find_nth(route, '/', 3)
            sub_route = route[third_slash_index:] if third_slash_index != -1 else ''
            for j in current_app.config['ROUTE_TEAM_ROLE_MAPPING'].keys():
                if sub_route[:len(j)] == j and i in current_app.config['ROUTE_TEAM_ROLE_MAPPING'][j]:
                    return True
        except Exception:
            continue
    return False

def get_user_info(token, key):
    decoded_token = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
    user = mongo.db.user.find_one({"email": decoded_token['user']})
    return user.get(key)


@api_page.route('/get-user-roles', methods=['OPTIONS', 'POST'])
@token_required
def getUserRole(token):
    roles = get_user_info(token, 'role')
    return jsonify({
        'token': get_refresh_token(token),
        'roles': roles}), 200


@api_page.route('/get_self_info', methods=['OPTIONS', 'POST'])
@token_required
def get_self_info(token):
    user = mongo.db.user.find_one({"email": get_user_info(token, 'email')}, {"_id": 0, "password": 0})
    credit_card = user.get("payment_credit_card", {})
    if credit_card:
        credit_card["number"] = "**** **** **** " + credit_card["number"][-4:]
        credit_card.pop("cvv", None)
    user["payment_credit_card"] = credit_card
    if "notification_method" not in user.keys():
        user["notification_method"] = {
            "web": True,
            "mail": True
        }
    return jsonify({
        "token": get_refresh_token(token),
        "user": user}), 200




@api_page.route('/get-user-avatar', methods=['OPTIONS', 'POST'])
@token_required
def get_user_avatar(token):
    profile_picture_filename = None
    user_email = get_user_info(token, 'email') 
    all_users_with_same_email = list(mongo.db.user.find({'email': user_email}))
    if all_users_with_same_email:
        full_user_document = all_users_with_same_email[0]
        profile_picture_filename = full_user_document.get('profile_picture')
    folder = os.path.abspath(current_app.config['AVATAR_UPLOAD_FOLDER'])
    if profile_picture_filename:
        full_path = os.path.join(folder, profile_picture_filename)
        if os.path.exists(full_path):
            return send_from_directory(folder, profile_picture_filename)
    default_avatar_filename = 'default-user.png'
    default_avatar_path = os.path.join(folder, default_avatar_filename)
    if os.path.exists(default_avatar_path):
        return send_from_directory(folder, default_avatar_filename)
    else:
        return abort(404, description="Avatar or default avatar not found on server.")


@api_page.route('/update_self_settings', methods=['OPTIONS', 'POST'])
@token_required
def update_self_settings(token):
    data = request.get_json()
    user_info = mongo.db.user.find_one({"email": get_user_info(token, 'email')}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": get_user_info(token, 'email')}).get("_id")
    user_info["firstname"] = data.get("user").get("firstname")
    user_info["lastname"] = data.get("user").get("lastname")
    user_info["phone"] = data.get("user").get("phone")
    user_info["notification_method"] = data.get("user").get('notification_method', {"web": True, "mail": True})
    update_json_in_mongo(user_info, 'user', user_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200


@api_page.route('/update_user_avatar', methods=['OPTIONS', 'POST'])
@token_required
def update_user_avatar(token):
    user_email = get_user_info(token, 'email')
    user_info = mongo.db.user.find_one({"email": user_email}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": user_email}).get("_id")

    file = request.files.get('profile_picture')
    if not file:
        return jsonify({"error": "No file uploaded"}), 400

    # 刪除舊檔案
    avatar_dir = current_app.config['AVATAR_UPLOAD_FOLDER']
    base_name = f"avatar_{user_email}"
    for f in os.listdir(avatar_dir):
        if f.startswith(base_name):
            os.remove(os.path.join(avatar_dir, f))

    # 儲存新檔案
    extension = os.path.splitext(file.filename)[1]
    filename = f"{base_name}{extension}"
    os.makedirs(avatar_dir, exist_ok=True)
    file.save(os.path.join(avatar_dir, filename))

    # 更新 DB
    user_info["profile_picture"] = filename
    update_json_in_mongo(user_info, 'user', user_dbId)

    return jsonify({"token": get_refresh_token(token)}), 200


@api_page.route('/update_payment', methods=['OPTIONS', 'POST'])
@token_required
def update_payment(token):
    data = request.get_json()
    payment_method = data.get("payment_method")
    user_email = get_user_info(token, 'email')
    user_info = mongo.db.user.find_one({"email": user_email}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": user_email}).get("_id")
    if payment_method not in ['credit', 'bank']:
        return jsonify({"msg": 'invalid payment method'}), 400
    user_info['payment_method'] = payment_method
    if payment_method == 'credit':
        user_info["payment_credit_card"] = data.get("credit_card")
    else:
        user_info["payment_bank"] = data.get("bank")
    update_json_in_mongo(user_info, 'user', user_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200

@api_page.route('/update_payout', methods=['OPTIONS', 'POST'])
@token_required
def update_payout(token):
    data = request.get_json()
    user_email = get_user_info(token, 'email')
    user_info = mongo.db.user.find_one({"email": user_email}, {"_id": 0})
    user_dbId = mongo.db.user.find_one({"email": user_email}).get("_id")
    user_info["payout_bank"] = data.get("bank")
    update_json_in_mongo(user_info, 'user', user_dbId)
    return jsonify({"token": get_refresh_token(token)}), 200



def generate_secure_random_string(length=12):
    chars = string.ascii_letters + string.digits
    return ''.join(secrets.choice(chars) for _ in range(length))


@api_page.route('/isAuthenticated', methods=['OPTIONS', 'POST'])
@token_required
def is_authenticated(token):
    return jsonify({'isAuthenticated': True, 'token': get_refresh_token(token)}), 200



@api_page.route('/verify-access', methods=['POST'])
@token_required
def verify_access(token):
    data = request.get_json()
    route = data.get('route')
    if check_user_access(token, route):
        return jsonify({'auth': True}), 200

    return jsonify({'auth': False}), 403


@api_page.route('/verify-enterprise-access', methods=['POST'])
@enterprise_role_required(['member'])
def verify_enterprise_access(groupId, token):
    data = request.get_json()
    route = data.get('route')
    if check_enterprise_user_access(groupId, token, route):
        return jsonify({'auth': True}), 200

    return jsonify({'auth': False}), 403

@api_page.route('/verify-team-access', methods=['POST'])
@team_role_required(['member'])
def verify_team_access(teamId, token):
    data = request.get_json()
    route = data.get('route')
    if check_team_user_access(teamId, token, route):
        return jsonify({'auth': True}), 200

    return jsonify({'auth': False}), 403
