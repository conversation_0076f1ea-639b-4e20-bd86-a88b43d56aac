from flask import request, Blueprint, jsonify, current_app
from accessories import mongo, sqldb,redis_client
from blueprints.api import get_user_info, get_refresh_token, verify_token, role_required
from sqlalchemy import text
from datetime import datetime, timezone, timedelta
from config import Config

mailbox_page = Blueprint('mailbox', __name__)

@mailbox_page.route('/load_mails', methods=['OPTIONS', 'POST', 'GET'])
@role_required(['user', 'project leader', 'warehouse', 'eng'])
def load_mails(token):
    try:
        data = request.get_json()
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))
        offset = (page - 1) * page_size
        user_email = get_user_info(token, 'email')
        database = data.get('database')
        engine = sqldb.get_engine(current_app, bind=database)
        taipei_tz = timezone(timedelta(hours=8))
        current_time = datetime.now(taipei_tz)
        with engine.connect() as connection:
            mail_query = text("""
                SELECT m.*, 
                       (SELECT event 
                        FROM mail_event 
                        WHERE mail_id = m.id 
                        ORDER BY event_time DESC 
                        LIMIT 1) as latest_status
                FROM mail_info m
                WHERE (receiver = :receiver_email OR (sender = :sender_email AND mail_type = 1)) 
                ORDER BY time DESC
                LIMIT :limit OFFSET :offset
            """)            
            mail_rows = list(connection.execute(mail_query, {
                'receiver_email': user_email,
                'sender_email': user_email,
                'limit': page_size,
                'offset': offset
            }))
            emails = {row.sender for row in mail_rows} | {row.receiver for row in mail_rows}
            user_info_cache = {
                doc['email']: doc 
                for doc in mongo.db.user.find(
                    {"email": {"$in": list(emails)}}, 
                    {"email": 1, "firstname": 1, "lastname": 1}
                )
            }
            mails = []
            for row in mail_rows:
                pattern = f"mail:{row.sender}:{row.receiver}:{row.subject}:{row.argument}"
                redis_keys = redis_client.keys(pattern)
                mail_token = redis_keys[0].decode('utf-8') if redis_keys else None
                sender_info = user_info_cache.get(row.sender, {})
                sender_name = f"{sender_info.get('firstname', '')} {sender_info.get('lastname', '')}".strip() or row.sender
                latest_status = row.latest_status or 'failed'
                if latest_status == 'send' and row.mail_type == 1:
                    time_diff_hours = (current_time - row.time.replace(tzinfo=taipei_tz)).total_seconds() / 3600
                    if time_diff_hours > row.due_time or not mail_token:
                        latest_status = 'failed'
                mails.append({
                    'id': row.id,
                    'sender_name': sender_name,
                    'receiver': row.receiver,
                    'subject': row.subject,
                    'content': row.content,
                    'status': latest_status,
                    'mail_type': row.mail_type,
                    'time': row.time.replace(tzinfo=taipei_tz).isoformat(),
                    'mail_token': mail_token
                })
            total = connection.execute(text("""
                SELECT COUNT(*) FROM mail_info 
                WHERE (receiver = :receiver_email OR (sender = :sender_email AND mail_type = 1))
            """), {
                'receiver_email': user_email, 
                'sender_email': user_email
            }).scalar()
        return jsonify({
            "token": get_refresh_token(token),
            "mail_List": mails,
            "total": total
        }), 200
    except Exception as e:
        current_app.logger.error(f"Error loading mails: {str(e)}")
        return jsonify({"error": "Failed to load mails", "message": str(e)}), 500

@mailbox_page.route('/view_mail_details/<mail_id>', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def view_mail_details(token, mail_id):
    data = request.get_json()
    database = data.get('database') 
    engine = sqldb.get_engine(current_app, bind=database)
    with engine.begin() as connection:
        query = text("""
            SELECT id, sender, argument, due_time, time AS mail_creation_time, mail_type 
            FROM mail_info 
            WHERE id = :mail_id
        """)
        result = connection.execute(query, {"mail_id": mail_id}).mappings().first()
        if not result:
            return jsonify({"error": "Mail not found"}), 404
        mail_type = result['mail_type']
        event_query = text("""
            SELECT event 
            FROM mail_event 
            WHERE mail_id = :mail_id 
            ORDER BY event_time DESC 
            LIMIT 1
        """)
        latest_event = connection.execute(event_query, {"mail_id": mail_id}).scalar()
        if latest_event != 'viewed':
            connection.execute(
                text("INSERT INTO mail_event (mail_id, event, event_time) VALUES (:mail_id, 'viewed', NOW())"),
                {"mail_id": mail_id}
            )
    return jsonify({
        "id": result['id'],
        "sender_name": result['sender'],
        "argument": result['argument'],
        "status": "response",
        "token": get_refresh_token(token),
    }), 200

@mailbox_page.route('/unread_mails_count', methods=['OPTIONS', 'POST'])
@role_required(['user'])
def unread_mails_count(token):
    data = request.get_json()
    user_email = get_user_info(token, 'email')
    database = data.get('database')
    engine = sqldb.get_engine(current_app, bind=database)
    taipei_tz = timezone(timedelta(hours=8))
    current_time = datetime.now(taipei_tz)
    with engine.connect() as connection:
        query = text("""
            SELECT COUNT(*) AS unread_count 
            FROM mail_info mi
            LEFT JOIN (
                SELECT mail_id, event
                FROM mail_event
                WHERE (mail_id, event_time) IN (
                    SELECT mail_id, MAX(event_time)
                    FROM mail_event
                    GROUP BY mail_id
                )
            ) AS me ON mi.id = me.mail_id
            WHERE mi.receiver = :receiver_email 
              AND me.event = 'send'
              AND (
                  (mi.mail_type = 1 AND TIMESTAMPDIFF(HOUR, mi.time, :current_time) < mi.due_time)
                  OR (mi.mail_type = 0)
              )
        """)
        result = connection.execute(query, {
            'receiver_email': user_email,
            'current_time': current_time
        }).fetchone()
        unread_count = result[0] if result else 0
    return jsonify({
        "token": get_refresh_token(token),
        "unread_count": unread_count >0
    }), 200
