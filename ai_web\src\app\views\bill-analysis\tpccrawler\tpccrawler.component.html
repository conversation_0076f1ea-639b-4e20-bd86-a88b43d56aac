<c-row>
  <c-col>
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>電費分析</strong>
      </c-card-header>
      <c-card-body>
          <c-col>
            <c-row class="mb-4">
                <label [sm]="2" cCol cLabel="col" for="place_id">
                  電號
                </label>
              <c-col [sm]="10">
                <input aria-label="place_id" cFormControl  [(ngModel)]="place_id" name="place_id" placeholder = "***********" required/>
              </c-col>
            </c-row>
            <c-row class="mb-4">
              <c-col [sm]="4">
                <label  cCol cLabel="col" >
                  是否有設定電子帳單
                </label>
              </c-col>
              <c-col [sm]="3">
                <input class="btn-check"      value="has_TPC_online_bills" name="flexRadioDefault" id="has_TPC_online_bills"  type="radio"  [(ngModel)]="TPC_online_bills_status"  />
                <label    cButton  cFormCheckLabel for ="has_TPC_online_bills" variant="ghost" >已在某帳號中設立電子帳單</label>
              </c-col>
              <c-col [sm]="3">
                <input class="btn-check"     value="not_has_TPC_online_bills" name="flexRadioDefault" id="not_has_TPC_online_bills" type="radio" [(ngModel)]="TPC_online_bills_status"  />
                <label    cButton  cFormCheckLabel for ="not_has_TPC_online_bills" variant="ghost" >未在任何帳號中設立電子帳單</label>
              </c-col>
            </c-row>
            <c-row class="mb-4"  *ngIf="TPC_online_bills_status =='has_TPC_online_bills'">
              <c-row class="mb-4">
                <c-col>
                  <label  cCol cLabel="col" for="account" >
                    帳號
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="account" cFormControl [(ngModel)]="account" name="account" placeholder = "***********" required/>
                </c-col>
              </c-row>
              <c-row >
                <c-col>
                  <label  cCol cLabel="col" for="password" >
                    密碼
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="password" cFormControl [(ngModel)]="password" name="password" placeholder = "***********" required/>
                </c-col>
              </c-row>
            </c-row>
            <c-row   *ngIf="TPC_online_bills_status =='not_has_TPC_online_bills'">
              <c-row class="mb-4" >
                <label [sm]="2" cCol cLabel="col" for="selectmonths">
                  帳單年月
                </label>
                <c-col [sm]="10">
                  <select  aria-label="selectmonths"  [(ngModel)]="selectmonths" cSelect>
                    <option [value]="selection1">{{selection1}}</option>
                    <option [value]="selection2">{{selection2}}</option>
                    <option [value]="selection3">{{selection3}}</option>
                  </select>
                </c-col>
              </c-row>
              <c-row class="mb-4">
                <label [sm]="2" cCol cLabel="col" for="select_authentication_method">
                  檢驗方式
                </label>
                <c-col [sm]="10">
                  <select  aria-label="select_authentication_method"  [(ngModel)]="select_authentication_method" cSelect>
                    <option value="fee_kwh">流動電費金額</option>
                    <option value="last_five_numbers_of_document">單據號碼末五碼</option>
                    <option value="last_five_numbers_of_payment_account">代繳帳號末五碼</option>
                  </select>
                </c-col>
              </c-row>
              <c-row class="mb-4"  *ngIf="select_authentication_method =='last_five_numbers_of_document'">
                <c-col>
                  <label  cCol cLabel="col" for="authentication" >
                    單據號碼末五碼
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="authentication" cFormControl [(ngModel)]="authentication" name="authentication" placeholder = "***********" required/>
                </c-col>
              </c-row>
              <c-row class="mb-4"  *ngIf="select_authentication_method=='last_five_numbers_of_payment_account'">
                <c-col>
                  <label [sm]="8" cCol cLabel="col" for="authentication" >
                    代繳帳號末五碼
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="authentication" cFormControl  [(ngModel)]="authentication" name="authentication" placeholder = "***********" required/>
                </c-col>
              </c-row>
              <c-row class="mb-4"  *ngIf="select_authentication_method=='fee_kwh'">
                <c-col>
                  <label [sm]="10" cCol cLabel="col" for="authentication" >
                    流動電費
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="authentication" cFormControl  [(ngModel)]="authentication" name="authentication" placeholder = "***********" required/>
                </c-col>
              </c-row>
            </c-row>
            <c-row class="mb-4">
              <label [sm]="2" cCol cLabel="col" for="select_legal_entity_type">
                所有人
              </label>
              <c-col [sm]="10">
                <select  aria-label="select_legal_entity_type" [(ngModel)]="select_legal_entity_type"  cSelect>
                  <option value="person">自然人</option>
                  <option value="company">公司法人</option>
                </select>
              </c-col>
            </c-row>
            <c-col *ngIf="select_legal_entity_type=='company'">
              <c-row class="mb-4"  >
                <c-col>
                  <label [sm]="10" cCol cLabel="col" for="legal_entity_id" >
                    公司統編
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="legal_entity_id" cFormControl  [(ngModel)]="legal_entity_id" name="legal_entity_id" placeholder = "00000000" required/>
                </c-col>
              </c-row>
            </c-col>
            <c-col *ngIf="select_legal_entity_type=='person'">
              <c-row class="mb-4"  >
                <c-col>
                  <label [sm]="10" cCol cLabel="col" for="legal_entity_id" >
                    身分證字號
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="legal_entity_id" cFormControl  [(ngModel)]="legal_entity_id" name="legal_entity_id" placeholder = "A123456789" required/>
                </c-col>
              </c-row>
            </c-col>
              <c-row class="mb-4"  >
                <c-col>
                  <label [sm]="10" cCol cLabel="col" for="contacter" >
                    聯絡人
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="contacter" cFormControl  [(ngModel)]="contacter" name="contacter" required/>
                </c-col>
              </c-row>
              <c-row class="mb-4"  >
                <c-col>
                  <label [sm]="10" cCol cLabel="col" for="mobile_phone_number" >
                    聯絡人手機號碼
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="mobile_phone_number" cFormControl  [(ngModel)]="mobile_phone_number" name="mobile_phone_number" placeholder = "0x00000000" required/>
                </c-col>
              </c-row>
              <c-row class="mb-4"  >
                <c-col>
                  <label [sm]="10" cCol cLabel="col" for="telephone_number" >
                    聯絡人電話號碼
                  </label>
                </c-col>  
                <c-col [sm]="10">
                  <input aria-label="telephone_number" cFormControl  [(ngModel)]="telephone_number" name="telephone_number" placeholder = "" required/>
                </c-col>
              </c-row>
            <button cButton variant="outline"
            color="primary" (click)="onSubmit()"  style="float: right;">新增分析需求</button>
          </c-col>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>