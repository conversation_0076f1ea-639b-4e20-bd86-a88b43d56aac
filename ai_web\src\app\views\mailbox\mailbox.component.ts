import {  Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { MailboxService } from '../../service/mailbox.service'
import { data } from 'jquery';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import {
  ButtonCloseDirective, ButtonDirective,
  CardBodyComponent, CardComponent,
  CardHeaderComponent, ColComponent, TableModule,
  GridModule, ModalBodyComponent, ModalComponent,
  ModalFooterComponent, ModalHeaderComponent, ModalTitleDirective,
  ThemeDirective, PageItemDirective, PageLinkDirective, PaginationComponent 
} from '@coreui/angular';
import { RowComponent, TextColorDirective, FormSelectDirective } from '@coreui/angular';
interface Mail {
  id: number;
  sender_name: string;
  receiver: string;
  time: string
  subject: string;
  content: string;
  status: 'send' | 'response'|'failed'|'viewed'|'accepted'|'rejected';
  mail_type: number; 
  mail_token: string;
}

@Component({
  selector: 'app-mailbox',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
      ReactiveFormsModule,
      ButtonCloseDirective,
    ButtonDirective,
    CardBodyComponent, CardComponent, CardHeaderComponent,
    ColComponent, CommonModule, FormsModule, ReactiveFormsModule, TableModule,
    GridModule, RowComponent, TextColorDirective, FormSelectDirective, ModalBodyComponent,
    ModalComponent,
    ModalFooterComponent,
    ModalHeaderComponent,
    ModalTitleDirective,
    ThemeDirective,
    PageItemDirective, PageLinkDirective, PaginationComponent 
  ],
  templateUrl: './mailbox.component.html',
  styleUrl: './mailbox.component.scss'
})

export class MailboxComponent  implements OnInit{
  mails: Mail[] = [];
  selectedMail: Mail | null = null;
  loading = false;
  error = '';
  id: number;
  mail_subject: string;
  receiver: string;
  filterStatus: string = '';
  visible = false;
  sanitizedContent: SafeHtml;
  actionType: 'accept' | 'reject' | null = null;
  currentPage = 1;
  pageSize = 10;
  total = 0;
  get totalPages() {
    return Math.ceil(this.total / this.pageSize) || 1;
  }

  private apiUrls = new Map<string, string>([
    ["專案協作邀請", "project_collaboration"],
    ["專案檢視邀請", "project_invitation"],
    ["角色指派邀請", "role_assignment"]
  ]);
  constructor(private router: Router,private http: HttpClient ,private MailboxService:MailboxService, private sanitizer: DomSanitizer) {
   }
   
  ngOnInit() {
    this.loadMails();
  }
  get filteredMails() {
    return this.filterStatus
      ? this.mails.filter(mail => mail.status === this.filterStatus)
      : this.mails;
  }

  openModal(mail: Mail, action: 'accept' | 'reject'): void {
    this.selectedMail = mail;
    this.actionType = action;
    this.mail_subject = mail.subject;
    this.id = mail.id;
    this.visible = true; 
  }

  cancel(): void {
    this.visible = false; 
    this.selectedMail = null;
    this.actionType = null;
  }
  confirmAction(): void {
    if (!this.selectedMail || !this.actionType) return;
    const mail = this.selectedMail;
    const apiUrl =
      this.actionType === 'accept'
        ? this.getAcceptApiUrl(mail.subject,  mail.mail_token)
        : this.getRejectApiUrl(mail.subject,  mail.mail_token);

    if (!apiUrl) {
      console.error('找不到對應的 API URL');
      return;
    }
    this.MailboxService.responceMail(apiUrl).subscribe(
      (data) => {
        mail.status = this.actionType === 'accept' ? 'accepted' : 'rejected';
        localStorage.setItem('token', data.token);
        this.MailboxService.updateUnreadMailStatus(false);
        this.cancel(); 
      },
      (error) => {
        mail.status = 'failed';
        console.error(`Error ${this.actionType}ing mail:`, error);
        this.cancel();
      }
    );
  }
  getContentPreview(content: string, length: number = 100): SafeHtml {
  if (!content) return this.sanitizer.bypassSecurityTrustHtml('');
  let preview = content.length > length ? content.substring(0, length) + '...' : content;
  return this.sanitizer.bypassSecurityTrustHtml(preview);
  }

  loadMails(page: number = 1): void {
    this.MailboxService.loadMails(page, this.pageSize).subscribe(data => {
      this.mails = data.mail_List;
      this.total = data.total;
      this.currentPage = page;
    },
    (error) => {
      this.error = error.message;
    }
  );
  }
  // 獲取分頁頁碼陣列
  getPaginationPages(): number[] {
    const totalPages = this.getTotalPages();
    
    if (totalPages <= 7) {
      // 如果總頁數少於等於7，顯示所有頁碼
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    } else {
      // 如果總頁數大於7，顯示部分頁碼
      const pages: number[] = [];
      
      // 總是顯示當前頁
      pages.push(this.currentPage);
      
      // 顯示當前頁前後各最多2頁
      for (let i = 1; i <= 2; i++) {
        if (this.currentPage - i > 1) {
          pages.push(this.currentPage - i);
        }
        if (this.currentPage + i < totalPages) {
          pages.push(this.currentPage + i);
        }
      }
      
      // 排序並去重
      return [...new Set(pages)].sort((a, b) => a - b);
    }
  }
  
  // 獲取總頁數
  getTotalPages(): number {
    return this.totalPages;
  }

  getPageArray(totalPages: number): number[] {
    const pages: number[] = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
    return pages;
  }
  onPageChange(page: number): void {
    this.loadMails(page);
  }
  goBack() {
    window.history.back();
  }
  convertContent(content: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(content); 
  }
  viewMailDetails(mail: Mail) {
    this.selectedMail = this.selectedMail?.id === mail.id ? null : mail;
    this.id = mail.id;
    this.mail_subject = mail.subject;
    if (mail.mail_type == 0) {
      this.MailboxService.viewMailDetails(mail.id).subscribe(
        (data) => {
          mail.status = 'viewed';
          this.MailboxService.updateUnreadMailStatus(false);
          localStorage.setItem('token', data.token);
        },
        (error) => {
          console.error('Error loading mail details:', error);
        }
      );
    }
  }
  getApiUrl(action: "accept" | "reject", subject: string, mailToken: string | number): string {
    if (!subject || !mailToken) {
      return '';  
    }
    const apiPath = this.apiUrls.get(subject);
    return apiPath ? `/role_interaction/${action}_${apiPath}/${mailToken}` : '';
  }
  getAcceptApiUrl(subject: string, mailToken: string | number): string {
    return this.getApiUrl("accept", subject, mailToken);
  }
  getRejectApiUrl(subject: string, mailToken: string | number): string {
    return this.getApiUrl("reject", subject, mailToken);
  }
}